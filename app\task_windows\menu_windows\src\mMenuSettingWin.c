/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"


void menuProcDateTime(winHandle handle,u32 parameNum,u32* parame);
void menuProcFormat(winHandle handle,u32 parameNum,u32* parame);
void menuProcDefault(winHandle handle,u32 parameNum,u32* parame);
void menuProcVersion(winHandle handle,u32 parameNum,u32* parame);
void menuProcSreenBright(winHandle handle,u32 parameNum,u32* parame);
void menuProcSysVolume(winHandle handle,u32 parameNum,u32* parame);
MENU_OPTION_START(photoResolution)//--------- photo resolution
	MENU_OPTION_STR(R_ID_STR_RES_48M)
	MENU_OPTION_STR(R_ID_STR_RES_40M)
	MENU_OPTION_STR(R_ID_STR_RES_24M)
	MENU_OPTION_STR(R_ID_STR_RES_20M)
	MENU_OPTION_STR(R_ID_STR_RES_18M)
	MENU_OPTION_STR(R_ID_STR_RES_16M)
	MENU_OPTION_STR(R_ID_STR_RES_12M)
	MENU_OPTION_STR(R_ID_STR_RES_10M)
	MENU_OPTION_STR(R_ID_STR_RES_8M)
	MENU_OPTION_STR(R_ID_STR_RES_5M)
	MENU_OPTION_STR(R_ID_STR_RES_3M)
	MENU_OPTION_STR(R_ID_STR_RES_2M)
	MENU_OPTION_STR(R_ID_STR_RES_1M)
MENU_OPTION_END()

MENU_OPTION_START(captureTimer)//---------video resolution
	MENU_OPTION_STR(R_ID_STR_COM_OFF)
	MENU_OPTION_STR(R_ID_STR_TIM_2SEC)
	MENU_OPTION_STR(R_ID_STR_TIM_5SEC)
	MENU_OPTION_STR(R_ID_STR_TIM_10SEC)
MENU_OPTION_END()

MENU_OPTION_START(videoResolution)//---------video resolution
	MENU_OPTION_STR(R_ID_STR_RES_FHD)
	MENU_OPTION_STR(R_ID_STR_RES_HD)
	MENU_OPTION_STR(R_ID_STR_RES_VGA)
MENU_OPTION_END()

MENU_OPTION_START(loopRecord)//--------loop record
	MENU_OPTION_STR(R_ID_STR_COM_OFF)
	MENU_OPTION_STR(R_ID_STR_TIM_3MIN)
	MENU_OPTION_STR(R_ID_STR_TIM_5MIN)
	MENU_OPTION_STR(R_ID_STR_TIM_10MIN)
MENU_OPTION_END()

MENU_OPTION_START(timeStamp)//--------time stamp
	MENU_OPTION_STR(R_ID_STR_COM_OFF)
	MENU_OPTION_STR(R_ID_STR_COM_ON)
MENU_OPTION_END()

MENU_OPTION_START(language)//--------LANGUAGE
#if CUSTOMER_ADD_HEBREW
	MENU_OPTION_STR(R_ID_STR_LAN_SCHINESE)
	MENU_OPTION_STR(R_ID_STR_LAN_ENGLISH)
	MENU_OPTION_STR(R_ID_STR_LAN_SPANISH)
	MENU_OPTION_STR(R_ID_STR_LAN_FRECH)
	MENU_OPTION_STR(R_ID_STR_LAN_ARABIA)
	MENU_OPTION_STR(R_ID_STR_LAN_GERMAN)
	MENU_OPTION_STR(R_ID_STR_LAN_JAPANESE)
	MENU_OPTION_STR(R_ID_STR_LAN_ITALIAN)
	MENU_OPTION_STR(R_ID_STR_LAN_KOERA)
	MENU_OPTION_STR(R_ID_STR_LAN_RUSSIAN)
	MENU_OPTION_STR(R_ID_STR_LAN_PORTUGUESE)
	MENU_OPTION_STR(R_ID_STR_LAN_DUTCH)
	MENU_OPTION_STR(R_ID_STR_LAN_TURKEY)
	MENU_OPTION_STR(R_ID_STR_LAN_HEBREW)
#elif CUSTOMER_XLT
	MENU_OPTION_STR(R_ID_STR_LAN_SCHINESE)
	MENU_OPTION_STR(R_ID_STR_LAN_ENGLISH)
	MENU_OPTION_STR(R_ID_STR_LAN_SPANISH)
	MENU_OPTION_STR(R_ID_STR_LAN_FRECH)
	MENU_OPTION_STR(R_ID_STR_LAN_ARABIA)
	MENU_OPTION_STR(R_ID_STR_LAN_GERMAN)
	MENU_OPTION_STR(R_ID_STR_LAN_JAPANESE)
	MENU_OPTION_STR(R_ID_STR_LAN_ITALIAN)
	MENU_OPTION_STR(R_ID_STR_LAN_KOERA)
	MENU_OPTION_STR(R_ID_STR_LAN_RUSSIAN)
	MENU_OPTION_STR(R_ID_STR_LAN_PORTUGUESE)
	MENU_OPTION_STR(R_ID_STR_LAN_DUTCH)
	MENU_OPTION_STR(R_ID_STR_LAN_TURKEY)
	MENU_OPTION_STR(R_ID_STR_LAN_POLISH)
#elif CUSTOMER_ADD_ROMANA_MAGYAR
	MENU_OPTION_STR(R_ID_STR_LAN_SCHINESE)
	MENU_OPTION_STR(R_ID_STR_LAN_TCHINESE)
	MENU_OPTION_STR(R_ID_STR_LAN_ENGLISH)
	MENU_OPTION_STR(R_ID_STR_LAN_SPANISH)
	MENU_OPTION_STR(R_ID_STR_LAN_FRECH)
	MENU_OPTION_STR(R_ID_STR_LAN_ARABIA)
	MENU_OPTION_STR(R_ID_STR_LAN_GERMAN)
	MENU_OPTION_STR(R_ID_STR_LAN_ROMANA)
	MENU_OPTION_STR(R_ID_STR_LAN_ITALIAN)
	MENU_OPTION_STR(R_ID_STR_LAN_MAGYAR)
	MENU_OPTION_STR(R_ID_STR_LAN_RUSSIAN)
	MENU_OPTION_STR(R_ID_STR_LAN_PORTUGUESE)
	MENU_OPTION_STR(R_ID_STR_LAN_DUTCH)
	MENU_OPTION_STR(R_ID_STR_LAN_TURKEY)
#elif CUSTOMER_ADD_THAI
	MENU_OPTION_STR(R_ID_STR_LAN_SCHINESE)
	MENU_OPTION_STR(R_ID_STR_LAN_ENGLISH)
	MENU_OPTION_STR(R_ID_STR_LAN_SPANISH)
	MENU_OPTION_STR(R_ID_STR_LAN_FRECH)
	MENU_OPTION_STR(R_ID_STR_LAN_ARABIA)
	MENU_OPTION_STR(R_ID_STR_LAN_GERMAN)
	MENU_OPTION_STR(R_ID_STR_LAN_JAPANESE)
	MENU_OPTION_STR(R_ID_STR_LAN_ITALIAN)
	MENU_OPTION_STR(R_ID_STR_LAN_KOERA)
	MENU_OPTION_STR(R_ID_STR_LAN_RUSSIAN)
	MENU_OPTION_STR(R_ID_STR_LAN_PORTUGUESE)
	MENU_OPTION_STR(R_ID_STR_LAN_DUTCH)
	MENU_OPTION_STR(R_ID_STR_LAN_TURKEY)
	MENU_OPTION_STR(R_ID_STR_LAN_TAI)


#elif CUSTOMER_ADD_French

	MENU_OPTION_STR(R_ID_STR_LAN_ENGLISH)
	MENU_OPTION_STR(R_ID_STR_LAN_SCHINESE)
	MENU_OPTION_STR(R_ID_STR_LAN_TCHINESE)
	MENU_OPTION_STR(R_ID_STR_LAN_JAPANESE)
	MENU_OPTION_STR(R_ID_STR_LAN_KOERA)
	MENU_OPTION_STR(R_ID_STR_LAN_RUSSIAN)
	MENU_OPTION_STR(R_ID_STR_LAN_FRENCH)
	MENU_OPTION_STR(R_ID_STR_LAN_GERMAN)
	MENU_OPTION_STR(R_ID_STR_LAN_CZECH)
	MENU_OPTION_STR(R_ID_STR_LAN_PORTUGUESE)
	MENU_OPTION_STR(R_ID_STR_LAN_POLISH)
	MENU_OPTION_STR(R_ID_STR_LAN_DUTCH)
	MENU_OPTION_STR(R_ID_STR_LAN_TURKEY)
	MENU_OPTION_STR(R_ID_STR_LAN_TAI)
	MENU_OPTION_STR(R_ID_STR_LAN_SPANISH)
	MENU_OPTION_STR(R_ID_STR_LAN_ITALIAN)

#elif CUSTOMER_Poland_1
	MENU_OPTION_STR(R_ID_STR_LAN_POLISH)

	MENU_OPTION_STR(R_ID_STR_LAN_SCHINESE)
	MENU_OPTION_STR(R_ID_STR_LAN_TCHINESE)
	MENU_OPTION_STR(R_ID_STR_LAN_ENGLISH)
	MENU_OPTION_STR(R_ID_STR_LAN_SPANISH)
	MENU_OPTION_STR(R_ID_STR_LAN_FRECH)
	MENU_OPTION_STR(R_ID_STR_LAN_ARABIA)
	MENU_OPTION_STR(R_ID_STR_LAN_GERMAN)
	MENU_OPTION_STR(R_ID_STR_LAN_JAPANESE)
	MENU_OPTION_STR(R_ID_STR_LAN_ITALIAN)
	MENU_OPTION_STR(R_ID_STR_LAN_KOERA)
	MENU_OPTION_STR(R_ID_STR_LAN_RUSSIAN)
	MENU_OPTION_STR(R_ID_STR_LAN_PORTUGUESE)
	MENU_OPTION_STR(R_ID_STR_LAN_DUTCH)
	MENU_OPTION_STR(R_ID_STR_LAN_TURKEY)
#elif CUSTOMER_Croatian_Slovenian
	MENU_OPTION_STR(R_ID_STR_LAN_ENGLISH)
	MENU_OPTION_STR(R_ID_STR_LAN_CROATIA)
	MENU_OPTION_STR(R_ID_STR_LAN_SLOVENIAN)
	MENU_OPTION_STR(R_ID_STR_LAN_SCHINESE)
	MENU_OPTION_STR(R_ID_STR_LAN_TCHINESE)
	MENU_OPTION_STR(R_ID_STR_LAN_JAPANESE)
	MENU_OPTION_STR(R_ID_STR_LAN_RUSSIAN)
	MENU_OPTION_STR(R_ID_STR_LAN_SPANISH)
	MENU_OPTION_STR(R_ID_STR_LAN_GERMAN)
	MENU_OPTION_STR(R_ID_STR_LAN_FRECH)
	MENU_OPTION_STR(R_ID_STR_LAN_ITALIAN)
	MENU_OPTION_STR(R_ID_STR_LAN_PORTUGUESE)

#elif CUSTOMER_Poland

	MENU_OPTION_STR(R_ID_STR_LAN_ENGLISH)
	MENU_OPTION_STR(R_ID_STR_LAN_SCHINESE)
	MENU_OPTION_STR(R_ID_STR_LAN_TCHINESE)
	MENU_OPTION_STR(R_ID_STR_LAN_SPANISH)
	MENU_OPTION_STR(R_ID_STR_LAN_FRECH)
	MENU_OPTION_STR(R_ID_STR_LAN_ARABIA)
	MENU_OPTION_STR(R_ID_STR_LAN_GERMAN)
	MENU_OPTION_STR(R_ID_STR_LAN_JAPANESE)
	MENU_OPTION_STR(R_ID_STR_LAN_ITALIAN)
	MENU_OPTION_STR(R_ID_STR_LAN_KOERA)
	MENU_OPTION_STR(R_ID_STR_LAN_RUSSIAN)
	MENU_OPTION_STR(R_ID_STR_LAN_PORTUGUESE)
	MENU_OPTION_STR(R_ID_STR_LAN_DUTCH)
	MENU_OPTION_STR(R_ID_STR_LAN_TURKEY)
	MENU_OPTION_STR(R_ID_STR_LAN_POLISH)

#else


	MENU_OPTION_STR(R_ID_STR_LAN_ENGLISH)//英
	MENU_OPTION_STR(R_ID_STR_LAN_SCHINESE)//中
	MENU_OPTION_STR(R_ID_STR_LAN_TCHINESE)//繁
	MENU_OPTION_STR(R_ID_STR_LAN_JAPANESE)//日
#if JAPANESE_HIRAGANA
	MENU_OPTION_STR(R_ID_STR_LAN_JAPANESE_HIRAGANA)//日
#endif
	MENU_OPTION_STR(R_ID_STR_LAN_KOERA)//韩
	MENU_OPTION_STR(R_ID_STR_LAN_RUSSIAN)//俄语
	MENU_OPTION_STR(R_ID_STR_LAN_FRECH)//法语
	MENU_OPTION_STR(R_ID_STR_LAN_GERMAN)//德语
	MENU_OPTION_STR(R_ID_STR_LAN_ARABIA)//阿拉伯
	MENU_OPTION_STR(R_ID_STR_LAN_ITALIAN)//意大利
	MENU_OPTION_STR(R_ID_STR_LAN_SPANISH)//西班牙
	MENU_OPTION_STR(R_ID_STR_LAN_PORTUGUESE)//葡萄牙
	MENU_OPTION_STR(R_ID_STR_LAN_DUTCH)//荷兰
	MENU_OPTION_STR(R_ID_STR_LAN_TURKEY)//土耳其
//	MENU_OPTION_STR(R_ID_STR_LAN_TAI)//泰

	// MENU_OPTION_STR(R_ID_STR_LAN_HEBREW)
#endif


MENU_OPTION_END()

//MENU_OPTION_START(sreen_bright)//--------sreen
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_1)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_2)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_3)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_4)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_5)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_6)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_7)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_8)
//MENU_OPTION_END()

MENU_OPTION_START(screenSave)//--------screen Save
	MENU_OPTION_STR(R_ID_STR_COM_OFF)
	MENU_OPTION_STR(R_ID_STR_TIM_1MIN)
	MENU_OPTION_STR(R_ID_STR_TIM_2MIN)
	MENU_OPTION_STR(R_ID_STR_TIM_3MIN)
MENU_OPTION_END()

MENU_OPTION_START(autoPowerOff)//----AUTO POWER OFF
	MENU_OPTION_STR(R_ID_STR_COM_OFF)
	MENU_OPTION_STR(R_ID_STR_TIM_3MIN)
	MENU_OPTION_STR(R_ID_STR_TIM_5MIN)
MENU_OPTION_END()

//MENU_OPTION_START(systermVolume)//--------key sound
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_0)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_1)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_2)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_3)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_4)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_5)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_6)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_7)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_8)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_9)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_10)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_11)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_12)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_13)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_14)
//	MENU_OPTION_STR(R_ID_STR_COM_VALUE_15)
//MENU_OPTION_END()









MENU_ITME_START(setting)
	MENU_ITEM_OPTIONS(photoResolution,	CONFIG_ID_PRESLUTION,	0,	R_ID_STR_SET_QUALITY)
	MENU_ITEM_OPTIONS(captureTimer,		CONFIG_ID_CAPTURE_TIME,	0,	R_ID_STR_SET_CAPTURE_TIMER)
	MENU_ITEM_OPTIONS(videoResolution,	CONFIG_ID_RESOLUTION,	0,	R_ID_STR_SET_RESOLUTION)

	MENU_ITEM_OPTIONS(loopRecord,		CONFIG_ID_LOOPTIME,		0,	R_ID_STR_SET_LOOPRECORD)


	
	MENU_ITEM_OPTIONS(timeStamp,		CONFIG_ID_TIMESTAMP,	0,	R_ID_STR_SET_TIMESTRAMP)
	MENU_ITEM_OPTIONS(language,			CONFIG_ID_LANGUAGE,		0,	R_ID_STR_SET_LANGUAGE)
	//MENU_ITEM_OPTIONS(sreen_bright,		CONFIG_ID_SREEN_BRIGHT,	0,	R_ID_STR_SET_SREEN_BRIGHT)
	MENU_ITEM_OPTIONS(screenSave,		CONFIG_ID_SCREENSAVE,	0,	R_ID_STR_SET_SCREENOFF)
	MENU_ITEM_OPTIONS(autoPowerOff,		CONFIG_ID_AUTOOFF,		0,	R_ID_STR_SET_AUTOOFF)
	MENU_ITEM_PROC(menuProcSreenBright,	0,	R_ID_STR_SET_SREEN_BRIGHT)
	MENU_ITEM_PROC(menuProcSysVolume,	0,	R_ID_STR_SET_SYSTEM_VOLUME)

	//MENU_ITEM_OPTIONS(systermVolume,	CONFIG_ID_SYSTEM_VOLUME,0,	R_ID_STR_SET_SYSTEM_VOLUME)

	MENU_ITEM_PROC(menuProcFormat,		0,	R_ID_STR_SET_FORMAT)
	MENU_ITEM_PROC(menuProcDateTime,	0,	R_ID_STR_SET_DATETIME)
	MENU_ITEM_PROC(menuProcDefault,		0,	R_ID_STR_SET_RESET)
	MENU_ITEM_PROC(menuProcVersion,		0,	R_ID_STR_SET_VERSION)



MENU_ITME_END()

//MENU_ITME_START(multiplePagesTest)
//	MENU_ITEM_OPTIONS(ev,CONFIG_ID_EV,R_ID_ICON_MENUEV,R_ID_STR_ISP_EXPOSURE)
//	MENU_ITEM_OPTIONS(md,CONFIG_ID_MOTIONDECTION,R_ID_ICON_MENUMOTION,R_ID_STR_SET_MOTIONDET)
//MENU_ITME_END()


MENU_PAGE_START(setting)
	MENU_PAGE_ITEMS(setting,R_ID_ICON_MTSETTING,R_ID_ICON_MTSETTING,0)
//MENU_PAGE_ITEMS(multiplePagesTest,R_ID_ICON_MTRECORD,R_ID_ICON_MTRECORD,R_ID_STR_SET_VIDEO)
MENU_PAGE_END()

MENU_DEFINE(setting)




