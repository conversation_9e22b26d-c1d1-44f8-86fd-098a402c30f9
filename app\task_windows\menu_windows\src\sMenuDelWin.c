/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	DEL_RECT_ID = 0,
	DEL_SELECT_ID
};

UNUSED ALIGNED(4) const widgetCreateInfor delWin[] =
{
	createFrameWin(						Rx(58),	Ry(58), Rw(204),Rh(124),SMENU_FRAME_COLOR,WIN_ABS_POS),
	createRect(DEL_RECT_ID,          	Rx(2),	<PERSON>y(2),  <PERSON>w(200),<PERSON>h(120),SMENU_UNSELECT_BG_COLOR),
	createItemManage(DEL_SELECT_ID,		Rx(2),	<PERSON>y(2),  <PERSON>w(200),Rh(120), SMENU_UNSELECT_BG_COLOR),
	widgetEnd(),
};


