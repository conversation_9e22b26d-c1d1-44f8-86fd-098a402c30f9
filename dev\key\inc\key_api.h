/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef KEY_API_H
#define KEY_API_H
/*******************************************************************************
* Function Name  : dev_key_init
* Description    : dev_key_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_key_init(void);
/*******************************************************************************
* Function Name  : dev_key_ioctrl
* Description    : dev_key_ioctrl
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_key_ioctrl(u32 op, u32 para);
/*******************************************************************************
* Function Name  : getKeyADCvalue
* Description    : getKeyADCvalue
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
u32 getKeyADCvalue(void);
/*******************************************************************************
* Function Name  : getKeyADCvalue
* Description    : getKeyADCvalue
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
u32 getKeyCurEvent(void);
/*******************************************************************************
* Function Name  : dev_key_ADCScan
* Description    : dev_key_ADCScan, updata dev_key_ctl.cur_key_event
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
u32 dev_key_ADCScan_Fast(void);
#endif
