/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"
#if NES_FUNC_SUPPORT
typedef struct NES_GAMES_OP_S{
    //int open_index;
    u32 src_type;
    //int cur_index;
    u32 start_addr;
    u32 total_len;
    u32 cur_offset;
}NES_GAMES_OP_T;
NESDATA_SECTION NES_GAMES_OP_T nes_games_op;
/*******************************************************************************
* Function Name  : nes_games_open
* Description    : The main loop of InfoNES
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/

int nes_games_open(int fd, u32 src_type)
{
    int res = 0;
    if(src_type == GAME_SRC_FS)
    {
        if(fd < 0)
        {
            res = -1;
            goto GAMES_OPEN_END;
        }
    }else if(src_type == GAME_SRC_NVFS)
    {
        //if(fd < 0 || fd >= ARRAY_NUM(nes_game_tab))
        //{
        //    res = -2;
        //    goto GAMES_OPEN_END;
        //}
		nes_games_op.start_addr = nv_open(fd);
		if(nes_games_op.start_addr < 0)
        {
            res = -2;
            goto GAMES_OPEN_END;
        }
		nes_games_op.total_len = nv_size(fd);
        //nes_games_op.start_addr = (u32)NES_RES_FLASH_ADDR(nes_game_tab[fd].game_tab);
        //nes_games_op.total_len  = nes_game_tab[fd].game_len;
        deg_Printf("[NES GAMES INNER]  addr :%x, len: %d\n",nes_games_op.start_addr,nes_games_op.total_len);
    }else if(src_type == GAME_SRC_RAM)
    {
        if(fd < 0)
        {
            res = -3;
            goto GAMES_OPEN_END;
        }
		nes_games_op.start_addr = fd;

    }else
    {
        res = -4;
        goto GAMES_OPEN_END;
    }
    deg_Printf("[NES GAME]open ok:%d,%d\n",src_type,fd);
    nes_games_op.src_type = src_type;
    nes_games_op.cur_offset = 0;
	//nes_games_op.cur_index = fd;
GAMES_OPEN_END:
    //if(res < 0 )
    //{
        //nes_games_op.cur_index = -1;
    //}
    return res;
}
/*******************************************************************************
* Function Name  : nes_games_read
* Description    : The main loop of InfoNES
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
int nes_games_read(int fd,void *buff,UINT len)
{
    //if(nes_games_op.cur_index < 0)
    //    return -1;
    if(nes_games_op.src_type == GAME_SRC_FS)
    {
        return fs_read(fd, (void*)buff,len);
    }else if(nes_games_op.src_type == GAME_SRC_RAM)
    {
        hx330x_bytes_memcpy((u8*)buff, (u8*)fd + nes_games_op.cur_offset, (u32)len);
        nes_games_op.cur_offset += len;
        return len;
    }else if(nes_games_op.src_type == GAME_SRC_NVFS)
    {
        //if(nes_games_op.cur_index != fd)
        //{
        //    return -2;
        //}
        if(nes_games_op.cur_offset  > nes_games_op.total_len )
        {
            return -3;
        }
        len = (nes_games_op.cur_offset + len > nes_games_op.total_len) ? (nes_games_op.total_len - nes_games_op.cur_offset) : len;
        nv_read((int)(nes_games_op.start_addr + nes_games_op.cur_offset), buff, len);
		//debgbuf(buff, 8);
        nes_games_op.cur_offset += len;
        return len;
    }
    return -1;
}
/*******************************************************************************
* Function Name  : nes_games_service
* Description    : nes_games_service
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
//void nes_games_service(void)
//{
//   nes_games_op.open_index = 0;
//    while(1)
//    {
//        if(nes_start(nes_games_op.open_index++, GAME_SRC_NVFS) < 0)
//            break;
//        nes_service();
//    }
//}
/*******************************************************************************
* Function Name  : nes_PadStateCheck
* Description    : Get a joypad state
* Input          : none
* Output         : none
* Return         :  0 : Normally
 *   				-1 : Exit an emulation
*******************************************************************************/
#if 0
typedef struct NES_PAD_TYPE_S{
	u16  key_event;
	u16  nes_pad_event;
}NES_PAD_TYPE_T;
NES_RODATA_SECTION
NES_PAD_TYPE_T nes_pad1_tab[] ={
	{ KEY_EVENT_POWER, 	NES_PAD_SYS_START },
	{ KEY_EVENT_OK,		NES_PAD_SYS_A },
	{ KEY_EVENT_UP,		NES_PAD_SYS_UP },
	{ KEY_EVENT_DOWN,	NES_PAD_SYS_DOWN },
	{ KEY_EVENT_RIGHT,	NES_PAD_SYS_RIGHT },
	{ KEY_EVENT_LEFT, 	NES_PAD_SYS_LEFT },
};


NES_TEXT_SECTION
void nes_PadStateCheck(void)
{

	u32 i;
	u32 cur_event = 0;
	nes_PAD1_Latch = 0;
	nes_PAD2_Latch = 0;
	nes_PAD_System = 0;
	u32 key_type = dev_key_ADCScan_Fast();
	if(key_type)
	{
		u16 pad1_key = key_type&0xffff;
		if(pad1_key)
		{
			//deg_Printf("pad1_key:%d\n",pad1_key);
			for(i = 0; i < sizeof(nes_pad1_tab)/sizeof(nes_pad1_tab[0]);i++)
			{
				if(nes_pad1_tab[i].key_event == pad1_key)
				{
					cur_event = nes_pad1_tab[i].nes_pad_event;
					//nes_PAD1_Latch = nes_pad1_tab[i].nes_pad_event;
					//deg_Printf("nes_pad_event:%d\n",nes_pad1_tab[i].nes_pad_event);
					break;
				}
			}
		}
		//deg_Printf("cur_event:%x,%x\n",cur_event,pad_last_event);
		//if(nes_PAD1_Latch == NES_PAD_SYS_SELECT)
		//	nes_PAD_System = NES_PAD_SYS_QUIT;

	}

	if(cur_event != pad_last_event)
	{
		if(pad_last_event)
		{
			//deg_Printf("pad_cnt:%d\n",pad_cnt);
			if(pad_last_event == NES_PAD_SYS_START)
			{
				if(pad_cnt > 15)
				{
					nes_PAD_System = NES_PAD_SYS_QUIT;
				}else
				{
					nes_PAD1_Latch |= NES_PAD_SYS_START;
				}
				goto PAD_CHECK_OUT;
			}
		}
		if(cur_event == NES_PAD_SYS_START)
		{
			goto PAD_CHECK_OUT;
		}
		pad_cnt = 0;
		pad_last_event = cur_event;

	}else
	{
		if(pad_last_event == NES_PAD_SYS_START)
		{
			pad_cnt++;
			return;
		}

	}
	if(cur_event)
	{
		nes_PAD1_Latch |= cur_event;
	}
	return;
PAD_CHECK_OUT:
	pad_cnt = 0;
	pad_last_event = cur_event;
	return;

}
#else
typedef struct NES_PAD_TYPE_S{
	u16  key_event;
	u16  nes_pad_event;
}NES_PAD_TYPE_T;
NES_RODATA_SECTION
NES_PAD_TYPE_T nes_pad1_tab[] ={
	{ KEY_EVENT_POWER, 	NES_PAD_SYS_QUIT },
	{ KEY_EVENT_OK,		NES_PAD_SYS_A },
	{ KEY_EVENT_UP,		NES_PAD_SYS_UP },
	{ KEY_EVENT_DOWN,	NES_PAD_SYS_DOWN },
	{ KEY_EVENT_RIGHT,	NES_PAD_SYS_RIGHT },
	{ KEY_EVENT_LEFT, 	NES_PAD_SYS_LEFT },
};

extern BYTE 			nes_reserve1;	
NES_TEXT_SECTION
void nes_PadStateCheck(void)
{

	u32 i;
	u32 cur_event = 0;
	nes_PAD1_Latch = 0;
	nes_PAD2_Latch = 0;
	nes_PAD_System = 0;
	u32 key_type = dev_key_ADCScan_Fast();
	if(key_type)
	{
		u16 pad1_key = key_type&0xffff;
		if(pad1_key)
		{
			//deg_Printf("pad1_key:%d\n",pad1_key);
			for(i = 0; i < sizeof(nes_pad1_tab)/sizeof(nes_pad1_tab[0]);i++)
			{
				if(nes_pad1_tab[i].key_event == pad1_key)
				{
					cur_event = nes_pad1_tab[i].nes_pad_event;
					//nes_PAD1_Latch = nes_pad1_tab[i].nes_pad_event;
					//deg_Printf("nes_pad_event:%d\n",nes_pad1_tab[i].nes_pad_event);
					break;
				}
			}
		}
		//deg_Printf("cur_event:%x,%x\n",cur_event,pad_last_event);
		//if(nes_PAD1_Latch == NES_PAD_SYS_SELECT)
		//	nes_PAD_System = NES_PAD_SYS_QUIT;

	}
	if(cur_event == NES_PAD_SYS_QUIT)
	{
		goto PAD_CHECK_OUT; 
	}else if(cur_event == NES_PAD_SYS_A && !(nes_reserve1 & 0x40))
	{
		cur_event = NES_PAD_SYS_START;
	}
	else{
		if(pad_last_event == NES_PAD_SYS_QUIT)
		{
			nes_PAD_System = NES_PAD_SYS_QUIT;
		}
		else if(pad_last_event == NES_PAD_SYS_START)
		{
			nes_PAD1_Latch |= NES_PAD_SYS_START;
		}
		else if(cur_event)
		{
			//if(cur_event == NES_PAD_SYS_A && !(nes_reserve1 & 0x40))
			//{
			//	nes_PAD1_Latch |= NES_PAD_SYS_START;
			//}else
			{
				nes_PAD1_Latch |= cur_event;
			}	
		}
	}
PAD_CHECK_OUT:
	pad_last_event = cur_event;

}
#endif

#endif

	
	