/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          : user_res.h
* Author             :
* Version            : v1
* Date               :
* Description        :
*******************************************************************/

#ifndef USER_RES_H
#define USER_RES_H

#define R_ID_MUSIC_TAKE_PHOTO           0
#define R_ID_MUSIC_POWER_ON             1
#define R_ID_MUSIC_POWER_OFF            2
#define R_ID_MUSIC_KEY_SOUND            3
#define R_ID_MUSIC_GAMES_MUSIC          4
#define R_ID_MUSIC_FOCUS                5
#define R_ID_MUSIC_BOOM                 6
#define R_ID_MUSIC_3660                 7
#define R_ID_IMAGE_USB_MODE             8
#define R_ID_IMAGE_SNAKE2               9
#define R_ID_IMAGE_SNAKE1               10
#define R_ID_IMAGE_SETTING_BACKGROUND   11
#define R_ID_IMAGE_PUSHBOX2             12
#define R_ID_IMAGE_PUSHBOX1             13
#define R_ID_IMAGE_POWER_ON             14
#define R_ID_IMAGE_POWER_OFF            15
#define R_ID_IMAGE_MUSIC_PLAY           16
#define R_ID_IMAGE_MAZE2                17
#define R_ID_IMAGE_MAZE1                18
#define R_ID_IMAGE_MAIN_VIDEO           19
#define R_ID_IMAGE_MAIN_VER             20
#define R_ID_IMAGE_MAIN_SETTINGS        21
#define R_ID_IMAGE_MAIN_PLAY            22
#define R_ID_IMAGE_MAIN_PHOTO           23
#define R_ID_IMAGE_MAIN_MP3             24
#define R_ID_IMAGE_MAIN_GAME            25
#define R_ID_IMAGE_MAIN_BACKGROUND      26
#define R_ID_IMAGE_ICON_FRAME9          27
#define R_ID_IMAGE_ICON_FRAME8          28
#define R_ID_IMAGE_ICON_FRAME7          29
#define R_ID_IMAGE_ICON_FRAME6          30
#define R_ID_IMAGE_ICON_FRAME5          31
#define R_ID_IMAGE_ICON_FRAME4          32
#define R_ID_IMAGE_ICON_FRAME3          33
#define R_ID_IMAGE_ICON_FRAME27         34
#define R_ID_IMAGE_ICON_FRAME26         35
#define R_ID_IMAGE_ICON_FRAME25         36
#define R_ID_IMAGE_ICON_FRAME24         37
#define R_ID_IMAGE_ICON_FRAME23         38
#define R_ID_IMAGE_ICON_FRAME22         39
#define R_ID_IMAGE_ICON_FRAME21         40
#define R_ID_IMAGE_ICON_FRAME20         41
#define R_ID_IMAGE_ICON_FRAME2          42
#define R_ID_IMAGE_ICON_FRAME19         43
#define R_ID_IMAGE_ICON_FRAME18         44
#define R_ID_IMAGE_ICON_FRAME17         45
#define R_ID_IMAGE_ICON_FRAME16         46
#define R_ID_IMAGE_ICON_FRAME15         47
#define R_ID_IMAGE_ICON_FRAME14         48
#define R_ID_IMAGE_ICON_FRAME13         49
#define R_ID_IMAGE_ICON_FRAME12         50
#define R_ID_IMAGE_ICON_FRAME11         51
#define R_ID_IMAGE_ICON_FRAME10         52
#define R_ID_IMAGE_ICON_FRAME1          53
#define R_ID_IMAGE_ICON_FRAME0          54
#define R_ID_IMAGE_FLY2                 55
#define R_ID_IMAGE_FLY1                 56
#define R_ID_IMAGE_BAT_SUB              57
#define R_ID_IMAGE_BAT_MAIN             58
#define R_ID_IMAGE_20482                59
#define R_ID_IMAGE_20481                60
#define R_ID_BIN_VERSION                61
#define R_ID_BIN_FONTLIB                62
#define R_ID_BIN_LANGSTR                63
#define R_ID_BIN_ICONLIB                64
#define R_ID_BIN_PALETTE                65

extern R_STRING_T User_String_Table[];

enum r_str_id_e {
    R_ID_STR_LAN_ENGLISH = RES_ID_TYPE_STR,
    R_ID_STR_LAN_SCHINESE,
    R_ID_STR_LAN_TCHINESE,
    R_ID_STR_LAN_JAPANESE,
    R_ID_STR_LAN_KOERA,
    R_ID_STR_LAN_RUSSIAN,
    R_ID_STR_LAN_TURKEY,
    R_ID_STR_LAN_TAI,
    R_ID_STR_LAN_CZECH,
    R_ID_STR_LAN_SPANISH,
    R_ID_STR_LAN_UKRAINIAN,
    R_ID_STR_LAN_GERMAN,
    R_ID_STR_LAN_FRECH,
    R_ID_STR_LAN_ITALIAN,
    R_ID_STR_LAN_PORTUGUESE,
    R_ID_STR_LAN_DUTCH,
    R_ID_STR_LAN_HEBREW,
    R_ID_STR_LAN_POLISH,
    R_ID_STR_LAN_ARABIA,
    R_ID_STR_LAN_CROATIA,
    R_ID_STR_LAN_SLOVENIAN,
    R_ID_STR_UPDATE_START,
    R_ID_STR_UPDATE_END,
    R_ID_STR_COM_OFF,
    R_ID_STR_COM_ON,
    R_ID_STR_COM_OK,
    R_ID_STR_COM_CANCEL,
    R_ID_STR_COM_YES,
    R_ID_STR_DC_OUT,
    R_ID_STR_COM_NO,
    R_ID_STR_COM_LOW,
    R_ID_STR_COM_MIDDLE,
    R_ID_STR_COM_HIGH,
    R_ID_STR_COM_WAITING,
    R_ID_STR_COM_50HZ,
    R_ID_STR_COM_60HZ,
    R_ID_STR_COM_P2_0,
    R_ID_STR_COM_P1_0,
    R_ID_STR_COM_P0_0,
    R_ID_STR_COM_N1_0,
    R_ID_STR_COM_N2_0,
    R_ID_STR_COM_ALWAYSON,
    R_ID_STR_COM_ECONOMIC,
    R_ID_STR_COM_NORMAL,
    R_ID_STR_COM_FINE,
    R_ID_STR_TIM_1MIN,
    R_ID_STR_TIM_2MIN,
    R_ID_STR_TIM_3MIN,
    R_ID_STR_TIM_5MIN,
    R_ID_STR_TIM_10MIN,
    R_ID_STR_TIM_2SEC,
    R_ID_STR_TIM_5SEC,
    R_ID_STR_TIM_10SEC,
    R_ID_STR_TIM_30SEC,
    R_ID_STR_SET_DATETIME,
    R_ID_STR_SET_AUTOOFF,
    R_ID_STR_SET_LANGUAGE,
    R_ID_STR_SET_SCREENOFF,
    R_ID_STR_SET_VIDEOROTATE,
    R_ID_STR_SET_RESET,
    R_ID_STR_SET_FORMAT,
    R_ID_STR_SET_VERSION,
    R_ID_STR_SET_RESOLUTION,
    R_ID_STR_SET_LOOPRECORD,
    R_ID_STR_SET_MOTIONDET,
    R_ID_STR_SET_AUDIOREC,
    R_ID_STR_SET_TIMESTRAMP,
    R_ID_STR_SET_FILLIGHT,
    R_ID_STR_IR_AUTO,
    R_ID_STR_SET_GSENSOR,
    R_ID_STR_SET_PARKMODE,
    R_ID_STR_SET_FASTVIEW,
    R_ID_STR_SET_DELETECUR,
    R_ID_STR_SET_DELETEALL,
    R_ID_STR_SET_LOCKCUR,
    R_ID_STR_SET_LOCKALL,
    R_ID_STR_SET_UNLOCKCUR,
    R_ID_STR_SET_UNLOCKALL,
    R_ID_STR_SET_LOCKED,
    R_ID_STR_SET_LOCK,
    R_ID_STR_SET_DELETE,
    R_ID_STR_SET_PLAYMODE,
    R_ID_STR_SET_REPEATALL,
    R_ID_STR_SET_REPEATRAD,
    R_ID_STR_SET_REPEATSIG,
    R_ID_STR_SET_AUDIOPLAY,
    R_ID_STR_SET_VOLUME,
    R_ID_STR_SET_THUMBNAIL,
    R_ID_STR_SET_SETTING,
    R_ID_STR_SET_VIDEO,
    R_ID_STR_SET_PHOTO,
    R_ID_STR_SET_PLAY,
    R_ID_STR_SET_MUSIC,
    R_ID_STR_SET_AUDIOMODE,
    R_ID_STR_SET_USBMASS,
    R_ID_STR_SET_USBCAM,
    R_ID_STR_SET_BEEPSOUND,
    R_ID_STR_SET_FREQUENCY,
    R_ID_STR_SET_QUALITY,
    R_ID_STR_SET_PROMT,
    R_ID_STR_RES_240P,
    R_ID_STR_RES_480P,
    R_ID_STR_RES_480FHD,
    R_ID_STR_RES_720P,
    R_ID_STR_RES_1024P,
    R_ID_STR_RES_1080P,
    R_ID_STR_RES_1080FHD,
    R_ID_STR_RES_1440P,
    R_ID_STR_RES_3024P,
    R_ID_STR_RES_QVGA,
    R_ID_STR_RES_VGA,
    R_ID_STR_RES_HD,
    R_ID_STR_RES_FHD,
    R_ID_STR_RES_48M,
    R_ID_STR_RES_40M,
    R_ID_STR_RES_24M,
    R_ID_STR_RES_20M,
    R_ID_STR_RES_18M,
    R_ID_STR_RES_16M,
    R_ID_STR_RES_12M,
    R_ID_STR_RES_10M,
    R_ID_STR_RES_8M,
    R_ID_STR_RES_5M,
    R_ID_STR_RES_3M,
    R_ID_STR_RES_2M,
    R_ID_STR_RES_1M,
    R_ID_STR_SDC_NULL,
    R_ID_STR_SDC_NULL1,
    R_ID_STR_SDC_FULL,
    R_ID_STR_SDC_ERROR,
    R_ID_STR_FIL_NULL,
    R_ID_STR_FIL_LOCKED,
    R_ID_STR_FMT_ING,
    R_ID_STR_FMT_SUCCESS,
    R_ID_STR_FMT_FAIL,
    R_ID_STR_FMT_RESET,
    R_ID_STR_FMT_FORMAT,
    R_ID_STR_FMT_DELETE,
    R_ID_STR_PWR_LOW,
    R_ID_STR_PWR_NO,
    R_ID_STR_PWR_BACKLOW,
    R_ID_STR_PWR_CHARGELOW,
    R_ID_STR_ISP_WHITEBL,
    R_ID_STR_ISP_ISO,
    R_ID_STR_ISP_ANTISHANK,
    R_ID_STR_ISP_AUTO,
    R_ID_STR_ISP_SOFT,
    R_ID_STR_ISP_STRONG,
    R_ID_STR_ISP_SUNLIGHT,
    R_ID_STR_ISP_CLOUDY,
    R_ID_STR_ISP_TUNGSTEN,
    R_ID_STR_ISP_FLUORESCENT,
    R_ID_STR_ISP_BLACKWHITE,
    R_ID_STR_ISP_SEPIA,
    R_ID_STR_ISP_ISO100,
    R_ID_STR_ISP_ISO200,
    R_ID_STR_ISP_ISO400,
    R_ID_STR_ISP_WDR,
    R_ID_STR_ISP_EXPOSURE,
    R_ID_STR_COM_SUCCESS,
    R_ID_STR_COM_FAILED,
    R_ID_STR_SET_CAPTURE_TIMER,
    R_ID_STR_SET_SYSTEM_VOLUME,
    R_ID_STR_SET_SREEN_BRIGHT,
    R_ID_STR_SET_SLOW,
    R_ID_STR_SET_FAST,
    R_ID_STR_COM_VALUE_0,
    R_ID_STR_COM_VALUE_1,
    R_ID_STR_COM_VALUE_2,
    R_ID_STR_COM_VALUE_3,
    R_ID_STR_COM_VALUE_4,
    R_ID_STR_COM_VALUE_5,
    R_ID_STR_COM_VALUE_6,
    R_ID_STR_COM_VALUE_7,
    R_ID_STR_COM_VALUE_8,
    R_ID_STR_COM_VALUE_9,
    R_ID_STR_COM_VALUE_10,
    R_ID_STR_COM_VALUE_11,
    R_ID_STR_COM_VALUE_12,
    R_ID_STR_COM_VALUE_13,
    R_ID_STR_COM_VALUE_14,
    R_ID_STR_COM_VALUE_15,
    R_ID_STR_TIPS_SREENSAVE,
    R_ID_STR_GAME_PASS,
    R_ID_STR_GAME_OVER,
    R_STR_MAX
};

enum r_lan_id_e {
    LAN_ENGLISH,
    LAN_SCHINESE,
    LAN_TCHINESE,
    LAN_JAPANESE,
    LAN_KOERA,
    LAN_RUSSIAN,
    LAN_TURKEY,
    LAN_TAI,
    LAN_CZECH,
    LAN_SPANISH,
    LAN_ARABIA,
    LAN_GERMAN,
    LAN_FRENCH,
    LAN_ITALIAN,
    LAN_PORTUGUESE,
    LAN_DUTCH,
    LAN_HEBREW,
    LAN_POLISH,
    LAN_SLOVENIAN,
    LAN_CROATIA,
    LAN_MAX
};

extern R_ICON_T User_Icon_Table[];

enum r_icon_id_e {
    R_ID_ICON_GAME_TIPS_ICON = RES_ID_TYPE_ICON,
    R_ID_ICON_MENUCLOCK,
    R_ID_ICON_MENUVOLUME,
    R_ID_ICON_MTBATTERY0,
    R_ID_ICON_MTBATTERY1,
    R_ID_ICON_MTBATTERY2,
    R_ID_ICON_MTBATTERY3,
    R_ID_ICON_MTBATTERY4,
    R_ID_ICON_MTBATTERYCHARGE,
    R_ID_ICON_MTLEFT,
    R_ID_ICON_MTMORE,
    R_ID_ICON_MTMUSIC,
    R_ID_ICON_MTPHOTO,
    R_ID_ICON_MTPLAY,
    R_ID_ICON_MTPLAY1,
    R_ID_ICON_MTRECMODE,
    R_ID_ICON_MTRECORD,
    R_ID_ICON_MTRECORDING,
    R_ID_ICON_MTRIGHT,
    R_ID_ICON_MTSDCNORMAL,
    R_ID_ICON_MTSDCNULL,
    R_ID_ICON_MTSETTING,
    R_ID_ICON_MTVIDEO1080P,
    R_ID_ICON_MTVIDEO720P,
    R_ID_ICON_MTVIDEOVGA,
    R_ID_ICON_MUSICBAR1,
    R_ID_ICON_MUSICBAR10,
    R_ID_ICON_MUSICBAR11,
    R_ID_ICON_MUSICBAR12,
    R_ID_ICON_MUSICBAR13,
    R_ID_ICON_MUSICBAR14,
    R_ID_ICON_MUSICBAR15,
    R_ID_ICON_MUSICBAR16,
    R_ID_ICON_MUSICBAR17,
    R_ID_ICON_MUSICBAR18,
    R_ID_ICON_MUSICBAR19,
    R_ID_ICON_MUSICBAR2,
    R_ID_ICON_MUSICBAR20,
    R_ID_ICON_MUSICBAR21,
    R_ID_ICON_MUSICBAR22,
    R_ID_ICON_MUSICBAR23,
    R_ID_ICON_MUSICBAR24,
    R_ID_ICON_MUSICBAR25,
    R_ID_ICON_MUSICBAR3,
    R_ID_ICON_MUSICBAR4,
    R_ID_ICON_MUSICBAR5,
    R_ID_ICON_MUSICBAR6,
    R_ID_ICON_MUSICBAR7,
    R_ID_ICON_MUSICBAR8,
    R_ID_ICON_MUSICBAR9,
    R_ID_ICON_MUSICMODECONTINUE,
    R_ID_ICON_MUSICMODECYCLE,
    R_ID_ICON_MUSICMODEORDER,
    R_ID_ICON_MUSICMODERAND,
    R_ID_ICON_PIC1024,
    R_ID_ICON_PIC128,
    R_ID_ICON_PIC16,
    R_ID_ICON_PIC2,
    R_ID_ICON_PIC2048,
    R_ID_ICON_PIC256,
    R_ID_ICON_PIC32,
    R_ID_ICON_PIC4,
    R_ID_ICON_PIC512,
    R_ID_ICON_PIC64,
    R_ID_ICON_PIC8,
    R_ID_ICON_PLANE_BUTTLE,
    R_ID_ICON_PLANE_DIJI1,
    R_ID_ICON_PLANE_DIJI2,
    R_ID_ICON_PLANE_DIJI3,
    R_ID_ICON_PLANE_FALL,
    R_ID_ICON_PLANE_FIGHT,
    R_ID_ICON_PUSHBOX_FLOOD,
    R_ID_ICON_PUSHBOX_MAN,
    R_ID_ICON_PUSHBOX_POINT,
    R_ID_ICON_PUSHBOX_REACH,
    R_ID_ICON_PUSHBOX_UNREACH,
    R_ID_ICON_PUSHBOX_WALL,
    R_ID_ICON_SHOOTS,
    R_ID_ICON_TCS_BODY,
    R_ID_ICON_TCS_FOOD,
    R_ID_ICON_TCS_HEAD_D,
    R_ID_ICON_TCS_HEAD_L,
    R_ID_ICON_TCS_HEAD_R,
    R_ID_ICON_TCS_HEAD_U,
    R_ICON_MAX
};


                                              // ( A  R  G  B)
#define R_ID_PALETTE_GREEN2              0xEF // (FF 4B C0 00)
#define R_ID_PALETTE_LightYellow         0xF0 // (FF FF FF 00)
#define R_ID_PALETTE_DoderBlue           0xF1 // (FF 00 80 E0)
#define R_ID_PALETTE_DarkGreen           0xF2 // (FF 02 29 59)
#define R_ID_PALETTE_LightGreen          0xF3 // (FF 60 C0 C0)
#define R_ID_PALETTE_Yellow              0xF4 // (FF EB AC 14)
#define R_ID_PALETTE_Blue                0xF5 // (FF 00 00 FF)
#define R_ID_PALETTE_Green               0xF6 // (FF 00 FF 00)
#define R_ID_PALETTE_Red                 0xF7 // (FF FF 00 00)
#define R_ID_PALETTE_DimGray             0xF8 // (FF 30 30 30)
#define R_ID_PALETTE_DarkGray            0xF9 // (FF 50 50 50)
#define R_ID_PALETTE_Gray                0xFA // (FF 75 75 75)
#define R_ID_PALETTE_TransBlack          0xFB // (80 00 00 00)
#define R_ID_PALETTE_White               0xFC // (FF FF FF FF)
#define R_ID_PALETTE_Black               0xFD // (FF 00 00 00)
#define R_ID_PALETTE_Transparent         0xFE // (00 00 00 00)
#define R_ID_PALETTE_Error               0xFF // (00 00 00 00)


#endif
