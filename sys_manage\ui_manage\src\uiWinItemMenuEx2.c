/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiItemMenuExProc
* Description    : uiItemMenuExProc
* Input          : uiWinMsg* msg
* Output         : none
* Return         : none
*******************************************************************************/
static void uiItemMenuEx2Proc(uiWinMsg* msg)
{
	winHandle hWin;
	uiWinObj *pWin;
	uiResInfo *infor;
	uiItemMenuEx2Obj *pItemMenuEx2;
	if(uiWidgetProc(msg))
		return;
	hWin		= msg->curWin;
	pItemMenuEx2	= (uiItemMenuEx2Obj*)uiHandleToPtr(hWin);
	pWin		= &(pItemMenuEx2->widget.win);
	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			//deg_msg("menuItemEx win create\n");
			return;
		case MSG_WIN_PAINT:
			if(pItemMenuEx2->select)
			{
				if(pItemMenuEx2->selectColor != INVALID_COLOR)
					uiWinDrawRect((uiRect*)(msg->para.p),pItemMenuEx2->selectColor);
			}else
			{
				if(pItemMenuEx2->color != INVALID_COLOR)
					uiWinDrawRect((uiRect*)(msg->para.p),pItemMenuEx2->color);
			}
			return;
		case MSG_WIN_INVALID_RESID:
			uiWinSetResid(pItemMenuEx2->hStr,		INVALID_RES_ID);
			uiWinSetResid(pItemMenuEx2->hStrSel,		INVALID_RES_ID);
			uiWinSetResid(pItemMenuEx2->hImageSel,	INVALID_RES_ID);

			return;
		case MSG_WIN_CHANGE_RESID:
			if(!RES_ID_IS_ICON(msg->para.v))
				uiWinSetResid(pItemMenuEx2->hStr,msg->para.v);
			return;
		case MSG_WIN_CHG_ITEM_SEL_RESID:
			if(RES_ID_IS_ICON(msg->para.v))
				uiWinSetResid(pItemMenuEx2->hImageSel,msg->para.v);
			else
				uiWinSetResid(pItemMenuEx2->hStrSel,msg->para.v);
			return;
		case MSG_WIN_SELECT_INFOR_EX:
			infor = (uiResInfo*)(msg->para.p);
			pItemMenuEx2->selectColor 		    = infor->color;
			//deg_Printf("MSG_WIN_SELECT_INFOR_EX:%x,%x\n", infor->bgColor, infor->fontColor);
			uiWinSendMsg(pItemMenuEx2->hStr,msg);
			{
				uiResInfo tempInfor;
				memcpy(&tempInfor,msg->para.p,sizeof(uiResInfo));
				tempInfor.fontColor = TRANSFER_COLOR;//INVALID_COLOR;//tempInfor.bgColor;
				uiWinSetSelectInfor(pItemMenuEx2->hImageSel,&tempInfor);

				memcpy(&tempInfor,msg->para.p,sizeof(uiResInfo));
				tempInfor.strAlign = ALIGNMENT_RIGHT;
				uiWinSetSelectInfor(pItemMenuEx2->hStrSel,&tempInfor);
			}
			
			return;
		case MSG_WIN_UNSELECT_INFOR_EX:
			infor = (uiResInfo*)(msg->para.p);
			pItemMenuEx2->color 		 	= infor->color;
			//deg_Printf("MSG_WIN_UNSELECT_INFOR_EX:%x,%x\n", infor->bgColor, infor->fontColor);
			uiWinSendMsg(pItemMenuEx2->hStr,msg);
			{
				uiResInfo tempInfor;
				memcpy(&tempInfor,msg->para.p,sizeof(uiResInfo));
				tempInfor.strAlign = ALIGNMENT_RIGHT;
				uiWinSetUnselectInfor(pItemMenuEx2->hStrSel,&tempInfor);

				memcpy(&tempInfor,msg->para.p,sizeof(uiResInfo));
				tempInfor.fontColor = tempInfor.color;//INVALID_COLOR;
				uiWinSetUnselectInfor(pItemMenuEx2->hImageSel,&tempInfor);
			}
			return;
		case MSG_WIN_CHANGE_STRINFOR:
			uiWinSendMsg(pItemMenuEx2->hStr,msg);
			((uiStrInfo*)(msg->para.p))->strAlign = ALIGNMENT_RIGHT;
			uiWinSendMsg(pItemMenuEx2->hStrSel,msg);
			return;
		case MSG_WIN_SELECT_INFOR:
			infor = (uiResInfo*)(msg->para.p);
			pItemMenuEx2->selectColor 		    = infor->color;
			return;
		case MSG_WIN_UNSELECT_INFOR:
			infor = (uiResInfo*)(msg->para.p);
			pItemMenuEx2->color 		 	= infor->color;
			return;
		case MSG_WIN_UNSELECT:
			if(pItemMenuEx2->select == 0)
				return;
			pItemMenuEx2->select = 0;
			if(uiWinIsVisible(hWin))
				uiWinParentRedraw(hWin);
			uiWinSendMsg(pItemMenuEx2->hStr,msg);
			uiWinSendMsg(pItemMenuEx2->hStrSel,msg);
			uiWinSendMsg(pItemMenuEx2->hImageSel,msg);
			return;
		case MSG_WIN_SELECT:
			if(pItemMenuEx2->select)
				return;
			pItemMenuEx2->select = 1;
			if(uiWinIsVisible(hWin))
				uiWinParentRedraw(hWin);
			uiWinSendMsg(pItemMenuEx2->hStr,msg);
			uiWinSendMsg(pItemMenuEx2->hStrSel,msg);
			uiWinSendMsg(pItemMenuEx2->hImageSel,msg);
			return;
		case MSG_WIN_VISIBLE_SET:
			if(msg->para.v)
			{
				uiWinSetVisible(pItemMenuEx2->hStr,1);
				uiWinSetVisible(pItemMenuEx2->hStrSel,1);
				uiWinSetVisible(pItemMenuEx2->hImageSel,1);
			}
			else
			{
				uiWinSetVisible(pItemMenuEx2->hStr,0);
				uiWinSetVisible(pItemMenuEx2->hStrSel,0);
				uiWinSetVisible(pItemMenuEx2->hImageSel,0);
			}
			break;
		case MSG_WIN_TOUCH:
			break;
		case MSG_WIN_TOUCH_GET_INFOR:
			return;
		default:
			break;
	}
	uiWinDefaultProc(msg);
}

/*******************************************************************************
* Function Name  : uiItemMenuExProc
* Description    : uiItemMenuExProc
* Input          : uiWinMsg* msg
* Output         : none
* Return         : none
*******************************************************************************/
winHandle uiItemCreateMenuItemEx2(s16 x0,s16 y0,u16 width,u16 height, u16 style)
{
	winHandle 		 hItemMenuEx2;
	uiItemMenuEx2Obj *pItemMenuEx2;
	hItemMenuEx2		= uiWinCreate(x0,y0,width,height,INVALID_HANDLE,uiItemMenuEx2Proc,sizeof(uiItemMenuEx2Obj),WIN_WIDGET|WIN_NOT_ZOOM);
	if(hItemMenuEx2 != INVALID_HANDLE)
	{
		pItemMenuEx2 = (uiItemMenuEx2Obj*)uiHandleToPtr(hItemMenuEx2);

		pItemMenuEx2->select		= 0;
		pItemMenuEx2->color			= INVALID_COLOR;
		pItemMenuEx2->selectColor	= INVALID_COLOR;
		uiWidgetSetId(hItemMenuEx2,INVALID_WIDGET_ID);
		uiWinSetbgColor(hItemMenuEx2, INVALID_COLOR);
		pItemMenuEx2->hStr		= uiStringIconCreateDirect(x0+10, 						y0, (width - height)*2/3, height, INVALID_HANDLE, WIN_NOT_ZOOM, INVALID_WIDGET_ID);
		pItemMenuEx2->hStrSel	= uiStringIconCreateDirect(x0 + (width - height)*2/3,	y0, (width - height)*1/3+20, height, INVALID_HANDLE, WIN_NOT_ZOOM, INVALID_WIDGET_ID);
		pItemMenuEx2->hImageSel	= uiImageIconCreateDirect(x0+width-height+10, 			y0+2, height,             height, INVALID_HANDLE, WIN_NOT_ZOOM, INVALID_WIDGET_ID);
	}
	return hItemMenuEx2;
}
