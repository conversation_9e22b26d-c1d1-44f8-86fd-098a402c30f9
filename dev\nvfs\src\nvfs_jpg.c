/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"


ALIGNED(4) static NV_JPG_OP nvJpgOpt = {0};

#define NVFS_VALID()	if(nvJpgOpt.fs == NULL) return;


/*little-endian*/
static WORD nv_ld_word (const BYTE* ptr)	/*	 Load a 2-byte little-endian word */
{
	WORD rv;

	rv = ptr[1];
	rv = rv << 8 | ptr[0];
	return rv;
}
static DWORD nv_ld_dword (const BYTE* ptr)	/* Load a 4-byte little-endian word */
{
	DWORD rv;

	rv = ptr[3];
	rv = rv << 8 | ptr[2];
	rv = rv << 8 | ptr[1];
	rv = rv << 8 | ptr[0];
	return rv;
}
static void nv_st_word (BYTE* ptr, WORD val)	/* Store a 2-byte word in little-endian */
{
	*ptr++ = (BYTE)val; val >>= 8;
	*ptr++ = (BYTE)val;
}
static void nv_st_dword (BYTE* ptr, DWORD val)	/* Store a 4-byte word in little-endian */
{
	*ptr++ = (BYTE)val; val >>= 8;
	*ptr++ = (BYTE)val; val >>= 8;
	*ptr++ = (BYTE)val; val >>= 8;
	*ptr++ = (BYTE)val;
}
static void nv_jpg_read(INT32U clus,INT32U buffer,INT32U len)
{
	//NVFS_VALID();
	hal_spiFlashRead(nvJpgOpt.startAddr + NV_CLUS_SIZE*clus,buffer,len);
}
static void nv_jpg_write(INT32U clus,INT32U buffer,INT32U len) //addr align 4k
{
	//NVFS_VALID();
#if NV_CLUS_SIZE == SF_SECTOR_SIZE
	while(len)
	{
		hal_spiFlashEraseSector(nvJpgOpt.startAddr + NV_CLUS_SIZE * clus, 1);
		hal_spiFlashWrite(nvJpgOpt.startAddr + NV_CLUS_SIZE * clus, buffer, (len > NV_CLUS_SIZE)?NV_CLUS_SIZE:len, 1);
		if(len > NV_CLUS_SIZE)
			len -= NV_CLUS_SIZE;
		else
			len = 0;
		clus++;
		buffer += NV_CLUS_SIZE;
	}
#elif NV_CLUS_SIZE == SF_BLOCK_SIZE
	while(len)
	{
		hal_spiFlashEraseSector(nvJpgOpt.startAddr + NV_CLUS_SIZE * clus, 1);
		hal_spiFlashWrite(nvJpgOpt.startAddr + NV_CLUS_SIZE * clus, buffer, (len > NV_CLUS_SIZE)?NV_CLUS_SIZE:len, 1);
		if(len > NV_CLUS_SIZE)
			len -= NV_CLUS_SIZE;
		else
			len = 0;
		clus++;
		buffer += NV_CLUS_SIZE;
	}
#endif
}
static void nv_sync_window (DWORD type)
{
	//NVFS_VALID();
	if(type == NVWIN_TYPE_BPB && nvJpgOpt.fs->wflagBpb)
	{
		nv_st_dword(nvJpgOpt.fs->winbpb + NV_Free_Count, 		nvJpgOpt.fs->free_clst);
		nv_st_dword(nvJpgOpt.fs->winbpb + NV_Nxt_Free, 			nvJpgOpt.fs->last_clst);
		nv_st_dword(nvJpgOpt.fs->winbpb + NV_BPB_RootFreeCnt, 	nvJpgOpt.fs->free_rootdir);	
		nv_jpg_write((INT32U)nvJpgOpt.fs->winBpbClus, (INT32U) nvJpgOpt.fs->winbpb, NV_CLUS_SIZE);
		nvJpgOpt.fs->wflagBpb = 0;
	}
	else if(type == NVWIN_TYPE_DIR && nvJpgOpt.fs->wflagDir)
	{
		nv_jpg_write((INT32U)nvJpgOpt.fs->winDirclus, (INT32U) nvJpgOpt.fs->winDir, NV_CLUS_SIZE);
		nvJpgOpt.fs->wflagDir = 0;
	}else if(type == NVWIN_TYPE_FAT && nvJpgOpt.fs->wflagFat)
	{
		nv_jpg_write((INT32U)nvJpgOpt.fs->winFatclus, (INT32U) nvJpgOpt.fs->winFat, NV_CLUS_SIZE);
		nvJpgOpt.fs->wflagFat = 0;
	}else if(type == NVWIN_TYPE_DAT && nvJpgOpt.fs->wflagDat)
	{
		nv_jpg_write((INT32U)nvJpgOpt.fs->winDatclus, (INT32U) nvJpgOpt.fs->winDat, NV_CLUS_SIZE);
		nvJpgOpt.fs->wflagDat = 0;
	}
}

static void nv_move_window (DWORD clus, DWORD type)
{
	//NVFS_VALID();
	if(type == NVWIN_TYPE_BPB)
	{
		if(clus != nvJpgOpt.fs->winBpbClus)
		{
			nv_sync_window(type);
			nv_jpg_read(clus,(INT32U)nvJpgOpt.fs->winbpb,NV_CLUS_SIZE);
			nvJpgOpt.fs->winBpbClus = clus;
		}
	}
	else if(type == NVWIN_TYPE_DIR)
	{
		if(clus != nvJpgOpt.fs->winDirclus)
		{
			nv_sync_window(type);
			nv_jpg_read(clus,(INT32U)nvJpgOpt.fs->winDir,NV_CLUS_SIZE);
			nvJpgOpt.fs->winDirclus = clus;
		}
	}else if(type == NVWIN_TYPE_FAT)
	{
		if(clus != nvJpgOpt.fs->winFatclus)
		{
			nv_sync_window(type);
			nv_jpg_read(clus,(INT32U)nvJpgOpt.fs->winFat,NV_CLUS_SIZE);
			nvJpgOpt.fs->winFatclus = clus;
		}
	}else if(type == NVWIN_TYPE_DAT)
	{
		if(clus != nvJpgOpt.fs->winDatclus)
		{
			nv_sync_window(type);
			nv_jpg_read(clus,(INT32U)nvJpgOpt.fs->winDat,NV_CLUS_SIZE);
			nvJpgOpt.fs->winDatclus = clus;
		}
	}
}



/*-----------------------------------------------------------------------*/
/* FAT access - Read value of a FAT entry                                */
/*-----------------------------------------------------------------------*/

static
DWORD nv_get_fat (	DWORD clst)
{
	if (nvJpgOpt.fs == NULL || clst >= nvJpgOpt.fs->n_fatent) {	/* Check if in valid range */
		return 1;	/* Internal error */
	} else {
		DWORD val;	/* Default value falls on disk error */
		nv_move_window(nvJpgOpt.fs->fatbase + (clst*2)/NV_CLUS_SIZE, NVWIN_TYPE_FAT);
		val = nv_ld_word(nvJpgOpt.fs->winFat + (clst *2) % NV_CLUS_SIZE);		/* Simple WORD array */
		return val;
	}
}
static
NVRESULT nv_put_fat (
	DWORD clst,		/* FAT index number (cluster number) to be changed */
	DWORD val		/* New value to be set to the entry */
)
{
	if (nvJpgOpt.fs == NULL || clst >= nvJpgOpt.fs->n_fatent) {	/* Check if in valid range */
		return NV_INT_ERR;	/* Internal error */
	}else
	{
		nv_move_window(nvJpgOpt.fs->fatbase + (clst*2)/NV_CLUS_SIZE, NVWIN_TYPE_FAT);
		nv_st_word(nvJpgOpt.fs->winFat + (clst *2) % NV_CLUS_SIZE, (WORD)val);	/* Simple WORD array */
		nvJpgOpt.fs->wflagFat = 1;
		return NV_OK;
	}
}

/*-----------------------------------------------------------------------*/
/* FAT handling - Remove a cluster chain                                 */
/*-----------------------------------------------------------------------*/
static
NVRESULT nv_remove_chain (	/* NV_OK(0):succeeded, !=0:error */
	DWORD clst,			/* Cluster to remove a chain from */
	DWORD pclst			/* Previous cluster of clst (0:entire chain) */
)
{
	NVRESULT res = NV_OK;
	DWORD nxt;

	if (nvJpgOpt.fs == NULL || clst >= nvJpgOpt.fs->n_fatent)  return NV_INT_ERR;	/* Check if in valid range */

	/* Mark the previous cluster 'EOC' on the FAT if it exists */
	if (pclst != 0) {
		res = nv_put_fat(pclst, 0xFFFFFFFF);
		if (res != NV_OK) return res;
	}
	/* Remove the chain */
	do {
		nxt = nv_get_fat(clst);				/* Get cluster status */
		if (nxt == 0) break;				/* Empty cluster? */
		if (nxt == 1) return NV_INT_ERR;	/* Internal error? */
		if (nxt == 0xFFFFFFFF) return NV_DISK_ERR;	/* Disk error? */
		res = nv_put_fat(clst, 0);			/* Mark the cluster 'free' on the FAT */
		if (res != NV_OK) return res;

		if (nvJpgOpt.fs->free_clst < nvJpgOpt.fs->n_fatent) {	/* Update FSINFO */
			nvJpgOpt.fs->free_clst++;
			nvJpgOpt.fs->wflagBpb = 1;
		}
		clst = nxt;					/* Next cluster */
	} while (clst < nvJpgOpt.fs->n_fatent);	/* Repeat while not the last link */

	return NV_OK;
}


/*-----------------------------------------------------------------------*/
/* FAT handling - Stretch a chain or Create a new chain                  */
/*-----------------------------------------------------------------------*/
static
DWORD nv_create_chain (	/* 0:No free cluster, 1:Internal error, 0xFFFFFFFF:Disk error, >=2:New cluster# */
	DWORD clst			/* Cluster# to stretch, 0:Create a new chain */
)
{
	DWORD cs, ncl, scl;
	NVRESULT res;
	if (clst == 0) {	/* Create a new chain */
		scl = nvJpgOpt.fs->last_clst;				/* Suggested cluster to start to find */
		if (scl == 0 || scl >= nvJpgOpt.fs->n_fatent) scl = 1;
	}
	else {				/* Stretch a chain */
		cs = nv_get_fat(clst);			/* Check the cluster status */
		if (cs < 2) return 1;				/* Test for insanity */
		if (cs == 0xFFFFFFFF) return cs;	/* Test for disk error */
		if (cs < nvJpgOpt.fs->n_fatent) return cs;	/* It is already followed by next cluster */
		scl = clst;							/* Cluster to start to find */
	}
	if (nvJpgOpt.fs->free_clst == 0) return 0;		/* No free cluster */

	/* On the FAT/FAT32 volume */
	ncl = 0;
	if (scl == clst) {						/* Stretching an existing chain? */
		ncl = scl + 1;						/* Test if next cluster is free */
		if (ncl >= nvJpgOpt.fs->n_fatent) ncl = 2;
		cs = nv_get_fat(ncl);				/* Get next cluster status */
		if (cs == 1 || cs == 0xFFFFFFFF) return cs;	/* Test for error */
		if (cs != 0) {						/* Not free? */
			cs = nvJpgOpt.fs->last_clst;				/* Start at suggested cluster if it is valid */
			if (cs < nvJpgOpt.fs->n_fatent) scl = cs;
			ncl = 0;
		}
	}
	if (ncl == 0) {	/* The new cluster cannot be contiguous and find another fragment */
		ncl = scl;	/* Start cluster */
		for (;;) {
			ncl++;							/* Next cluster */
			if (ncl >= nvJpgOpt.fs->n_fatent) {		/* Check wrap-around */
				ncl = 2;
				if (ncl > scl) return 0;	/* No free cluster found? */
			}
			cs = nv_get_fat(ncl);			/* Get the cluster status */
			if (cs == 0) break;				/* Found a free cluster? */
			if (cs == 1 || cs == 0xFFFFFFFF) return cs;	/* Test for error */
			if (ncl == scl) return 0;		/* No free cluster found? */
		}
	}
	res = nv_put_fat(ncl, 0xFFFFFFFF);		/* Mark the new cluster 'EOC' */
	if (res == NV_OK && clst != 0) {
		res = nv_put_fat(clst, ncl);		/* Link it from the previous one if needed */
	}


	if (res == NV_OK) {			/* Update FSINFO if function succeeded. */
		nvJpgOpt.fs->last_clst = ncl;
		if (nvJpgOpt.fs->free_clst <= nvJpgOpt.fs->n_fatent) nvJpgOpt.fs->free_clst--;
		nvJpgOpt.fs->wflagBpb = 1;
		//deg_Printf("cretate clst:%x, %x\n",nvJpgOpt.fs->free_clst,nvJpgOpt.fs->last_clst );
	} else {
		ncl = (res == NV_DISK_ERR) ? 0xFFFFFFFF : 1;	/* Failed. Generate error status */
	}

	return ncl;		/* Return new cluster number or error status */
}
static
NVRESULT nv_check_fs(void)
{
	//deg_Printf("nv_check_fs\n");
	//debgbuf(nvJpgOpt.fs->winDir,32);

	if (nv_ld_word(nvJpgOpt.fs->winbpb + NV_BS_55AA) != 0xAA55) return NV_INT_ERR;	/* Check boot record signature (always placed here even if the sector size is >512) */
	if (nvJpgOpt.fs->winbpb[NV_BS_JmpBoot] == 0xEB && 
		nvJpgOpt.fs->winbpb[NV_BS_JmpBoot + 1] == 0x77 &&
		nvJpgOpt.fs->winbpb[NV_BS_JmpBoot + 2] == 0x90) {	/* Valid JumpBoot code? */
		return NV_OK;
	}

	return NV_INT_ERR;	/* Valid BS but not FAT */
}

/*-----------------------------------------------------------------------*/
/* Directory handling - Move directory table index next                  */
/*-----------------------------------------------------------------------*/
static
NVRESULT nv_dir_next (	/* NV_OK(0):succeeded, FR_NO_FILE:End of table*/
	NV_DIR* dp		/* Pointer to the directory object */
)
{
	DWORD ofs;
	DWORD base_ofs;
	ofs = dp->dptr + NV_SZDIRE;	/* Next entry */
	base_ofs = (nvJpgOpt.fs->winDirclus - nvJpgOpt.fs->dirbase) * NV_CLUS_SIZE;
	//deg_Printf("dp->dptr:%x, %x\n", dp->dptr, ofs);
	if (((ofs+base_ofs)/NV_SZDIRE) >= nvJpgOpt.fs->n_rootdir) return NV_NO_FILE;	/* Report EOT when offset has reached max value */
	
	if(ofs >= NV_CLUS_SIZE)
	{
		nv_move_window(nvJpgOpt.fs->winDirclus + 1, NVWIN_TYPE_DIR);
		dp->dptr = 0; 
		ofs = 0;
	}else
	{
		dp->dptr += NV_SZDIRE;
	}
	
	dp->dir_ptr = nvJpgOpt.fs->winDir + ofs;/* Pointer to the entry in the win[] */

	return NV_OK;
}
/*-----------------------------------------------------------------------*/
/* Read an object from the directory                                     */
/*-----------------------------------------------------------------------*/
NVRESULT nv_dir_read (
	NV_DIR* dp	/* Pointer to the directory object */
)
{
	NVRESULT res = NV_OK;
	BYTE c;
	while (1) {
		hal_wdtClear();

		c = dp->dir_ptr[NV_DIR_Name];	/* Test for the entry type */
		//deg_Printf("nv_dir_read:%x, %x\n", c,dp->dir_ptr);
		if (c == 0) {
			res = NV_NO_FILE; break; /* Reached to end of the directory */
		}
		if(c != NV_DDEM)
		{
			dp->lok_flag = (dp->dir_ptr[NV_DIR_LOK])&0x01;
			dp->index 	= nv_ld_word(dp->dir_ptr + NV_DIR_INDEX);  //hx330x_str2num((char*)dp->dir_ptr,4);
			dp->ftime 	= nv_ld_dword(dp->dir_ptr + NV_DIR_CrtTime);
			dp->sclust	= nv_ld_dword(dp->dir_ptr + NV_DIR_FstClus);
			dp->fsize	= nv_ld_dword(dp->dir_ptr + NV_DIR_FileSize);
			//deg_Printf("index:%x\n", dp->index);
			//deg_Printf("ftime:%x\n", dp->ftime);
			//deg_Printf("sclust:%x\n", dp->sclust);
			//deg_Printf("fsize:%x\n", dp->fsize);
			res = NV_OK;
			break;
		}

		res = nv_dir_next(dp);		/* Next entry */
		if (res != NV_OK) break;
	}
	return res;
}

NVRESULT nv_dir_readfirst(
	NV_DIR* dp	/* Pointer to the directory object */
)
{
	if(nvJpgOpt.fs == NULL)
	{
		return NV_NO_FILE;
	}
	nv_move_window(nvJpgOpt.fs->dirbase, NVWIN_TYPE_DIR);
	dp->dir_ptr = nvJpgOpt.fs->winDir;
	dp->dptr = 0;/* Rewind directory object */
	return nv_dir_read(dp);
}
NVRESULT nv_dir_readnext(
	NV_DIR* dp	/* Pointer to the directory object */
)
{
	NVRESULT res;
	if(nvJpgOpt.fs == NULL)
	{
		return NV_NO_FILE;
	}
	res = nv_dir_next(dp);		/* Next entry */
	if(res == NV_OK)
	{
		res = nv_dir_read(dp);
	}
	return res;
}
/*-----------------------------------------------------------------------*/
/* Directory handling - Find an object in the directory                  */
/*-----------------------------------------------------------------------*/
static
NVRESULT nv_dir_find (	/* NV_OK(0):succeeded, !=0:error */
	NV_DIR* dp,		/* Pointer to the directory object with the file name */
	u32    index
)
{
	NVRESULT res;
	nv_move_window(nvJpgOpt.fs->dirbase, NVWIN_TYPE_DIR);
	dp->dir_ptr = nvJpgOpt.fs->winDir;
	dp->dptr = 0;/* Rewind directory object */
	/* On the FAT/FAT32 volume */
	do {
		res = nv_dir_read(dp);
		if (res != NV_OK) break;
		if(dp->index == index) break;
		res = nv_dir_next(dp);		/* Next entry */
	} while (res == NV_OK);

	return res;
}
/*-----------------------------------------------------------------------*/
/* Directory handling - Reserve a block of directory entries             */
/*-----------------------------------------------------------------------*/
static
NVRESULT nv_dir_alloc (	/* NV_OK(0):succeeded, !=0:error */
	NV_DIR* dp		/* Pointer to the directory object */
)
{
	NVRESULT res = NV_OK;
	//deg_Printf("nv_dir_alloc:%x\n", dp->index);
	nv_move_window(nvJpgOpt.fs->dirbase, NVWIN_TYPE_DIR);
	dp->dir_ptr = nvJpgOpt.fs->winDir;
	dp->dptr = 0;/* Rewind directory object */
	do{
		hal_wdtClear();
		//deg_Printf("dp->dir_ptr:%x, %x,%x,%x,%x\n",dp->dir_ptr, dp->dir_ptr[0],dp->dir_ptr[1], dp->dir_ptr[2], dp->dir_ptr[3]);
		if (dp->dir_ptr[NV_DIR_Name] == NV_DDEM || dp->dir_ptr[NV_DIR_Name] == 0)
		{
			break;	/* A block of contiguous free entries is found */
		}
		res = nv_dir_next(dp);		/* Next entry */
	}while(res == NV_OK);
	if (res == NV_NO_FILE) res = NV_DENIED;	/* No directory entry to allocate */
	return res;
}
/*-----------------------------------------------------------------------*/
/* Register an object to the directory                                   */
/*-----------------------------------------------------------------------*/

static
NVRESULT nv_dir_register (	/* NV_OK:succeeded, FR_DENIED:no free entry or too many SFN collision, FR_DISK_ERR:disk error */
	NV_DIR* dp				/* Target directory with object name to be created */
)
{
	NVRESULT res;
	//deg_Printf("nv_dir_register:%x\n", dp->index);
	res = nv_dir_alloc(dp);		/* Allocate an entry for SFN */
	/* Set SFN entry */
	if (res == NV_OK) {
		dp->ftime = get_fattime();
		dp->sclust = 0;
		dp->fsize = 0;
		dp->dir_ptr[NV_DIR_Name] = NV_RDDEM;
		dp->dir_ptr[NV_DIR_LOK]  = (dp->lok_flag) ? 1 : 0;
		nv_st_word(dp->dir_ptr + NV_DIR_INDEX, dp->index);
		nv_st_dword(dp->dir_ptr + NV_DIR_CrtTime, dp->ftime);
		nv_st_dword(dp->dir_ptr + NV_DIR_FstClus, dp->sclust);
		nv_st_dword(dp->dir_ptr + NV_DIR_FileSize, dp->fsize);
		nvJpgOpt.fs->free_rootdir--;
		nvJpgOpt.fs->wflagDir = 1;
		nvJpgOpt.fs->wflagBpb = 1;
	}

	return res;
}
static
void nv_dir_remove (
	NV_DIR* dp				/* Directory object pointing the entry to be removed */
)
{
	/* Non LFN configuration */
	dp->dir_ptr[NV_DIR_Name] = NV_DDEM;
	nvJpgOpt.fs->free_rootdir++;
	nvJpgOpt.fs->wflagDir = 1;
	nvJpgOpt.fs->wflagBpb = 1;
}
/*******************************************************************************
* Function Name  : nv_jpg_ex_init
* Description    : nv_jpg_ex_init
* Input          : none
* Output         : none
* Return         : int

*******************************************************************************/
void nv_jpg_ex_force_init(u32 force)
{
	nvJpgOpt.force_init = force;
}
/*******************************************************************************
* Function Name  : nv_jpg_ex_init
* Description    : nv_jpg_ex_init
* Input          : none
* Output         : none
* Return         : int

*******************************************************************************/
void nv_jpg_ex_init(u32 resAddress, u32 resSize)
{
	u8 flash_density = hal_spiFlashReadID() & 0xff;
	u32 flashcap = 0;
	if( flash_density < 0x20 )
	{
		flashcap = (1 << flash_density);
	}else if( flash_density == 0x20 )
	{
		flashcap = 64*1024*1024L;
	}
	deg_Printf("FLASH CAP:%dMb\n", flashcap>>20);
	if(resAddress == 0 || flashcap == 0)
		return;
	nvJpgOpt.startAddr 	= ((resAddress + resSize + 0xfff)&~0xfff) + 0x1000;
	nvJpgOpt.remainSize = flashcap - nvJpgOpt.startAddr;
	deg_Printf("NV JPG:start:%x, remainsize:%x\n", nvJpgOpt.startAddr, nvJpgOpt.remainSize);
}
/*******************************************************************************
* Function Name  : nv_jpg_init
* Description    : nv_jpg_init
* Input          : none
* Output         : none
* Return         : int

*******************************************************************************/
void nv_jpg_formart_init(void)
{
	u32 i;
	nvJpgOpt.fs->n_fatent	= nvJpgOpt.remainSize/NV_CLUS_SIZE;
	nvJpgOpt.fs->fsize		= (nvJpgOpt.fs->n_fatent*2 + NV_CLUS_SIZE - 1)/NV_CLUS_SIZE;
	nvJpgOpt.fs->n_rootdir	= NV_JPG_MAX_NUM;//(NV_CLUS_SIZE - 512 )/NV_SZDIRE;
	nvJpgOpt.fs->free_rootdir = nvJpgOpt.fs->n_rootdir;//(nvJpgOpt.fs->n_rootdir > NV_JPG_MAX_NUM)? NV_JPG_MAX_NUM:nvJpgOpt.fs->n_rootdir;
	nvJpgOpt.fs->dirbase	= 1;
	nvJpgOpt.fs->fatbase	= nvJpgOpt.fs->dirbase + (nvJpgOpt.fs->n_rootdir*NV_SZDIRE + 512 + NV_CLUS_SIZE - 1)/NV_CLUS_SIZE;//1
	nvJpgOpt.fs->database	= nvJpgOpt.fs->fatbase + nvJpgOpt.fs->fsize;
	nvJpgOpt.fs->free_clst	= nvJpgOpt.fs->n_fatent - nvJpgOpt.fs->database;
	nvJpgOpt.fs->last_clst	= nvJpgOpt.fs->database - 1;
	//hx330x_bytes_memcpy((u8*)nvJpgOpt.fs->winbpb + NV_BS_JmpBoot, (u8*)"\xEB\x76\x90" "MSDOS5.0", 11);
	hx330x_bytes_memcpy((u8*)nvJpgOpt.fs->winbpb + NV_BS_JmpBoot, (u8*)"\xEB\x77\x90" "MSDOS5.0", 11);
	nv_st_dword(nvJpgOpt.fs->winbpb + NV_BPB_TotClusSz, 	nvJpgOpt.fs->n_fatent);
	nv_st_dword(nvJpgOpt.fs->winbpb + NV_BPB_FATSz32, 		nvJpgOpt.fs->fsize);
	nv_st_dword(nvJpgOpt.fs->winbpb + NV_BPB_RootEntCnt, 	nvJpgOpt.fs->n_rootdir);
	nv_st_dword(nvJpgOpt.fs->winbpb + NV_Free_Count, 		nvJpgOpt.fs->free_clst);
	nv_st_dword(nvJpgOpt.fs->winbpb + NV_Nxt_Free, 			nvJpgOpt.fs->last_clst);
	nv_st_dword(nvJpgOpt.fs->winbpb + NV_BPB_RootFreeCnt, 	nvJpgOpt.fs->free_rootdir);
	nv_st_word(nvJpgOpt.fs->winbpb + NV_BS_55AA, 			0xAA55);
	nvJpgOpt.fs->winBpbClus = 0;
	nvJpgOpt.fs->wflagBpb = 1;
	nv_sync_window(NVWIN_TYPE_BPB);
	//dir init
	for(i = nvJpgOpt.fs->dirbase; i < nvJpgOpt.fs->fatbase; i++)
	{
		nvJpgOpt.fs->winDirclus = i;
		nvJpgOpt.fs->wflagDir = 1;
		nv_sync_window(NVWIN_TYPE_DIR);
	}
	nvJpgOpt.fs->winDirclus = nvJpgOpt.fs->dirbase;
	//fat init
	for(i = nvJpgOpt.fs->fatbase; i < nvJpgOpt.fs->fsize; i++)
	{
		nvJpgOpt.fs->winFatclus = i;
		nvJpgOpt.fs->wflagFat = 1;
		nv_sync_window(NVWIN_TYPE_FAT);
	}
	nvJpgOpt.fs->winFatclus = nvJpgOpt.fs->fatbase;
	for(i = 0; i <= nvJpgOpt.fs->fsize; i++)
	{
		nv_st_word(nvJpgOpt.fs->winFat + i*2 , NV_FAT_CLUS_EOC);
	}
	nvJpgOpt.fs->wflagFat = 1;
	nv_sync_window(NVWIN_TYPE_FAT);
	nvJpgOpt.fs->winDatclus = 0xffffffff;
}
/*******************************************************************************
* Function Name  : nv_jpg_init
* Description    : nv_jpg_init
* Input          : none
* Output         : none
* Return         : int

*******************************************************************************/
NVRESULT nv_jpg_init(void)
{
	NVRESULT res;

	if(nvJpgOpt.startAddr == 0 || nvJpgOpt.remainSize < NVJPG_MINSIZE || nvJpgOpt.fs != NULL)
	{
		return NV_INT_ERR;
	}
	nvJpgOpt.fs = (NV_JPGFS*)hal_sysMemMallocLast(sizeof(NV_JPGFS));
	if(nvJpgOpt.fs == NULL)
	{
		return NV_MEM_ERR;
	}
	hx330x_bytes_memset((u8*)nvJpgOpt.fs, 0, sizeof(NV_JPGFS));
	nvJpgOpt.fs->winBpbClus = 0xffffffff;
	nvJpgOpt.fs->winDirclus = 0xffffffff;
	nvJpgOpt.fs->winFatclus = 0xffffffff;
	nvJpgOpt.fs->winDatclus = 0xffffffff;
	nv_move_window(0, NVWIN_TYPE_BPB);
	//nvJpgOpt.fs->winDirclus = 0;
	//check BPB
	if(nvJpgOpt.force_init)
	{
		deg_Printf("nvjpg force init\n");
		res = NV_INT_ERR;
	}else
	{
		res = nv_check_fs();
	}
	nvJpgOpt.force_init = 0;
	if(res == NV_OK)
	{
		//deg_Printf("111111\n");
		nvJpgOpt.fs->n_fatent 	= nv_ld_dword(nvJpgOpt.fs->winbpb + NV_BPB_TotClusSz);
		nvJpgOpt.fs->fsize	  	= nv_ld_dword(nvJpgOpt.fs->winbpb + NV_BPB_FATSz32);
		nvJpgOpt.fs->n_rootdir	= nv_ld_dword(nvJpgOpt.fs->winbpb + NV_BPB_RootEntCnt);
		nvJpgOpt.fs->free_clst	= nv_ld_dword(nvJpgOpt.fs->winbpb + NV_Free_Count);
		nvJpgOpt.fs->last_clst	= nv_ld_dword(nvJpgOpt.fs->winbpb + NV_Nxt_Free);
		nvJpgOpt.fs->free_rootdir = nv_ld_dword(nvJpgOpt.fs->winbpb + NV_BPB_RootFreeCnt);
		nvJpgOpt.fs->dirbase	= 1;
		nvJpgOpt.fs->fatbase	= nvJpgOpt.fs->dirbase + (nvJpgOpt.fs->n_rootdir*NV_SZDIRE + 512 + NV_CLUS_SIZE - 1)/NV_CLUS_SIZE;
		nvJpgOpt.fs->database	= nvJpgOpt.fs->fatbase + nvJpgOpt.fs->fsize;
		if(nvJpgOpt.fs->n_rootdir != NV_JPG_MAX_NUM)
		{
			nv_jpg_formart_init();
		}else
		{
			nv_move_window(nvJpgOpt.fs->fatbase + ((nvJpgOpt.fs->last_clst + 1)*2)/NV_CLUS_SIZE, NVWIN_TYPE_FAT);
		}
		

	}else{
		//deg_Printf("22222\n");
		hx330x_bytes_memset((u8*)nvJpgOpt.fs->winbpb, 0, NV_CLUS_SIZE);
		hx330x_bytes_memset((u8*)nvJpgOpt.fs->winDir, 0, NV_CLUS_SIZE);
		hx330x_bytes_memset((u8*)nvJpgOpt.fs->winFat, 0, NV_CLUS_SIZE);
		hx330x_bytes_memset((u8*)nvJpgOpt.fs->winDat, 0, NV_CLUS_SIZE);
		nv_jpg_formart_init();

	}
	//deg_Printf("clst:%x, %x\n",nvJpgOpt.fs->free_clst,nvJpgOpt.fs->last_clst );
	//deg_Printf("n_fatent:%x\n",nvJpgOpt.fs->n_fatent);
	//deg_Printf("fsize:%x\n",nvJpgOpt.fs->fsize);
	//deg_Printf("n_rootdir:%x\n",nvJpgOpt.fs->n_rootdir);
	//deg_Printf("fatbase:%x\n",nvJpgOpt.fs->fatbase);
	//deg_Printf("database:%x\n",nvJpgOpt.fs->database);
	//deg_Printf("winDirclus:%x\n",nvJpgOpt.fs->winDirclus);
	//deg_Printf("winFatclus:%x\n",nvJpgOpt.fs->winFatclus);
	return NV_OK;
}
/*******************************************************************************
* Function Name  : nv_jpg_uinit
* Description    : nv_jpg_uinit
* Input          : none
* Output         : none
* Return         : int

*******************************************************************************/
void nv_jpg_uinit(void)
{
	if(nvJpgOpt.fs == NULL)
	{
		return;
	}
	nv_sync_window(NVWIN_TYPE_BPB);
	nv_sync_window(NVWIN_TYPE_DIR);
	nv_sync_window(NVWIN_TYPE_FAT);
	nv_sync_window(NVWIN_TYPE_DAT);
	hal_sysMemFree((void*)nvJpgOpt.fs);
	nvJpgOpt.fs = NULL;
}
/*******************************************************************************
* Function Name  : nv_jpg_format
* Description    : nv_jpg_format
* Input          : none
* Output         : none
* Return         : int

*******************************************************************************/
NVRESULT nv_jpg_format(void)
{
	if(nvJpgOpt.startAddr == 0 || nvJpgOpt.remainSize < NVJPG_MINSIZE)
	{
		return NV_INT_ERR;
	}
	if(nvJpgOpt.fs)
		hal_sysMemFree((void*)nvJpgOpt.fs);
	nvJpgOpt.fs = (NV_JPGFS*)hal_sysMemMallocLast(sizeof(NV_JPGFS));
	if(nvJpgOpt.fs == NULL)
	{
		return NV_MEM_ERR;
	}
	hx330x_bytes_memset((u8*)nvJpgOpt.fs, 0, sizeof(NV_JPGFS));
	nv_jpg_formart_init();
	return NV_OK;

}
/*-----------------------------------------------------------------------*/
/* Open or Create a File                                                 */
/*-----------------------------------------------------------------------*/

NVRESULT nv_jpg_open (
	u32 index,
	BYTE mode			/* Access mode and file open mode flags */
)
{
	NVRESULT res;
	DWORD  cl, clst, ofs;
	if(nvJpgOpt.fs == NULL || nvJpgOpt.fs->fil.stat != 0 || index == (u32)(-1))
	{
		return NV_INT_ERR;
	}
	NV_DIR *dp = &nvJpgOpt.fs->fil.dir;
	//deg_Printf("nv_jpg_open:%x,%x\n", index, mode);
	res = nv_dir_find(dp,index);
	//deg_Printf("nv_dir_find:%d\n", res);
	/* Create or Open a file */
	if (mode & (NVFA_CREATE_ALWAYS | NVFA_OPEN_ALWAYS | NVFA_CREATE_NEW))
	{
		if (res != NV_OK) {					/* No file, create new */
			if (res == NV_NO_FILE) {		/* There is no file to open, create a new entry */
				dp->index = index;
				res = nv_dir_register(dp);
				//deg_Printf("nv_dir_register:%d\n", res);
			}
			mode |= NVFA_OPEN_ALWAYS;		/* File is created */
		}else /* Any object with the same name is already existing */
		{
			if (mode & NVFA_CREATE_NEW) res = NV_EXIST;	/* Cannot create as new file */
		}
		if (res == NV_OK && (mode & NVFA_CREATE_ALWAYS)) {	/* Truncate the file if overwrite mode */
			/* Set directory entry initial state */
			dp->ftime = get_fattime();
			dp->fsize = 0;
			nv_st_dword(dp->dir_ptr + NV_DIR_CrtTime, dp->ftime);
			nv_st_dword(dp->dir_ptr + NV_DIR_FileSize, dp->fsize);
			nvJpgOpt.fs->wflagDir = 1;
			cl = dp->sclust;
			//deg_Printf("cl:%x\n", cl);
			if(cl != 0)
			{
				res = nv_remove_chain(cl, 0);
				if(res == NV_OK)
				{
					nvJpgOpt.fs->last_clst = cl - 1;		/* Reuse the cluster hole */
					//deg_Printf("nv_remove_chain clst:%x, %x\n",nvJpgOpt.fs->free_clst,nvJpgOpt.fs->last_clst );
				}
			}
		}
	}
	else {	/* Open an existing file */

	}
	if (res == NV_OK) {
		if (mode & NVFA_CREATE_ALWAYS) mode |= NVFA_MODIFIED;	/* Set file change flag if created or overwritten */
		nvJpgOpt.fs->fil.flag = mode;		/* Set file access mode */
		nvJpgOpt.fs->fil.err = NV_OK;			/* Clear error flag */
		nvJpgOpt.fs->fil.fptr = 0;			/* Set file pointer top of the file */
		if ((mode & NVFA_SEEKEND) && dp->fsize > 0) {	/* Seek to end of file if FA_OPEN_APPEND is specified */
			nvJpgOpt.fs->fil.fptr = dp->fsize;			/* Offset to seek */
			clst = dp->sclust;	/* Follow the cluster chain */
			for (ofs = dp->fsize; res == NV_OK && ofs > NV_CLUS_SIZE; ofs -= NV_CLUS_SIZE) {
				clst = nv_get_fat(clst);
				if (clst <= 1) res = NV_INT_ERR;
				if (clst == 0xFFFFFFFF) res = NV_DISK_ERR;
			}
			nvJpgOpt.fs->fil.clust = clst;
		}
	}
	if (res == NV_OK)
	{
		nvJpgOpt.fs->fil.stat = 1;
	}
	return res;
}

NVRESULT nv_jpg_change_lock (
	u32 index,
	u32 lok
)
{
	if(nvJpgOpt.fs == NULL || index == (u32)(-1))
	{
		return NV_INT_ERR;
	}
	NV_DIR *dp = &nvJpgOpt.fs->fil.dir;
	NVRESULT res = nv_dir_find(dp,index);
	if (res == NV_OK)
	{
		dp->lok_flag = lok;
		dp->dir_ptr[NV_DIR_LOK] = (dp->lok_flag) ? 1 : 0;
		nvJpgOpt.fs->wflagDir = 1;
		nv_sync_window(NVWIN_TYPE_BPB);
		nv_sync_window(NVWIN_TYPE_DIR);
		nv_sync_window(NVWIN_TYPE_FAT);
		nv_sync_window(NVWIN_TYPE_DAT);
	}

	return res;
}

/*-----------------------------------------------------------------------*/
/* Close File                                                            */
/*-----------------------------------------------------------------------*/
NVRESULT nv_jpg_close (void)
{
	if(nvJpgOpt.fs == NULL || nvJpgOpt.fs->fil.stat == 0)
	{
		return NV_INT_ERR;
	}
	if (nvJpgOpt.fs->fil.flag & NVFA_MODIFIED) {
		NV_DIR *dp = &nvJpgOpt.fs->fil.dir;
		nv_st_dword(dp->dir_ptr + NV_DIR_CrtTime, dp->ftime);
		nv_st_dword(dp->dir_ptr + NV_DIR_FstClus, dp->sclust);
		nv_st_dword(dp->dir_ptr + NV_DIR_FileSize,dp->fsize);
		nvJpgOpt.fs->wflagDir = 1;
		nvJpgOpt.fs->wflagBpb = 1;
		nv_sync_window(NVWIN_TYPE_BPB);
		nv_sync_window(NVWIN_TYPE_DIR);
		nv_sync_window(NVWIN_TYPE_FAT);
		nv_sync_window(NVWIN_TYPE_DAT);
	}
	nvJpgOpt.fs->fil.stat = 0;
	return NV_OK;
}


/*-----------------------------------------------------------------------*/
/* Read File                                                             */
/*-----------------------------------------------------------------------*/
NVRESULT nv_jpgfile_read (
	void* buff,	/* Pointer to data buffer */
	u32  btr,	/* Number of bytes to read */
	u32* br	/* Pointer to number of bytes read */
)
{
	DWORD clst, remain, rcnt;
	BYTE *rbuff = (BYTE*)buff;
	*br = 0;	/* Clear read byte counter */
	if(nvJpgOpt.fs == NULL || nvJpgOpt.fs->fil.stat == 0 )
	{
		return NV_INT_ERR;
	}
	NVFS_FIL *fp = &nvJpgOpt.fs->fil;
	if(fp->err != NV_OK)
	{
		return fp->err;
	}
	if (!(fp->flag & NVFA_READ)) return NV_DENIED; /* Check access mode */
	remain = fp->dir.fsize - fp->fptr;
	if (btr > remain) btr = (UINT)remain;		/* Truncate btr by remaining bytes */

	for ( ;  btr;								/* Repeat until all data read */
		btr -= rcnt, *br += rcnt, rbuff += rcnt, fp->fptr += rcnt) {
		if (fp->fptr % NV_CLUS_SIZE == 0) {			/* On the clus boundary? */
			if (fp->fptr == 0) {			/* On the top of the file? */
				clst = fp->dir.sclust;		/* Follow cluster chain from the origin */
			}else
			{
				clst = nv_get_fat(fp->clust);	/* Follow cluster chain on the FAT */
			}
			if (clst < 2)
			{
				fp->err = NV_INT_ERR; return NV_INT_ERR;
			}
			if (clst == 0xFFFFFFFF)
			{
				fp->err = NV_DISK_ERR; return NV_DISK_ERR;
			}
			fp->clust = clst;				/* Update current cluster */
			if (btr >= NV_CLUS_SIZE) {						/* Read maximum contiguous sectors directly */
				if ((nvJpgOpt.fs->wflagDat) && clst == nvJpgOpt.fs->winDatclus)		/* Replace one of the read sectors with cached data if it contains a dirty sector */
				{
					hx330x_bytes_memcpy(rbuff, nvJpgOpt.fs->winDat, NV_CLUS_SIZE);
					hx330x_sysDcacheWback((u32)rbuff,NV_CLUS_SIZE);
				}else
				{
					nv_jpg_read(clst,(INT32U)rbuff,NV_CLUS_SIZE);
				}
				rcnt = NV_CLUS_SIZE;				/* Number of bytes transferred */
				continue;
			}
		}
		rcnt = NV_CLUS_SIZE - (UINT)fp->fptr % NV_CLUS_SIZE;	/* Number of bytes left in the sector */
		if (rcnt > btr) rcnt = btr;					/* Clip it by btr if needed */
		nv_move_window(fp->clust, NVWIN_TYPE_DAT);
		hx330x_bytes_memcpy(rbuff, nvJpgOpt.fs->winDat + fp->fptr % NV_CLUS_SIZE, rcnt);	/* Extract partial sector */
		hx330x_sysDcacheWback((u32)rbuff,rcnt);
	}
	return NV_OK;
}
/*-----------------------------------------------------------------------*/
/* Write File                                                            */
/*-----------------------------------------------------------------------*/
static NVRESULT nv_jpgfile_create_linkmap (
	u32 btw/* Number of bytes to write */
)
{
	DWORD clst;
	
	DWORD pcl, ncl, tcl, ulen, *tbl;
	NVFS_FIL *fp = &nvJpgOpt.fs->fil;
	NVRESULT res = NV_OK;
	fp->cltbl = NULL;

#if NVFF_USE_FASTSEEK
	fp->clb_mm[0] = 0;
	tbl = &fp->clb_mm[1];
	ulen = 2;
	tcl  = pcl = ncl =  0;		
	clst = fp->dir.sclust;	
	if(btw > 0)
	{
		//fp->fptr += btw;
		if (clst == 0) {						/* If no cluster chain, create a new chain */
			clst = nv_create_chain(0);
			if (clst == 1)
			{
				fp->err = NV_INT_ERR; return NV_INT_ERR;
			}
			if (clst == 0xFFFFFFFF)
			{
				fp->err = NV_DISK_ERR; return NV_DISK_ERR;
			}
			if (clst == 0)
			{
				fp->err = NV_INT_ERR; return NV_INT_ERR;
			}
			fp->dir.sclust = clst;
			tcl  = pcl = clst;
			ncl = 1;
		}
		while(btw > NV_CLUS_SIZE)
		{
			btw -= NV_CLUS_SIZE; 
			clst = nv_create_chain(clst);	/* Follow chain with forceed stretch */
			if (clst == 0) {				/* Clip file size in case of disk full */
				btw = 0; break;
			}
			if (clst == 1)
			{
				fp->err = NV_INT_ERR; return NV_INT_ERR;
			}
			if (clst == 0xFFFFFFFF)
			{
				fp->err = NV_DISK_ERR; return NV_DISK_ERR;
			}
			if(clst == pcl + 1)  
			{
				ncl++;
			}else{
				if(ulen <= (NVFF_FASTSEEK_BLK_SIZE - 2)){
					*tbl++ = ncl; *tbl++ = tcl; 
				}
				tcl = clst; ncl = 1; 
				ulen += 2;
			} 
			pcl = clst;
			fp->clust = clst;
		}
		if(ulen <= (NVFF_FASTSEEK_BLK_SIZE - 2)){
			*tbl++ = ncl; *tbl++ = tcl; 
			*tbl = 0;		/* Terminate table */
			fp->clb_mm[0] = ulen;	/* Number of items used */
			fp->cltbl = fp->clb_mm;
			//u32 i;
			deg_Printf("[NVJPG] create linkmap item[%d]\n", ulen);
			//for(i = 1; i < ulen; i+=2)
			//{
			//	deg_Printf("linkmap item[%d] [ncl:%d, start:%d]\n", i/2, fp->clb_mm[i], fp->clb_mm[i+1]);
			//}

		}else
		{
			res = NV_NOT_ENOUGH_CORE;	/* Given table size is smaller than required */
			deg_Printf("[NVJPG]creat link map fail\n");
		}
	}
#endif
	return res;
}
SDRAM_TEXT_SECTION
NVRESULT nv_jpg_write_by_linkmap(	
	const void* buff,	/* Pointer to the data to be written */
	u32 btw,			/* Number of bytes to write */
	u32* bw			/* Pointer to number of bytes written */) 
{
	NVFS_FIL *fp = &nvJpgOpt.fs->fil;
	DWORD *tbl = &fp->cltbl[1];
	DWORD buffer = (DWORD)buff;
	DWORD ncl, scl, wcnt;
	bool res;
	hal_spiModeSwitch(0, 1);
	//erase
	while(tbl[0])
	{
		hal_wdtClear();
		ncl = *tbl++;
		scl = *tbl++;
		while(ncl)
		{
			hal_wdtClear();

			//if(ncl >= (SF_BLOCK_SIZE/SF_SECTOR_SIZE))
			//{
			//	res = hal_spiFlashEraseBlock(nvJpgOpt.startAddr + NV_CLUS_SIZE * scl, 0);
			//	scl += (SF_BLOCK_SIZE/SF_SECTOR_SIZE);
			//	ncl -= (SF_BLOCK_SIZE/SF_SECTOR_SIZE);
			//}else
			{
				res = hal_spiFlashEraseSector(nvJpgOpt.startAddr + NV_CLUS_SIZE * scl, 0);
				scl++;
				ncl--;		
			}
			if(res == false)
			{
				return NV_DISK_ERR;
			}
		}
	}
	tbl = &fp->cltbl[1];
	while(tbl[0] && btw)
	{
		hal_wdtClear();
		ncl = *tbl++;
		scl = *tbl++;
		wcnt = (btw > (ncl*NV_CLUS_SIZE))?(ncl*NV_CLUS_SIZE):btw;
		hal_spiFlashWrite(nvJpgOpt.startAddr + NV_CLUS_SIZE * scl, buffer, wcnt, 0);
		fp->fptr += wcnt;
		fp->dir.fsize = (fp->fptr > fp->dir.fsize) ? fp->fptr : fp->dir.fsize;
		btw -= wcnt;
		*bw += wcnt;
		buffer += wcnt;
	}
	hal_spiModeSwitch(1, 1);
	return NV_OK;
}
/*-----------------------------------------------------------------------*/
/* Write File                                                            */
/*-----------------------------------------------------------------------*/
NVRESULT nv_jpgfile_write (
	const void* buff,	/* Pointer to the data to be written */
	u32 btw,			/* Number of bytes to write */
	u32* bw			/* Pointer to number of bytes written */
)
{
	DWORD clst;
	UINT wcnt=0;
	const BYTE *wbuff = (const BYTE*)buff;
	*bw = 0;	/* Clear write byte counter */
	if(nvJpgOpt.fs == NULL || nvJpgOpt.fs->fil.stat == 0 )
	{
		return NV_INT_ERR;
	}
	NVFS_FIL *fp = &nvJpgOpt.fs->fil;
	if(fp->err != NV_OK)
	{
		return fp->err;
	}
	if (!(fp->flag & FA_WRITE)) return NV_DENIED; /* Check access mode */
	if(nvjpg_free_size() < btw)
	{
		return NV_DISK_ERR;
	}
	//deg_Printf("111 btw:%x\n", btw);
	/* Check fptr wrap-around (file size cannot reach 4 GiB at FAT volume) */
	if ((DWORD)(fp->fptr + btw) < (DWORD)fp->fptr) {
		btw = (UINT)(0xFFFFFFFF - (DWORD)fp->fptr);
	}
	nv_jpgfile_create_linkmap(btw);
	if(fp->cltbl)
	{
		if(nv_jpg_write_by_linkmap(buff, btw, bw) != NV_OK)
		{
			fp->err = NV_DISK_ERR; return NV_DISK_ERR;
		}
	}else
	{
		//deg_Printf("2222 btw:%x,%x\n", btw, fp->fptr);
		for ( ;  btw;							/* Repeat until all data written */
			btw -= wcnt, *bw += wcnt, wbuff += wcnt, fp->fptr += wcnt, fp->dir.fsize = (fp->fptr > fp->dir.fsize) ? fp->fptr : fp->dir.fsize) {
			//deg_Printf("333 btw:%x,%x\n", btw, fp->fptr);
			if (fp->fptr % NV_CLUS_SIZE == 0) {		/* On the sector boundary? */
				if (fp->fptr == 0) /* On the top of the file? */
				{
					clst = fp->dir.sclust;	/* Follow from the origin */
					if (clst == 0)
					{		/* If no cluster is allocated, */
						clst = nv_create_chain(0);	/* create a new cluster chain */
					}
				}else
				{
					clst = nv_create_chain(fp->clust);	/* Follow or stretch cluster chain on the FAT */
				}
				if (clst == 0) /* Could not allocate a new cluster (disk full) */
					break;
				if (clst == 1)
				{
					fp->err = NV_INT_ERR; return NV_INT_ERR;
				}
				if (clst == 0xFFFFFFFF)
				{
					fp->err = NV_DISK_ERR; return NV_DISK_ERR;
				}
				fp->clust = clst;			/* Update current cluster */
				if (fp->dir.sclust == 0)
					fp->dir.sclust = clst;	/* Set start cluster if the first write */
				nv_sync_window(NVWIN_TYPE_DAT); /* Write-back sector cache */
				if (btw >= NV_CLUS_SIZE) {					/* Write maximum contiguous sectors directly */
					nv_jpg_write(clst,(INT32) wbuff,NV_CLUS_SIZE);
					if(nvJpgOpt.fs->winDatclus == clst)
					{
						hx330x_bytes_memcpy((u8*)nvJpgOpt.fs->winDat, (u8*)wbuff, NV_CLUS_SIZE);
						nvJpgOpt.fs->wflagDat = 0;
					}
					wcnt = NV_CLUS_SIZE;		/* Number of bytes transferred */
					continue;
				}
			}
			nv_move_window(fp->clust, NVWIN_TYPE_DAT);

			wcnt = NV_CLUS_SIZE - (UINT)fp->fptr % NV_CLUS_SIZE;	/* Number of bytes left in the sector */
			if (wcnt > btw) wcnt = btw;					/* Clip it by btw if needed */
			hx330x_bytes_memcpy((u8*)(nvJpgOpt.fs->winDat + fp->fptr % NV_CLUS_SIZE), (u8*)wbuff, wcnt);	/* Fit data to the sector */
			nvJpgOpt.fs->wflagDat = 1;
		}
	}


	fp->flag |= NVFA_MODIFIED;				/* Set file change flag */

	return NV_OK;
}
/*-----------------------------------------------------------------------*/
/* Seek File Read/Write Pointer                                          */
/*-----------------------------------------------------------------------*/
NVRESULT nv_jpgfile_seek (
	u32 ofs,		/* File pointer from top of file */
	DWORD opt		/* create linkmap when seek to end */
)
{
	DWORD clst;
	DWORD ifptr;

	if(nvJpgOpt.fs == NULL || nvJpgOpt.fs->fil.stat == 0 )
	{
		return NV_INT_ERR;
	}
	NVFS_FIL *fp = &nvJpgOpt.fs->fil;
	if(fp->err != NV_OK)
	{
		return fp->err;
	}

	if (ofs > fp->dir.fsize && !(fp->flag & NVFA_WRITE)) {	/* In read-only mode, clip offset with the file size */
		ofs = fp->dir.fsize;
	}
	ifptr = fp->fptr;
	fp->fptr  = 0;
	if (ofs > 0) {
		if( (!(ifptr > 0 && (ofs - 1) / NV_CLUS_SIZE >= (ifptr - 1) / NV_CLUS_SIZE)))    /*  not seek to same or following cluster*/
		{
			clst = fp->dir.sclust;
			if (fp->flag & NVFA_WRITE) {			/* Check if in write mode or not */
				if (clst == 0) {						/* If no cluster chain, create a new chain */
					clst = nv_create_chain(0);
					if (clst == 1)
					{
						fp->err = NV_INT_ERR; return NV_INT_ERR;
					}
					if (clst == 0xFFFFFFFF)
					{
						fp->err = NV_DISK_ERR; return NV_DISK_ERR;
					}
					fp->dir.sclust = clst;
				}
			}
			fp->clust = clst;
		}else /* When seek to same or following cluster, */
		{
			fp->fptr = (ifptr - 1) & ~(DWORD)(NV_CLUS_SIZE - 1);	/* start from the current cluster */
			ofs -= fp->fptr;
			clst = fp->clust;
		}
		if (clst != 0) {
			while (ofs > NV_CLUS_SIZE) {						/* Cluster following loop */
				hal_wdtClear();
				ofs -= NV_CLUS_SIZE; fp->fptr += NV_CLUS_SIZE;
				if (fp->flag & NVFA_WRITE) {			/* Check if in write mode or not */
					clst = nv_create_chain(clst);	/* Follow chain with forceed stretch */
					if (clst == 0) {				/* Clip file size in case of disk full */
						ofs = 0; break;
					}
				} else
				{
					clst = nv_get_fat(clst);	/* Follow cluster chain if not in write mode */
				}
				if (clst == 0xFFFFFFFF)
				{
					fp->err = NV_DISK_ERR; return NV_DISK_ERR;
				}
				if (clst <= 1 || clst >= nvJpgOpt.fs->n_fatent)
				{
					fp->err = NV_INT_ERR; return NV_INT_ERR;
				}
				fp->clust = clst;
			}
			fp->fptr += ofs;
		}

	}
	if (fp->fptr > fp->dir.fsize) {	/* Set file change flag if the file size is extended */
		fp->dir.fsize = fp->fptr;
		fp->flag |= NVFA_MODIFIED;
	}
	if (fp->fptr % NV_CLUS_SIZE) {	/* Fill sector cache if needed */
		nv_move_window(fp->clust, NVWIN_TYPE_DAT);
	}

	return NV_OK;
}


/*-----------------------------------------------------------------------*/
/* Delete a File/Directory                                               */
/*-----------------------------------------------------------------------*/
NVRESULT nv_jpgfile_delete(u32 index)
{
	NVRESULT res;
	if(nvJpgOpt.fs == NULL || index == (u32)(-1))
	{
		return NV_INT_ERR;
	}
	nv_jpg_close();
	NV_DIR *dp = &nvJpgOpt.fs->fil.dir;
	res = nv_dir_find(dp,index);
	if (res == NV_OK)
	{
		nv_dir_remove(dp);			/* Remove the directory entry */
		if ( dp->sclust) {	/* Remove the cluster chain if exist */
			res = nv_remove_chain( dp->sclust, 0);
		}
		if (res == NV_OK)
		{
			nv_sync_window (NVWIN_TYPE_BPB);
			nv_sync_window (NVWIN_TYPE_DIR);
			nv_sync_window (NVWIN_TYPE_FAT);
		}
	}
	return res;
}
/*******************************************************************************
* Function Name  : fs_size
* Description    : fs file size get
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
INT32U nv_jpgfile_size(void)
{
	if(nvJpgOpt.fs == NULL || nvJpgOpt.fs->fil.stat == 0 )
	{
		return 0;
	}else
	{
		return nvJpgOpt.fs->fil.dir.fsize;
	}
}
/*******************************************************************************
* Function Name  : fs_free_size
* Description    : fs_free_size
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
INT32U nvjpg_free_size(void)
{
	if(nvJpgOpt.fs == NULL)
	{
		return 0;
	}else
	{
		return nvJpgOpt.fs->free_clst * NV_CLUS_SIZE;
	}
}
/*******************************************************************************
* Function Name  : nvjpg_free_dir
* Description    : nvjpg_free_dir
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
INT32U nvjpg_free_dir(void)
{
	if(nvJpgOpt.fs == NULL)
	{
		return 0;
	}else{
		return nvJpgOpt.fs->free_rootdir;
	}
}