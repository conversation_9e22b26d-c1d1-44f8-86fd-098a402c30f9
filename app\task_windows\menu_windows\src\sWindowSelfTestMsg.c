/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sWindowSelfTestWin.c"

/*******************************************************************************
* Function Name  : selfTestMsgAll
* Description    : selfTestMsgAll
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int:
*******************************************************************************/
static int selfTestMsgAll(winHandle handle,u32 parameNum,u32* parame)
{
	return 0;
}
/*******************************************************************************
* Function Name  : selfTestSysMsgSD
* Description    : selfTestSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int:
*******************************************************************************/
static int selfTestSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NULL)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : selfTestSysMsg1S
* Description    : selfTestSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int:
*******************************************************************************/
static int selfTestSysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
	{

		if(uiWinIsVisible(winItem(handle,TEST_BATTERY_ID)))
			uiWinSetVisible(winItem(handle,TEST_BATTERY_ID),0);
		else
		{
			uiWinSetVisible(winItem(handle,TEST_BATTERY_ID),1);
			uiWinSetResid(winItem(handle,TEST_BATTERY_ID),R_ID_ICON_MTBATTERYCHARGE);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : selfTestSysMsg500MS
* Description    : selfTestSysMsg500MS
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int:
*******************************************************************************/
static int selfTestSysMsg500MS(winHandle handle,u32 parameNum,u32* parame)
{
	if(hal_auadcBufferGet(NULL,NULL,NULL))
		hal_auadcBufferRelease();
	selfTestBatShow(handle);
	selfTestKeyShow(handle);
	selfTestIrShow(handle);
	selfTestSDShow(handle);
	selfTestMicShow(handle);
	selfTestUsbDevShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : selfTestOpenWin
* Description    : selfTestOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int:
*******************************************************************************/
static int selfTestOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]selfTestOpenWin\n");

	hal_auadcMemInit();
	hal_auadcStart(PCM_REC_TYPE_WAV,MEDIA_CFG_AUDSRATE,MEDIA_CFG_MIC_VOLUME);
	uiWinSetResid(winItem(handle,TEST_RTC_ID),	RAM_ID_MAKE(hal_rtcTime2String(hal_rtcTimeGet())) );
	uiWinSetResid(winItem(handle,TEST_GSENSOR_ID),RAM_ID_MAKE(gSensorGetName()));
	uiWinSetResid(winItem(handle,TEST_LCD_ID),	RAM_ID_MAKE(LcdGetName()));
	uiWinSetResid(winItem(handle,TEST_SENSOR_ID),	RAM_ID_MAKE(SensorGetName()));

	selfTestBatShow(handle);
	selfTestKeyShow(handle);
	selfTestIrShow(handle);
	selfTestSDShow(handle);
	selfTestMicShow(handle);
	selfTestUsbDevShow(handle);

	return 0;
}
/*******************************************************************************
* Function Name  : selfTestCloseWin
* Description    : selfTestCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int:
*******************************************************************************/
static int selfTestCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]selfTestCloseWin\n");
	hx330x_auadcEnable(0);
	hal_auadcMemUninit();
	return 0;
}
/*******************************************************************************
* Function Name  : selfTestWinChildClose
* Description    : selfTestWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int:
*******************************************************************************/
static int selfTestWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]selfTestWinChildClose\n");
	return 0;
}



ALIGNED(4) msgDealInfor selfTestMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	selfTestOpenWin},
	{SYS_CLOSE_WINDOW,	selfTestCloseWin},
	{SYS_CHILE_COLSE,	selfTestWinChildClose},
	{KEY_EVENT_OK,		selfTestMsgAll},
	{KEY_EVENT_UP,		selfTestMsgAll},
	{KEY_EVENT_DOWN,	selfTestMsgAll},
	{KEY_EVENT_LEFT,	selfTestMsgAll},
	{KEY_EVENT_RIGHT,	selfTestMsgAll},
	{KEY_EVENT_POWER,	selfTestMsgAll},
	{KEY_EVENT_POWEROFF,selfTestMsgAll},
	{SYS_EVENT_SDC,		selfTestSysMsgSD},
	{SYS_EVENT_USBDEV,	selfTestMsgAll},
	{SYS_EVENT_BAT,		selfTestMsgAll},
	{SYS_EVENT_1S,		selfTestSysMsg1S},
	{SYS_EVENT_500MS,	selfTestSysMsg500MS},
	{EVENT_MAX,NULL},
};

WINDOW(selfTestWindow,selfTestMsgDeal,selfTestWin)



