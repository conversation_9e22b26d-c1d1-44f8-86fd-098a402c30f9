/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskVersionWin.c"
/*******************************************************************************
* Function Name  : VersionKeyMsgOk
* Description    : VersionKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int VersionKeyMsgCom(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{

	}
	return 0;
}
/*******************************************************************************
* Function Name  : VersionKeyMsgOk
* Description    : VersionKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int VersionKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		app_taskStart(TASK_MAIN,0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : VersionSysMsgUSBDEV
* Description    : VersionSysMsgUSBDEV
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int VersionSysMsgUSBDEV(winHandle handle,uint32 parameNum,uint32* parame)
{
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
		app_taskStart(TASK_POWER_OFF,0);
	return 0;
}
/*******************************************************************************
* Function Name  : VersionSysMsgUSBDEV
* Description    : VersionSysMsgUSBDEV
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int VersionSysMsg1s(winHandle handle,uint32 parameNum,uint32* parame)
{
	if(usb_update_flag == 0)
	{
		task_com_sreen_check(SREEN_RESET_AUTOOFF);	
	}
	task_com_auto_poweroff(1);
	return 0;
}

/*******************************************************************************
* Function Name  : VersionOpenWin
* Description    : VersionOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int VersionOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]VersionOpenWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : VersionCloseWin
* Description    : VersionCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int VersionCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]VersionCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : VersionWinChildClose
* Description    : VersionWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int VersionWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]VersionWinChildClose\n");
	return 0;
}


ALIGNED(4) msgDealInfor VersionMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	VersionOpenWin},
	{SYS_CLOSE_WINDOW,	VersionCloseWin},
	{SYS_CHILE_COLSE,	VersionWinChildClose},
	{KEY_EVENT_OK,		VersionKeyMsgCom},
	{KEY_EVENT_UP,		VersionKeyMsgCom},
	{KEY_EVENT_DOWN,	VersionKeyMsgCom},
	{KEY_EVENT_LEFT,	VersionKeyMsgCom},
	{KEY_EVENT_RIGHT,	VersionKeyMsgCom},
	{KEY_EVENT_POWER,	VersionKeyMsgPower},
	{KEY_EVENT_POWEROFF,VersionKeyMsgPower},
	{SYS_EVENT_USBDEV,	VersionSysMsgUSBDEV},
	{SYS_EVENT_1S,		VersionSysMsg1s},
	{EVENT_MAX,NULL},
};

WINDOW(VersionWindow,VersionMsgDeal,VersionWin)