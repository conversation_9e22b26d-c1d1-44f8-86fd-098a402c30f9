/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef NES_PAPU_H
#define NES_PAPU_H

#define APU_FPS_DEFAULT					(60)	//ms
#define APU_VSYNC_INTERVAL				(1000/APU_FPS_DEFAULT)	//ms

#define APU_QUALITY_SEL 				1	//0:11025, 1: 22050, 2:44100

#define APU_BASEFREQ   					(1789772.7272727272727272)
/* to/from 16.16 fixed point */
#define APU_TO_FIXED(x)    				((x) << 16)
#define APU_FROM_FIXED(x)  				((x) >> 16)
/* look up table madness */
#define APU_OVERSAMPLE
#define APU_VOLUME_DECAY(x) 		 	((x) -= ((x) >> 7))
/*-------------------------------------------------------------------*/ 
/* Rectangle Wave #0                                                 */
/* Reg0: 0-3=Volume, 4=Envelope, 5=Hold, 6-7=Duty Cycle              */
/* Reg1: 0-2=sweep shifts, 3=sweep inc, 4-6=sweep length, 7=sweep on */
/* Reg2: 8 bits of freq                                              */
/* Reg3: 0-2=high freq, 7-4=vbl length counter                       */
/*-------------------------------------------------------------------*/ 
#define ApuC1Vol            			( nes_apu_C1a & 0x0f )
#define ApuC1Env            			( nes_apu_C1a & 0x10 )
#define ApuC1Hold           			( nes_apu_C1a & 0x20 )
#define ApuC1DutyCycle      			( nes_apu_C1a & 0xc0 )
//#define ApuC1EnvDelay       			( ( WORD )( nes_apu_C1a & 0x0f ) << 8 )
#define ApuC1EnvDelay       			( ( nes_apu_C1a & 0x0f ) + 1 )
#define ApuC1SweepOn        			( nes_apu_C1b & 0x80 )
#define ApuC1SweepIncDec    			( nes_apu_C1b & 0x08 )
#define ApuC1SweepShifts    			( nes_apu_C1b & 0x07 ) 
//#define ApuC1SweepDelay     			( ( ( ( WORD )nes_apu_C1b & 0x70 ) >> 4 ) << 8 )
#define ApuC1SweepDelay     			( ( ( nes_apu_C1b & 0x70 ) >> 4 ) + 1 )
#define ApuC1FreqLimit      			( ApuFreqLimit[ ( nes_apu_C1b & 0x07 ) ] )

/*-------------------------------------------------------------------*/ 
/* Rectangle Wave #1                                                 */
/* Reg0: 0-3=Volume, 4=Envelope, 5=Hold, 6-7=Duty Cycle              */
/* Reg1: 0-2=sweep shifts, 3=sweep inc, 4-6=sweep length, 7=sweep on */
/* Reg2: 8 bits of freq                                              */
/* Reg3: 0-2=high freq, 7-4=vbl length counter                       */
/*-------------------------------------------------------------------*/ 
#define ApuC2Vol            			( nes_apu_C2a & 0x0f )
#define ApuC2Env            			( nes_apu_C2a & 0x10 )
#define ApuC2Hold           			( nes_apu_C2a & 0x20 )
#define ApuC2DutyCycle      			( nes_apu_C2a & 0xc0 )
//#define ApuC2EnvDelay     			  ( ( WORD )( nes_apu_C2a & 0x0f ) << 8 )
#define ApuC2EnvDelay       			( ( nes_apu_C2a & 0x0f ) + 1 )
#define ApuC2SweepOn        			( nes_apu_C2b & 0x80 )
#define ApuC2SweepIncDec    			( nes_apu_C2b & 0x08 )
#define ApuC2SweepShifts    			( nes_apu_C2b & 0x07 ) 
//#define ApuC2SweepDelay   			  ( ( ( ( WORD )nes_apu_C2b & 0x70 ) >> 4 ) << 8 )
#define ApuC2SweepDelay     			( ( ( nes_apu_C2b & 0x70 ) >> 4 ) + 1 )
#define ApuC2FreqLimit      			( ApuFreqLimit[ ( nes_apu_C2b & 0x07 ) ] )

/*-------------------------------------------------------------------*/ 
/* Triangle Wave                                                     */
/* Reg0: 7=Holdnote, 6-0=Linear Length Counter                       */
/* Reg2: 8 bits of freq                                              */
/* Reg3: 0-2=high freq, 7-4=vbl length counter                       */
/*-------------------------------------------------------------------*/ 
#define ApuC3Holdnote       			( nes_apu_C3a & 0x80 )
#define ApuC3LinearLength   			( ( (WORD)nes_apu_C3a & 0x7f ) << 6 )
#define ApuC3LengthCounter  			( ApuAtl[ ( ( nes_apu_C3d & 0xf8) >> 3 ) ] )
#define ApuC3Freq           			( ( ( (WORD)nes_apu_C3d & 0x07) << 8) + nes_apu_C3c )

/*-------------------------------------------------------------------*/ 
/* White Noise Channel                                               */
/* Reg0: 0-3=Volume, 4=Envelope, 5=Hold                              */
/* Reg2: 7=Small(93byte) sample, 3-0=Freq Lookup                     */
/* Reg3: 7-3=vbl length counter                                      */
/*-------------------------------------------------------------------*/ 
/* define this for realtime generated noise */
#define  REALTIME_NOISE
/* length of generated noise */
#define  APU_NOISE_32K  				0x7FFF
#define  APU_NOISE_93   				93

//#define ApuC4Vol            			( ( nes_apu_C4a & 0x0f ) | ( ( nes_apu_C4a & 0x0f ) << 4 ) )
#define ApuC4Vol            			( nes_apu_C4a & 0x0f )
//#define ApuC4EnvDelay     			  ( ( WORD )( nes_apu_C4a & 0x0f ) << 8 )
#define ApuC4EnvDelay       			( ( nes_apu_C4a & 0x0f ) + 1 )
#define ApuC4Env            			( nes_apu_C4a & 0x10 )
#define ApuC4Hold           			( nes_apu_C4a & 0x20 )
#define ApuC4Freq           			( ApuNoiseFreq [ ( nes_apu_C4c & 0x0f ) ] )
#define ApuC4Small          			( nes_apu_C4c & 0x80 )
//#define ApuC4LengthCounter  			( ApuAtl[ ( ( nes_apu_C4d & 0xf8 ) >> 3 ) ] )
#define ApuC4LengthCounter  			( ApuAtl[ ( nes_apu_C4d >> 3 ) ] << 1 )

/*-------------------------------------------------------------------*/ 
/* DPCM Channel                                                      */
/* Reg0: 0-3=Frequency, 6=Looping                                    */
/* Reg1: 0-6=DPCM Value                                              */
/* Reg2: 0-7=Cache Addr                                              */
/* Reg3: 0-7=Cache DMA Length                                        */
/*-------------------------------------------------------------------*/ 
#define ApuC5_Freq           			( ApuDpcmCycles[ ( nes_apu_C5Reg[0] & 0x0F ) ] )
#define ApuC5_Looping        			( nes_apu_C5Reg[0] & 0x40 )
#define ApuC5IRQEnable					( nes_apu_C5Reg[0] & 0x80 )
#define ApuC5_DpcmValue      			( ( nes_apu_C5Reg[1] & 0x7F ) >> 1 )
#define ApuC5_CacheAddr      			( 0xc000 + (WORD)(nes_apu_C5Reg[2] << 6) )
#define ApuC5_CacheDmaLength 			( ( ( nes_apu_C5Reg[3] << 4 ) + 1 ) << 3 )

/*-------------------------------------------------------------------*/
/*  pAPU Event resources                                             */
/*-------------------------------------------------------------------*/

#define APU_EVENT_MAX   				1024//15000
typedef struct ApuEvent_s {
	long time;
	BYTE type;
	BYTE data;
	WORD reserve;
}ApuEvent_t;
#define APUET_MASK      				0xfc
#define APUET_C1        				0x00
#define APUET_W_C1A     				0x00
#define APUET_W_C1B     				0x01
#define APUET_W_C1C     				0x02
#define APUET_W_C1D     				0x03
#define APUET_C2        				0x04
#define APUET_W_C2A     				0x04
#define APUET_W_C2B     				0x05
#define APUET_W_C2C     				0x06
#define APUET_W_C2D     				0x07
#define APUET_C3        				0x08
#define APUET_W_C3A     				0x08
#define APUET_W_C3B     				0x09
#define APUET_W_C3C     				0x0a
#define APUET_W_C3D     				0x0b
#define APUET_C4        				0x0c
#define APUET_W_C4A     				0x0c
#define APUET_W_C4B     				0x0d
#define APUET_W_C4C     				0x0e
#define APUET_W_C4D     				0x0f
#define APUET_C5        				0x10
#define APUET_W_C5A     				0x10
#define APUET_W_C5B     				0x11
#define APUET_W_C5C     				0x12
#define APUET_W_C5D     				0x13
#define APUET_W_CTRL    				0x20
#define APUET_SYNC      				0x40

typedef struct ApuQualityData_S 
{
	DWORD pulse_magic;
	DWORD triangle_magic;
	DWORD noise_magic;
	DWORD samples_per_sync;
	DWORD cycles_per_sample;
	DWORD sample_rate;
	DWORD cycle_rate;
}ApuQualityData_T;



extern s16*		nes_apu_soundBuff;
extern u32		nes_apu_apu_time_sync;
extern u32		nes_apu_fps_cnt;
extern DWORD    nes_apu_pulse_magic;		//NESDATA_SECTION ApuQualityData_T nes_apu_Quality;
extern DWORD    nes_apu_triangle_magic;
extern DWORD    nes_apu_noise_magic;
extern DWORD    nes_apu_samples_per_sync;
extern DWORD    nes_apu_cycles_per_sample;
extern DWORD    nes_apu_sample_rate;
extern DWORD    nes_apu_cycle_rate;
extern BYTE 	nes_apu_Ctrl;
extern BYTE 	nes_apu_CtrlNew;
extern WORD 	nes_apu_entertime;
extern int  	nes_apu_cur_event;
extern BYTE  	nes_apu_APU_update;
extern BYTE 	nes_apu_C1a;		/*Rectangle Wave #1 */
extern BYTE		nes_apu_C1b;
extern BYTE 	nes_apu_C1c;
extern BYTE 	nes_apu_C1d;
extern BYTE 	nes_apu_C1EnvVol;
extern BYTE 	nes_apu_C1Adder;
extern BYTE 	nes_apu_C1DutyFlip;
extern BYTE* 	nes_apu_C1Wave;	
extern DWORD 	nes_apu_C1Skip;
extern DWORD 	nes_apu_C1Index;
extern DWORD 	nes_apu_C1Freq;
extern int   	nes_apu_C1Atl;
extern int   	nes_apu_C1EnvPhase;
extern int   	nes_apu_C1SweepPhase;
extern int   	nes_apu_C1Vol; 
extern int   	nes_apu_C1PhaseAcc;
extern int   	nes_apu_C1SweepDelay;
extern int   	nes_apu_C1EnvDelay;
extern BYTE 	nes_apu_C2a;		/*Rectangle Wave #2*/
extern BYTE 	nes_apu_C2b;
extern BYTE 	nes_apu_C2c;
extern BYTE 	nes_apu_C2d;
extern BYTE 	nes_apu_C2EnvVol;
extern BYTE 	nes_apu_C2Adder;
extern BYTE 	nes_apu_C2DutyFlip;
extern BYTE 	nes_apu_C2Reserve;
extern BYTE*	nes_apu_C2Wave;
extern DWORD 	nes_apu_C2Skip;
extern DWORD 	nes_apu_C2Index;
extern DWORD 	nes_apu_C2Freq;
extern int   	nes_apu_C2Atl;
extern int   	nes_apu_C2EnvPhase;
extern int   	nes_apu_C2SweepPhase;
extern int   	nes_apu_C2Vol; 
extern int   	nes_apu_C2PhaseAcc;
extern int   	nes_apu_C2SweepDelay;
extern int   	nes_apu_C2EnvDelay;	
extern BYTE 	nes_apu_C3a;			/*Triangle Wave */
extern BYTE 	nes_apu_C3b;
extern BYTE 	nes_apu_C3c;
extern BYTE 	nes_apu_C3d;
extern BYTE 	nes_apu_C3CounterStarted;
extern BYTE 	nes_apu_C3Adder;
extern WORD 	nes_apu_C3Reserve;
extern DWORD 	nes_apu_C3Skip;
extern DWORD 	nes_apu_C3Index;	
extern int  	nes_apu_C3Atl;
extern int 		nes_apu_C3Llc;          /* Linear Length Counter */
extern int  	nes_apu_C3WriteLatency;
extern int 		nes_apu_C3Vol; 
extern int 		nes_apu_C3PhaseAcc;
extern BYTE 	nes_apu_C4a;			/*Noise Wave*/
extern BYTE 	nes_apu_C4b;
extern BYTE 	nes_apu_C4c;
extern BYTE 	nes_apu_C4d;
extern BYTE  	nes_apu_C4EnvVol;
extern BYTE 	nes_apu_C4Adder;
extern BYTE 	nes_apu_C4NoiseXorTap;
extern BYTE 	nes_apu_C4NoiseShortSample;
extern DWORD 	nes_apu_C4Sr;          /* Shift register */
extern DWORD 	nes_apu_C4Fdc;         /* Frequency divide counter */
extern DWORD 	nes_apu_C4Skip;
extern DWORD 	nes_apu_C4Index;
extern int 		nes_apu_C4Atl;
extern int 		nes_apu_C4EnvPhase;
extern int 		nes_apu_C4Vol; 
extern int 		nes_apu_C4PhaseAcc;	
extern int   	nes_apu_C4EnvDelay;
extern int  	nes_apu_C4NoiseCurPos;
extern BYTE  	nes_apu_C5Reg[4];		/*DPCM Wave*/
extern BYTE  	nes_apu_C5Enable;
extern BYTE  	nes_apu_C5Looping;
extern BYTE  	nes_apu_C5CurByte;
extern BYTE  	nes_apu_C5DpcmValue;
extern BYTE  	nes_apu_C5IrqOccurred;
extern BYTE  	nes_apu_C5IrqGen;
extern WORD 	nes_apu_C5Reserve;
extern WORD  	nes_apu_C5Address;
extern WORD 	nes_apu_C5CacheAddr;
extern int 		nes_apu_C5Vol; 
extern int   	nes_apu_C5Freq;
extern int   	nes_apu_C5PhaseAcc;
extern int   	nes_apu_C5DmaLength;
extern int		nes_apu_C5CacheDmaLength;
extern int 		nes_apu_decay_lut[16];
extern int 		nes_apu_vbl_lut[32];
extern int 		nes_apu_trilength_lut[128];
extern ApuEvent_t nes_apu_ApuEventQueue[ APU_EVENT_MAX ];	
extern WORD 	nes_apu_wave_buffers[735];      /* 44100 / 60 = 735 samples per sync */	
extern XWork_T* nes_apu_pAPU_sync;
extern BYTE ApuEventType[21];
/*******************************************************************************
* Function Name  : nes_ApuDacIsr
* Description    : Callback Function per Vsync   
* Input          : 
* Output         : none                                            
* Return         : 
*******************************************************************************/
//NES_TEXT_SECTION
void nes_apu_vsync(void);

/*******************************************************************************
* Function Name  : nes_apu_init
* Description    : Initialize pApu    
* Input          : 
* Output         : none                                            
* Return         : 
*******************************************************************************/
//NES_TEXT_SECTION
int nes_apu_init(void);
/*******************************************************************************
* Function Name  : nes_apu_uinit
* Description    : Finalize pApu         
* Input          : 
* Output         : none                                            
* Return         : 
*******************************************************************************/
//NES_TEXT_SECTION
void nes_apu_uinit(void);
/*******************************************************************************
* Function Name  : nes_apu_init
* Description    : Initialize pApu    
* Input          : 
* Output         : none                                            
* Return         : 
*******************************************************************************/
//NES_TEXT_SECTION
void nes_apu_sync_init(void);

#endif
