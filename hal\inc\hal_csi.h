/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HAL_CSI_H
     #define HAL_CSI_H
	
 


typedef enum{
	SAVE_RAW_STOP  = 0,
	SAVE_RAW_START = 1,
	SAVE_RAW_SUCESS = 2,
	SAVE_RAW_FAIL  = -1,

}SAVE_RAW_STATE;

/*******************************************************************************
* Function Name  : hal_mjpA_SrcRam
* Description    : hal layer .mjpeg get data from sdram
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_csi_init(void);

#define  hal_csiEnable     hx330x_csiEnable

/*******************************************************************************
* Function Name  : hal_csi_save_raw_start
* Description    : 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
int hal_csi_save_raw_start(char* filename);
/*******************************************************************************
* Function Name  : hal_csi_save_raw_start
* Description    : 
* Input          : None
* Output         : None
* Return         : SAVE_RAW_FAIL(失败), SAVE_RAW_SUCESS(成功), SAVE_RAW_START(正在进行)
*******************************************************************************/
int hal_csi_save_raw_state(void);










#endif
