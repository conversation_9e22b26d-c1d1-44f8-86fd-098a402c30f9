/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../hal/inc/hal.h"

#if  MEDIA_VIDEO_ENCODE_EN > 0
typedef struct MediaVideoCTL_S
{
	u32*		   junkbuf;
	VideoRec_Ctl_T videoCtl;
}MediaVideoCTL_T;
ALIGNED(4) MediaVideoCTL_T	mediaVideoCtl;


/*******************************************************************************
* Function Name  : videoRecordInit
* Description    : initial video record
* Input          : VIDEO_ARG_T *arg : video initial argument
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordInit(VIDEO_ARG_T *arg)
{
//	u32 i;
	if(arg == NULL)
		return STATUS_FAIL;
	//-------------------------video information-------------------------------------------
	memset((void *)&mediaVideoCtl,0, sizeof(mediaVideoCtl));
	{
		memcpy((void *)&mediaVideoCtl.videoCtl.arg,(void *)arg,sizeof(VIDEO_ARG_T));
		mediaVideoCtl.videoCtl.stat			= MEDIA_STAT_STOP;
		mediaVideoCtl.videoCtl.sync_stat	= MEDIA_SYNC_INIT;
		mediaVideoCtl.videoCtl.arg.avi_arg.media_ch = -1;
		mediaVideoCtl.videoCtl.photoFd		= -1;
	}
	mediaVideoCtl.videoCtl.videoFrameGet 	= hal_mjpA_RawBufferGet;
	mediaVideoCtl.videoCtl.audioFrameGet 	= hal_auadcBufferGet;
	mediaVideoCtl.videoCtl.videoFrameFree 	= hal_mjpA_RawBufferfree;
	mediaVideoCtl.videoCtl.audioFrameFree 	= hal_auadcBufferRelease;


	mediaVideoCtl.junkbuf = (u32*)hal_sysMemMalloc(32*1024L);
	if(mediaVideoCtl.junkbuf == NULL)
	{
		deg_Printf("video junk buf malloc fail\n");
		return STATUS_FAIL;
	}
	memset((void *)mediaVideoCtl.junkbuf, 0, 32*1024L);
	mediaVideoCtl.videoCtl.arg.avi_arg.avi_cache = (u32*)mediaVideoCtl.junkbuf;



	hal_jpg_watermark_init();

    //hal_mjpA_EncodeInit();

    hal_csiEnable(1);

	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoRecordUninit
* Description    : uninitial video record
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordUninit(void)
{
    hal_jpg_watermark_uinit();
	if(mediaVideoCtl.junkbuf)
	{
		hal_sysMemFree(mediaVideoCtl.junkbuf);
		mediaVideoCtl.junkbuf = NULL;
	}
    //hal_auadcStop();
	//hal_mjpA_EncodeUninit();
    //hal_csiEnable(0);

	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoRecordFileStart
* Description    : start video record start for file handler
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordFileStart(VideoRec_Ctl_T *ctrl)
{
	int ret;
	int fd[2] = {-1,-1};

	MEDIA_NULL_CHECK(ctrl,STATUS_FAIL);
    MEDIA_NULL_CHECK(ctrl->arg.callback,STATUS_FAIL);

	ctrl->stat 		= MEDIA_STAT_START;
	ctrl->photoStat = MEDIA_STAT_STOP;

	ret = ctrl->arg.callback(CMD_VIDEO_RECORD_START,(INT32U)&fd[0]); // user create file
	if(ret<0)
	{
		VIDEOREC_DBG("[video rec]: create file fail.\n");
		return STATUS_FAIL;
	}
    ctrl->space = ret; // kb
   // deg_Printf("space:%d\n",ctrl->space);
    ctrl->arg.avi_arg.fd[0] = fd[0];

    if(ctrl->arg.ftype == MEDIA_AVI_ODML)
    {
		ctrl->arg.avi_arg.fd[1] = fd[0]; // using the same file when fast write enable
		VIDEOREC_DBG("[video rec]: AVI_TYPE_OPENDML.\n");

    }
    else if(ctrl->arg.ftype == MEDIA_AVI_STD)
    {
		ctrl->arg.avi_arg.fd[1] = fd[1];
		VIDEOREC_DBG("[video rec]: AVI_TYPE_STANDARD.\n");
    }
	else
	{
		VIDEOREC_DBG("[video rec]: AVI_TYPE UNKNOW %d.\n",ctrl->arg.ftype);
		return STATUS_FAIL;
	}
	ret = api_multimedia_init(MEDIA_ENCODE, ctrl->arg.ftype);
	if(ret < 0)
	{
		VIDEOREC_DBG("[video rec]: aviEncodeInit fail.%d\n",ret);
		return STATUS_FAIL;
	}
	ctrl->arg.avi_arg.media_ch = ret;
	ret = api_multimedia_start(ctrl->arg.avi_arg.media_ch,(void*)&ctrl->arg.avi_arg);
	if(ret<0)
	{
		api_multimedia_uninit(ctrl->arg.avi_arg.media_ch);
		ctrl->arg.avi_arg.media_ch = -1;
		VIDEOREC_DBG("[video rec]: encode parse fail %d .\n",ret);
		return STATUS_FAIL;
	}
	ctrl->filstat		= 0;
	ctrl->sync_stat 	= MEDIA_SYNC_WRITING;
	ctrl->abchangetime 	= XOSTimeGet();
	ctrl->arg.recDelayCurCnt = 0;
    return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoRecordFileStop
* Description    : stop video record
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordFileStop(VideoRec_Ctl_T *ctrl)
{
	int totaltime;
    MEDIA_NULL_CHECK(ctrl,STATUS_FAIL);
	MEDIA_NULL_CHECK(ctrl->arg.callback,STATUS_FAIL);

	VIDEOREC_DBG("[video rec]: ready to stop.\n");
	if(ctrl->stat != MEDIA_STAT_START)
		return STATUS_OK;
	api_multimedia_gettime(ctrl->arg.avi_arg.media_ch, &totaltime, NULL);
	VIDEOREC_DBG("[video rec]: stop.[%d ms].\n",totaltime);
	api_multimedia_end(ctrl->arg.avi_arg.media_ch);
	api_multimedia_uninit(ctrl->arg.avi_arg.media_ch);
    if(ctrl->arg.callback)
    {
		ctrl->arg.callback(CMD_VIDEO_RECORD_STOP,(INT32U)&ctrl->arg.avi_arg.fd[0]);
    }

	ctrl->stat 				= MEDIA_STAT_STOP;
	ctrl->sync_stat 		= MEDIA_SYNC_INIT;
	ctrl->arg.avi_arg.fd[0] = -1;
	ctrl->arg.avi_arg.fd[1] = -1;
	ctrl->arg.avi_arg.media_ch 	= -1;

	//sd_api_Stop();

   // sd_api_unlock();
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoRecordFileStop
* Description    : stop video record
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordFileError(VideoRec_Ctl_T *ctrl)
{
	api_multimedia_uninit(ctrl->arg.avi_arg.media_ch);
	if(ctrl->arg.callback)
    {
		ctrl->arg.callback(CMD_COM_ERROR,(INT32U)&ctrl->arg.avi_arg.fd[0]);
	}
	ctrl->arg.avi_arg.media_ch 	= -1;
	ctrl->arg.avi_arg.fd[0] = -1;
	ctrl->arg.avi_arg.fd[1] = -1;

	ctrl->stat 				= MEDIA_STAT_STOP;
	ctrl->sync_stat	 		= MEDIA_SYNC_INIT;
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoRecordStop
* Description    : stop video record
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordError(void)
{
	if(mediaVideoCtl.videoCtl.arg.recDelayMode)
	{
		mediaVideoCtl.videoCtl.arg.avi_arg.audio_en = mediaVideoCtl.videoCtl.arg.audio_en_save;
	}
    hal_mjpA_Enc_Stop();

	hal_auadcStop();
	hal_jpg_watermarkStart(0,0,0);
	videoRecordFileError(&mediaVideoCtl.videoCtl);
	mediaVideoCtl.videoCtl.arg.mdtime = 0;
	sd_api_Stop();

    sd_api_unlock();
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoRecordStart
* Description    : start video record
* Input          : AUDIO_RECORD_ARG_T *arg : video record argument
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordStart(void)
{
	VideoRec_Ctl_T * ctl = &mediaVideoCtl.videoCtl;
	sd_api_lock();
	ctl->photoFd 	= -1;
	ctl->photoStat = MEDIA_STAT_STOP;
	if(ctl->arg.recDelayMode)
	{
		ctl->arg.audio_en_save = ctl->arg.avi_arg.audio_en;
		ctl->arg.avi_arg.audio_en = 0;
	}
    if(videoRecordFileStart(ctl) != STATUS_OK)
    {
		goto VIDEO_REC_ERROR;
    }

	if(false == hal_auadcStart(PCM_REC_TYPE_AVI,ctl->arg.avi_arg.samplerate,ctl->arg.avi_arg.audio_en * MEDIA_CFG_MIC_VOLUME))// set adc for audio sample rate
	{
		VIDEOREC_DBG("[video rec 0 ]: auadc start fail\n");
		goto VIDEO_REC_ERROR;
	}
	ctl->audioFrameGet 	= hal_auadcBufferGet;
	ctl->audioFrameFree = hal_auadcBufferRelease;
	ctl->videoFrameGet 	= hal_mjpA_RawBufferGet;
	ctl->videoFrameFree = hal_mjpA_RawBufferfree;

    if(false == hal_mjpA_Enc_Video_Start(MJPEG_TYPE_AVI, ctl->arg.avi_arg.width, ctl->arg.avi_arg.height, ctl->arg.quality, ctl->arg.timestramp))
    {
		goto VIDEO_REC_ERROR;
    }
	VIDEOREC_DBG("[video rec ]: [%d - %d]start\n",ctl->arg.avi_arg.width, ctl->arg.avi_arg.height);


	return STATUS_OK;
VIDEO_REC_ERROR:
	videoRecordError();
	VIDEOREC_DBG("[video rec]: start fail\n");
	return STATUS_FAIL;
}
/*******************************************************************************
* Function Name  : videoRecordStop
* Description    : stop video record
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordStop(void)
{
	int ret;
	int curtime;
	VideoRec_Ctl_T * ctl = &mediaVideoCtl.videoCtl;
	if(ctl->arg.recDelayMode)
	{
		ctl->arg.avi_arg.audio_en = ctl->arg.audio_en_save;
	}
	api_multimedia_gettime(mediaVideoCtl.videoCtl.arg.avi_arg.media_ch,NULL, &curtime);
	if(curtime == 0)
	{
		hal_sysDelayMS(20);
		videoRecordService();
	}
	
    hal_mjpA_Enc_Stop();
	hal_auadcStop();
	hal_jpg_watermarkStart(0,0,0);
	if(ctl->sync_stat & MEDIA_SYNC_WRITING)
		ctl->sync_stat |= MEDIA_SYNC_JUNK;

	if(videoRecordJunkSync(ctl) != STATUS_OK)
	{
		ret = STATUS_FAIL;
	}
	if(videoRecordFileStop(ctl) != STATUS_OK)
	{
		ret =  STATUS_FAIL;
	}

	sd_api_Stop();

    sd_api_unlock();
	ret = STATUS_OK;
	return ret;
}

/*******************************************************************************
* Function Name  : videoRecordGetTimeSec
* Description    : videoRecordGetTimeSec
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
uint32 videoRecordGetTimeSec(void)
{
	int curtime;
	api_multimedia_gettime(mediaVideoCtl.videoCtl.arg.avi_arg.media_ch,NULL, &curtime);
	return (curtime/1000 + 1);

}
/*******************************************************************************
* Function Name  : videoRecordRestart
* Description    : pause video record restart for loop recording
* Input          : u32 mjp_stop: 0: mjp not stop, 1: mjp stop
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordRestart(u32 mjp_stop)
{
	int stime  = XOSTimeGet();
	VideoRec_Ctl_T * ctl = &mediaVideoCtl.videoCtl;

	if(mjp_stop)
	{
		if(videoRecordStop() == STATUS_OK)
		{
			if(videoRecordStart() != STATUS_OK)
				goto VIDEO_REC_RESTART_ERR;
		}
	}else
	{
		if(videoRecordJunkSync(ctl) != STATUS_OK)
		{
			goto VIDEO_REC_RESTART_ERR;
		}
		if(videoRecordFileStop(ctl) != STATUS_OK)
		{
			goto VIDEO_REC_RESTART_ERR;
		}
		VIDEOREC_DBG("[video rec]: stop %dms\n", XOSTimeGet()-stime);
		stime  = XOSTimeGet();
		if(videoRecordFileStart(ctl) != STATUS_OK)
		{
			goto VIDEO_REC_RESTART_ERR;
		}
	}
	VIDEOREC_DBG("[video rec]: %d restart %dms\n", mjp_stop, XOSTimeGet()-stime);
	return STATUS_OK;


VIDEO_REC_RESTART_ERR:
	VIDEOREC_DBG("[video rec]: restart fail\n");
	videoRecordError(); // stop
	return STATUS_FAIL;
}
/*******************************************************************************
* Function Name  : videoRecordGetStatus
* Description    : get video record
* Input          : none
* Output         : none
* Return         : int

*******************************************************************************/
int videoRecordGetStatus(void)
{
	return mediaVideoCtl.videoCtl.stat;
}
/*******************************************************************************
* Function Name  : videoRecordFileError
* Description    : start video record error for file handler
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordJunkSync(VideoRec_Ctl_T *ctrl)
{
	if((ctrl->sync_stat & MEDIA_SYNC_JUNK) == MEDIA_SYNC_JUNK)
	{
		if(api_multimedia_addjunk(ctrl->arg.avi_arg.media_ch) < 0)
			return STATUS_FAIL;
	}
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoRecordService
* Description    : video record service
* Input          : none
* Output         : none
* Return         : int

*******************************************************************************/
int videoRecordFrameProcess(VideoRec_Ctl_T *ctrl)
{

	if(!(ctrl->sync_stat & MEDIA_SYNC_WRITING))
	{
		return STATUS_OK;
	}
	u32 vids_size, vids_sync_cur, vids_sync_next;
	u32 auds_size, auds_sync_cur, auds_sync_next;
	int res = 0, curtime;
	void * vids_buf = ctrl->videoFrameGet(&vids_size,&vids_sync_cur, &vids_sync_next);
	void * auds_buf = ctrl->audioFrameGet(&auds_size,&auds_sync_cur, &auds_sync_next);
	if((!vids_buf) && (!auds_buf))
	{
		return STATUS_OK;
	}
#if 0
	if(vids_buf)
	{
		VIDEOREC_DBG("vsync[%d, %d]\n",vids_sync_cur,vids_sync_next);
		//VIDEOREC_DBG("[%x, %d]\n",vids_buf,vids_size);
	}
	if(auds_buf)
	{
		//VIDEOREC_DBG("async[%d, %d]\n",auds_sync_cur,auds_sync_next);
		//VIDEOREC_DBG("[%x, %d]\n",auds_buf,auds_size);
	}
#endif

	if(!ctrl->filstat)
	{
		if(auds_buf)
		{
			//case1: ex. file fisrt write, auds start before vids, drop auds
			if((!vids_buf)||(vids_sync_cur >= auds_sync_cur))
			{
				VIDEOREC_DBG("[Warn]audio start before video[V:%d][A:%d]\n",vids_sync_cur,auds_sync_cur);
				ctrl->audioFrameFree();
				return STATUS_OK;
			}
			//case2: ex. file fisrt write, vids have not auds frame, drop vids
			else if((vids_buf) &&(vids_sync_next < auds_sync_cur))
			{
				VIDEOREC_DBG("[Warn]drop vids[V:%d][A:%d]\n",vids_sync_cur,auds_sync_cur);
				ctrl->videoFrameFree();
				return STATUS_OK;
			}
		}
		if(vids_buf)  //tsync init
		{
			ctrl->tsync_cur  = vids_sync_cur;
			ctrl->tsync_next = vids_sync_next;
			ctrl->iframe_cnt = 0;
			ctrl->sync_cnt   = vids_sync_next - vids_sync_cur;
		}
	}else
	{
		//case 3:ex. vids_sync_cur = 15, auds_sync_cur = 7 //when drop too many vids frame
		if(auds_buf && ( (vids_buf&&(ctrl->tsync_next!=auds_sync_cur)&&(vids_sync_cur>=auds_sync_cur)) || ((!vids_buf)&&(ctrl->tsync_cur>=auds_sync_cur))))
		{
			VIDEOREC_DBG("[Warn]drop auds[%d %d]<V:%d,A:%d>\n",ctrl->tsync_cur,ctrl->tsync_next, vids_sync_cur,auds_sync_cur);
			ctrl->audioFrameFree();
			//ctrl->tsync_cur  = vids_sync_cur;
			//ctrl->tsync_next = vids_sync_next;
			//ctrl->iframe_cnt = 0;
			//ctrl->sync_cnt   = vids_sync_next - vids_sync_cur;
			//auds_buf = NULL;
			//goto VIDS_WRITE;
			return STATUS_OK;
		}
		//case 4 :ex vids_sync_next = 7, auds_sync_cur = 14   when auds drop
		else if(vids_buf && auds_buf &&(vids_sync_next<auds_sync_cur))
		{
			VIDEOREC_DBG("[Warn]drop vids[%d %d]<V:%d,A:%d>\n",ctrl->tsync_cur,ctrl->tsync_next, vids_sync_cur,auds_sync_cur);
			ctrl->videoFrameFree();
			ctrl->sync_cnt = ctrl->tsync_next - ctrl->tsync_cur - ctrl->sync_cnt;  //可能是一个负值
			return STATUS_OK;
		}
	}
	ctrl->filstat &= ~2; //clear auds write flag
//---------------------video frame ------------------------------------------------------------
//VIDS_WRITE:
	if(vids_buf)
	{
		//VIDEOREC_DBG("c[%d, %d, %d, %d]\n",ctrl->iframe_cnt,ctrl->sync_cnt,ctrl->tsync_cur,ctrl->tsync_next);
		if(ctrl->tsync_cur == vids_sync_cur) //还是当前tsync_cur
		{
			if(ctrl->sync_cnt <= 0)
			{
				//VIDEOREC_DBG("[Warn]drop vids 3 <V:%d %d>\n",vids_sync_cur,vids_sync_next);
				ctrl->videoFrameFree();
				goto AUDS_WRITE;
			}
		}else
		{

			ctrl->iframe_cnt = (ctrl->sync_cnt > 0)?ctrl->sync_cnt:0;
			ctrl->sync_cnt   = vids_sync_next - vids_sync_cur + ctrl->sync_cnt;
			ctrl->tsync_cur  = vids_sync_cur;
			ctrl->tsync_next = vids_sync_next;
		}
VIDS_SYNC_IFRAME:
		if(ctrl->arg.recDelayMode)
		{
			ctrl->arg.recDelayCurCnt++;
			if(ctrl->arg.recDelayCurCnt >= ctrl->arg.recDelayStep)
			{
				ctrl->arg.recDelayCurCnt = 0;
			}else
			{
				goto VIDS_SYNC_IFRAME_2;
			}
		}

		if(api_multimedia_encodeframe(ctrl->arg.avi_arg.media_ch,(void*)vids_buf, vids_size, AVI_FRAME_DC_VIDEO) < 0)
		{
			ctrl->videoFrameFree();
			res = -1;
			goto VIDEO_FRAME_ERROR;
		}
	VIDS_SYNC_IFRAME_2:
        if((ctrl->photoStat == MEDIA_STAT_START)&&(vids_size>0))
        {
			fs_write(ctrl->photoFd,(void *)(vids_buf+8),vids_size-8);
			ctrl->photoStat = MEDIA_STAT_READY;
        }
		ctrl->filstat |= 1;  // set video start flag
		ctrl->sync_cnt--;
		while(ctrl->iframe_cnt > 0)
		{
			ctrl->iframe_cnt--;
			if(ctrl->arg.recDelayMode == 0)
			{
			#if AVI_IFRAME_REAL == 0
				vids_size = 0;
			#endif
			}

			//VIDEOREC_DBG("|");
			goto VIDS_SYNC_IFRAME;
		}
		ctrl->videoFrameFree();
	}
//---------------------audio frame ------------------------------------------------------------
AUDS_WRITE:
	if(auds_buf && (ctrl->tsync_cur >= auds_sync_cur))
	{
		hal_wdtClear();
		//VIDEOREC_DBG("A[%d, %d, %d, %d]\n",ctrl->iframe_cnt,ctrl->sync_cnt,ctrl->tsync_cur,ctrl->tsync_next);
		//VIDEOREC_DBG("[%x, %d]\n",auds_buf,auds_size);
		//if(auds_size==0) VIDEOREC_DBG("<audio size error>\n");
		if(ctrl->arg.recDelayMode == 0)
		{
			if(api_multimedia_encodeframe(ctrl->arg.avi_arg.media_ch,(void*)auds_buf, auds_size, AVI_FRAME_WD_AUDIO) < 0)
			{
				ctrl->audioFrameFree();
				res = -2;
				goto VIDEO_FRAME_ERROR;
			}
		}

		ctrl->filstat |= 2;  // audio frame ok
		ctrl->audioFrameFree();
	}
	//------------------------------time loop check---------------------------------------------------------
	if(ctrl->sync_stat & MEDIA_SYNC_PRERESTART)
	{
		if(ctrl->filstat & 0x02)
		{
			ctrl->sync_stat = (ctrl->sync_stat &~(MEDIA_SYNC_WRITING|MEDIA_SYNC_PRERESTART))|MEDIA_SYNC_JUNK|MEDIA_SYNC_RESTART;
			if(vids_buf)
			{
				VIDEOREC_DBG("vsync[%d, %d]\n",vids_sync_cur,vids_sync_next);
				//VIDEOREC_DBG("[%x, %d]\n",vids_buf,vids_size);
			}
			if(auds_buf)
			{
				VIDEOREC_DBG("async[%d, %d]\n",auds_sync_cur,auds_sync_next);
				//VIDEOREC_DBG("[%x, %d]\n",auds_buf,auds_size);
			}
			VIDEOREC_DBG("[Video %d] pre to loop.\n");
		}
	}else{

		api_multimedia_gettime(ctrl->arg.avi_arg.media_ch,NULL, &curtime);
		if((ctrl->arg.rectime*1000) <= curtime || (ctrl->arg.mdtime && ctrl->arg.mdtime <= curtime))
		{
			if(ctrl->filstat & 0x02) //auds had writed
			{
				ctrl->sync_stat = (ctrl->sync_stat &~MEDIA_SYNC_WRITING)|MEDIA_SYNC_JUNK|MEDIA_SYNC_RESTART;
				VIDEOREC_DBG("[Video] loop.\n");


			}else
			{
				ctrl->sync_stat |= MEDIA_SYNC_PRERESTART;
				if(vids_buf)
				{
					VIDEOREC_DBG("vsync[%d, %d]\n",vids_sync_cur,vids_sync_next);
					//VIDEOREC_DBG("[%x, %d]\n",vids_buf,vids_size);
				}
				if(auds_buf)
				{
					VIDEOREC_DBG("async[%d, %d]\n",auds_sync_cur,auds_sync_next);
					//VIDEOREC_DBG("[%x, %d]\n",auds_buf,auds_size);
				}
				VIDEOREC_DBG("[Video] pre loop.\n");
			}
		}
	}
    return STATUS_OK;
VIDEO_FRAME_ERROR:
	VIDEOREC_DBG("[Video Err] res = %d.\n",res);
    //videoRecordStop();  // recorder can not handler this error.only stop recording

	return STATUS_FAIL;

}
/*******************************************************************************
* Function Name  : videoRecordService
* Description    : video record service
* Input          : none
* Output         : none
* Return         : int

*******************************************************************************/
int videoRecordService(void)
{
	int res = 0;
	u32 curtime;
	u32 size;
	VideoRec_Ctl_T * ctl = &mediaVideoCtl.videoCtl;
	hal_wdtClear();
	if(ctl->stat != MEDIA_STAT_START)
		return ctl->stat;

//----------------space check-------------------
	size = fs_tell(ctl->arg.avi_arg.fd[0]);
	if(ctl->arg.ftype == MEDIA_AVI_STD)
	{
		size += fs_size(ctl->arg.avi_arg.fd[1]);
	}
	size >>= 10;
	if(ctl->space  < (size + 1024))
	{
		VIDEOREC_DBG("[video rec]: [space %d,size:%d]\n",ctl->space,size);
		goto VIDEO_RECORD_RESTART;
	}
//-----------------VIDEO A & B coop control-----------------------------------
	if(ctl->sync_stat & MEDIA_SYNC_RESTART)
	{
		goto VIDEO_RECORD_RESTART;
	}
//-----------------frame write control-----------------------------------
//VIDEO_RECORD_VIDEO_FRAME:
	if(videoRecordJunkSync(ctl) != STATUS_OK)
	{
		res = -3;
		goto VIDEO_RECORD_ERROR;
	}
	if(videoRecordFrameProcess(ctl) !=STATUS_OK )
	{
		res = -4;
		goto VIDEO_RECORD_ERROR;
	}

    return ctl->stat;
VIDEO_RECORD_RESTART:

	api_multimedia_gettime(ctl->arg.avi_arg.media_ch,NULL, (int*)&curtime);
	if(ctl->arg.mdtime)
	{
		if(ctl->arg.mdtime <= curtime)
		{
			ctl->arg.mdtime = 0;
			return videoRecordStop();
		}else
		{
			ctl->arg.mdtime -= curtime;

		}
	}
	if(ctl->tsync_cur > 0xffffff00)
	{
		videoRecordRestart(1); //stop mjp restart
	}else{
		videoRecordRestart(0);
	}
	return ctl->stat;

VIDEO_RECORD_ERROR:

	VIDEOREC_DBG("[video rec err]: res no %d.size: %dKB, space=%dKB\n",res,fs_tell(ctl->arg.avi_arg.fd[0])>>10, ctl->space);
    videoRecordError();  // recorder can not handler this error.only stop recording
	return ctl->stat;
}
/*******************************************************************************
* Function Name  : videoRecordCmdSet
* Description    : video record set paramater
* Input          : INT32U cmd : command
                      INT32U para : paramater
* Output         : none
* Return         : int

*******************************************************************************/
int videoRecordCmdSet(INT32U cmd,INT32U para)
{
	VideoRec_Ctl_T * ctl = &mediaVideoCtl.videoCtl;
	if((ctl->stat != MEDIA_STAT_STOP) && (cmd != CMD_COM_AUDIOEN))
		return STATUS_FAIL;
	if(cmd == CMD_COM_LOOPTIME)
	{
		ctl->arg.rectime = para;
	}
	else if(cmd == CMD_COM_LOOPREC)
	{
		ctl->arg.looprecord = para;
	}
	else if(cmd == CMD_COM_TIMESTRAMP)
	{
		ctl->arg.timestramp = para;
		//watermark_enable(mediaVideoCtrl.arg.timestramp);
	}
	else if(cmd == CMD_COM_AUDIOEN)
	{
		ctl->arg.avi_arg.audio_en = para;
	}
	else if(cmd == CMD_COM_RESOLUTIONN)
	{
		ctl->arg.avi_arg.width = para>>16;
		ctl->arg.avi_arg.height = para &0xffff;
	}
	else if(cmd == CMD_COM_QUALITY)
	{
		ctl->arg.quality = para;
	}
	else if(cmd == CMD_COM_FPS)
	{
		ctl->arg.avi_arg.fps = para;
	}else if(cmd == CMD_COM_MDTIME)
	{
		ctl->arg.mdtime = para;
	}else if(cmd == CMD_COM_DELAY)
	{
		if(para)
		{
			ctl->arg.recDelayMode = 1;
			ctl->arg.recDelayStep = para;
		}else
		{
			ctl->arg.recDelayMode = 0;
		}

	}

	else
	{
		return STATUS_FAIL;
	}


	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoRecordSizePreSec
* Description    : video record size pre second
* Input          : INT32U time_s : time,second
                      int channel : video channel ,A,B
* Output         : none
* Return         : int   kb

*******************************************************************************/
int videoRecordSizePreSec(INT32U time_s)
{
	INT32U res,size;
	VideoRec_Ctl_T * ctl = &mediaVideoCtl.videoCtl;
	res = hal_mjpA_Sizecalculate(ctl->arg.quality, ctl->arg.avi_arg.width, ctl->arg.avi_arg.height) >> 10;

	size = (res * HAL_AVI_FRAMRATE)*time_s;

	res = (size>>10)/10;  // 10M bytes

	if(res == 0)
		return size;

	{
		u32 add_size = 0;
		if(time_s > 1)
		{
			add_size = 2*((time_s+59)/60);	//720P  add 20M / min for save
		}
		if(ctl->arg.avi_arg.width <= 640)
		{
			res+=add_size/2;
		}
		else if(ctl->arg.avi_arg.width <= 720)
		{
			res+=add_size;
		}
		else if(ctl->arg.avi_arg.width <= 1920)
		{
			res+=add_size*2;
		}
    }

	size = (((res*10)<<10) + 0x1f)&~(0x1f);  //KB align 32KB
	return size;
}
/*******************************************************************************
* Function Name  : videoRecordTakePhoto
* Description    : video record take photo when recording
* Input          : int fd : file handle
* Output         : none
* Return         : int : status

*******************************************************************************/
void videoRecordTakePhotoCfg(u32 stat,int fd)
{
	VideoRec_Ctl_T * ctl = &mediaVideoCtl.videoCtl;
	ctl->photoStat = stat;
	ctl->photoFd   = fd;
}
/*******************************************************************************
* Function Name  : videoRecordTakePhotoStatus
* Description    : video record tabke photo status
* Input          : none
* Output         : none
* Return         : int : status

*******************************************************************************/
int videoRecordTakePhotoStatus(void)
{
	return mediaVideoCtl.videoCtl.photoStat;
}
void videoRecordSetPhotoStatus(INT32S status)
{
	mediaVideoCtl.videoCtl.photoStat = status;
}
int videoRecordTakePhotoFd(void)
{
	return mediaVideoCtl.videoCtl.photoFd;
}
#endif
