/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
/*******************************************************************************
* Function Name  : taskRecordWinChangeProcess
* Description    : taskRecordWinChangeProcess function.
* Input          : 
* Output         : none                                            
* Return         : int fd : file handle
*******************************************************************************/
static void taskPowerOffWinChangeProcess(void)
{
	taskMainWinInit(INVALID_RES_ID,MEDIA_SRC_NVFS, R_ID_IMAGE_POWER_OFF, 0,SUB_TO_MAIN_CENTER);
	taskWinChangeProcess();

}
/*******************************************************************************
* Function Name  : taskPowerOffOpen
* Description    : taskPowerOffOpen
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskPowerOffOpen(uint32 arg)
{
	//电池电量很低时，快速关机，不做其他处理
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL && SysCtrl.dev_stat_battery  == BATTERY_STAT_0)
	{
		u32 temp;
		hal_sysUninit();
		dev_ioctrl(SysCtrl.dev_fd_lcd, DEV_LCD_BK_WRITE, 0); // back light off
		dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_PWR_CHECK, (INT32U)&temp);
		if(!(temp&& (SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)))
		{
			hal_wkiWakeupTriger(1); //wki wakeup rising trigger
			hal_wkiCleanPending();
			hal_vddWKOEnable(0, hardware_setup.wkiWakeUpEn);
			hx330x_sysCpuMsDelay(50);
			while(1);
		}
	}
	app_lcdCsiVideoShowStop();
	app_lcdUiShowUinit();
	app_logo_show(1,1,0, 0);
	taskPowerOffWinChangeProcess();
	
    uiWinUninit();
	app_uninit();
}

ALIGNED(4) sysTask_T taskPowerOff =
{
	"Power Off",
	0,
	taskPowerOffOpen,
	NULL,
	NULL,
};


