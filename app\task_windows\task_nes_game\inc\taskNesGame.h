/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/

#ifndef  __TASK_NES_GAME_H
#define  __TASK_NES_GAME_H

typedef enum{
	NES_GAME_DISABLE = 0,
	NES_GAME_INIT,
	NES_GAME_START,
	NES_GAME_STOP,
}TASK_NES_GAME_STATE;
typedef enum{
	NES_GAME_INNER = 0,
	NES_GAME_CARD,
}TASK_NES_GAME_STEP;
typedef struct NESGAME_OP_S
{
	u32  nesGame_state;
	u32  music_delay;
	u32  openListTotal;			//记录当前列表的文件个数
	u32  nesGame_step;
	u32  nesGame_inner_index;
	u32  nesGame_inner_indexmax;
	u32  curIdShowUp;
	u32  curId;
}NESGAME_OP_T;
typedef struct{
	u32 fd;
	u32 imgDnId;
	u32 imgUpId;
	u8  name[32];
}NES_GAME_NAME;
EXTERN_WINDOW(nesGameInnerWindow);
EXTERN_WINDOW(nesGameWindow);
EXTERN_WINDOW(nesGameSubWindow);
extern NESGAME_OP_T task_nesGame_op;
extern sysTask_T taskNesGame;
extern const NES_GAME_NAME nes_game_inner_list[];

/*******************************************************************************
* Function Name  : taskNesGameIsStart
* Description    : taskNesGameIsStart
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
bool taskNesGameIsStart(void);
/*******************************************************************************
* Function Name  : taskNesGameOpenList
* Description    : taskNesGameOpenList
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
void taskNesGameOpenList(void);
/*******************************************************************************
* Function Name  : taskNesGameCloseList
* Description    : taskNesGameCloseList
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
void taskNesGameCloseList(void);
/*******************************************************************************
* Function Name  : taskNesGameOpenList
* Description    : taskNesGameOpenList
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
void taskNesGameStart(void);
/*******************************************************************************
* Function Name  : taskNesGameInnerBackGroundShow
* Description    : taskNesGameInnerBackGroundShow
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
void taskNesGameInnerCurWinShow(void);
#endif
