/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

/*******************************************************************************
* Function Name  : taskRecordWinChangeProcess
* Description    : taskRecordWinChangeProcess function.
* Input          : 
* Output         : none                                            
* Return         : int fd : file handle
*******************************************************************************/
static void taskVersionWinChangeProcess(u8 enter)
{
#if No_games
	if(SysCtrl.winChangeEnable == 0)
		return;
	if(enter)
	{
		taskMainWinInit(0,MEDIA_SRC_NVFS, R_ID_IMAGE_MAIN_VER, 0,MAIN_TO_SUB_VOR_UP);
		if(taskWinChangeProcess() < 0)
		{
			res_image_show(R_ID_IMAGE_MAIN_VER, 0);
		}
	}else
	{
		taskMainWinInit(0,MEDIA_SRC_NVFS, R_ID_IMAGE_MAIN_VER, 0,SUB_TO_MAIN_VOR_DOWN);
	}
#endif
}
/*******************************************************************************
* Function Name  : taskVersionOpen
* Description    : taskVersionOpen function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskVersionOpen(u32 arg)
{
	app_lcdCsiVideoShowStop();
	taskVersionWinChangeProcess(1);
	//uiOpenWindow(&menuItemWindow,0,1,&MENU(Version));
	uiOpenWindow(&VersionWindow,0,0);
	//res_image_show(R_ID_IMAGE_MAIN_BACKGROUND, 0);
	//uiOpenWindow(&usbDeviceWindow,0,0);
}
/*******************************************************************************
* Function Name  : taskVersionClose
* Description    : taskVersionClose function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskVersionClose(uint32 arg)
{
	taskVersionWinChangeProcess(0);
}
/*******************************************************************************
* Function Name  : taskVersionService
* Description    : taskVersionService function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskVersionService(uint32 arg)
{


}

ALIGNED(4) sysTask_T taskVersion =
{
	"Version",
	0,
	taskVersionOpen,
	taskVersionClose,
	taskVersionService,
};


