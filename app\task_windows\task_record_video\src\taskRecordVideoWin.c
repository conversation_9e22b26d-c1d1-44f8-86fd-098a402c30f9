/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	VIDEO_MODE_ID=0,
	VIDEO_SD_ID,
	VIDEO_SDVOL_ID,
	//VIDEO_RES_RECT_ID,
	VIDEO_RESOLUTION_ID,
	VIDEO_REC_POINT_ID,
	VIDEO_REC_TIME_ID,

	VIDEO_REC_MODE_IMG_ID,
	VIDEO_REC_MODE_STR_ID,
	
	VIDEO_SCALER_BAR_ID,
	VIDEO_SCALER_ID,
	VIDEO_SYSTIME_ID,
	VIDEO_BATERRY_ID,

	VIDEO_LINE_00,
	VIDEO_LINE_01,
	VIDEO_LINE_10,
	VIDEO_LINE_11,
	VIDEO_LINE_20,
	VIDEO_LINE_21,
	VIDEO_LINE_30,
	VIDEO_LINE_31,

	VIDEO_REC_TEST_ON,
	VIDEO_MAX_ID
};

	

UNUSED ALIGNED(4) const widgetCreateInfor recordVideoWin[] =
{
	createFrameWin( 						Rx(0),   Ry(0),   Rw(320), Rh(240), R_ID_PALETTE_Transparent, WIN_ABS_POS),
	createImageIcon(VIDEO_MODE_ID,      	Rx(4),   Ry(0),   Rw(32),  Rh(32),  R_ID_ICON_MTRECORD, 	ALIGNMENT_CENTER),
	createImageIcon(VIDEO_SD_ID,        	Rx(40),  Ry(0),   Rw(40),  Rh(32), 	R_ID_ICON_MTSDCNORMAL,	ALIGNMENT_CENTER),
	createStringIcon(VIDEO_SDVOL_ID,		Rx(40),  Ry(5),   Rw(36),  Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_GREEN2,	DEFAULT_FONT),
	createImageIcon(VIDEO_RESOLUTION_ID,    Rx(80),  Ry(0),   Rw(48),  Rh(32),  R_ID_ICON_MTVIDEO1080P, ALIGNMENT_CENTER),
	createImageIcon(VIDEO_REC_POINT_ID,     Rx(208), Ry(0),   Rw(32),  Rh(32), 	R_ID_ICON_MTRECORDING,	ALIGNMENT_RIGHT),
	createStringIcon(VIDEO_REC_TIME_ID,  	Rx(240), Ry(4),   Rw(76),  Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_RIGHT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	createImageIcon(VIDEO_REC_MODE_IMG_ID,  Rx(0),   Ry(40),  Rw(32),  Rh(32), 	R_ID_ICON_MTRECMODE,	ALIGNMENT_RIGHT),
	createStringIcon(VIDEO_REC_MODE_STR_ID, Rx(40),  Ry(44),  Rw(200),  Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_White, DEFAULT_FONT),
	
	createProgressBarVer(VIDEO_SCALER_BAR_ID,Rx(293),Ry(51),  Rw(12),  Rh(130), R_ID_PALETTE_Transparent,R_ID_PALETTE_DoderBlue, R_ID_PALETTE_White,ALIGNMENT_RIGHT),
	createStringIcon(VIDEO_SCALER_ID,      	Rx(265), Ry(184), Rw(50),  Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_RIGHT, 	R_ID_PALETTE_LightYellow,DEFAULT_FONT),
	createStringIcon(VIDEO_SYSTIME_ID,      Rx(4),   Ry(208), Rw(220), Rh(32),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_LightYellow,DEFAULT_FONT),
	createImageIcon(VIDEO_BATERRY_ID,    	Rx(265), Ry(208), Rw(50),  Rh(32), 	R_ID_ICON_MTBATTERY4,	ALIGNMENT_RIGHT),
	//x = 100, y = 90
	createLine(VIDEO_LINE_00,				Rx(70),  Ry(50),  Rx(90),  Ry(50),  Rw(5), 	R_ID_PALETTE_Transparent),
	createLine(VIDEO_LINE_01,				Rx(230), Ry(50),  Rx(250), Ry(50),  Rw(5), 	R_ID_PALETTE_Transparent),
	createLine(VIDEO_LINE_10,				Rx(70),  Ry(55),  Rx(70),  Ry(70),  Rw(5), 	R_ID_PALETTE_Transparent),
	createLine(VIDEO_LINE_11,				Rx(245), Ry(55),  Rx(245), Ry(70),  Rw(5), 	R_ID_PALETTE_Transparent),
	createLine(VIDEO_LINE_20,				Rx(70),  Ry(170), Rx(70),  Ry(185), Rw(5), 	R_ID_PALETTE_Transparent),
	createLine(VIDEO_LINE_21,				Rx(245), Ry(170), Rx(245), Ry(185), Rw(5), 	R_ID_PALETTE_Transparent),
	createLine(VIDEO_LINE_30,				Rx(70),  Ry(185), Rx(90),  Ry(185), Rw(5), 	R_ID_PALETTE_Transparent),
	createLine(VIDEO_LINE_31,				Rx(230), Ry(185), Rx(250), Ry(185), Rw(5), 	R_ID_PALETTE_Transparent),

	
	createStringIcon(VIDEO_REC_TEST_ON,      Rx(4),   Ry(38), Rw(120), Rh(32),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_Red,DEFAULT_FONT),
		
	widgetEnd(),
};
/*******************************************************************************
* Function Name  : videoSDShow
* Description    : videoSDShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoSDShow(winHandle handle)
{
	task_com_sdlist_scan(0, 1);
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
	{
		uiWinSetResid(winItem(handle,VIDEO_SD_ID),R_ID_ICON_MTSDCNORMAL);
		uiWinSetVisible(winItem(handle,VIDEO_SDVOL_ID),1);
		uiWinSetResid(winItem(handle,VIDEO_SDVOL_ID),RAM_ID_MAKE(task_com_sdcCap_str()));
		
	}	
	else{
		uiWinSetResid(winItem(handle,VIDEO_SD_ID),R_ID_ICON_MTSDCNULL);
		uiWinSetVisible(winItem(handle,VIDEO_SDVOL_ID),0);
	}		
		
}
/*******************************************************************************
* Function Name  : videoResolutionShow
* Description    : videoResolutionShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoResolutionShow(winHandle handle)
{
	switch(user_config_get(CONFIG_ID_RESOLUTION))
	{
		case R_ID_STR_RES_VGA: uiWinSetResid(winItem(handle,VIDEO_RESOLUTION_ID),R_ID_ICON_MTVIDEOVGA); break;
		case R_ID_STR_RES_HD:  uiWinSetResid(winItem(handle,VIDEO_RESOLUTION_ID),R_ID_ICON_MTVIDEO720P); break;
		case R_ID_STR_RES_FHD: uiWinSetResid(winItem(handle,VIDEO_RESOLUTION_ID),R_ID_ICON_MTVIDEO1080P); break;
		default:			   uiWinSetResid(winItem(handle,VIDEO_RESOLUTION_ID),R_ID_ICON_MTVIDEO1080P); break;
	}
}
/*******************************************************************************
* Function Name  : videoRemainTimeShow
* Description    : videoRemainTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoRemainTimeShow(winHandle handle)
{
	uiWinSetStrInfor(winItem(handle,VIDEO_REC_TIME_ID),DEFAULT_FONT,ALIGNMENT_RIGHT,R_ID_PALETTE_White);
	uiWinSetResid(	winItem(handle,VIDEO_REC_TIME_ID),RAM_ID_MAKE(task_com_rec_remain_time_str()));
}

/*******************************************************************************
* Function Name  : videoRecTimeShow
* Description    : videoRecTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoRecTimeShow(winHandle handle)
{
	uiWinSetStrInfor(winItem(handle,VIDEO_REC_TIME_ID),	DEFAULT_FONT,	ALIGNMENT_RIGHT,R_ID_PALETTE_Red);
	uiWinSetResid(	winItem(handle,VIDEO_REC_TIME_ID),	RAM_ID_MAKE(task_com_rec_show_time_str()));
}
/*******************************************************************************
* Function Name  : videoRecTimeShow
* Description    : videoRecTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoRecPointShow(winHandle handle, u32 visable)
{
	static u32 pointVisable = 0;
	//deg_Printf("visable:%d\n");
	if(visable)
	{
		pointVisable ^= 1;
	}else
	{
		pointVisable = 0;
	}
	//deg_Printf("visable:%d,%d\n",pointVisable);
	//if(visable && pointVisable)
	//{
	//	uiWinSetResid(	winItem(handle,VIDEO_REC_POINT_ID),R_ID_ICON_MTRECORDING);
	//}else
	//{
	//	uiWinSetResid(	winItem(handle,VIDEO_REC_POINT_ID),INVALID_RES_ID);
	//}
	uiWinSetVisible(winItem(handle,VIDEO_REC_POINT_ID),(visable & pointVisable));
}

/*******************************************************************************
* Function Name  : videoRemainTimeShow
* Description    : videoRemainTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoRecordModeShow(winHandle handle)
{
	if(recordVideoOp.recordMode == RECORD_MODE_NORMAL)
	{
		uiWinSetVisible(winItem(handle,VIDEO_REC_MODE_IMG_ID),0);
		uiWinSetVisible(winItem(handle,VIDEO_REC_MODE_STR_ID),0);
	}else
	{
		uiWinSetVisible(winItem(handle,VIDEO_REC_MODE_IMG_ID),1);
		uiWinSetVisible(winItem(handle,VIDEO_REC_MODE_STR_ID),1);
		if(recordVideoOp.recordMode == RECORD_MODE_SLOW)
		{
			uiWinSetResid(	winItem(handle,VIDEO_REC_MODE_STR_ID),R_ID_STR_SET_SLOW);
		}else if(recordVideoOp.recordMode == RECORD_MODE_DELAY)
		{
			uiWinSetResid(	winItem(handle,VIDEO_REC_MODE_STR_ID),R_ID_STR_SET_FAST);
		}
		
	}

}
/*******************************************************************************
* Function Name  : videoSysTimeShow
* Description    : videoSysTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoSysTimeShow(winHandle handle)
{
	INT32U value = user_configValue2Int(CONFIG_ID_TIMESTAMP);
	if(value)
	uiWinSetResid(winItem(handle,VIDEO_SYSTIME_ID),RAM_ID_MAKE(hal_rtcTime2String(hal_rtcTimeGet())));
}
/*******************************************************************************
* Function Name  : videoBaterryShow
* Description    : videoBaterryShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoBaterryShow(winHandle handle)
{
	uiWinSetVisible(winItem(handle,VIDEO_BATERRY_ID),1);
	uiWinSetResid(winItem(handle,VIDEO_BATERRY_ID),task_com_battery_res_get());

}
/*******************************************************************************
* Function Name  : videoScalerShow
* Description    : videoScalerShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoScalerShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,VIDEO_SCALER_ID),RAM_ID_MAKE(task_com_scaler_str()));
}
/*******************************************************************************
* Function Name  : recordPhotoScalerBarShow
* Description    : recordPhotoScalerBarShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoScalerBarShow(winHandle handle, u8 visable)
{
	u32 rate = task_com_scaler_rate();
	if(rate == 0)
	{
		visable = 0;
	}
	uiWinSetProgressRate(winItem(handle,VIDEO_SCALER_BAR_ID), rate);
	uiWinSetVisible(winItem(handle,VIDEO_SCALER_BAR_ID),visable);
}
/*******************************************************************************
* Function Name  : recordPhotoResShow
* Description    : recordPhotoResShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static  void recordvideoLineShow(winHandle handle, u32 sta)
{
	uiColor color;
	u32 visable;
	switch(sta)
	{
		case 0: color = R_ID_PALETTE_Transparent; visable = 0; break;
		case 1: color = R_ID_PALETTE_Red;		  visable = 1;break;
		case 2: color = R_ID_PALETTE_LightYellow;	  visable = 1;break;
		default: return;
	}
	uiWinSetbgColor(winItem(handle,VIDEO_LINE_00), color);
	uiWinSetbgColor(winItem(handle,VIDEO_LINE_01), color);
	uiWinSetbgColor(winItem(handle,VIDEO_LINE_10), color);
	uiWinSetbgColor(winItem(handle,VIDEO_LINE_11), color);
	uiWinSetbgColor(winItem(handle,VIDEO_LINE_20), color);
	uiWinSetbgColor(winItem(handle,VIDEO_LINE_21), color);
	uiWinSetbgColor(winItem(handle,VIDEO_LINE_30), color);
	uiWinSetbgColor(winItem(handle,VIDEO_LINE_31), color);

	uiWinSetVisible(winItem(handle,VIDEO_LINE_00),visable);
	uiWinSetVisible(winItem(handle,VIDEO_LINE_01),visable);
	uiWinSetVisible(winItem(handle,VIDEO_LINE_10),visable);
	uiWinSetVisible(winItem(handle,VIDEO_LINE_11),visable);
	uiWinSetVisible(winItem(handle,VIDEO_LINE_20),visable);
	uiWinSetVisible(winItem(handle,VIDEO_LINE_21),visable);
	uiWinSetVisible(winItem(handle,VIDEO_LINE_30),visable);
	uiWinSetVisible(winItem(handle,VIDEO_LINE_31),visable);
	
}



UNUSED static void videoTESTONShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,VIDEO_REC_TEST_ON),RAM_ID_MAKE("TEST ON"));
}



