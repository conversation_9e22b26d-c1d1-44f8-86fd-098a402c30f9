/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskCGameWin.c"


	ALIGNED(4) const C_GAME_NAME c_game_inner_list[]= {
	{R_ID_IMAGE_SNAKE1,	R_ID_IMAGE_SNAKE2,"snake"},
	{R_ID_IMAGE_PUSHBOX1,	R_ID_IMAGE_PUSHBOX2,"PushBox"},
	{R_ID_IMAGE_MAZE1,	R_ID_IMAGE_MAZE2,"Maze"},	
	{R_ID_IMAGE_FLY1,	R_ID_IMAGE_FLY2,"fly"},	
	//{R_ID_IMAGE_TERISA1,	R_ID_IMAGE_TERISA2,"TERISA"},
	{R_ID_IMAGE_20481,	R_ID_IMAGE_20482,"2048"},
};

static u8 game_in_flag = 0;// enter game and stop music play
static int volumegame = 0;	
/*******************************************************************************
* Function Name  : getPlayAudioResInfor
* Description    : audioKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
void taskCGameCurWinShow(void)
{
	if(task_cGame_op.curIdShowUp)
	{
		task_cGame_op.curId = c_game_inner_list[task_cGame_op.cGame_inner_index].imgUpId;
	}else
	{
		task_cGame_op.curId = c_game_inner_list[task_cGame_op.cGame_inner_index].imgDnId;
	}
	res_image_show(task_cGame_op.curId, 0);
}


/*******************************************************************************
* Function Name  : taskCGameWinChageShow
* Description    : taskCGameWinChageShow
* Input          : dir: 0: move to left, 1: move to right
* Output         : none
* Return         : none
*******************************************************************************/
void taskCGameWinChageShow(u8 dir)
{
	u32 win_type;
	u32 cur_id = task_cGame_op.curId;
	if(dir > 0)
	{
		task_cGame_op.cGame_inner_index++;
		if(task_cGame_op.cGame_inner_index >= task_cGame_op.cGame_inner_indexmax)
		{
			task_cGame_op.cGame_inner_index = 0;
		}
		win_type = MAIN_TO_SUB_HOR_RIGHT;
	}else
	{
		if(task_cGame_op.cGame_inner_index <= 0)
		{
			task_cGame_op.cGame_inner_index = task_cGame_op.cGame_inner_indexmax -1;
		}else
		{
			task_cGame_op.cGame_inner_index--;
		}
		win_type = MAIN_TO_SUB_HOR_LEFT;
	}
	if(task_cGame_op.curIdShowUp)
	{
		task_cGame_op.curId = c_game_inner_list[task_cGame_op.cGame_inner_index].imgUpId;
	}else
	{
		task_cGame_op.curId = c_game_inner_list[task_cGame_op.cGame_inner_index].imgDnId;
	}
	taskMainWinInit(cur_id,MEDIA_SRC_NVFS, task_cGame_op.curId, 0,win_type);
	if(taskWinChangeProcess() < 0)
	{
		res_image_show(task_cGame_op.curId, 0);
	}

}
/*******************************************************************************
* Function Name  : cGameOpenWin
* Description    : cGameOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int cGameOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("cGameOpenWin\n");
	task_cGame_op.cGame_step  = NES_GAME_INNER;
	task_cGame_op.cGame_state = C_GAME_STOP;
	task_cGame_op.cGame_inner_indexmax = ARRAY_NUM(c_game_inner_list);

	//cGameNameShow(handle);
	volumegame = task_com_curVolume_get();
	// if (volumegame > 42)
	// {
	// 	volumegame = 42;
	// }

	res_music_start(R_ID_MUSIC_GAMES_MUSIC,0,volumegame);

	//res_image_show(R_ID_IMAGE_PUSHBOX1,0);
	//task_cGame_op.cGame_state = NES_GAME_START;
	return 0;
}
/*******************************************************************************
* Function Name  : cGameCloseWin
* Description    : cGameCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int cGameCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	task_cGame_op.curId = 0;
	task_cGame_op.cGame_inner_index = 0;
	res_music_end();
	return 0;
}
/*******************************************************************************
* Function Name  : cGameWinChildOpen
* Description    : cGameWinChildOpen
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int cGameWinChildOpen(winHandle handle,u32 parameNum,u32* parame)
{
	res_music_end();
	return 0;
}
/*******************************************************************************
* Function Name  : cGameWinChildClose
* Description    : cGameWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int cGameWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("cGameWinChildClose\n");
	//task_com_lcdbk_set(40);
	taskCGameCurWinShow();
	volumegame = task_com_curVolume_get();
	// if (volumegame > 42)
	// {
	// 	volumegame = 42;
	// }

	res_music_start(R_ID_MUSIC_GAMES_MUSIC,0,volumegame);

	app_draw_Service(1);
	task_cGame_op.cGame_step = NES_GAME_INNER;
	task_cGame_op.cGame_state = C_GAME_STOP;
	return 0;
}
/*******************************************************************************
* Function Name  : cGameKeyMsgOk
* Description    : cGameKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int cGameKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(C_GAME_STOP == task_cGame_op.cGame_state){
			task_cGame_op.cGame_state = C_GAME_START;
		}
		res_music_end();
		//task_cGame_op.cGame_step = NES_GAME_CSOURCE;
		


	#if 1
		if(task_cGame_op.cGame_inner_index == 2){
//			uiOpenWindow(&terisWindow, 0, 0);
			uiOpenWindow(&mazeWindow, 0, 0);//uiOpenWindow(&g2048Window, 0, 0);
		}else if(task_cGame_op.cGame_inner_index == 1){
			uiOpenWindow(&pushBoxWindow, 0, 0);//uiOpenWindow(&snakeWindow, 0, 0);
		}else if(task_cGame_op.cGame_inner_index == 3){
			
			uiOpenWindow(&gFighterWindow, 0, 0);//uiOpenWindow(&g2048Window, 0, 0);//uiOpenWindow(&mazeWindow, 0, 0);
		}else if(task_cGame_op.cGame_inner_index == 4){
			uiOpenWindow(&g2048Window, 0, 0);//uiOpenWindow(&gFighterWindow, 0, 0);
		}else{
			//uiOpenWindow(&pushBoxWindow, 0, 0);
			uiOpenWindow(&snakeWindow, 0, 0);
		}
	#elif 1
		if(task_cGame_op.cGame_inner_index == 0){
			task_cGame_op.cGame_step = NES_GAME_CSOURCE;
			res_music_end();
			uiOpenWindow(&pushBoxWindow, 0, 0);
		}
	#else
		//taskNesGameStart();
	#endif
	}

	return 0;
}
/*******************************************************************************
* Function Name  : cGameKeyMsgUp
* Description    : cGameKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int cGameKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{

	}
	return 0;
}
/*******************************************************************************
* Function Name  : cGameKeyMsgDown
* Description    : cGameKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int cGameKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{

	}
	return 0;
}
/*******************************************************************************
* Function Name  : cGameKeyMsgLeft
* Description    : cGameKeyMsgLeft
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int cGameKeyMsgLeft(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		taskCGameWinChageShow(0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : cGameKeyMsgRight
* Description    : cGameKeyMsgRight
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int cGameKeyMsgRight(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		taskCGameWinChageShow(1);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : cGameKeyMsgMain
* Description    : cGameKeyMsgMain
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int cGameKeyMsgMain(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		res_music_end();
		taskMainWinInit(0,MEDIA_SRC_NVFS, R_ID_IMAGE_PUSHBOX1, 0,SUB_TO_MAIN_VOR_UP);
		app_taskStart(TASK_MAIN,0);
		//hal_sysMemPrint();
	}
	return 0;
}
/*******************************************************************************
* Function Name  : cGameKeyMsgMode
* Description    : cGameKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int cGameKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//hal_sysMemPrint();
		res_music_end();
		app_taskStart(TASK_MAIN,0);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : cGameSysMsg1S
* Description    : cGameSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int cGameSysMsg500MS(winHandle handle,u32 parameNum,u32* parame)
{
	uiRect rect;
	static u8 flag  = 1;
	static u32 image = 0;
#if 0
	mainWindowDrawBackground(R_ID_IMAGE_GAME_BACKGROUND);
	uiWinGetPos(winItem(handle,NESGAMEINNER_SELECT_ID),&rect);

	if(flag){
		flag = 0;
		rect.y0+=10;
		rect.y1+=10;
	}else{
		flag = 1;
	}

	DrawIconOnDispBuffer(nes_game_inner_list[task_cGame_op.cGame_inner_index].image,rect.x0,rect.x1,rect.y0,rect.y1,hal_lcdVideoShowFrameGet());

#else
	if(flag){
		flag = 0;
		image = c_game_inner_list[task_cGame_op.cGame_inner_index].imgUpId;
	}else{
		flag = 1;
		image = c_game_inner_list[task_cGame_op.cGame_inner_index].imgDnId;
	}
	res_image_show(image,0);


#endif
	return 0;
}



/*******************************************************************************
* Function Name  : getPlayAudioResInfor
* Description    : audioKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static u32 getCGameResInfor(u32 item,u32* image,u32* str)
{
	if(image)
		*image 	= INVALID_RES_ID;
	if(str)
		*str	= NULL;
	return 0;
}

/*******************************************************************************
* Function Name  : cGameSysMsgSD
* Description    : cGameSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int cGameSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	return 0;
}
/*******************************************************************************
* Function Name  : cGameSysMsgUSB
* Description    : cGameSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int cGameSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{
	return 0;
}
/*******************************************************************************
* Function Name  : cGameSysMsgBattery
* Description    : cGameSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int cGameSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainSysMsg1S
* Description    : playMp3MainSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int cGameSysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
	return 0;
}



ALIGNED(4) msgDealInfor cGameMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		cGameOpenWin},
	{SYS_CLOSE_WINDOW,		cGameCloseWin},
	{SYS_CHILE_COLSE,		cGameWinChildClose},
	{KEY_EVENT_OK,			cGameKeyMsgOk},
	{KEY_EVENT_UP,			cGameKeyMsgUp},
	{KEY_EVENT_DOWN,		cGameKeyMsgDown},
	{KEY_EVENT_LEFT,		cGameKeyMsgLeft},
	{KEY_EVENT_RIGHT,		cGameKeyMsgRight},
	{KEY_EVENT_POWER,		cGameKeyMsgMain},
	{SYS_EVENT_SDC,			cGameSysMsgSD},
	{SYS_EVENT_USBDEV,		cGameSysMsgUSB},
	{SYS_EVENT_BAT,			cGameSysMsgBattery},
	{SYS_EVENT_500MS,		cGameSysMsg500MS},
	{EVENT_MAX,NULL},
};

WINDOW(cGameWindow,cGameMsgDeal,cGameWin)


