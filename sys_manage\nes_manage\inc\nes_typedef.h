/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/

#ifndef NES_TYPEDEF_H
#define NES_TYPEDEF_H

//PPU_MirrorTable
#define NES_MIR_TABLE0  					8
#define NES_MIR_TABLE1  					9
#define NES_MIR_TABLE2  					10
#define NES_MIR_TABLE3  					11

#define NES_MIR_TABLE_V_MASK 				2
#define NES_MIR_TABLE_H_MASK 				1

//NES RESOURCE SIZE
#define NES_RAM_SIZE     					0x2000	//8K
#define NES_SRAM_SIZE    					0x2000	//8K
#define NES_PPURAM_SIZE  					0x4000	//16K
#define NES_SPRRAM_SIZE  					256
#define NES_CHR_BUF_SIZE					(256 * 2 * 8 * 8 )
#define NES_DRAM_SIZE						0xA000
#define NES_ROM_BANK_SIZE					0x4000
#define NES_VROM_BANK_SIZE					0x2000
// Sprite
#define NES_SPR_Y    						0
#define NES_SPR_CHR  						1
#define NES_SPR_ATTR 						2
#define NES_SPR_X    						3
#define NES_SPR_ATTR_COLOR  				0x3
#define NES_SPR_ATTR_V_FLIP 				0x80
#define NES_SPR_ATTR_H_FLIP 				0x40
#define NES_SPR_ATTR_PRI    				0x20

//PPU Register
#define PPU_R0_NMI_VB      					0x80
#define PPU_R0_NMI_SP      					0x40
#define PPU_R0_SP_SIZE     					0x20
#define PPU_R0_BG_ADDR     					0x10
#define PPU_R0_SP_ADDR     					0x08
#define PPU_R0_INC_ADDR    					0x04
#define PPU_R0_NAME_ADDR   					0x03

#define PPU_R1_BACKCOLOR   					0xe0
#define PPU_R1_SHOW_SP     					0x10
#define PPU_R1_SHOW_SCR    					0x08
#define PPU_R1_CLIP_SP     					0x04
#define PPU_R1_CLIP_BG     					0x02
#define PPU_R1_MONOCHROME  					0x01

#define PPU_R2_IN_VBLANK   					0x80
#define PPU_R2_HIT_SP      					0x40
#define PPU_R2_MAX_SP      					0x20
#define PPU_R2_WRITE_FLAG  					0x10

//PPU_Scanline pos
#define PPU_SCAN_TOP_OFF_SCREEN     		0
#define PPU_SCAN_ON_SCREEN          		1
#define PPU_SCAN_BOTTOM_OFF_SCREEN  		2
#define PPU_SCAN_UNKNOWN            		3
#define PPU_SCAN_VBLANK             		4

#define PPU_SCAN_TOP_OFF_SCREEN_START       0
#define PPU_SCAN_ON_SCREEN_START            8
#define PPU_SCAN_BOTTOM_OFF_SCREEN_START  	232
#define PPU_SCAN_UNKNOWN_START            	240
#define PPU_SCAN_VBLANK_START             	243
#define PPU_SCAN_VBLANK_END               	262

#define PPU_STEP_PER_SCANLINE             	113
#define PPU_STEP_PER_FRAME                	29828

/* NES display size */
#define NES_DISP_WIDTH      				256
#define NES_DISP_HEIGHT    			 		240

/* NES PAD FUN */
#define NES_PAD_SYS_A     					(1<<0)
#define NES_PAD_SYS_B   					(1<<1)
#define NES_PAD_SYS_SELECT 					(1<<2)
#define NES_PAD_SYS_START  					(1<<3)
#define NES_PAD_SYS_UP      				(1<<4)
#define NES_PAD_SYS_DOWN      				(1<<5)
#define NES_PAD_SYS_LEFT    				(1<<6)
#define NES_PAD_SYS_RIGHT    				(1<<7)
#define NES_PAD_SYS_A_REPEAT				(1<<8)	// unknow
#define NES_PAD_SYS_B_REPEAT				(1<<9)	// unknow
#define NES_PAD_SYS_QUIT   					(1<<10)

#define NES_PAD_PUSH(a,b)  					( ( (a) & (b) ) != 0 )

/* 6502 Flags */
#define K6502_FLAG_C 						0x01  //进位标志
#define K6502_FLAG_Z 						0x02  //零标志
#define K6502_FLAG_I						0x04  //中断标志，是否允许系统中断
#define K6502_FLAG_D 						0x08
#define K6502_FLAG_B 						0x10   //
#define K6502_FLAG_R 						0x20  //
#define K6502_FLAG_V 						0x40  //overflow标志
#define K6502_FLAG_N 						0x80  //负数标志

/* Stack Address */
#define K6502_BASE_STACK   					0x100

/* Interrupt Vectors */
#define K6502_VECTOR_NMI   					0xfffa
#define K6502_VECTOR_RESET 					0xfffc
#define K6502_VECTOR_IRQ   					0xfffe

typedef enum{ //8k range
	K6502_RAM_DATA 	= 0x00,
	K6502_PPU_DATA 	= 0x01,
	K6502_PAPU_DATA	= 0x02,
	K6502_SRAM_DATA	= 0x03,
	K6502_ROM_DATA0	= 0x04,
	K6502_ROM_DATA1	= 0x05,
	K6502_ROM_DATA2	= 0x06,
	K6502_ROM_DATA3	= 0x07,
}K6502_ADDR_TYPE;


typedef struct NES_GAME_CTRL_S{
    char game_name[32];
    u8*  game_tab;
    u32  game_len;
}NES_GAME_CTRL_T;

typedef enum  // source type
{
	GAME_SRC_FS=0,   // file system
	GAME_SRC_NVFS,   // spi flash
	GAME_SRC_RAM,    // SDRAM OR RAM
	GAME_SRC_MAX,
}GAME_SRC_TYPE;

#endif
