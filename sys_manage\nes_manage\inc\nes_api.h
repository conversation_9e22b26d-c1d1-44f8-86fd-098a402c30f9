/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/

#ifndef NES_API_H
#define NES_API_H


#define NES_FUNC_SUPPORT			1 //change this should updata lib file

#define NES_FRAME_SKIP				1

#define NES_APU_INT					1 //1:在中断中计算APU，0: 在HSYNC中计算

#define K6502_RW_USE_FUNC			0
#define K6502_STEP_USE_FUNC			0
#define FAST_IMPROVE	            1

#define NES_RODATA_SECTION			NESCODE_SECTION
#define NES_TEXT_SECTION			SDRAM_TEXT_SECTION//NES_COM_TEXT_SECTION //SDRAM_TEXT_SECTION
#define NES_DATA_SECTION			NESDATA_SECTION

#define NES_DRAW_USE_UI_UP          2
#define NES_DRAW_USE_VIDEO		    1
#define NES_DRAW_USE_UI		        0
#define NES_DRAW_USE			    NES_DRAW_USE_UI_UP





#include "nes_typedef.h"
#include "nes_mapper.h"
#include "nes_pApu.h"
#include "nes_dac.h"
#include "nes_k6502.h"
#include "nes_hardware.h"
#include "nes_games.h"

/*******************************************************************************
* Function Name  : nes_api_init
* Description    : nes_api_init
* Input          : none
* Output         : none
* Return         : -1 : fail, 0: success
*******************************************************************************/
//NES_TEXT_SECTION
int nes_api_init(void);
/*******************************************************************************
* Function Name  : nes_api_init
* Description    : nes_api_init
* Input          : none
* Output         : none
* Return         : -1 : fail, 0: success
*******************************************************************************/
//NES_TEXT_SECTION
void nes_api_uinit(void);
/*******************************************************************************
* Function Name  : nes_api_init
* Description    : nes_api_init
* Input          : none
* Output         : none
* Return         : -1 : fail, 0: success
*******************************************************************************/
//NES_TEXT_SECTION
void nes_palette_init(void);
/*******************************************************************************
* Function Name  : nes_loadFrame
* Description    : nes_loadFrame
* Input          : none
* Output         : none
* Return         : -1 : fail, 0: success
*******************************************************************************/
//NES_TEXT_SECTION
void nes_loadFrame(void);
/*******************************************************************************
* Function Name  : nes_service
* Description    : The main loop of InfoNES
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
int nes_start(int fd, u32 type);
/*******************************************************************************
* Function Name  : nes_service
* Description    : The main loop of InfoNES
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
int nes_service(void);
/*******************************************************************************
* Function Name  : nes_PadStateCheck
* Description    : Get a joypad state
* Input          : none
* Output         : none
* Return         :  0 : Normally
 *   				-1 : Exit an emulation
*******************************************************************************/
void nes_PadStateCheck(void);
#endif
