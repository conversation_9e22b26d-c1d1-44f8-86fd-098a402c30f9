/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          : ascii_tab, font num1 :9*16
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"
/*******************************************************************************
* Function Name  : 
* Description    : 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
const unsigned char ascii_num0_32[]= // ' '
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_33[]= // '!'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_35[]= // '#'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x36,0x00,0x36,0x00,0x36,0x00,0xff,0x00,0x36,0x00,
   0x6c,0x00,0x6c,0x00,0xff,0x00,0x6c,0x00,0x6c,0x00,0x6c,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_36[]= // '$'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x18,0x00,0x3e,0x00,0x7b,0x00,0x7b,0x00,0x78,0x00,0x38,0x00,
   0x1c,0x00,0x1e,0x00,0x7b,0x00,0x7b,0x00,0x7b,0x00,0x3e,0x00,0x18,0x00,0x18,0x00,
};
const unsigned char ascii_num0_37[]= // '%'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x66,0x00,0xfc,0x00,0xfc,0x00,0xfc,0x00,0xf8,0x00,
   0x7e,0x00,0x3f,0x00,0x3f,0x00,0x3f,0x00,0x6f,0x00,0x66,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_38[]= // '&'
{
   0x09,0x10,
   0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,
   0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,
};
const unsigned char ascii_num0_39[]= // '''
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0xc0,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_40[]= // '('
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x01,0x80,0x03,0x00,0x06,0x00,0x06,0x00,0x0c,0x00,0x0c,0x00,
   0x0c,0x00,0x0c,0x00,0x06,0x00,0x06,0x00,0x03,0x00,0x01,0x80,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_41[]= // ')'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0xc0,0x00,0x60,0x00,0x30,0x00,0x30,0x00,0x18,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x30,0x00,0x30,0x00,0x60,0x00,0xc0,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_42[]= // '*'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0xff,0x00,0x3c,0x00,
   0x3c,0x00,0xff,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_43[]= // '+'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0x18,0x00,
   0xff,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_44[]= // ','
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0xc0,0x00,
};
const unsigned char ascii_num0_45[]= // '-'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_46[]= // '.'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_47[]= // '/'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x01,0x80,0x03,0x00,0x03,0x00,0x06,0x00,0x06,0x00,0x06,0x00,
   0x0c,0x00,0x0c,0x00,0x18,0x00,0x18,0x00,0x30,0x00,0x30,0x00,0x60,0x00,0x00,0x00,
};
const unsigned char ascii_num0_48[]= // '0'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x1e,0x00,0x33,0x00,0x61,0x80,0x61,0x80,0x61,0x80,
   0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x33,0x00,0x1e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_49[]= // '1'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x38,0x00,0x78,0x00,0x18,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_50[]= // '2'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x1c,0x00,0x36,0x00,0x63,0x00,0x63,0x00,0x03,0x00,
   0x06,0x00,0x0c,0x00,0x18,0x00,0x30,0x00,0x60,0x00,0x7f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_51[]= // '3'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x3e,0x00,0x63,0x00,0x63,0x00,0x03,0x00,0x03,0x00,
   0x0e,0x00,0x03,0x00,0x03,0x00,0x63,0x00,0x63,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_52[]= // '4'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x07,0x00,0x0f,0x00,0x1b,0x00,0x1b,0x00,
   0x33,0x00,0x63,0x00,0x7f,0x80,0x03,0x00,0x03,0x00,0x03,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_53[]= // '5'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x00,0x60,0x00,0x60,0x00,0x7e,0x00,0x63,0x00,
   0x03,0x00,0x03,0x00,0x03,0x00,0x63,0x00,0x63,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_54[]= // '6'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x1e,0x00,0x33,0x00,0x63,0x00,0x60,0x00,0x7e,0x00,
   0x73,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x33,0x00,0x1e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_55[]= // '7'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x00,0x03,0x00,0x03,0x00,0x06,0x00,0x06,0x00,
   0x06,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_56[]= // '8'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x1e,0x00,0x33,0x00,0x61,0x80,0x61,0x80,0x33,0x00,
   0x1e,0x00,0x33,0x00,0x61,0x80,0x61,0x80,0x33,0x00,0x1e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_57[]= // '9'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,0x66,0x00,0x63,0x00,0x63,0x00,0x63,0x00,
   0x67,0x00,0x3f,0x00,0x03,0x00,0x63,0x00,0x66,0x00,0x3c,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_58[]= // ':'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_59[]= // ';'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0x30,0x00,
};
const unsigned char ascii_num0_60[]= // '<'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x06,0x00,0x0c,0x00,0x18,0x00,0x30,0x00,
   0x60,0x00,0x30,0x00,0x18,0x00,0x0c,0x00,0x06,0x00,0x03,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_61[]= // '='
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_62[]= // '>'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x30,0x00,0x18,0x00,0x0c,0x00,0x06,0x00,
   0x03,0x00,0x06,0x00,0x0c,0x00,0x18,0x00,0x30,0x00,0x60,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_63[]= // '?'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x3e,0x00,0x63,0x00,0x63,0x00,0x03,0x00,0x06,0x00,
   0x0c,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_64[]= // '@'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,0x66,0x00,0x7f,0x00,0xff,0x00,0xff,0x00,
   0xff,0x00,0xff,0x00,0xff,0x00,0x7e,0x00,0x60,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,
};

const unsigned char ascii_num0_65[]= // 'A'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x1c,0x00,0x1c,0x00,0x36,0x00,0x36,0x00,0x36,0x00,
   0x63,0x00,0x63,0x00,0x7f,0x00,0x63,0x00,0xc1,0x80,0xc1,0x80,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_66[]= // 'B'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0xc6,0x00,0xc3,0x00,0xc3,0x00,0xc6,0x00,
   0xfc,0x00,0xc6,0x00,0xc3,0x00,0xc3,0x00,0xc6,0x00,0xfc,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_67[]= // 'C'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,0x66,0x00,0xc3,0x00,0xc3,0x00,0xc0,0x00,
   0xc0,0x00,0xc0,0x00,0xc3,0x00,0xc3,0x00,0x66,0x00,0x3c,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_68[]= // 'D'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0xc6,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,
   0xc3,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,0xc6,0x00,0xfc,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_69[]= // 'E'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,
   0x7e,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x7f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_70[]= // 'F'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,
   0xfe,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_71[]= // 'G'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,0x66,0x00,0xc3,0x00,0xc3,0x00,0xc0,0x00,
   0xcf,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,0x67,0x00,0x3f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_72[]= // 'H'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,
   0x7f,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_73[]= // 'I'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_74[]= // 'J'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x03,0x00,0x03,0x00,0x03,0x00,0x03,0x00,
   0x03,0x00,0x03,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_75[]= // 'K'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x80,0x63,0x00,0x66,0x00,0x6c,0x00,0x78,0x00,
   0x7c,0x00,0x6c,0x00,0x66,0x00,0x63,0x00,0x63,0x00,0x61,0x80,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_76[]= // 'L'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,
   0xc0,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0xff,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_77[]= // 'M'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xc3,0x00,0xc3,0x00,0xe7,0x00,0xe7,0x00,0xe7,0x00,
   0xff,0x00,0xff,0x00,0xff,0x00,0xff,0x00,0xdb,0x00,0xdb,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_78[]= // 'N'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x73,0x00,0x73,0x00,0x7b,0x00,0x7b,0x00,
   0x7b,0x00,0x6f,0x00,0x6f,0x00,0x67,0x00,0x67,0x00,0x63,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_79[]= // 'O'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x1c,0x00,0x36,0x00,0x63,0x00,0x63,0x00,0x63,0x00,
   0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x36,0x00,0x1c,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_80[]= // 'P'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0xc6,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,
   0xc6,0x00,0xfc,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_81[]= // 'Q'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x1c,0x00,0x36,0x00,0x63,0x00,0x63,0x00,0x63,0x00,
   0x63,0x00,0x63,0x00,0x6f,0x00,0x6f,0x00,0x36,0x00,0x1f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_82[]= // 'R'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0xc6,0x00,0xc3,0x00,0xc3,0x00,0xc6,0x00,
   0xfc,0x00,0xcc,0x00,0xcc,0x00,0xc6,0x00,0xc6,0x00,0xc3,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_83[]= // 'S'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,0x66,0x00,0xc3,0x00,0xc0,0x00,0x70,0x00,
   0x1c,0x00,0x06,0x00,0xc3,0x00,0xc3,0x00,0x66,0x00,0x3c,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_84[]= // 'T'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_85[]= // 'U'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,
   0xc3,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,0x66,0x00,0x3c,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_86[]= // 'V'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xc3,0x00,0xc3,0x00,0x66,0x00,0x66,0x00,0x66,0x00,
   0x66,0x00,0x3c,0x00,0x3c,0x00,0x3c,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_87[]= // 'W'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xdb,0x00,0xdb,0x00,0xdb,0x00,0xff,0x00,0xff,0x00,
   0xff,0x00,0xff,0x00,0x66,0x00,0x66,0x00,0x66,0x00,0x66,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_88[]= // 'X'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xc3,0x00,0x66,0x00,0x66,0x00,0x3c,0x00,0x3c,0x00,
   0x18,0x00,0x3c,0x00,0x3c,0x00,0x66,0x00,0x66,0x00,0xc3,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_89[]= // 'Y'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xc3,0x00,0x66,0x00,0x66,0x00,0x3c,0x00,0x3c,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_90[]= // 'Z'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x00,0x03,0x00,0x06,0x00,0x0c,0x00,0x0c,0x00,
   0x18,0x00,0x30,0x00,0x60,0x00,0x60,0x00,0xc0,0x00,0xff,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_91[]= // '['
{
   0x09,0x10,
   0x1f,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x1f,0x00,0x00,0x00,
};
const unsigned char ascii_num0_92[]= // '\'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x18,0x00,
   0x18,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x06,0x00,0x06,0x00,0x03,0x00,0x03,0x00,
};
const unsigned char ascii_num0_93[]= // ']'
{
   0x09,0x10,
   0x7c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,
   0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x7c,0x00,0x00,0x00,
};
const unsigned char ascii_num0_94[]= // '^'
{
   0x09,0x10,
   0x00,0x00,0x1c,0x00,0x36,0x00,0x63,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_95[]= // '_'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x80,
};
const unsigned char ascii_num0_96[]= // '`'
{
   0x09,0x10,
   0x00,0x00,0x38,0x00,0x1c,0x00,0x06,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_97[]= // 'a'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3e,0x00,
   0x63,0x00,0x03,0x00,0x3f,0x00,0x63,0x00,0x67,0x00,0x3f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_98[]= // 'b'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x7e,0x00,
   0x73,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x73,0x00,0x7e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_99[]= // 'c'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3e,0x00,
   0x63,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0x63,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_100[]= // 'd'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x03,0x00,0x03,0x00,0x03,0x00,0x3f,0x00,
   0x67,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x67,0x00,0x3f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_101[]= // 'e'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,
   0x66,0x00,0xc3,0x00,0xff,0x00,0xc0,0x00,0x63,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_102[]= // 'f'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x7f,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_103[]= // 'g'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0x00,
   0x66,0x00,0x66,0x00,0x66,0x00,0x3c,0x00,0x60,0x00,0x7e,0x00,0xc3,0x00,0x7e,0x00,
};
const unsigned char ascii_num0_104[]= // 'h'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x7e,0x00,
   0x73,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_105[]= // 'i'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_106[]= // 'j'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x0c,0x00,0x0c,0x00,0x00,0x00,0x00,0x00,0x0c,0x00,
   0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x78,0x00,
};
const unsigned char ascii_num0_107[]= // 'k'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x66,0x00,
   0x6c,0x00,0x78,0x00,0x7c,0x00,0x6c,0x00,0x66,0x00,0x63,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_108[]= // 'l'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_109[]= // 'm'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfe,0x00,
   0xdb,0x00,0xdb,0x00,0xdb,0x00,0xdb,0x00,0xdb,0x00,0xdb,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_110[]= // 'n'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7e,0x00,
   0x73,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_111[]= // 'o'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,
   0x66,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,0x66,0x00,0x3c,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_112[]= // 'p'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7e,0x00,
   0x73,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x73,0x00,0x7e,0x00,0x60,0x00,0x60,0x00,
};
const unsigned char ascii_num0_113[]= // 'q'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0x00,
   0x67,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x67,0x00,0x3f,0x00,0x03,0x00,0x03,0x00,
};
const unsigned char ascii_num0_114[]= // 'r'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x37,0x00,
   0x3c,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_115[]= // 's'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3e,0x00,
   0x63,0x00,0x60,0x00,0x3e,0x00,0x03,0x00,0x63,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_116[]= // 't'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0x7f,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x0f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_117[]= // 'u'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,
   0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x67,0x00,0x3f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_118[]= // 'v'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xc3,0x00,
   0x66,0x00,0x66,0x00,0x66,0x00,0x3c,0x00,0x3c,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_119[]= // 'w'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xdb,0x00,
   0xdb,0x00,0xff,0x00,0xff,0x00,0xff,0x00,0x66,0x00,0x66,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_120[]= // 'x'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xc3,0x00,
   0x66,0x00,0x3c,0x00,0x18,0x00,0x3c,0x00,0x66,0x00,0xc3,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_121[]= // 'y'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xc3,0x00,
   0x66,0x00,0x66,0x00,0x66,0x00,0x3c,0x00,0x3c,0x00,0x18,0x00,0x18,0x00,0x70,0x00,
};
const unsigned char ascii_num0_122[]= // 'z'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x00,
   0x06,0x00,0x0c,0x00,0x18,0x00,0x30,0x00,0x60,0x00,0x7f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_123[]= // '{'
{
   0x09,0x10,
   0x00,0x00,0x0e,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x18,0x00,
   0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_124[]= // '|'
{
   0x09,0x10,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,
};
const unsigned char ascii_num0_125[]= // '}'
{
   0x09,0x10,
   0x00,0x00,0x70,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x18,0x00,
   0x30,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x70,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num0_126[]= // '~'
{
   0x09,0x10,
   0x3b,0x00,0x6f,0x00,0x6e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};


ALIGNED(4) const unsigned char * const ascii_num0_table[] =
{
   ascii_num0_32,// ' '
   ascii_num0_33,// '!'
   ascii_num0_35,// '#'
   ascii_num0_36,// '$'
   ascii_num0_37,// '%'
   ascii_num0_38,// '&'
   ascii_num0_39,// '''
   ascii_num0_40,// '('
   ascii_num0_41,// ')'
   ascii_num0_42,// '*'
   ascii_num0_43,// '+'
   ascii_num0_44,// ','
   ascii_num0_45,// '-'
   ascii_num0_46,// '.'
   ascii_num0_47,// '/'
   ascii_num0_48,// '0'
   ascii_num0_49,// '1'
   ascii_num0_50,// '2'
   ascii_num0_51,// '3'
   ascii_num0_52,// '4'
   ascii_num0_53,// '5'
   ascii_num0_54,// '6'
   ascii_num0_55,// '7'
   ascii_num0_56,// '8'
   ascii_num0_57,// '9'
   ascii_num0_58,// ':'
   ascii_num0_59,// ';'
   ascii_num0_60,// '<'
   ascii_num0_61,// '='
   ascii_num0_62,// '>'
   ascii_num0_63,// '?'
   ascii_num0_64,// '@'
   ascii_num0_65,// 'A'
   ascii_num0_66,// 'B'
   ascii_num0_67,// 'C'
   ascii_num0_68,// 'D'
   ascii_num0_69,// 'E'
   ascii_num0_70,// 'F'
   ascii_num0_71,// 'G'
   ascii_num0_72,// 'H'
   ascii_num0_73,// 'I'
   ascii_num0_74,// 'J'
   ascii_num0_75,// 'K'
   ascii_num0_76,// 'L'
   ascii_num0_77,// 'M'
   ascii_num0_78,// 'N'
   ascii_num0_79,// 'O'
   ascii_num0_80,// 'P'
   ascii_num0_81,// 'Q'
   ascii_num0_82,// 'R'
   ascii_num0_83,// 'S'
   ascii_num0_84,// 'T'
   ascii_num0_85,// 'U'
   ascii_num0_86,// 'V'
   ascii_num0_87,// 'W'
   ascii_num0_88,// 'X'
   ascii_num0_89,// 'Y'
   ascii_num0_90,// 'Z'
   ascii_num0_91,// '['
   ascii_num0_92,// '\'
   ascii_num0_93,// ']'
   ascii_num0_94,// '^'
   ascii_num0_95,// '_'
   ascii_num0_96,// '`'
   ascii_num0_97,// 'a'
   ascii_num0_98,// 'b'
   ascii_num0_99,// 'c'
   ascii_num0_100,// 'd'
   ascii_num0_101,// 'e'
   ascii_num0_102,// 'f'
   ascii_num0_103,// 'g'
   ascii_num0_104,// 'h'
   ascii_num0_105,// 'i'
   ascii_num0_106,// 'j'
   ascii_num0_107,// 'k'
   ascii_num0_108,// 'l'
   ascii_num0_109,// 'm'
   ascii_num0_110,// 'n'
   ascii_num0_111,// 'o'
   ascii_num0_112,// 'p'
   ascii_num0_113,// 'q'
   ascii_num0_114,// 'r'
   ascii_num0_115,// 's'
   ascii_num0_116,// 't'
   ascii_num0_117,// 'u'
   ascii_num0_118,// 'v'
   ascii_num0_119,// 'w'
   ascii_num0_120,// 'x'
   ascii_num0_121,// 'y'
   ascii_num0_122,// 'z'
   ascii_num0_123,// '{'
   ascii_num0_124,// '|'
   ascii_num0_125,// '}'
   ascii_num0_126,// '~'
};