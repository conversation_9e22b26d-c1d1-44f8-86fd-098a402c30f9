/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

enum
{
	Fighter_TIPS_RECT_ID1 = 0,
	Fighter_TIPS_RECT_ID2,
	Fighter_TIPS_STR_TIP,

	Fighter_MAX_ID
};

typedef struct{
	INT16U w;
	INT16U h;
	INT8U *ptr;
}ICON_INFOR;

typedef struct {
	INT16U icon_w;
	INT16U icon_h;
	INT16U transparent;
	INT16U pos_x;
	INT16U pos_y;
} DISPLAY_ICONSHOW;

enum {
	GAME_ENEMY1 = 1,//
	GAME_ENEMY2 ,
	GAME_ENEMY3,
	GAME_FIGHTER,
	GAME_FALL,
	GAME_BUTTLE,//6  子弹
	GAME_PASS_TIPS,
	GAME_FLIGHTER_ICON_MAX
};



//-----------------------------------------------------game-------------------------------------------------endf-----
#if 0//defined(  ICON_SAVE_DEBUG)
    ICON_INFOR  game2048_buff[GAME_ICON_MAX]={
        {GAME_ICON_MAX,GAME_ICON_MAX,NULL},
        {24,24,figure_up},
        {24,24,figure_down},
        {24,24,figure_left},
        {24,24,figure_right},
        {1,32,figure_bg},
        {24,24,figure_bg_wall},
        {24,24,figure_box},
        {24,24,figure_target_box},
        {24,24,figure_target_Point},
    };
#elif 1//
ICON_INFOR  gameFighter_buff[GAME_FLIGHTER_ICON_MAX]={
    {GAME_FLIGHTER_ICON_MAX,GAME_FLIGHTER_ICON_MAX,NULL},
    {20,24,NULL},
    {20,24,NULL},
    {20,24,NULL},
    {20,24,NULL},
    {20,24,NULL},
    {20,24,NULL},
    {20,24,NULL},
    {220,80,NULL},
};
#else
ICON_INFOR  game2048_buff[GAME_ICON_MAX]={
    {GAME_ICON_MAX,GAME_ICON_MAX,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
};

#endif


UNUSED ALIGNED(4) const widgetCreateInfor gFighterWin[] =
{
	createFrameWin( 				Rx(0),   Ry(0),   Rw(320), Rh(240), R_ID_PALETTE_Black,     WIN_ABS_POS|WIN_NOT_ZOOM),

	//createRect(Fighter_TIPS_RECT_ID1,              Rx((220-104)/2),Ry((176-56)/2), Rw(104),Rh(56),R_ID_PALETTE_Yellow),
	//createRect(Fighter_TIPS_RECT_ID2,              Rx((220-96)/2),Ry((176-48)/2), Rw(96),Rh(48),R_ID_PALETTE_Gray),
	//createStringIcon(Fighter_TIPS_STR_TIP, Rx(0),Ry(105),Rw(220),Rh(30),R_ID_STR_COM_FAILED,ALIGNMENT_CENTER, R_ID_PALETTE_White,0),

	widgetEnd(),
};


