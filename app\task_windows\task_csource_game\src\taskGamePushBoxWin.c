/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{

	PUSHBOX_TIPS_RECT_ID1 = 0,
	PUSHBOX_TIPS_RECT_ID2,
	PUSHBOX_TIPS_STR_TIP,
	PUSHBOX_TIPS_MODE_STR_ID,

	PUSHBOX_MAX_ID
};

typedef struct{
	INT16U w;
	INT16U h;
	INT8U *ptr;
}ICON_INFOR;

typedef struct {
	INT16U icon_w;
	INT16U icon_h;
	INT16U transparent;
	INT16U pos_x;
	INT16U pos_y;
} DISPLAY_ICONSHOW;

enum {
	GAME_MAN_UP = 1,
	GAME_MAN_DOWN,
	GAME_MAN_LEFT,
	GAME_MAN_RIGHT,
	GAME_MAN_BG,
	GAME_MAN_WALL,
	GAME_BOX,
	GAME_TARGET_BOX,
	GAME_TARGET_POINT,
	GAME_PASS_TIPS,
	GAME_ICON_MAX
};

INT8U level_temp[8][8]=
 {
 0, 0, 0, 0, 0, 0, 0, 0,
 0, 0, 0, 0, 0, 0, 0, 0,
 0, 0, 0, 0, 0, 0, 0, 0,
 0, 0, 0, 0, 0, 0, 0, 0,
 0, 0, 0, 0, 0, 0, 0, 0,
 0, 0, 0, 0, 0, 0, 0, 0,
 0, 0, 0, 0, 0, 0, 0, 0,
 0, 0, 0, 0, 0, 0, 0, 0,

 };

//0 绌哄湴 1 锟�? 2鑳屾櫙 3 锟�?鐨勫湴 4 绠卞瓙 5 锟�? 7 鎴愬姛绠卞瓙

const INT8U level[][8][8]=
{

	{//1
	0, 0, 0, 1, 1, 1, 0, 0,
	0, 0, 0, 1, 3, 1, 0 ,0,
	1, 1, 1, 1, 2, 1, 0 ,0,
	1, 3, 2, 4, 4, 1, 1, 1,
	1, 1, 1, 2, 5, 4, 3, 1,
	0, 0, 1, 4, 1, 1, 1, 1,
	0, 0, 1, 3, 1, 0, 0, 0,
	0, 0, 1, 1, 1, 0, 0, 0,

	},
	{//2
	0, 0, 0, 0, 0, 0, 0, 0,
	0, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 5, 1, 1, 3, 3, 1,
	1, 2, 4, 4, 2, 4, 3, 1,
	1, 2, 2, 2, 4, 2, 2, 1,
	1, 1, 1, 1, 2, 2, 3, 1,
	0, 0, 0, 1, 1, 1, 1, 1,
	0, 0, 0, 0, 0, 0, 0, 0,
	},

	{//3
	0, 0, 0, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 3, 3, 3, 1,
	1, 2, 5, 1, 1, 4, 2, 1,
	1, 2, 4, 2, 2, 2, 2, 1,
	1, 1, 2, 1, 1, 2, 2, 1,
	1, 1, 2, 2, 2, 1, 4, 1,
	0, 1, 1, 1, 2, 2, 2, 1,
	0, 0, 0, 1, 1, 1, 1, 1,
	},
	/*锟�?1锟�?*/
    {
	0, 0, 1, 1, 1, 1, 0, 0,
	0, 0, 1, 5, 2, 1, 0, 0,
	1, 1, 1, 2, 4, 1, 0, 0,
	1, 2, 4, 4, 3, 1, 1, 0,
	1, 2, 4, 3, 3, 2, 1, 0,
	1, 2, 4, 3, 3, 2, 1, 0,
	1, 2, 2, 2, 1, 1, 1, 0,
	1, 1, 1, 1, 1, 0, 0, 0,

    },

	{//7
	0 ,1, 1, 1, 1, 0, 0, 0,
	0 ,1, 2, 2, 1, 0, 0, 0,
	1 ,1, 2, 2, 1, 1, 1, 0,
	1 ,2, 4, 3, 2, 2, 1, 0,
	1 ,2, 3, 4, 2, 2, 1, 0,
	1 ,5, 2, 3, 4, 2, 1, 0,
	1 ,1, 1, 2, 2, 1, 1, 0,
	0 ,0, 1, 1, 1, 1, 0, 0,

	},
	/*锟�?3锟�?*/
	{
	0, 0, 0, 0, 1, 1, 1, 1,
	0, 0, 1, 1, 1, 2, 2, 1,
	1, 1, 1, 2, 2, 2, 2, 1,
	1, 3, 2, 2, 4, 1, 5, 1,
	1, 3, 3, 4, 2, 4, 2, 1,
	1, 1, 1, 3, 2, 4, 2, 1,
	0, 0, 1, 1, 1, 2, 2, 1,
	0, 0, 0, 0, 1, 1, 1, 1,

	},
	/*锟�?4锟�?*/
	{
	1, 1, 1, 1, 1, 1, 1, 0,
	1, 2, 2, 5, 2, 2, 1, 0,
	1, 2, 4, 4, 4, 2, 1, 0,
	1, 1, 3, 3, 3, 1, 1, 0,
	1, 2, 3, 2, 3, 2, 1, 0,
	1, 2, 4, 2, 4, 2, 1, 0,
	1, 2, 2, 1, 2, 2, 1, 0,
	1, 1, 1, 1, 1, 1, 1, 0,
	},
	/*锟�?5锟�?*/
	{
	0, 1, 1, 1, 1, 1, 0, 0,
	1, 1, 2, 3, 2, 1, 0, 0,
	1, 2, 4, 3, 4, 1, 1, 0,
	1, 2, 2, 3, 4, 2, 1, 0,
	1, 2, 4, 3, 2, 5, 1, 0,
	1, 2, 4, 3, 4, 1, 1, 0,
	1, 1, 2, 3, 2, 1, 0, 0,
	1, 0, 1, 1, 1, 1, 0, 0,

	},
		/*锟�?4锟�?*/
	{
	0, 0, 1, 1, 1, 1, 1, 0, 
	1, 1, 1, 2, 2, 5, 1, 0, 
	1, 2, 2, 4, 3, 2, 1, 1, 
	1, 2, 2, 3, 4, 3, 2, 1, 
	1, 1, 1, 2, 7, 4, 2, 1, 
	0, 0, 1, 2, 2, 2, 1, 1, 
	0, 0, 1, 1, 1, 1, 1, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	},
	/*锟�?5锟�?*/
	{
	0, 1, 1, 1, 1, 1, 1, 0, 
	0, 1, 3, 2, 3, 3, 1, 0, 
	0, 1, 3, 2, 4, 3, 1, 0, 
	1, 1, 1, 2, 2, 4, 1, 1, 
	1, 2, 4, 2, 2, 4, 2, 1, 
	1, 2, 1, 4, 1, 1, 2, 1, 
	1, 2, 2, 2, 5, 2, 2, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	}

};

//-----------------------------------------------------game-------------------------------------------------endf-----
#if 0//defined(  ICON_SAVE_DEBUG)
    ICON_INFOR  game_buff[GAME_ICON_MAX]={
        {GAME_ICON_MAX,GAME_ICON_MAX,NULL},
        {24,24,figure_up},
        {24,24,figure_down},
        {24,24,figure_left},
        {24,24,figure_right},
        {1,32,figure_bg},
        {24,24,figure_bg_wall},
        {24,24,figure_box},
        {24,24,figure_target_box},
        {24,24,figure_target_Point},
    };
#elif 1//
ICON_INFOR  game_buff[GAME_ICON_MAX]={
    {GAME_ICON_MAX,GAME_ICON_MAX,NULL},
    {30,30,NULL},
    {30,30,NULL},
    {30,30,NULL},
    {30,30,NULL},
    {30,30,NULL},
    {30,30,NULL},
    {30,30,NULL},
    {30,30,NULL},
    {30,30,NULL},
	{220,80,NULL},
};
#else
ICON_INFOR  game_buff[GAME_ICON_MAX]={
    {GAME_ICON_MAX,GAME_ICON_MAX,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
};

#endif


UNUSED ALIGNED(4) const widgetCreateInfor pushBoxWin[] =
{
	createFrameWin( 				Rx(0),   Ry(0),   Rw(320), Rh(240), R_ID_PALETTE_Black,     WIN_ABS_POS|WIN_NOT_ZOOM),

	//createRect(PUSHBOX_TIPS_RECT_ID1,              Rx((220-104)/2),Ry((176-56)/2), Rw(104),Rh(56),R_ID_PALETTE_Yellow),
	//createRect(PUSHBOX_TIPS_RECT_ID2,              Rx((220-96)/2),Ry((176-48)/2), Rw(96),Rh(48),R_ID_PALETTE_Gray),
	//createStringIcon(PUSHBOX_TIPS_STR_TIP, Rx(0),Ry(105),Rw(320),Rh(30),R_ID_STR_COM_FAILED,ALIGNMENT_CENTER, R_ID_PALETTE_White,0),

	widgetEnd(),
};


