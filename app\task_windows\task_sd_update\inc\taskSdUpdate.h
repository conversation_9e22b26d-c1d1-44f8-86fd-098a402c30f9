/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef  __TASK_SD_UPDATE_H
#define  __TASK_SD_UPDATE_H

#define SD_UPDATE_VERIFY          	1
#define SD_UPDATE_END_SAVE          1
#define SD_UPDATE_CHECK_SAME        1   // verify bin between cur and sdcard, if same , no to update
#define SD_UPDATE_DELETE          	0   // delete update file
#define SD_UPDATE_RESOURCE       	0   // update resource when upgrade end

#define SD_UPDATE_PATH_NAME      	"Xly100s.bin"//"hx330x_sdk.bin"

typedef struct SDUpdate_OP_S{
	winHandle progressBar;
	winHandle strHandle;
	int       fd;
	u32       ofs;
	u32       clustsize;
	u32*      clbtbl;
	u8*		  binbuf;
	u32       binsize;
	u32       binsize_align;
	u8*		  binverifybuf;
	u32	  	  binverifysize;
	u8*		  videoBuff;
	u32		  videoYSize;
	u8*		  videoYAddr;
	u8*		  videoUVAddr;
	u8*		  uiDrawBuff;
	u32		  uiDrawSize;
	u8*		  music_buff;
	u32		  music_size;
	u32		  music_offset;
	s32		  video_scan_mode;
	s32       ui_scan_mode;
	u16		  video_w, video_h;
	u16 	  ui_w, ui_h; 
	u16       video_stride, ui_stride;
}SDUpdate_OP;
extern SDUpdate_OP sd_update_op;
extern sysTask_T taskSDUpdate;

/*******************************************************************************
* Function Name  : taskSdUpdate_uiProgress
* Description    : taskSdUpdate_uiProgress
* Input          : 
* Output         : none                                            
* Return         : 
*******************************************************************************/
void taskSdUpdate_uiProgress(u32 progress);
/*******************************************************************************
* Function Name  : taskSdUpdate_uiProgress
* Description    : taskSdUpdate_uiProgress
* Input          : 
* Output         : none                                            
* Return         : 
*******************************************************************************/
void taskSdUpdate_uiInit(void);
/*******************************************************************************
* Function Name  : upgrade
* Description    : upgrade
* Input          : 
* Output         : none                                            
* Return         : int : 0: ->upgrade fail
                            -1:->no upgrade file
                           <-1:->upgrade fail,fireware maybe error
*******************************************************************************/
int taskSdUpdateProcess(void);



#endif
