/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef KID_FRAME_API_H
#define KID_FRAME_API_H

#define KID_FRAME_FUNC_SUPPORT      1 //change this should updata lib file

/*******************************************************************************
* Function Name  : kid_frame_init
* Description    : kid_frame_init
* Input          : 
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
void kid_frame_init(void);
/*******************************************************************************
* Function Name  : kid_frame_uinit
* Description    : kid_frame_uinit
* Input          : 
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
void kid_frame_uinit(void);
/*******************************************************************************
* Function Name  : kid_frame_uinit
* Description    : kid_frame_uinit
* Input          : 
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
void kid_frame_lcd_add(u8 *lcd_yuv_buf);
/*******************************************************************************
* Function Name  : kid_frame_uinit
* Description    : kid_frame_uinit
* Input          : 
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
void kid_frame_csi_add(u8 *lcd_yuv_buf);

/*******************************************************************************
* Function Name  : kid_frame_create
* Description    : kid_frame_create:should  reset pip mode
* Input          : 
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
int kid_frame_create(u32 res_idx, u8 lcd_frame_en, u8 csi_frame_en);

/*******************************************************************************
* Function Name  : kid_frame_uinit
* Description    : kid_frame_uinit
* Input          : 
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
void kid_frame_add_en(u8 en);




#endif
