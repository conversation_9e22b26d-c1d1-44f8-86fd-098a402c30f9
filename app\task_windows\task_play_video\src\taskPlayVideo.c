/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"


ALIGNED(4) PLAYVIDEO_OP_T  playVideoOp;
/*******************************************************************************
* Function Name  : taskPlayVideoSlideStyleProcess
* Description    : taskPlayVideoSlideStyleProcess
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static void taskPlayVideoSlideStyleProcess(void)
{
	playVideoOp.slide.step_cur++;
	if(playVideoOp.slide.step_cur == playVideoOp.slide.step_max)
	{
		playVideoOp.slide.rect_type = SLIDE_RECT_SLIDE;
		playVideoOp.slide.slide_rect.xs	= 0;
		playVideoOp.slide.slide_rect.ys	= 0;
		playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe;
		playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye;
	}else
	{
		u16 slide_w = playVideoOp.slide.main_rect.xe / playVideoOp.slide.step_max * playVideoOp.slide.step_cur;
		u16 slide_h = playVideoOp.slide.main_rect.ye / playVideoOp.slide.step_max * playVideoOp.slide.step_cur;
		if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_AROUND_OPEN)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_SLIDE;
			playVideoOp.slide.slide_rect.xs	= playVideoOp.slide.main_rect.xe/2 - slide_w/2;
			playVideoOp.slide.slide_rect.ys	= playVideoOp.slide.main_rect.ye/2 - slide_h/2;
			playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe/2 + slide_w/2;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye/2 + slide_h/2;
		}else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_AROUND_CLOSE)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_MAIN;
			playVideoOp.slide.slide_rect.xs	= slide_w/2;
			playVideoOp.slide.slide_rect.ys	= slide_h/2;
			playVideoOp.slide.slide_rect.xe = playVideoOp.slide.main_rect.xe - slide_w/2;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye - slide_h/2;
		}else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_HOR_OPEN)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_SLIDE;
			playVideoOp.slide.slide_rect.xs	= playVideoOp.slide.main_rect.xe/2 - slide_w/2;
			playVideoOp.slide.slide_rect.ys	= 0;
			playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe/2 + slide_w/2;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye;
		}
		else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_HOR_CLOSE)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_MAIN;
			playVideoOp.slide.slide_rect.xs	= slide_w/2;
			playVideoOp.slide.slide_rect.ys	= 0;
			playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe - slide_w/2;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye;
		}
		else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_VER_OPEN)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_SLIDE;
			playVideoOp.slide.slide_rect.xs	= 0;
			playVideoOp.slide.slide_rect.ys	= playVideoOp.slide.main_rect.ye/2 - slide_h/2;
			playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye/2 + slide_h/2;
		}else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_VER_CLOSE)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_MAIN;
			playVideoOp.slide.slide_rect.xs	= 0;
			playVideoOp.slide.slide_rect.ys	= slide_h/2;
			playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye - slide_h/2;
		}else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_LEFT)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_SLIDE;
			playVideoOp.slide.slide_rect.xs	= 0;
			playVideoOp.slide.slide_rect.ys	= 0;
			playVideoOp.slide.slide_rect.xe	= slide_w;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye;
		}else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_RIGHT)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_SLIDE;
			playVideoOp.slide.slide_rect.xs	= playVideoOp.slide.main_rect.xe - slide_w;
			playVideoOp.slide.slide_rect.ys	= 0;
			playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye;
		}else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_UP)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_SLIDE;
			playVideoOp.slide.slide_rect.xs	= 0;
			playVideoOp.slide.slide_rect.ys	= 0;
			playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe;
			playVideoOp.slide.slide_rect.ye	= slide_h;
		}else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_DOWN)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_SLIDE;
			playVideoOp.slide.slide_rect.xs	= 0;
			playVideoOp.slide.slide_rect.ys	= playVideoOp.slide.main_rect.ye - slide_h;
			playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye;
		}
	}
}
/*******************************************************************************
* Function Name  : taskPlayVideoSlideRectDraw
* Description    : taskPlayVideoSlideRectDraw
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static void taskPlayVideoSlideRectDraw(lcdshow_frame_t* displaybuf)
{
	u16 i;
	u8* tar_buf 	= displaybuf->y_addr;
	u8* main_buf 	= playVideoOp.slide.mainBuf;
	u8* slide_buf 	= playVideoOp.slide.slideBuf;
	u16 stride 		= (playVideoOp.slide.main_rect.xe + 0x1f) & ~0x1f;
	playVideoOp.slide.slide_rect.xs = playVideoOp.slide.slide_rect.xs&~3;
	playVideoOp.slide.slide_rect.ys = playVideoOp.slide.slide_rect.ys&~3;
	playVideoOp.slide.slide_rect.xe = playVideoOp.slide.slide_rect.xe&~3;
	playVideoOp.slide.slide_rect.ye = playVideoOp.slide.slide_rect.ye&~3;

	for(i = 0; i < playVideoOp.slide.main_rect.ye; i++) //y addr
	{
		if(i >= playVideoOp.slide.slide_rect.ys && i < playVideoOp.slide.slide_rect.ye)
		{
			if(playVideoOp.slide.rect_type == SLIDE_RECT_SLIDE) //叠加mainbuf
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf,
											(void*)main_buf,
											playVideoOp.slide.slide_rect.xs);
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf + playVideoOp.slide.slide_rect.xs,
											(void*)slide_buf + playVideoOp.slide.slide_rect.xs,
											playVideoOp.slide.slide_rect.xe - playVideoOp.slide.slide_rect.xs);
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf + playVideoOp.slide.slide_rect.xe,
											(void*)main_buf + playVideoOp.slide.slide_rect.xe,
											 playVideoOp.slide.main_rect.xe - playVideoOp.slide.slide_rect.xe);
			}else
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf,
											(void*)slide_buf,
											playVideoOp.slide.slide_rect.xs);
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf + playVideoOp.slide.slide_rect.xs,
											(void*)main_buf + playVideoOp.slide.slide_rect.xs,
											playVideoOp.slide.slide_rect.xe - playVideoOp.slide.slide_rect.xs);
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf + playVideoOp.slide.slide_rect.xe,
											(void*)slide_buf + playVideoOp.slide.slide_rect.xe,
											 playVideoOp.slide.main_rect.xe - playVideoOp.slide.slide_rect.xe);
			}
		}else //rect 外区域
		{
			if(playVideoOp.slide.rect_type == SLIDE_RECT_SLIDE) //叠加mainbuf
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf,
												(void*)main_buf,
												playVideoOp.slide.main_rect.xe);
			}else //叠加slidebuf
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf,
												(void*)slide_buf,
												playVideoOp.slide.main_rect.xe);
			}
		}
		tar_buf 	+= stride;
		main_buf 	+= stride;
		slide_buf 	+= stride;
	}
	tar_buf = displaybuf->uv_addr;
	for(i = 0; i < playVideoOp.slide.main_rect.ye/2; i++) //uv addr
	{
		if(i >= playVideoOp.slide.slide_rect.ys/2 && i < playVideoOp.slide.slide_rect.ye/2)
		{
			if(playVideoOp.slide.rect_type == SLIDE_RECT_SLIDE) //叠加mainbuf
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf,
											(void*)main_buf,
											playVideoOp.slide.slide_rect.xs);
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf + playVideoOp.slide.slide_rect.xs,
											(void*)slide_buf + playVideoOp.slide.slide_rect.xs,
											playVideoOp.slide.slide_rect.xe - playVideoOp.slide.slide_rect.xs);
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf + playVideoOp.slide.slide_rect.xe,
											(void*)main_buf + playVideoOp.slide.slide_rect.xe,
											 playVideoOp.slide.main_rect.xe - playVideoOp.slide.slide_rect.xe);
			}else
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf,
											(void*)slide_buf,
											playVideoOp.slide.slide_rect.xs);
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf + playVideoOp.slide.slide_rect.xs,
											(void*)main_buf + playVideoOp.slide.slide_rect.xs,
											playVideoOp.slide.slide_rect.xe - playVideoOp.slide.slide_rect.xs);
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf + playVideoOp.slide.slide_rect.xe,
											(void*)slide_buf + playVideoOp.slide.slide_rect.xe,
											 playVideoOp.slide.main_rect.xe - playVideoOp.slide.slide_rect.xe);
			}
		}else //rect 外区域
		{
			if(playVideoOp.slide.rect_type == SLIDE_RECT_SLIDE) //叠加mainbuf
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf,
												(void*)main_buf,
												playVideoOp.slide.main_rect.xe);
			}else //叠加slidebuf
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf,
												(void*)slide_buf,
												playVideoOp.slide.main_rect.xe);
			}
		}
		tar_buf 	+= stride;
		main_buf 	+= stride;
		slide_buf 	+= stride;
	}
}
/*******************************************************************************
* Function Name  : taskPlayVideoSlideOpen
* Description    : taskPlayVideoSlideOpen
* Input          : none
* Output         : none
* Return         : int 0: success, <0 fail
*******************************************************************************/
int taskPlayVideoSlideOpen(void)
{
	if(videoPlaybackGetStatus() != MEDIA_STAT_STOP)
		videoPlaybackStop();
	if(SysCtrl.file_cnt < 2)
		return -1;
#if 1 // 按最大VIDEO SIZE 显示
	hal_lcdGetVideoPos(&playVideoOp.slide.main_rect.xs,&playVideoOp.slide.main_rect.ys);
	hal_lcdGetVideoResolution(&playVideoOp.slide.main_rect.xe,&playVideoOp.slide.main_rect.ye);
#else //按当前ratio显示
	hal_lcdGetVideoRatioPos(&playVideoOp.slide.main_rect.xs,&playVideoOp.slide.main_rect.ys);
	hal_lcdGetVideoRatioResolution(&playVideoOp.slide.main_rect.xe,&playVideoOp.slide.main_rect.ye);
#endif
	playVideoOp.slide.bufsize 	= ((playVideoOp.slide.main_rect.xe + 0x1f)&~0x1f) * playVideoOp.slide.main_rect.ye*3/2;
	playVideoOp.slide.mainBuf    = hal_sysMemMalloc(playVideoOp.slide.bufsize);
	playVideoOp.slide.slideBuf   = hal_sysMemMalloc(playVideoOp.slide.bufsize);
	if(playVideoOp.slide.mainBuf == NULL || playVideoOp.slide.slideBuf == NULL)
	{
		deg_Printf("[SLIDE OPEN] malloc fail\n");
		return -1;
	}
	deg_Printf("[SLIDE OPEN]\n");
	playVideoOp.playMode 		= PLAYVIDEO_SLIDE;
	playVideoOp.slide.playstat	= PLAYVIDEO_SLIDE_STOP;
	playVideoOp.slide.rect_type = 0;
	playVideoOp.slide.step_cur  = 0;
	playVideoOp.slide.step_max  = 10;
	playVideoOp.slide.style     = PLAYVIDEO_SLIDE_AROUND_OPEN;
	playVideoOp.slide.frame_interval  = 2000; //2sec播放一个step
	playVideoOp.slide.playtime  = 0;
	playVideoOp.slide.file_index = SysCtrl.file_index;
	return 0;
}
/*******************************************************************************
* Function Name  : taskPlayVideoSlideClose
* Description    : taskPlayVideoSlideClose
* Input          : none
* Output         : none
* Return         : int 0: success, <0 fail
*******************************************************************************/
void taskPlayVideoSlideClose(void)
{
	playVideoOp.playMode 		= PLAYVIDEO_MAIN;
	playVideoOp.slide.playstat	= PLAYVIDEO_SLIDE_STOP;
	hal_sysMemFree(playVideoOp.slide.mainBuf);
	hal_sysMemFree(playVideoOp.slide.slideBuf);
	playVideoOp.slide.mainBuf = playVideoOp.slide.slideBuf = NULL;
}
/*******************************************************************************
* Function Name  : taskPlayVideoSlidePause
* Description    : taskPlayVideoSlidePause
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void taskPlayVideoSlidePause(void)
{
	if(playVideoOp.slide.playstat == PLAYVIDEO_SLIDE_START)
		playVideoOp.slide.playstat = PLAYVIDEO_SLIDE_PAUSE;
	else if(playVideoOp.slide.playstat == PLAYVIDEO_SLIDE_PAUSE)
		playVideoOp.slide.playstat = PLAYVIDEO_SLIDE_START;
}
/*******************************************************************************
* Function Name  : taskPlayVideoSlideStart
* Description    : taskPlayVideoSlideStart
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void taskPlayVideoSlideStart(void)
{
	lcdshow_frame_t* p_lcd_buffer;
	if(playVideoOp.playMode != PLAYVIDEO_SLIDE)
		return;
	deg_Printf("taskPlayVideoSlideStart:%d\n",playVideoOp.slide.playstat);
	if(playVideoOp.slide.playstat == PLAYVIDEO_SLIDE_STOP)
	{
		//mainBuf:  SysCtrl.file_index
		//slidebuf: playVideoOp.slide.file_index
		deg_Printf("taskPlayVideoSlideStart\n");
		
		if(taskComDecodeImg(MEDIA_SRC_FS, SysCtrl.file_index, -1, playVideoOp.slide.slideBuf,playVideoOp.slide.main_rect.xe,playVideoOp.slide.main_rect.ye) < 0)
		{
			hal_lcdSetBufYUV_2(playVideoOp.slide.mainBuf,playVideoOp.slide.bufsize,0,0x80);
		}
		do{
			p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
		}while(p_lcd_buffer==NULL);
		hal_lcdVideoFrameFlush(p_lcd_buffer,
								playVideoOp.slide.main_rect.xs,playVideoOp.slide.main_rect.ys,
								playVideoOp.slide.main_rect.xe,playVideoOp.slide.main_rect.ye,
								playVideoOp.slide.main_rect.xe,playVideoOp.slide.main_rect.ye);

		hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->y_addr, (void*)playVideoOp.slide.slideBuf, playVideoOp.slide.bufsize);
		hal_lcdVideoSetFrame((void *)p_lcd_buffer);

		playVideoOp.slide.file_index = SysCtrl.file_index + 1;
		if(playVideoOp.slide.file_index >= SysCtrl.file_cnt)
		{
			playVideoOp.slide.file_index = 0;
		}
		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_PLAY,MSG_PLAY_START));
		playVideoOp.slide.playtime = XOSTimeGet();
		playVideoOp.slide.playstat = PLAYVIDEO_SLIDE_START;
		playVideoOp.slide.step_cur = 0;
		playVideoOp.slide.style    = PLAYVIDEO_SLIDE_AROUND_OPEN;
		playVideoOp.slide.step_interval = playVideoOp.slide.frame_interval;

	}
}
/*******************************************************************************
* Function Name  : taskPlayVideoSlideStyleProcess
* Description    : taskPlayVideoSlideStyleProcess
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static void taskPlayVideoSlideService(void)
{
	lcdshow_frame_t* p_lcd_buffer;
	if(playVideoOp.playMode != PLAYVIDEO_SLIDE)
		return;
	if(playVideoOp.slide.playstat != PLAYVIDEO_SLIDE_START)
		return;
	if(XOSTimeGet() - playVideoOp.slide.playtime < playVideoOp.slide.step_interval)
		return;

	if(playVideoOp.slide.file_index != SysCtrl.file_index)
	{
		u8 *tempbuf = playVideoOp.slide.mainBuf;
		playVideoOp.slide.mainBuf = playVideoOp.slide.slideBuf;
		playVideoOp.slide.slideBuf = tempbuf;
		if(taskComDecodeImg(MEDIA_SRC_FS, SysCtrl.file_index, -1, playVideoOp.slide.slideBuf,playVideoOp.slide.main_rect.xe,playVideoOp.slide.main_rect.ye) < 0)
		{
			hal_lcdSetBufYUV_2(playVideoOp.slide.slideBuf,playVideoOp.slide.bufsize,0,0x80);
		}
		SysCtrl.file_index = playVideoOp.slide.file_index;
	}
	do{
		p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
	}while(p_lcd_buffer==NULL);
	hal_lcdVideoFrameFlush(p_lcd_buffer,
						playVideoOp.slide.main_rect.xs,playVideoOp.slide.main_rect.ys,
						playVideoOp.slide.main_rect.xe,playVideoOp.slide.main_rect.ye,
						playVideoOp.slide.main_rect.xe,playVideoOp.slide.main_rect.ye);
	taskPlayVideoSlideStyleProcess();
	taskPlayVideoSlideRectDraw(p_lcd_buffer);
	if(playVideoOp.slide.step_cur == playVideoOp.slide.step_max)
	{
		playVideoOp.slide.step_cur = 0;
		playVideoOp.slide.style++;
		if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_TYPE_MAX)
			playVideoOp.slide.style = PLAYVIDEO_SLIDE_AROUND_OPEN;

		playVideoOp.slide.step_interval = playVideoOp.slide.frame_interval;
		//deg_Printf("next\n");
		playVideoOp.slide.file_index++;
		if(playVideoOp.slide.file_index >= SysCtrl.file_cnt)
		{
			playVideoOp.slide.file_index = 0;
		}

		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_PLAY,MSG_PLAY_START));
	}else
	{
		playVideoOp.slide.step_interval = 1000/playVideoOp.slide.step_max;
	}
	hal_lcdVideoSetFrame((void *)p_lcd_buffer);
	playVideoOp.slide.playtime = XOSTimeGet();
}
/*******************************************************************************
* Function Name  : taskPlayVideoMainStart
* Description    : taskPlayVideoMainStart
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
int taskPlayVideoMainStart(int index)
{
	char * name;
	int  type;
	u32  para;
	playVideoOp.playErrIndex = -1;

	//playVideoOp.playTotalTime = 0;
	//playVideoOp.playCurTime   = 0;
	//playVideoOp.playLastTime  = 0;
	if(videoPlaybackGetStatus() != MEDIA_STAT_STOP)
		videoPlaybackStop();
	if(SysCtrl.file_cnt < 1)
		goto PLAY_ERROR;
	deg_Printf("[PLAY VIDEO] start: index = %d ",index);
	name = filelist_GetFileFullNameByIndex(playVideoOp.list,index,&type);
	if(name == NULL)
	{
		goto PLAY_ERROR;
	}
	SysCtrl.play_total_time = 0;
	SysCtrl.play_cur_time   = 0;
	SysCtrl.play_last_time  = 0;
    SysCtrl.file_index = index;
    SysCtrl.file_type  = type;
	deg_Printf(": %s\n",name);
	u16 x, y, width, height, dest_w, dest_h;

#if 0 //按最大video size显示
	hal_lcdGetVideoPos(&x,&y);
	hal_lcdGetVideoResolution(&width,&height);
	hal_lcdGetVideoResolution(&dest_w,&dest_h);
#else //按实际ratio后的size显示
	hal_lcdGetVideoRatioPos(&x,&y);
	hal_lcdGetVideoRatioResolution(&width,&height);
	hal_lcdGetVideoRatioDestResolution(&dest_w,&dest_h);
#endif
	if((type & FILELIST_TYPE_MASK) == FILELIST_TYPE_JPG)
	{
		lcdshow_frame_t *p_lcd_buffer;
		JPG_DEC_ARG jpg_dec_arg;

		do{
			p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
		}while(p_lcd_buffer==NULL);

		hal_lcdVideoFrameFlush(p_lcd_buffer,x,y,width,height,dest_w,dest_h);
		jpg_dec_arg.type 		= MEDIA_FILE_JPG;
		jpg_dec_arg.wait 		= 1;
		jpg_dec_arg.fd 			= fs_open(name,FA_READ);
		deg_Printf("jpg_dec_arg.fd:%d\n",jpg_dec_arg.fd);
		jpg_dec_arg.src_type	= MEDIA_SRC_FS;
		jpg_dec_arg.dst_width	= width;
		jpg_dec_arg.dst_height  = height;
		jpg_dec_arg.jpgsize		= 0;
        jpg_dec_arg.yout  		= p_lcd_buffer->y_addr;
        jpg_dec_arg.uvout 		= p_lcd_buffer->uv_addr;
		jpg_dec_arg.step_yout 	= NULL;
		jpg_dec_arg.p_lcd_buffer = p_lcd_buffer;
	    if(imageDecodeStart(&jpg_dec_arg)<0)
		{
			fs_close(jpg_dec_arg.fd);
			hal_dispframeFree((lcdshow_frame_t *) p_lcd_buffer);

			goto PLAY_ERROR;
		}
		kid_frame_lcd_add(p_lcd_buffer->y_addr);
		hal_lcdVideoSetFrame((void *)p_lcd_buffer);


		fs_close(jpg_dec_arg.fd);

		deg_Printf("[PLAY VIDEO]: JPG[%d:%d]\n",jpg_dec_arg.src_width,jpg_dec_arg.src_height);
	}
	else if((type & FILELIST_TYPE_MASK) == FILELIST_TYPE_AVI)
	{
		VIDEO_PARG_T video_dec_arg;
		video_dec_arg.avi_arg.src_type = MEDIA_SRC_FS;
		video_dec_arg.avi_arg.fd = (int)fs_open(name,FA_READ);
		if((int)video_dec_arg.avi_arg.fd < 0)
			goto PLAY_ERROR;
		video_dec_arg.pos_x			= x;
		video_dec_arg.pos_y			= y;
		video_dec_arg.tar_width 	= width;
		video_dec_arg.tar_height 	= height;
		video_dec_arg.rotate 		= 0;
		video_dec_arg.firstframe 	= TASK_PLAYVIDEO_STOPFRAME; // pause at first frame waiting user key
		fs_seek(video_dec_arg.avi_arg.fd,0,FA_CREATE_LINKMAP);// enable fast seek for this file
		task_com_sound_wait_end(); // wait key sound end
		if(videoPlaybackStart(&video_dec_arg)<0)
		{
			deg_Printf("[PLAY VIDEO] avi decode fail<0x%x>\n",(int)video_dec_arg.avi_arg.fd);
			fs_close(video_dec_arg.avi_arg.fd);
			goto PLAY_ERROR;
		}
		deg_Printf("[%d,%d]\n",width,height);
		videoPlaybackGetTime(&SysCtrl.play_total_time,&SysCtrl.play_cur_time);
		SysCtrl.play_last_time = SysCtrl.play_total_time;
		deg_Printf("[PLAY VIDEO] : AVI[%d:%d],total time = %dms\n",video_dec_arg.avi_arg.width,video_dec_arg.avi_arg.height,SysCtrl.play_total_time);
	}else if((type & FILELIST_TYPE_MASK) == FILELIST_TYPE_SPI)
	{
		lcdshow_frame_t *p_lcd_buffer;
		JPG_DEC_ARG jpg_dec_arg;
		do{
			p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
		}while(p_lcd_buffer==NULL);

		hal_lcdVideoFrameFlush(p_lcd_buffer,x,y,width,height,dest_w,dest_h);
		jpg_dec_arg.type 		= MEDIA_FILE_JPG;
		jpg_dec_arg.wait 		= 1;
		jpg_dec_arg.fd 			= (nv_jpg_open(filelist_GetFileIndexByIndex(playVideoOp.list,index),NVFA_READ) == NV_OK) ? 0 : -1;
		deg_Printf("jpg_dec_arg.fd:%d\n",jpg_dec_arg.fd);
		jpg_dec_arg.src_type	= MEDIA_SRC_NVJPG;
		jpg_dec_arg.dst_width	= width;
		jpg_dec_arg.dst_height  = height;
		jpg_dec_arg.jpgsize		= 0;
        jpg_dec_arg.yout  		= p_lcd_buffer->y_addr;
        jpg_dec_arg.uvout 		= p_lcd_buffer->uv_addr;
		jpg_dec_arg.step_yout 	= NULL;
		jpg_dec_arg.p_lcd_buffer = p_lcd_buffer;
	    if(imageDecodeSpiStart(&jpg_dec_arg)<0)
		{
			nv_jpg_close();
			hal_dispframeFree((lcdshow_frame_t *) p_lcd_buffer);

			goto PLAY_ERROR;
		}
		kid_frame_lcd_add(p_lcd_buffer->y_addr);
		hal_lcdVideoSetFrame((void *)p_lcd_buffer);


		nv_jpg_close();

		deg_Printf("[PLAY VIDEO]: JPG[%d:%d]\n",jpg_dec_arg.src_width,jpg_dec_arg.src_height);
	}else
	{
		goto PLAY_ERROR;
	}
#if 1
	para = MSG_PLAY_START;
	app_msgDealByType(SYS_EVENT_PLAY, uiWinGetCurrent(), 1, &para);
#else
	XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_PLAY,MSG_PLAY_START));
#endif
	//XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_TIME_UPDATE,0));
	//deg_Printf("[PLAY VIDEO] start.<%s>\n",name);
	return type;
PLAY_ERROR:
	playVideoOp.playErrIndex = SysCtrl.file_index;
#if 1
	para = MSG_PLAY_ERROR;
	app_msgDealByType(SYS_EVENT_PLAY, uiWinGetCurrent(), 1, &para);
#else
	XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_PLAY,MSG_PLAY_ERROR));
#endif
	return -1;
}
/*******************************************************************************
* Function Name  : taskPlayVideoDelWinShowKick
* Description    : taskPlayVideoDelWinShowKick
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
int taskPlayVideoDelWinShowKick(void)
{
	int ret = 0;
#if FUN_PLAYBACK_DEL_WIN_SHOW
	if(app_taskCurId()  != TASK_PLAY_VIDEO)
	{
		return 0;
	}
	if(playVideoOp.step >= 0)
	{
		return 0;
	}
	if(playVideoOp.wintype <= LCDSHOW_NOT_SUB_WIN || playVideoOp.wintype >= LCDSHOW_WIN_MAX)
	{
		return 0;
	}
	VIDEO_PARG_T arg;
	JPG_DEC_ARG jpg_dec_arg;
	lcdshow_frame_t *p_lcd_buffer = NULL;
	u16 video_w,video_h;
	u16 video_x, video_y;
	u16 dest_w,dest_h;
	playVideoOp.jpgbuf = NULL;


	u32 offset, length;
	int type;
	char *name = NULL;
	arg.avi_arg.src_type	= MEDIA_SRC_FS;
	arg.avi_arg.fd  		= -1;
#if 0 //按最大video size显示
	hal_lcdGetVideoPos(&video_x,&video_y);
	hal_lcdGetVideoResolution(&video_w,&video_h);
	hal_lcdGetVideoResolution(&dest_w,&dest_h);
#else //按实际ratio后的size显示
	hal_lcdGetVideoRatioPos(&video_x, &video_y);
	hal_lcdGetVideoRatioResolution(&video_w,&video_h);
	hal_lcdGetVideoRatioDestResolution(&dest_w,&dest_h);
#endif
	name = filelist_GetFileFullNameByIndex(playVideoOp.list, SysCtrl.file_index,  &type);
	if(name == NULL)
	{
		ret = -1;
		goto WINKICK_END;
	}
	if((type & FILELIST_TYPE_MASK) == FILELIST_TYPE_SPI)
	{
		arg.avi_arg.fd 			= (nv_jpg_open(filelist_GetFileIndexByIndex(playVideoOp.list,SysCtrl.file_index),NVFA_READ) == NV_OK) ? 0 : -1;
	}else
	{
		arg.avi_arg.fd			= (int)fs_open(name,FA_READ);
	}
	
	if((int)arg.avi_arg.fd < 0)
	{
		ret = -2;
		goto WINKICK_END;
	}

	if((type & FILELIST_TYPE_MASK) == FILELIST_TYPE_JPG)
	{
		length = fs_size(arg.avi_arg.fd);
		offset = 0;

	}else if((type & FILELIST_TYPE_MASK) == FILELIST_TYPE_AVI)
	{
		if(videoGetFirstFrame(&arg, &offset, &length) != STATUS_OK)
		{
			ret = -3;
			goto WINKICK_END;
		}

	}else if((type & FILELIST_TYPE_MASK) == FILELIST_TYPE_SPI)
	{
		length = nv_jpgfile_size();
		offset = 0;

	}
	else
	{
		ret = -4;
		goto WINKICK_END;
	}
	if((type & FILELIST_TYPE_MASK) != FILELIST_TYPE_SPI)
	{
		if(fs_seek(arg.avi_arg.fd,offset,0) < 0)
		{
			ret = -6;
			goto WINKICK_END;
		}
	}

	do{
		p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
	}while(p_lcd_buffer==NULL);

	hal_lcdVideoFrameFlush(p_lcd_buffer,video_x,video_y,video_w,video_h,dest_w, dest_h);
	jpg_dec_arg.type 		= MEDIA_FILE_JPG;
	jpg_dec_arg.wait 		= 1;
	jpg_dec_arg.fd 			= arg.avi_arg.fd;
	jpg_dec_arg.src_type	= MEDIA_SRC_FS;
	jpg_dec_arg.dst_width	= video_w;
	jpg_dec_arg.dst_height  = video_h;
	jpg_dec_arg.jpgsize	    = length;
    jpg_dec_arg.yout  		= p_lcd_buffer->y_addr;
    jpg_dec_arg.uvout 		= p_lcd_buffer->uv_addr;
	jpg_dec_arg.step_yout 	= NULL;
	jpg_dec_arg.p_lcd_buffer = p_lcd_buffer;
	if((type & FILELIST_TYPE_MASK) == FILELIST_TYPE_SPI)
	{
		jpg_dec_arg.src_type	= MEDIA_SRC_NVJPG;
		if(imageDecodeSpiStart(&jpg_dec_arg)<0)
		{

			ret = -7;
			goto WINKICK_END;
		}		
	}else
	{
		if(imageDecodeStart(&jpg_dec_arg)<0)
		{

			ret = -7;
			goto WINKICK_END;
		}
	}

	playVideoOp.jpgsize = hal_sysMemRemain() - 128*1024L;
	playVideoOp.jpgbuf	= (u8*)hal_sysMemMalloc(playVideoOp.jpgsize);
	if(playVideoOp.jpgbuf == NULL)
	{
		ret = -8;
		goto WINKICK_END;
	}

	kid_frame_lcd_add(p_lcd_buffer->y_addr);

	hx330x_mjpA_EncodeInit(1,0);
	hx330x_mjpA_Encode_inlinebuf_init((u32)jpg_dec_arg.yout,(u32)jpg_dec_arg.uvout);

	hx330x_mjpA_EncodeSizeSet(jpg_dec_arg.dst_width,jpg_dec_arg.dst_height, jpg_dec_arg.dst_width,jpg_dec_arg.dst_height);
	hx330x_mjpA_EncodeQuilitySet(JPEG_Q_31);

	hx330x_mjpA_EncodeBufferSet((u32)playVideoOp.jpgbuf,(u32)(playVideoOp.jpgbuf+playVideoOp.jpgsize));
	hx330x_mjpA_EncodeInfoSet(0);
	hx330x_mjpA_Encode_manual_on();
	if(hx330x_mjpA_Encode_check() == false)
	{
		ret = -9;
		goto WINKICK_END;
	}
	if(hal_mjpDecodeParse((u8 *)playVideoOp.jpgbuf,320,240) < 0)
	{
		ret = -10;
		goto WINKICK_END;
	}

WINKICK_END:
	if(p_lcd_buffer)
		hal_dispframeFree((lcdshow_frame_t *) p_lcd_buffer);
	if(ret < 0)
	{
		deg_Printf("PlayVideoDelWinShowKick fail:%d\n",ret);

		if(arg.avi_arg.fd >=0)
		{
			if((type & FILELIST_TYPE_MASK) == FILELIST_TYPE_SPI)
			{
				nv_jpg_close();
			}else 
			{
				fs_close(arg.avi_arg.fd);
			}
			
			
			if(playVideoOp.jpgbuf)
			{
				hal_sysMemFree((void*)playVideoOp.jpgbuf);
				playVideoOp.jpgbuf = NULL;
			}
		}
		playVideoOp.winState = 0;
	}else
	{
		if((type & FILELIST_TYPE_MASK) == FILELIST_TYPE_SPI)
		{
			nv_jpg_close();
		}else
		{
			fs_close(arg.avi_arg.fd);
		}
		
		arg.avi_arg.fd = -1;
		playVideoOp.winStepCur = 100;
		playVideoOp.winState = 1;
	}
#endif
	return ret;
}
/*******************************************************************************
* Function Name  : taskPlayVideoDelWinShowProcess
* Description    : taskPlayVideoDelWinShowProcess
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
#define PLAYVIDEOWIN_SHOWSPEED		50 //ms
int taskPlayVideoDelWinShowProcess(void)
{

#if FUN_PLAYBACK_DEL_WIN_SHOW
	//int res;
	if(playVideoOp.winState)
	{
		u16 video_w,video_h;
		u16 video_x, video_y;
		u16 dest_w,dest_h;
		u16 src_w, src_h;
		u16 win_sub_step_w, win_sub_step_h;
		u16 winB_x, winB_y,winB_w, winB_h;
		lcdshow_frame_t* p_lcd_buffer;
		u32 timedelay, timecnt;
		app_draw_Service(1);
#if 0 //按最大video size显示
		hal_lcdGetVideoPos(&x,&y);
		hal_lcdGetVideoResolution(&width,&height);
		hal_lcdGetVideoResolution(&dest_w,&dest_h);
#else //按实际ratio后的size显示
		hal_lcdGetVideoRatioPos(&video_x, &video_y);
		hal_lcdGetVideoRatioResolution(&video_w,&video_h);
		hal_lcdGetVideoRatioDestResolution(&dest_w,&dest_h);
#endif


		hal_mjpDecodeGetResolution(&src_w, &src_h);
		do{
			hal_wdtClear();
			timedelay = XOSTimeGet();
			playVideoOp.winStepCur += playVideoOp.step;
			if(playVideoOp.winStepCur <= 0)
			{
				break;
			}
			win_sub_step_w = (playVideoOp.winStepCur * video_w)/100;
			win_sub_step_h = (playVideoOp.winStepCur * video_h)/100;
			if((win_sub_step_w < (src_w/32)) || (win_sub_step_h < (src_h/32)))
			{
				break;
			}
			switch(playVideoOp.wintype)
			{
				case LCDSHOW_WIN_LEFTTOP:
					winB_x = 0; winB_y = 0;
					winB_w = win_sub_step_w &~7;
					winB_h = win_sub_step_h &~1;
					break;
				case LCDSHOW_WIN_RIGHTTOP:
					winB_x = (video_w - win_sub_step_w)&~7;
					winB_y = 0;
					winB_w = video_w - winB_x; winB_h = (win_sub_step_h)&~1;
					break;
				case LCDSHOW_WIN_LEFTDOWN:
					winB_x = 0;
					winB_y = (video_h - win_sub_step_h)&~1;
					winB_w = video_w - winB_x;
					winB_h = video_h - winB_y;

					break;
				case LCDSHOW_WIN_RIGHTDONW:
					winB_x = (video_w - win_sub_step_w)&~7;
					winB_y = (video_h - win_sub_step_h)&~1;
					winB_w = video_w - winB_x;
					winB_h = video_h - winB_y;

					break;
				case LCDSHOW_WIN_LEFT:
					winB_x = 0; winB_y = 0;
					winB_w = win_sub_step_w &~7;
					winB_h = video_h;
					break;
				case LCDSHOW_WIN_RIGHT:
					winB_x = (video_w - win_sub_step_w)&~7;
					winB_y = 0;
					winB_w = video_w - winB_x;
					winB_h = video_h;
					break;
				case LCDSHOW_WIN_TOP:
					winB_x = 0;
					winB_y = 0;
					winB_w = video_w;
					winB_h = win_sub_step_h&~1;

					break;
				case LCDSHOW_WIN_DOWN:
					winB_x = 0;
					winB_y = (video_h - win_sub_step_h)&~1;
					winB_w = video_w;
					winB_h = video_h - winB_y;
					break;
				default : goto WIN_PROCESS_END;
			}
			do{
				p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
				hal_wdtClear();
			}while(p_lcd_buffer==NULL);
			hal_lcdVideoFrameFlush(p_lcd_buffer,video_x,video_y,video_w,video_h,dest_w,dest_h);
			hal_lcdSetBufYUV(p_lcd_buffer,0,0x80,0x80);
			hx330x_lcdWinABConfig(LCDWIN_B,winB_x,winB_y,winB_w,winB_h, WINAB_EN);

        	hx330x_lcdVideoSetScaleLine(winB_y + winB_h - 1,1);
			hx330x_mjpB_reset();
        	hx330x_mjpB_DecodeODma1Cfg(0, 0, 0);
        	hal_mjpDecodeOneFrame_Fast(playVideoOp.jpgbuf,playVideoOp.jpgbuf + playVideoOp.jpgsize + 64,
                                   		&p_lcd_buffer->y_addr[winB_x + (winB_y *  p_lcd_buffer->stride)],
								   		&p_lcd_buffer->uv_addr[winB_x + (winB_y / 2 * p_lcd_buffer->stride)],
                                        winB_w,winB_h,
                                        p_lcd_buffer->stride);
			hx330x_timerTickStart();
			while(hal_mjpDecodeBusyCheck())
			{
				hal_wdtClear();
				if(hx330x_timerTickCount() >= hardware_setup.sys_clk) //1秒超时
				{
					hx330x_timerTickStop();
					goto WIN_PROCESS_END;
				}
			}
			hx330x_timerTickStop();
			if(hal_mjpDecodeErrorCheck()!=0)
			{
				goto WIN_PROCESS_END;
			}
			//这里加点延时可控制速度
			timecnt = XOSTimeGet() - timedelay;
			if(timecnt < PLAYVIDEOWIN_SHOWSPEED )
			{
				XOSTimeDly(PLAYVIDEOWIN_SHOWSPEED - timecnt);
			}
			hal_lcdVideoSetFrame((void *)p_lcd_buffer);

		}while(1);

WIN_PROCESS_END:
		hal_lcdSetWinEnable(0);
		if(playVideoOp.jpgbuf)
		{
			hal_sysMemFree((void*)playVideoOp.jpgbuf);
			playVideoOp.jpgbuf = NULL;
		}
		playVideoOp.winState = 0;

	}
#endif
	return 0;

}
/*******************************************************************************
* Function Name  : taskRecordWinChangeProcess
* Description    : taskRecordWinChangeProcess function.
* Input          : 
* Output         : none                                            
* Return         : int fd : file handle
*******************************************************************************/
static void taskPlayVideoWinChangeProcess(u8 enter, u32 index)
{
	if(SysCtrl.winChangeEnable == 0)
		return;
	char * name;
	int  type;
	u32  sub_type, sub_id;

	if(SysCtrl.file_cnt < 1)
	{
		sub_id = INVALID_RES_ID;
		goto OUT;
	}
	name = filelist_GetFileFullNameByIndex(playVideoOp.list,index,&type);
	if(name == NULL)
	{
		sub_id = INVALID_RES_ID;
		goto OUT;	
	}
	if((type & FILELIST_TYPE_MASK) == FILELIST_TYPE_SPI)
	{
		sub_type = MEDIA_SRC_NVJPG;
	}else
	{
		sub_type = MEDIA_SRC_FS;
	}
	sub_id = index;
OUT:
	if(enter)
	{
		deg_Printf("sub_type:%d, sub_id:%x\n", sub_type, sub_id);
		taskMainWinInit(0,sub_type, sub_id, 0,MAIN_TO_SUB_VOR_UP);
		taskWinChangeProcess();
	}else
	{
		taskMainWinInit(0,sub_type, sub_id, 0,SUB_TO_MAIN_VOR_DOWN);
	}
}
/*******************************************************************************
* Function Name  : taskPlayVideoMainOpen
* Description    : taskPlayVideoMainOpen
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
static void taskPlayVideoMainOpen(u32 arg)
{
	SysCtrl.play_total_time 	= 0;
	SysCtrl.play_cur_time   	= 0;
	SysCtrl.play_last_time  	= 0;
	playVideoOp.playMode 		= PLAYVIDEO_MAIN;
	playVideoOp.slide.playstat	= PLAYVIDEO_SLIDE_STOP;
	playVideoOp.playErrIndex 	= -1;
	playVideoOp.winState     	= 0;
	playVideoOp.jpgbuf       	= NULL;
	playVideoOp.wintype			= FUN_PLAYBACK_DEL_WIN_TYPE;
	playVideoOp.step			= -FUN_PLAYBACK_DEL_WIN_STEP; //缩放比例调整step , > 0 放大，< 0缩小
	task_com_spijpg_Init(0);
	task_com_sdlist_scan(0, 2);
	//playVideoOp.playTotalTime = 0;
	//playVideoOp.playCurTime   = 0;
	//playVideoOp.playLastTime  = 0;
	if(SysCtrl.spi_jpg_list >= 0)
	{
		deg_Printf("[PLAY SPI PHOTO] count = %d\n",filenode_api_CountGet(SysCtrl.spi_jpg_list));
		playVideoOp.list = SysCtrl.spi_jpg_list;
	}else
	{
		deg_Printf("[PLAY VIDEO] count = %d\n",filenode_api_CountGet(SysCtrl.avi_list));
		//deg_Printf("[PLAY VIDEOB] count = %d\n",filenode_api_CountGet(SysCtrl.avib_list));
		deg_Printf("[PLAY PHOTO] count = %d\n",filenode_api_CountGet(SysCtrl.jpg_list));
		playVideoOp.list = SysCtrl.avi_list;
	}


	app_lcdCsiVideoShowStop();
	hal_csiEnable(0);
	//hal_lcdSetBufYUV(hal_lcdVideoShowFrameGet(),0,0x80,0x80);
	task_com_sound_wait_end(); // wait key sound end
	videoPlaybackInit();
	videoPlaybackSetVolume(100);
	SysCtrl.file_cnt = filelist_api_CountGet(playVideoOp.list);
	if(SysCtrl.file_cnt > 0)
	{
		SysCtrl.file_index = SysCtrl.file_cnt - 1;	
	}
	taskPlayVideoWinChangeProcess(1, SysCtrl.file_index);
	if(SysCtrl.file_cnt > 0)
	{
		taskPlayVideoMainStart(SysCtrl.file_index);
	}
	
	uiOpenWindow(&playVideoMainWindow, 0, 0);

}
/*******************************************************************************
* Function Name  : taskPlayVideoMainService
* Description    : taskPlayVideoMainService
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
static void taskPlayVideoMainService(u32 arg)
{
	if(menuWinIsOpen())
	{
		return;
	}
	if(SysCtrl.file_cnt <= 0)
	{
		return;
	}
	switch(playVideoOp.playMode)
	{
		case PLAYVIDEO_THUMBNALL: 	break;
		case PLAYVIDEO_SLIDE:		taskPlayVideoSlideService(); break;
		case PLAYVIDEO_MAIN:
		if((videoPlaybackGetStatus() == MEDIA_STAT_STOP) && (SysCtrl.file_type & (FILELIST_TYPE_AVI)))
#if TASK_PLAYVIDEO_AUTOPLAY > 0
		{
	#if (TASK_PLAYVIDEO_AUTOPLAY > 1)
			SysCtrl.file_index++;
			if(SysCtrl.file_index >= SysCtrl.file_cnt)
				SysCtrl.file_index = 0;
	#endif
			if(playVideoOp.playErrIndex != SysCtrl.file_index)
				taskPlayVideoMainStart(SysCtrl.file_index);
		}
#else
		{
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_PLAY,MEDIA_STAT_STOP));
		}
	
#endif
		videoPlaybackGetTime(NULL,&SysCtrl.play_cur_time);
		if((SysCtrl.play_last_time/1000 != SysCtrl.play_cur_time/1000)&&(SysCtrl.play_cur_time!=0))
		{
			SysCtrl.play_last_time = SysCtrl.play_cur_time;
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_TIME_UPDATE,0));
		}
		videoPlaybackService();
		break;
	}
}
/*******************************************************************************
* Function Name  : taskPlayVideoMainService
* Description    : taskPlayVideoMainService
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
static void taskPlayVideoMainClose(u32 arg)
{
	task_com_sound_wait_end(); // wait key sound end
	if(videoPlaybackGetStatus() != MEDIA_STAT_STOP)
		videoPlaybackStop();	
	videoPlaybackUninit();
	taskPlayVideoWinChangeProcess(0, SysCtrl.file_index);
	//task_com_spijpg_Init(1);
}



ALIGNED(4) sysTask_T taskPlayVideo =
{
	"Play Video",
	0,
	taskPlayVideoMainOpen,
	taskPlayVideoMainClose,
	taskPlayVideoMainService,
};


