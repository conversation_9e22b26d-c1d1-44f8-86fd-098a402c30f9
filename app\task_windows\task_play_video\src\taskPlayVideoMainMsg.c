/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskPlayVideoMainWin.c"


/*******************************************************************************
* Function Name  : playVideoMainKeyMsgOk
* Description    : playVideoMainKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(SysCtrl.file_cnt > 0 && playVideoOp.playErrIndex != SysCtrl.file_index)
		{
			if(videoPlaybackGetStatus() == MEDIA_STAT_PLAY)
			{
				playVideoMainStatShow(handle, 1);
				videoPlaybackPause();
				
				while(videoPlaybackGetStatus() == MEDIA_STAT_START);
				task_com_keysound_play();
				task_com_sound_wait_end();
			}
			else if(videoPlaybackGetStatus() == MEDIA_STAT_PAUSE /*|| videoPlaybackGetStatus() == MEDIA_STAT_FIRST_PAUSE*/)
			{
				playVideoMainSDShow(handle, 0);
				playVideoMainErrorShow(handle, 0);
				playVideoMainStatShow(handle, 0);	
				playVideoMainPlayTimeShow(handle, 1);
				playVideoMainFileNameShow(handle, 0);
				playVideoMainResolutionShow(handle);
				//if(videoPlaybackGetStatus() == MEDIA_STAT_FIRST_PAUSE)
				//{
				//	playVideoMainSDShow(handle, 0);
				//	playVideoMainFileNameShow(handle,0);
				//	playVideoMainPlayTimeShow(handle,1);
				//		
				//}
				/*task_com_keysound_play();
				task_com_sound_wait_end();*/
				videoPlaybackResume();
			}
			else 
			{
				/*task_com_keysound_play();
				task_com_sound_wait_end();*/
				if(SysCtrl.file_type & FILELIST_TYPE_AVI)
					taskPlayVideoMainStart(SysCtrl.file_index);
			}
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainKeyMsgUp
* Description    : playVideoMainKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(SysCtrl.file_cnt > 0)
		{
			deg_Printf("playVideoMainFileNameShow1111111\n");
			if(videoPlaybackGetStatus() != MEDIA_STAT_STOP)
			{
					deg_Printf("22222222222222222\n");
                videoPlaybackStop();
				playVideoMainStatShow(handle, 0);
				playVideoMainPlayTimeShow(handle, 0);
				playVideoMainFileNameShow(handle, 1);
			}
			task_com_keysound_play();
			task_com_sound_wait_end();
			uiOpenWindow(&delWindow,0,0);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainKeyMsgDown
* Description    : playVideoMainKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(SysCtrl.file_cnt > 0)
		{
			if(videoPlaybackGetStatus() != MEDIA_STAT_STOP)
			{
                videoPlaybackStop();
				playVideoMainStatShow(handle, 0);
				playVideoMainPlayTimeShow(handle, 0);
				playVideoMainFileNameShow(handle, 1);
			}
			task_com_keysound_play();
			task_com_sound_wait_end();
			uiOpenWindow(&delWindow,0,0);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainKeyMsgLeft
* Description    : playVideoMainKeyMsgLeft
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainKeyMsgLeft(winHandle handle, u32 parameNum, u32* parame)
{
    u32 keyState = KEY_STATE_INVALID;
    if (parameNum == 1)
        keyState = parame[0];
    
    if (keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
    {
        if (SysCtrl.file_cnt > 0)
        {
            if (videoPlaybackGetStatus() != MEDIA_STAT_STOP)
            {
                videoPlaybackStop();    
            }
            task_com_keysound_play();
            task_com_sound_wait_end();
            
            #if TASK_PLAYVIDEO_SUB_SET == TASK_PLAYVIDEO_SUB_NONE    
            SysCtrl.file_index--;
            if (SysCtrl.file_index < 0)
                SysCtrl.file_index = SysCtrl.file_cnt - 1;
            
            // 获取文件大小
            char *filename = filelist_GetFileFullNameByIndex(playVideoOp.list, SysCtrl.file_index, &SysCtrl.file_type);
            int fd = fs_open(filename, FA_READ);
            INT32U fileSize = fs_size(fd);
            fs_close(fd);
            
            // 将文件大小从字节转换为KB
            INT32U fileSizeKB = fileSize / 1024;
            INT32U fileSizeMB = fileSizeKB / 1024;
            INT32U remainingKB = fileSizeKB % 1024;
            
            if (fileSizeMB > 0) {
                deg_Printf("[FILE SIZE] File: %s, Size: %u MB %u KB\n", filename, fileSizeMB, remainingKB);
            } else {
                deg_Printf("[FILE SIZE] File: %s, Size: %u KB\n", filename, fileSizeKB);
            }
            
            taskPlayVideoMainStart(SysCtrl.file_index);
            //playVideoMainErrorShow(handle, 0);
            //playVideoMainStatShow(handle, 0);
            //playVideoMainPlayTimeShow(handle, 0);
            //playVideoMainFileNameShow(handle, 1);
            //playVideoMainResolutionShow(handle);
            #elif TASK_PLAYVIDEO_SUB_SET == TASK_PLAYVIDEO_SUB_SLIDE
            uiOpenWindow(&playVideoSlideWindow, 0, 0);
            #elif TASK_PLAYVIDEO_SUB_SET == TASK_PLAYVIDEO_SUB_THUMBNALL
            uiOpenWindow(&playVideoThumbnallWindow, 0, 0);
            #endif
        }
    }
    return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainKeyMsgRight
* Description    : playVideoMainKeyMsgRight
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainKeyMsgRight(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		if(SysCtrl.file_cnt > 0)
		{
			if(videoPlaybackGetStatus() != MEDIA_STAT_STOP)
			{
                videoPlaybackStop();
			}
			task_com_keysound_play();
			task_com_sound_wait_end();
			SysCtrl.file_index++;
			if(SysCtrl.file_index >= SysCtrl.file_cnt)
				SysCtrl.file_index = 0;

			taskPlayVideoMainStart(SysCtrl.file_index);
			//playVideoMainErrorShow(handle, 0);
			//playVideoMainStatShow(handle, 0);
			//playVideoMainPlayTimeShow(handle, 0);
			//playVideoMainFileNameShow(handle, 1);
			//playVideoMainResolutionShow(handle);
		}
	}
	return 0;
}

/*******************************************************************************
* Function Name  : playVideoMainKeyMsgPower
* Description    : playVideoMainKeyMsgPower
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(SysCtrl.file_cnt > 0)
		{
			if(videoPlaybackGetStatus() != MEDIA_STAT_STOP)
			{
                videoPlaybackStop();
				playVideoMainStatShow(handle, 0);
				playVideoMainPlayTimeShow(handle, 0);
				playVideoMainFileNameShow(handle, 1);
			}
		}
		task_com_keysound_play();
		task_com_sound_wait_end();
		SysCtrl.winChangeEnable = 1;
		app_taskStart(TASK_MAIN,0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainSysMsgErr
* Description    : playVideoMainSysMsgErr
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainSysMsgPlay(winHandle handle,u32 parameNum,u32* parame)
{
	u32 playState = MSG_PLAY_MAX;
	if(parameNum == 1)
		playState = parame[0];
	deg_Printf("[WIN]playVideoMainSysMsgPlay : %d\n", playState);
	if(playState == MSG_PLAY_ERROR)
	{
		hal_lcdSetBufYUV(hal_lcdVideoShowFrameGet(),0,0x80,0x80);
		playVideoMainErrorShow(handle, 1);
		playVideoMainStatShow(handle, 0);
		playVideoMainPlayTimeShow(handle, 0);
		playVideoMainFileNameShow(handle, 1);
		playVideoMainResolutionShow(handle);
	}else if(playState == MSG_PLAY_START || playState == MSG_PLAY_STOP)
	{
		playVideoMainSDShow(handle, 1);
		playVideoMainErrorShow(handle, 0);
		if(SysCtrl.file_type & FILELIST_TYPE_AVI)
			playVideoMainStatShow(handle, 1);
		else
			playVideoMainStatShow(handle, 0);
		playVideoMainPlayTimeShow(handle, 0);
		playVideoMainFileNameShow(handle, 1);
		playVideoMainResolutionShow(handle);
	}

	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainSysMsgSD
* Description    : playVideoMainSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[Play Video] : sdc stat ->%d\n",SysCtrl.dev_stat_sdc);
	playVideoMainSDShow(handle, 1);
	if(videoPlaybackGetStatus() == MEDIA_STAT_PLAY)
		videoPlaybackStop();
	SysCtrl.file_cnt = 0;

	task_com_spijpg_Init(0);
	task_com_sdlist_scan(0, 2);
	if(SysCtrl.spi_jpg_list >= 0)
	{
		playVideoOp.list = SysCtrl.spi_jpg_list;
	}else
	{
		playVideoOp.list = SysCtrl.avi_list;
	}
	SysCtrl.file_cnt = filelist_api_CountGet(playVideoOp.list);
	SysCtrl.file_index = SysCtrl.file_cnt - 1;
	if(SysCtrl.file_cnt > 0)
	{
		taskPlayVideoMainStart(SysCtrl.file_index);
	}else
	{
		uiOpenWindow(&noFileWindow, 0, 0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainSysMsgUSB
* Description    : playVideoMainSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{
	playVideoMainBaterryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainSysMsgBattery
* Description    : playVideoMainSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
		playVideoMainBaterryShow(handle);
	return 0;
}

/*******************************************************************************
* Function Name  : playVideoMainSysMsgTimeUpdate
* Description    : playVideoMainSysMsgTimeUpdate
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainSysMsgTimeUpdate(winHandle handle,u32 parameNum,u32* parame)
{
	if(videoPlaybackGetStatus() == MEDIA_STAT_PLAY)
	{
		playVideoMainPlayTimeShow(handle, 1);
	}
	
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainSysMsg1S
* Description    : playVideoMainSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainSysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
	
	//if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
	//{
	//	if(uiWinIsVisible(winItem(handle,PLAYVIDEOMAIN_BATERRY_ID)))
	//		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_BATERRY_ID),0);
	//	else
	//	{
	//		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_BATERRY_ID),1);
	//		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_BATERRY_ID),R_ID_ICON_MTBATTERYCHARGE);
	//	}
	//}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainOpenWin
* Description    : playVideoMainOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]playVideoMainOpenWin\n");
	playVideoOp.playMode = PLAYVIDEO_MAIN;

	task_com_spijpg_Init(0);
	task_com_sdlist_scan(0, 2);
	if(SysCtrl.spi_jpg_list >= 0)
	{
		playVideoOp.list = SysCtrl.spi_jpg_list;
	}else
	{
		playVideoOp.list = SysCtrl.avi_list;
	}
	SysCtrl.file_cnt = filelist_api_CountGet(playVideoOp.list);
	
	SysCtrl.file_index = SysCtrl.file_cnt - 1;
	if(SysCtrl.file_cnt <= 0)
	{
		uiOpenWindow(&noFileWindow, 0, 0);
	}else
	{

		playVideoMainSDShow(handle, 1);
		playVideoMainErrorShow(handle, 0);
		if(SysCtrl.file_type & FILELIST_TYPE_AVI)
		{
			playVideoMainStatShow(handle, 1);
			//playVideoVolumeShow(handle,1);
			//playVideoMainVolumeShow(handle);
			
		}
		else
		{
			playVideoMainStatShow(handle, 0);
		}


		playVideoMainPlayTimeShow(handle, 0);
		playVideoMainFileNameShow(handle, 1);
		playVideoMainResolutionShow(handle);
		playVideoMainBaterryShow(handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainCloseWin
* Description    : playVideoMainCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]playVideoMainCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainWinChildClose
* Description    : playVideoMainWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]playVideoMainWinChildClose\n");
	playVideoOp.playMode = PLAYVIDEO_MAIN;
	//add win show
	taskPlayVideoDelWinShowProcess();
	task_com_spijpg_Init(0);
	task_com_sdlist_scan(0, 2);
	if(SysCtrl.spi_jpg_list >= 0)
	{
		playVideoOp.list = SysCtrl.spi_jpg_list;
	}else
	{
		playVideoOp.list = SysCtrl.avi_list;
	}
	SysCtrl.file_cnt = filelist_api_CountGet(playVideoOp.list);
	SysCtrl.file_index = SysCtrl.file_cnt - 1;
	if(SysCtrl.file_cnt <= 0)
	{
		uiOpenWindow(&noFileWindow, 0, 0);
		return 0;
	}
	else
	{
		taskPlayVideoMainStart(SysCtrl.file_index);
		//playVideoMainSDShow(handle, 1);
		playVideoMainBaterryShow(handle);
		//playVideoMainErrorShow(handle, 0);
		//playVideoMainStatShow(handle, 0);
		//playVideoMainPlayTimeShow(handle, 0);
		//playVideoMainFileNameShow(handle, 1);
		//playVideoMainResolutionShow(handle);
	}
	return 0;
}


ALIGNED(4) msgDealInfor playVideoMainMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		playVideoMainOpenWin},
	{SYS_CLOSE_WINDOW,		playVideoMainCloseWin},
	{SYS_CHILE_COLSE,		playVideoMainWinChildClose},
	{KEY_EVENT_OK,			playVideoMainKeyMsgOk},
	{KEY_EVENT_UP,			playVideoMainKeyMsgUp},
	{KEY_EVENT_DOWN,		playVideoMainKeyMsgDown},
	{KEY_EVENT_LEFT,		playVideoMainKeyMsgLeft},
	{KEY_EVENT_RIGHT,		playVideoMainKeyMsgRight},
	{KEY_EVENT_POWER,		playVideoMainKeyMsgPower},

	{SYS_EVENT_PLAY,		playVideoMainSysMsgPlay},
	{SYS_EVENT_SDC,			playVideoMainSysMsgSD},
	{SYS_EVENT_USBDEV,		playVideoMainSysMsgUSB},
	{SYS_EVENT_BAT,			playVideoMainSysMsgBattery},
	{SYS_EVENT_1S,			playVideoMainSysMsg1S},
	{SYS_EVENT_TIME_UPDATE,	playVideoMainSysMsgTimeUpdate},
	{EVENT_MAX,NULL},
};

WINDOW(playVideoMainWindow,playVideoMainMsgDeal,playVideoMainWin)


