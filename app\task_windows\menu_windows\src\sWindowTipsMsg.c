/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sWindowTipsWin.c"

ALIGNED(4) static u32 tipResId;
ALIGNED(4) static u32 continueTime = 0xffffffff;

/*******************************************************************************
* Function Name  : tipsKeyMsgAll
* Description    : tipsKeyMsgAll
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tipsKeyMsgAll(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//if( tipResId == (u32)TIPS_USENSOR_POWER_LOW||
		//		tipResId == (u32)TIPS_POWER_LOW||
		//		tipResId == (u32)TIPS_NO_POWER||
		//		tipResId == (u32)TIPS_NO_DC_POWEROFF)
		{
			uiWinDestroy(&handle);
		}
	}

	return 0;
}
/*******************************************************************************
* Function Name  : tipsKeyMsgPower
* Description    : tipsKeyMsgPower
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tipsKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : tipsSysMsgSD
* Description    : tipsSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tipsSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	if(tipResId == TIPS_NO_DC_POWEROFF)
		return 0;
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL)
	{
		if( tipResId == (u32)TIPS_SD_NOT_INSERT||
			tipResId == (u32)TIPS_SD_FULL||
			tipResId == (u32)TIPS_SD_ERROR||
			tipResId == (u32)TIPS_SPI_FULL)
		{
			uiWinDestroy(&handle);
			return 0;
		}
	}
	uiParentDealMsg(handle,SYS_EVENT_SDC);
	return 0;
}
/*******************************************************************************
* Function Name  : tipsSysMsgUSB
* Description    : tipsSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tipsSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
	{
		if( tipResId == (u32)TIPS_USENSOR_POWER_LOW||
			tipResId == (u32)TIPS_POWER_LOW||
			tipResId == (u32)TIPS_NO_POWER||
			tipResId == (u32)TIPS_NO_DC_POWEROFF)
		{
			uiWinDestroy(&handle);
			return 0;
		}
			
	}
	uiParentDealMsg(handle,SYS_EVENT_USBDEV);
	return 0;
}
/*******************************************************************************
* Function Name  : tipsSysMsgBattery
* Description    : tipsSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tipsSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{
	if(tipResId == (u32)TIPS_NO_DC_POWEROFF)
		return 0;
	if(SysCtrl.dev_stat_battery == BATTERY_STAT_1)
	{
		tipResId = (u32)TIPS_POWER_LOW;
		uiWinSetResid(winItem(handle,TIP_STRING_ID),tipResId);
	}
	else if(SysCtrl.dev_stat_battery == BATTERY_STAT_0)
	{
		tipResId = (u32)TIPS_NO_POWER;
		uiWinSetResid(winItem(handle,TIP_STRING_ID),tipResId);
	}
	uiParentDealMsg(handle,SYS_EVENT_BAT);
	return 0;
}
/*******************************************************************************
* Function Name  : tipsSysMsgMd
* Description    : tipsSysMsgMd
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tipsSysMsgMd(winHandle handle,u32 parameNum,u32* parame)
{
	uiParentDealMsg(handle,SYS_EVENT_MD);
	return 0;
}
/*******************************************************************************
* Function Name  : tipsSysMsg500Ms
* Description    : tipsSysMsg500Ms
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tipsSysMsg500Ms(winHandle handle,u32 parameNum,u32* parame)
{
	uiParentDealMsg(handle,SYS_EVENT_500MS);
	return 0;
}
/*******************************************************************************
* Function Name  : tipsSysMsg1S
* Description    : tipsSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tipsSysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
	uiParentDealMsg(handle,SYS_EVENT_1S);
	if(continueTime)
		continueTime--;
	if(continueTime == 0)
		uiWinDestroy(&handle);
	return 0;
}
/*******************************************************************************
* Function Name  : tipsSysMsg1S
* Description    : tipsSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tipsSysMsgTimeUpdate(winHandle handle,u32 parameNum,u32* parame)
{
	uiParentDealMsg(handle,SYS_EVENT_TIME_UPDATE);
	return 0;
}
/*******************************************************************************
* Function Name  : tipsSysMsg500Ms
* Description    : tipsSysMsg500Ms
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tipsSysMsgRecord(winHandle handle,u32 parameNum,u32* parame)
{
	uiParentDealMsg(handle,SYS_EVENT_RECORD);
	return 0;
}
/*******************************************************************************
* Function Name  : tipsSysMsg500Ms
* Description    : tipsSysMsg500Ms
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tipsSysMsgPlay(winHandle handle,u32 parameNum,u32* parame)
{
	uiParentDealMsg(handle,SYS_EVENT_PLAY);
	return 0;
}
/*******************************************************************************
* Function Name  : tipsOpenWin
* Description    : tipsOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tipsOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum < 2)
		return 0;
	tipResId 		= parame[0];
	continueTime	= parame[1];
	uiWinSetResid(winItem(handle,TIP_STRING_ID),tipResId);
	deg_Printf("[WIN]tipsOpenWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : tipsCloseWin
* Description    : tipsCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tipsCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	//tipResId = INVALID_RES_ID;
	deg_Printf("[WIN]tipsCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : tipsWinChildClose
* Description    : tipsWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tipsWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]tipsWinChildClose\n");
	return 0;
}


ALIGNED(4) msgDealInfor tipsMsgDeal[]=
{
	{SYS_OPEN_WINDOW,			tipsOpenWin},
	{SYS_CLOSE_WINDOW,			tipsCloseWin},
	{SYS_CHILE_COLSE,			tipsWinChildClose},
	{KEY_EVENT_OK,				tipsKeyMsgAll},
	{KEY_EVENT_UP,				tipsKeyMsgAll},
	{KEY_EVENT_DOWN,			tipsKeyMsgAll},
	{KEY_EVENT_LEFT,			tipsKeyMsgAll},
	{KEY_EVENT_RIGHT,			tipsKeyMsgAll},
	{KEY_EVENT_POWER,			tipsKeyMsgPower},
	{SYS_EVENT_SDC,				tipsSysMsgSD},
	{SYS_EVENT_USBDEV,			tipsSysMsgUSB},
	{SYS_EVENT_BAT,				tipsSysMsgBattery},
	{SYS_EVENT_MD,				tipsSysMsgMd},
	{SYS_EVENT_500MS,			tipsSysMsg500Ms},
	{SYS_EVENT_1S,				tipsSysMsg1S},
	{SYS_EVENT_TIME_UPDATE,		tipsSysMsgTimeUpdate},
	{SYS_EVENT_RECORD,			tipsSysMsgRecord},
	{SYS_EVENT_PLAY,			tipsSysMsgPlay},
	{EVENT_MAX,NULL},
};

MULTIWIN(tipsWindow,tipsMsgDeal,tipsWin)



