/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuDateTimeWin.c"

#define DATE_TIME_ITEM_MAX      8
#define DATE_TIME_STR_LEN       8

static char dateTimeStr[6][DATE_TIME_STR_LEN];
static u32  dateTimeData[6];


/*******************************************************************************
* Function Name  : dateTimeStrUpdate
* Description    : dateTimeStrUpdate
* Input          : u32 num
* Output         : none
* Return         : none
*******************************************************************************/
static void dateTimeStrUpdate(u32 num)
{
	if(num<6)
	{
		hx330x_num2str_cnt(dateTimeStr[num], dateTimeData[num], DATE_TIME_STR_LEN);
	}
}
/*******************************************************************************
* Function Name  : dateTimeIncreace
* Description    : dateTimeIncreace
* Input          : u32 num
* Output         : none
* Return         : none
*******************************************************************************/
static void dateTimeIncreace(u32 num)
{
	u8 days_max;
	if(num >= 6)
		return;
	dateTimeData[num] += 1;
	switch(num)
	{
		case 0:
			if(dateTimeData[num] > 2100)
				dateTimeData[num] = 1980;
			break;
		case 1:
			if(dateTimeData[num] > 12)
				dateTimeData[num] = 1;
			break;
		case 2:
			days_max = monDaysTable[dateTimeData[1]-1];
			if(dateTimeData[1] == 2)
				days_max += hal_rtcLeapYear(dateTimeData[0]);
			if(dateTimeData[num] > days_max)
				dateTimeData[num] = 1;
			break;
		case 3:
			if(dateTimeData[num] > 23)
				dateTimeData[num] = 0;
			break;
		case 4:
		case 5:
			if(dateTimeData[num] > 59)
				dateTimeData[num] = 0;	
			break;
	}
	dateTimeStrUpdate(num);
}
/*******************************************************************************
* Function Name  : dateTimeDecreace
* Description    : dateTimeDecreace
* Input          : u32 num
* Output         : none
* Return         : none
*******************************************************************************/
static void dateTimeDecreace(u32 num)
{
	u8 days_max;
	if(num>=6)
		return;
	switch(num)
	{
		case 0:
			if(dateTimeData[num] < 1980)
				dateTimeData[num] = 2100 + 1;
			break;
		case 1:
			if(dateTimeData[num] == 1)
				dateTimeData[num] = 12 + 1;
			break;
		case 2:
			days_max = monDaysTable[dateTimeData[1]-1];
			if(dateTimeData[1] == 2)
				days_max += hal_rtcLeapYear(dateTimeData[0]);
			if(dateTimeData[num] == 1)
				dateTimeData[num] = days_max + 1;
			break;
		case 3:
			if(dateTimeData[num] == 0)
				dateTimeData[num] = 23 + 1;
			break;
		case 4:
		case 5:
			if(dateTimeData[num] == 0)
				dateTimeData[num] = 59 + 1;	
			break;
	}
	dateTimeData[num] -= 1;
	dateTimeStrUpdate(num);
}
/*******************************************************************************
* Function Name  : getDateTimeResInfor
* Description    : getDateTimeResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : none
*******************************************************************************/
static u32 getDateTimeResInfor(u32 item,u32* image,u32* str)
{
	if(item<6)
	{
		if(image)
			*image=INVALID_RES_ID;
		if(str)
			*str=(uint32)(&dateTimeStr[item]);
	}
	else if(item==6)
	{
		if(image)
			*image=INVALID_RES_ID;
		if(str)
			*str=R_ID_STR_COM_OK;
	}
	else if(item==7)
	{
		if(image)
			*image=INVALID_RES_ID;
		if(str)
			*str=R_ID_STR_COM_CANCEL;
	}
	else
	{
		if(image)
			*image=INVALID_RES_ID;
		if(str)
			*str=INVALID_RES_ID;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : dateTimeKeyMsgOk
* Description    : dateTimeKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int dateTimeKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	DATE_TIME_T rtcTime;
	if(parameNum == 1)
		keyState=parame[0];
	if(keyState == KEY_PRESSED)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,DATETIME_SELECT_ID));
		if(item < 6)
			uiItemManageNextItem(winItem(handle,DATETIME_SELECT_ID));
		else if(item == 6)
		{
			uiWinDestroy(&handle);
			rtcTime.year 	= dateTimeData[0];
			rtcTime.month 	= dateTimeData[1];
			rtcTime.day 	= dateTimeData[2];
			rtcTime.hour 	= dateTimeData[3];
			rtcTime.min 	= dateTimeData[4];
			rtcTime.sec 	= dateTimeData[5];		
			hal_rtcTimeSet(&rtcTime);
			uiWinDestroy(&handle);
		}
		else if(item == 7)
			uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : dateTimeKeyMsgUp
* Description    : dateTimeKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int dateTimeKeyMsgUp(winHandle handle,uint32 parameNum,uint32* parame)
{
	uint32 keyState = KEY_STATE_INVALID;
	uint32 item;
	if(parameNum == 1)
		keyState=parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,DATETIME_SELECT_ID));
		if(item<6)
		{
			dateTimeIncreace(item);
			uiItemManageUpdateCurItem(winItem(handle,DATETIME_SELECT_ID));
		}
		else
			uiItemManagePreItem(winItem(handle,DATETIME_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : dateTimeKeyMsgDown
* Description    : dateTimeKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int dateTimeKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,DATETIME_SELECT_ID));
		if(item < 6)
		{
			dateTimeDecreace(item);
			uiItemManageUpdateCurItem(winItem(handle,DATETIME_SELECT_ID));
		}
		else
		  uiItemManageNextItem(winItem(handle,DATETIME_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : dateTimeKeyMsgMenu
* Description    : dateTimeKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int dateTimeKeyMsgLeft(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiItemManagePreItem(winItem(handle,DATETIME_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : dateTimeKeyMsgMode
* Description    : dateTimeKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int dateTimeKeyMsgRight(winHandle handle,u32 parameNum,u32* parame)
{
	uint32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiItemManageNextItem(winItem(handle,DATETIME_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : dateTimeKeyMsgMode
* Description    : dateTimeKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int dateTimeKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	uint32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : dateTimeOpenWin
* Description    : dateTimeOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int dateTimeOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	DATE_TIME_T *rtcTime;
	deg_Printf("[WIN]dateTimeOpenWin\n");
	uiItemManageSetRowSum(winItem(handle,DATETIME_SELECT_ID),3,Rh(35));

	uiItemManageSetColumnSumWithGap(winItem(handle,DATETIME_SELECT_ID),0,3,Rw(60),Rw(3));
	uiItemManageSetColumnSumWithGap(winItem(handle,DATETIME_SELECT_ID),1,3,Rw(60),Rw(3));
	uiItemManageSetColumnSumWithGap(winItem(handle,DATETIME_SELECT_ID),2,2,Rw(100),Rw(0));

	uiItemManageCreateItem(		winItem(handle,DATETIME_SELECT_ID),uiItemCreateMenuOption,getDateTimeResInfor,DATE_TIME_ITEM_MAX);

#if 0
	uiItemManageSetCharInfor(	winItem(handle,DATETIME_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_White);
	uiItemManageSetSelectColor(	winItem(handle,DATETIME_SELECT_ID),R_ID_PALETTE_DoderBlue);
	uiItemManageSetUnselectColor(winItem(handle,DATETIME_SELECT_ID),R_ID_PALETTE_Gray);
#else
	uiItemManageSetSelectColorEx(winItem(handle,DATETIME_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER, SMENU_SELECT_FN_COLOR, SMENU_SELECT_BG_COLOR);
	uiItemManageSetUnselectColorEx(winItem(handle,DATETIME_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER, SMENU_UNSELECT_FN_COLOR, SMENU_UNSELECT_BG_COLOR);
#endif


	rtcTime = hal_rtcTimeGet();
	dateTimeData[0] = rtcTime->year;
	dateTimeData[1] = rtcTime->month;
	dateTimeData[2] = rtcTime->day;
	dateTimeData[3] = rtcTime->hour;
	dateTimeData[4] = rtcTime->min;
	dateTimeData[5] = rtcTime->sec;
	dateTimeStrUpdate(0);
	dateTimeStrUpdate(1);
	dateTimeStrUpdate(2);
	dateTimeStrUpdate(3);
	dateTimeStrUpdate(4);
	dateTimeStrUpdate(5);
	uiItemManageSetCurItem(winItem(handle,DATETIME_SELECT_ID),0);
	
	return 0;
}
/*******************************************************************************
* Function Name  : dateTimeCloseWin
* Description    : dateTimeCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int dateTimeCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]dateTimeCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : dateTimeWinChildClose
* Description    : dateTimeWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int dateTimeWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]dateTimeWinChildClose\n");
	return 0;
}


ALIGNED(4) msgDealInfor dateTimeMsgDeal[] =
{
	{SYS_OPEN_WINDOW,	dateTimeOpenWin},
	{SYS_CLOSE_WINDOW,	dateTimeCloseWin},
	{SYS_CHILE_COLSE,	dateTimeWinChildClose},
	{KEY_EVENT_OK,		dateTimeKeyMsgOk},
	{KEY_EVENT_UP,		dateTimeKeyMsgUp},
	{KEY_EVENT_DOWN,	dateTimeKeyMsgDown},
	{KEY_EVENT_LEFT,	dateTimeKeyMsgLeft},
	{KEY_EVENT_RIGHT,	dateTimeKeyMsgRight},
	{KEY_EVENT_POWER,	dateTimeKeyMsgPower},
	{EVENT_MAX,NULL},
};

WINDOW(dateTimeWindow,dateTimeMsgDeal,dateTimeWin)


