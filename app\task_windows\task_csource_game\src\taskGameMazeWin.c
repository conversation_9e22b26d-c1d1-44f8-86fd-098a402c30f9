/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	MAZE_MODE_ID=0,
	MAZE_BG1_ID,
	MAZE_BG2_ID,
	MAZE_BG3_ID,
	MAZE_BG4_ID,
	MAZE_TIPS_ID,
	MAZE_GRADE_ID,
	MAZE_LEVEL_ID,
	MAZE_LEVEL_NUM_ID,
	MAZE_NEXT_STR_ID,
	MAZE_NEXT_ICO_ID,
	MAZE_BATERRY_ID,
	
	MAZE_TIPS_RECT_ID1,
	MAZE_TIPS_RECT_ID2,
	MAZE_TIPS_STR_TIP,
	
	MAZE_MAX_ID
};
typedef struct{
	INT16U w;
	INT16U h;
	INT8U *ptr;
}ICON_INFOR;

enum {
	GAME_PASS_TIPS =1 ,
	GAME_MAZE_ICON_MAX
};
ICON_INFOR  gamemaze_buff[GAME_MAZE_ICON_MAX]={
    {GAME_MAZE_ICON_MAX,GAME_MAZE_ICON_MAX,NULL},
	{220,80,NULL},
};
UNUSED ALIGNED(4) const widgetCreateInfor mazeWin[] =
{
	createFrameWin(                     Rx(0),  Ry(0),   Rw(320), Rh(240),  R_ID_PALETTE_Gray,WIN_ABS_POS),

	//createRect(MAZE_TIPS_RECT_ID1,              Rx((320-104)/2),Ry((240-56)/2), Rw(104),Rh(56),R_ID_PALETTE_Yellow),
	//createRect(MAZE_TIPS_RECT_ID2,              Rx((320-96)/2),Ry((240-48)/2), Rw(96),Rh(48),R_ID_PALETTE_Gray),
	//createStringIcon(MAZE_TIPS_STR_TIP, Rx(0),Ry(105),Rw(320),Rh(30),R_ID_STR_COM_FAILED,ALIGNMENT_CENTER, R_ID_PALETTE_White,0),
	
	widgetEnd(),
};
