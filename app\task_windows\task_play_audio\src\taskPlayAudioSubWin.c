/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	PLAYAUDIOSUB_MODE_ID   = 0,
	PLAYAUDIOSUB_SD_ID,
	PLAYAUDIOSUB_SDVOL_ID,
	PLAYAUDIOSUB_VOLUME_ID,
	PLAYAUDIOSUB_VOLUME_VALUE_ID,
	PLAYAUDIOSUB_BATERRY_ID,

	PLAYAUDIOSUB_NAME_ID,
	PLAYAUDIOSUB_TIME_ID,
	PLAYAUDIOSUB_INDEX_ID,
	PLAYAUDIOSUB_BAR_ID
};

UNUSED ALIGNED(4) const widgetCreateInfor playAudioSubWin[] =
{
	createFrameWin( 								Rx(0),   Ry(0),   Rw(320), Rh(240), R_ID_PALETTE_Transparent,WIN_ABS_POS),
	createImageIcon(PLAYAUDIOSUB_MODE_ID,      		Rx(0),   Ry(0),   Rw(40),  Rh(40),  R_ID_ICON_MTMUSIC, 		ALIGNMENT_CENTER),
	createImageIcon(PLAYAUDIOSUB_SD_ID,        		Rx(40),  Ry(0),   Rw(40),  Rh(40),  R_ID_ICON_MTSDCNORMAL,	ALIGNMENT_CENTER),
	createStringIcon(PLAYAUDIOSUB_SDVOL_ID,			Rx(40),  Ry(0),   Rw(40),  Rh(40),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_Yellow,	DEFAULT_FONT),
	createImageIcon(PLAYAUDIOSUB_VOLUME_ID,    		Rx(80),  Ry(0),   Rw(40),  Rh(40),  R_ID_ICON_MENUVOLUME,	ALIGNMENT_CENTER),
	createStringIcon(PLAYAUDIOSUB_VOLUME_VALUE_ID,	Rx(120), Ry(0),   Rw(40),  Rh(40),	RAM_ID_MAKE(" "),	    ALIGNMENT_LEFT, 	R_ID_PALETTE_White,DEFAULT_FONT),
	createImageIcon(PLAYAUDIOSUB_BATERRY_ID,   		Rx(280), Ry(0),   Rw(40),  Rh(40), 	R_ID_ICON_MTBATTERY3,	ALIGNMENT_RIGHT),

	createStringIcon(PLAYAUDIOSUB_NAME_ID,			Rx(10),  Ry(40),  Rw(240), Rh(40),	RAM_ID_MAKE(" "),	    ALIGNMENT_LEFT, 	R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(PLAYAUDIOSUB_TIME_ID,			Rx(40),  Ry(200), Rw(120), Rh(30),	RAM_ID_MAKE(" "),	    ALIGNMENT_LEFT, 	R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(PLAYAUDIOSUB_INDEX_ID,			Rx(160), Ry(200), Rw(120), Rh(30),	RAM_ID_MAKE(" "),	    ALIGNMENT_RIGHT, 	R_ID_PALETTE_White,DEFAULT_FONT),
	createProgressBar(PLAYAUDIOSUB_BAR_ID,     		Rx(40),  Ry(230), Rw(240), Rh(5),	R_ID_PALETTE_DarkGray,	R_ID_PALETTE_Yellow, ALIGNMENT_LEFT),
};
/*******************************************************************************
* Function Name  : playAudioSubVolumeShow
* Description    : playAudioSubVolumeShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playAudioSubVolumeShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,PLAYAUDIOSUB_VOLUME_VALUE_ID),RAM_ID_MAKE(task_com_curVolume_strid()));
}
/*******************************************************************************
* Function Name  : playAudioSubFileNameShow
* Description    : playAudioSubFileNameShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playAudioSubFileNameShow(winHandle handle)
{
	char* name = filelist_GetFileFullNameByIndex(SysCtrl.wav_list,SysCtrl.file_index,NULL);
	if(name)
		uiWinSetResid(winItem(handle,PLAYAUDIOSUB_NAME_ID),RAM_ID_MAKE(name));
	else
		uiWinSetResid(winItem(handle,PLAYAUDIOSUB_NAME_ID),RAM_ID_MAKE(" "));
	uiWinSetResid(winItem(handle,PLAYAUDIOSUB_INDEX_ID),RAM_ID_MAKE(task_com_fileIndex_str()));
}
/*******************************************************************************
* Function Name  : playAudioSubTimeShow
* Description    : playAudioSubTimeShow
* Input          : winHandle handle,u32 playTime,u32 totalTime
* Output         : none
* Return         : none
*******************************************************************************/
static void playAudioSubTimeShow(winHandle handle)
{
    uiWinSetResid(winItem(handle,PLAYAUDIOSUB_TIME_ID),RAM_ID_MAKE(task_com_play_time_str(1)));
	uiWinSetPorgressRate(winItem(handle,PLAYAUDIOSUB_BAR_ID),(SysCtrl.play_cur_time * 100)/SysCtrl.play_total_time);
}
/*******************************************************************************
* Function Name  : playAudioSubSDShow
* Description    : playAudioSubSDShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playAudioSubSDShow(winHandle handle)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
	{
		uiWinSetResid(winItem(handle,PLAYAUDIOSUB_SD_ID),R_ID_ICON_MTSDCNORMAL);
		uiWinSetVisible(winItem(handle,PLAYAUDIOSUB_SDVOL_ID),1);
		uiWinSetResid(winItem(handle,PLAYAUDIOSUB_SDVOL_ID),RAM_ID_MAKE(task_com_sdcCap_str()));
		
	}	
	else{
		uiWinSetResid(winItem(handle,PLAYAUDIOSUB_SD_ID),R_ID_ICON_MTSDCNULL);
		uiWinSetVisible(winItem(handle,PLAYAUDIOSUB_SDVOL_ID),0);
	}			
}
/*******************************************************************************
* Function Name  : playAudioSubBaterryShow
* Description    : playAudioSubBaterryShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playAudioSubBaterryShow(winHandle handle)
{
	uiWinSetVisible(winItem(handle,PLAYAUDIOSUB_BATERRY_ID),1);
	uiWinSetResid(winItem(handle,PLAYAUDIOSUB_BATERRY_ID),task_com_battery_res_get());	
}
