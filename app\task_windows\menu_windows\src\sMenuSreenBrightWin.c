/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	SREEN_B_RECT_ID = 0,
	SREEN_B_SELECT_ID
};

UNUSED ALIGNED(4) const widgetCreateInfor sreenBrightWin[] =
{
	createFrameWin(						Rx(58),	Ry(58), Rw(204),Rh(124),SMENU_FRAME_COLOR,WIN_ABS_POS),
	createRect(SREEN_B_RECT_ID,         Rx(2),	<PERSON><PERSON>(2),  <PERSON>w(200),<PERSON>h(120),SMENU_UNSELECT_BG_COLOR),
	createItemManage(SREEN_B_SELECT_ID,	Rx(10),	Ry(2),  Rw(184),Rh(120),SMENU_UNSELECT_BG_COLOR),
	widgetEnd(),
};

