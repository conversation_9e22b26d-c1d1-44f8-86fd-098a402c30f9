/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskGame2048Win.c"

#define  N   4
int grid[N][N]={0};  //4x4閺嶇厧鐡欓敓锟�?
//int D=0;		//閹稿绗呴幐澶愭暛閻ㄥ嫬绨敓锟�?
int M=2048;//2048

static INT8U curx,cury;
//INT8U g_liu=0; //閹侯煉鎷�?閿熻棄鐡欓崗鍐插幢

//static INT8U game_continue;
// static INT8U game_Level;
static u8* g2048_temp_icons = NULL;
static u8* g2048_buffer_string = NULL;

extern INT8U game_over_in;

extern u8 *game_frame_buff;

extern u16 game_frame_w;
extern u16 game_frame_h;

#if 1//defined(  ICON_SAVE_DEBUG)
	//---------------------------------game--------------------------------------

#endif

//闂呭繑婧€閺佹澘鐡�
void randomdata()
{
	int r,c,x;
	x = (rand()%2) *2 + 2;
	do
	{
		r = rand()%N;
	    c = rand()%N;
	}while(grid[r][c]!=0);
	grid[r][c]=x;
	deg_Printf("x:%d,y:%d,value:%d\n",r,c,x);

}
void zeroDataArray(void)
{
    int i,j;
	for(i=0;i<N;i++)
	for(j=0;j<N;j++)
		grid[i][j] = 0;
}

void Init2048Game(void)
{
	INT16U tft_w,tft_h;
	game_over_in=0;
	//hal_lcdSetBufYUV(hal_lcdVideoShowFrameGet(),0x80,0x80,0x80);
  // 	 hal_lcdGetVideoResolution(&tft_w,&tft_h);
//	 deg_Printf("Init2048Game222222,tft_w[%d],tft_h[%d]\n",tft_w,tft_h);
	//g_liu= 1;//ap_state_config_2048_get();
	memset(game_frame_buff,R_ID_PALETTE_Black,game_frame_w*game_frame_h);
	//app_draw_Service(1);
	SysCtrl.gui_flush_sta = 0;
	zeroDataArray();
	randomdata();//闂呭繑婧€閿燂拷?娑撯偓閿燂拷?閿燂拷?
//	grid[0][0]=2;

	g2048_draw_map(game_frame_buff);
	memcpy(ui_draw_ctrl.bufStart,game_frame_buff,game_frame_w*game_frame_h);
	app_draw_Service(1);
}

//void pass(u8 * buf_show,u32 line)
//{
//	INT8U i,j;
//	game_continue=1;
//    deg_Printf("pass---->%d\r\n",line);
//
//	for(i=0;i<12;i++){
//		if(game_continue==0)
//			break;
//		for(j=0;j<12;j++){
//			if(level[g_liu][j][i]==3||level[g_liu][j][i]==7){
//				if(level_temp[i][j]==7){
//					game_continue=1;
//				}else{
//					game_continue=0;
//					break;
//				}
//			}
//		}
//	}
//	if(game_continue==1){
//		if(g_liu < 9){
//			g_liu += 1;
//			game_Level=0;
//			//show_game_state(0);
//		}else{
//			g_liu=0;
//			game_Level=1;
//			//show_game_state(1);
//		}
//        game_over_in=1;
//        //ap_state_config_2048_set(g_liu);
//        //2048();
//
//        //show_game_state(val);
//        //XOSTimeDly(80);
//
//        //2048_draw_map(buf_show);
//
//        //LCD_WriteNumlen(126,280,g+1,2,RED,BLACK,1);
//        //LCD_ShowxNum1(111,304,g_liu+1,2,16,0,rgb(20,106,222),RED);
//	}
//}

static void game_over(winHandle handle,INT8U* display_buf)
{
	ENTER();
	//INT32U	buf_show= setting_frame_show->base + setting_frame_show->offset;
	//uiWinSetResid(winItem(handle,G2048_TIPS_STR_TIP),"game over");
	//game_draw_str( display_buf,STR_60HZ);
	game_over_in=1;
	//uiWinDestroy(&handle);
	//msgQSend(ApQ, MSG_APQ_MODE, NULL, 0, MSG_PRI_NORMAL);
	//g2048_draw_map(display_buf);
}

//void image_darwbg(INT8U *buf_show,INT8U* icon_stream,INT8U cow,INT8U col,INT16U w,INT16U h)
//{
//#if 1
//	DISPLAY_ICONSHOW	icon;
//	INT16U tft_w = game_frame_w;
//	INT16U tft_h = game_frame_h;
//	INT16U x,y;
//	x =w*cow+w;
//	y= h*col+h;
//
//	if((x>=tft_w)||(y>=tft_h)){
//		return;
//	}
//
//	if(g_liu==0){
//		x+=w;
//    }else if(g_liu<7){
//		x+=w*3;
//	}
//	#if 0
//	icon.transparent=0x87c1;
//	icon.icon_w=w;
//	icon.icon_h=h;
//	icon.pos_x=x;
//	icon.pos_y=y;
//	ap_setting_special_icon_draw(buf_show, icon_stream, &icon);
//	//OSQPost(DisplayTaskQ, (void *) (MSG_DISPLAY_TASK_MJPEG_DRAW | (INT32U)display_frame));
//	#else
//	OSDFrameDrawIcon(buf_show,icon_stream,x,y,w,h);
//	#endif
//#else
//	OSDFrameDrawIcon(buf_show,icon_stream,cow,col,w,h);
//#endif
//}

#define  BASE_X   40//30//32
#define  BASE_Y   0//4//8//8//32

static void image_draw_picture(INT8U *buf_show,INT8U* icon_stream,INT8U cow,INT8U col,INT16U w,INT16U h)
{
#if 1
	INT16U x,y;
	INT16U tft_w = game_frame_w;
	INT16U tft_h = game_frame_h;

    x =w*cow+BASE_X;
    y= h*col+BASE_Y;
    if((x>=tft_w)||(y>=tft_h)){
        return;
    }
//	if(g_liu==0){
//		x+=w;
//	}else if(g_liu<7){
//		x+=w*3;
//	}
	//deg_Printf("x:%d  y:%d\r\n",x,y);
	#if 0
	DISPLAY_ICONSHOW	icon;
	icon.transparent=0x87c1;
	icon.icon_w=w;
	icon.icon_h=h;
	icon.pos_x=x;
	icon.pos_y=y;
	ap_display_icon_draw(buf_show, icon_stream, &icon);
	//ap_setting_icon_draw(buf_show,icon_stream, &icon, SETTING_ICON_NORMAL_DRAW);
	#else
	OSDFrameDrawIcon(buf_show,icon_stream,x,y,w,h);
	#endif
#else
	OSDFrameDrawIcon(buf_show,icon_stream,cow,col,w,h);
#endif
}



int getIconIndex(int number)
{
	switch(number){
		case 0:
			return GAME_NUM_NULL;
		case 2:
			return GAME_NUM_2;
		case 4: 	/*wall-1*/
			return GAME_NUM_4;
		case 8: 	/*wall-1*/
			return GAME_NUM_8;
		case 16: 	/*wall-1*/
			return GAME_NUM_16;
		case 32:	/*wall-1*/
			return GAME_NUM_32;
		case 64:	/*wall-1*/
			return GAME_NUM_64;

		case 128:	/*wall-1*/
			return GAME_NUM_128;
		case 256:	/*wall-1*/
			return GAME_NUM_256;
		case 512:	/*wall-1*/
			return GAME_NUM_512;
		case 1024:	/*wall-1*/
			return GAME_NUM_1024;
		case 2048:	/*wall-1*/
			return GAME_NUM_2048;
	}
}

void g2048_draw_map(INT8U * buf_show)
{
    INT8U i,j,iconIndex;
    INT16U tft_w = game_frame_w;
    INT16U tft_h = game_frame_h;
//    curx=0;
//    cury=0;
    deg_Printf("g2048_draw_map\r\n");
	//0 缁屽搫婀� 1 ??2閼冲本娅� 3 閿燂拷?閿燂拷???4 缁犲崬鐡� 5 ??
	g_Draw_Fill(buf_show,BASE_X,BASE_Y,BASE_X+60*4,BASE_Y+60*4-1,R_ID_PALETTE_DimGray);//R_ID_PALETTE_Transparent R_ID_PALETTE_DoderBlue//BASE_Y+42*4

	for(i=0;i<N;i++)
	{
		for(j=0;j<N;j++)
		{
			iconIndex = getIconIndex(grid[i][j]);

			deg_Printf("i:%d,j:%d,iconIndex:%d\n",i,j,iconIndex);
			deg_Printf("game2048_buff[x].ptr:%x\n",game2048_buff[iconIndex].ptr);
			if(grid[i][j]>0)
		    image_draw_picture(buf_show,game2048_buff[iconIndex].ptr,i,j,game2048_buff[iconIndex].w,game2048_buff[iconIndex].h);
		}
	}
}












#if 0
void game_draw_str(INT16U* display_buf,INT16U str_idx)
{
	STRING_INFO 			str = { 0 };
	DISPLAY_ICONSHOW		icon;
	t_STRING_TABLE_STRUCT	str_res;
	INT16U tft_w = game_frame_w;
	INT16U tft_h = game_frame_h;


    str.font_type = 0;
	str.str_idx = str_idx;//???????
	str.buff_w = tft_w;
	str.buff_h = tft_h;
	str.language = ap_state_config_language_get() - 1;
	ap_state_resource_string_resolution_get(&str, &str_res);
	str.pos_x = (tft_w - str_res.string_width) >> 1;
	str.pos_y = (tft_h - str_res.string_height) >> 1;
	str.font_color = R_ID_PALETTE_Red;
/*
	icon.icon_w = 216;
	icon.icon_h = 144;
	icon.pos_x = (tft_w - icon.icon_w) / 2;
	icon.pos_y = (tft_h - icon.icon_h) / 2;
*/
	icon.transparent = R_ID_PALETTE_Transparent;

	str.pos_x +=1;
	str.pos_y +=1;
	icon.icon_w = str_res.string_width+12;
	icon.icon_h = str_res.string_height+12;
	icon.pos_x = (tft_w-icon.icon_w)>>1;
	icon.pos_y = (tft_h-icon.icon_h)>>1;
	ap_setting_rect_draw(display_buf, 0x2595,0xffe0,1, &icon);
	ap_state_resource_string_draw( display_buf, &str);
}
#endif


//閸掋倖鏌囬敓锟�?閸氾附婀佺粚杞扮秴
int isNotFull()
{
    int i,j,k=0;
	for(i=0;i<N;i++)
	for(j=0;j<N;j++)
		if(grid[i][j]==0)
		{
			k=1; break;
		}
	return k;
}
//閼惧嘲褰囬張鈧径褝鎷�?
int getMax()
{
    int i,j,max=0;
	for(i=0;i<N;i++)
	for(j=0;j<N;j++)
		if(max<grid[i][j]) max=grid[i][j];
	return max;
}
static INT8U *hal_WartermarkFill(char *str,INT8U *buffer,INT16U width,INT8U font)
{
    int i,j,n;
	INT8U *src,*tar,tw;
	u16 w, h;
    if((str==NULL) || (buffer == NULL))
		return NULL;
	n = 0;
	tw = (width+7)/8;	//width = 42*20/8
	tar = buffer;
    while(str[n])
    {
		src = (INT8U *)res_ascii_get((char)str[n],&w,&h,font);
		if(src != NULL)
		{
			//src = (INT8U *)res_ascii_get((char)' ',&w,&h,font);
			for(i=0;i<h;i++)
			{
				for(j=0;j<(w+7)/8;j++)
				    tar[i*tw+j] = *src++;
			}
			
		}

		tar+=(w+7)/8;
		n++;
		if(tar>=&buffer[width>>3])
			break;
    }
//	deg_Printf("streamsprintf %d,%d\n",n,font);
	return buffer;
}

//s8 printString(const char *pszStr)
//{
//	int length;
//	length = hx330x_str_len(pszStr);
//		
//	if(NULL == g2048_buffer_string)
//		g2048_buffer_string = hal_sysMemMalloc(16*32*length);	
//	
//	if(NULL == g2048_buffer_string){
//		deg_Printf("mem malloc printString fail--->\n");
//		return -1;
//	}
//
//	hal_WartermarkFill(pszStr,g2048_buffer_string,16*length,RES_FONT_NUM0);//RES_FONT_NUM2
////	image_draw_picture(game_frame_buff,g2048_buffer_string,i,j,game2048_buff[iconIndex].w,game2048_buff[iconIndex].h);
//	OSDFrameDrawIcon(game_frame_buff,g2048_buffer_string,32,32,16*length,16);
//
//
//}


void g2048_movedir(winHandle handle,INT32U type)
{
   int i,j,cr,w,F=0;
   int isf;

   if(game_over_in==1){
   		return;
	   game_over_in=0;
	   uiWinDestroy(&handle);
   }
   

   if(game_over_in==0){
	   if(type==KEY_EVENT_LEFT){//KEY_EVENT_UP
		   for(i=1;i<N;i++)//閺佹澘鐡鐡掑﹤鐨崷銊ょ瑐閺傜櫢绱漣鐡掑﹤銇囬崷銊ょ瑓閿燂拷?
		   for(j=0;j<N;j++)
		   {
			   cr=i;w=0;//0:閿燂拷?閸氬牆鑻�  1閿涙艾鎮庨獮鎯扮箖
			   while(cr>=1 && grid[cr][j]!=0 )
			   {
				   if(grid[cr-1][j]==0)//娑撳﹥鏌熼張澶屸敄娴ｅ稄绱濇稉濠勑�
				   {
					   grid[cr-1][j]=grid[cr][j];
					   grid[cr][j]=0;
					   F=1;
				   }
				   else//娑撳﹥鏌熼弮鐘碘敄閿燂拷?
				   {
					   if(grid[cr-1][j]==grid[cr][j]&& w==0)//閻╁摜鐡戦敍宀€娴夐敓锟�?
					   {
						   grid[cr-1][j]=grid[cr-1][j]*2;
						   grid[cr][j]=0;
						   w=1;
						   F=1;
					   }
					   else//娑撳秶鐡�
					   {
						   break;
					   }
				   }
				   cr--;
			   }
		   }
	   	}else if(type==KEY_EVENT_RIGHT){//KEY_EVENT_DOWN
			for(i=N-2;i>=0;i--)
			for(j=0;j<N;j++)
			{
				cr=i;w=0;
				while(cr<=N-2 && grid[cr][j]!=0 )
				{
					if(grid[cr+1][j]==0)//娑撳鏌熼張澶屸敄娴ｅ稄绱濇稉瀣�
					{
					    grid[cr+1][j]=grid[cr][j];
						grid[cr][j]=0;F=1;
					}
					else//娑撳鏌熼弮鐘碘敄閿燂拷?
					{
						if(grid[cr+1][j]==grid[cr][j] && w==0)//閻╁摜鐡戦敍宀€娴夐敓锟�?
						{
						    grid[cr+1][j]=grid[cr+1][j]*2;
							grid[cr][j]=0; w=1;F=1;
						}
						else//娑撳秶鐡�
						{
							break;
						}
					}
					cr++;
				}
			}
	  	}else if(type==KEY_EVENT_UP){//KEY_EVENT_LEFT
			for(i=0;i<N;i++)
			for(j=1;j<N;j++)
			{
				cr=j;w=0;
				while(cr>=1 && grid[i][cr]!=0 )
				{
					if(grid[i][cr-1]==0)//瀹革附鏌熼張澶屸敄娴ｅ稄绱濆锔拘�
					{
					    grid[i][cr-1]=grid[i][cr];
						grid[i][cr]=0;F=1;
					}
					else//瀹革附鏌熼弮鐘碘敄閿燂拷?
					{
						if(grid[i][cr-1]==grid[i][cr] && w==0)//閻╁摜鐡戦敍宀€娴夐敓锟�?
						{
						    grid[i][cr-1]=grid[i][cr-1]*2;
							grid[i][cr]=0;w=1;F=1;
						}
						else//娑撳秶鐡�
						{
							break;
						}
					}
					cr--;
				}
			}
		}else if(type==KEY_EVENT_DOWN){//KEY_EVENT_RIGHT
			 for(i=0;i<N;i++)
			 for(j=N-2;j>=0;j--)
			 {
				 cr=j;w=0;
				 while(cr<=N-2 && grid[i][cr]!=0 )
				 {
					 if(grid[i][cr+1]==0)//閸欒櫕鏌熼張澶屸敄娴ｅ稄绱濋崣宕囆�
					 {
						 grid[i][cr+1]=grid[i][cr];
						 grid[i][cr]=0;F=1;
					 }
					 else//閸欒櫕鏌熼弮鐘碘敄閿燂拷?
					 {
						 if(grid[i][cr+1]==grid[i][cr] && w==0)//閻╁摜鐡戦敍宀€娴夐敓锟�?
						 {
							 grid[i][cr+1]=grid[i][cr+1]*2;
							 grid[i][cr]=0; w=1; F=1;
						 }
						 else//娑撳秶鐡�
						 {
							 break;
						 }
					 }
					 cr++;
				 }
			 }
		}
   }
   if(F)
	g2048_draw_map(game_frame_buff);

	if(getMax()==M)//閸掋倖鏌囬敓锟�?閸氾箒鍎ㄩ敓锟�?
	{
//	   printString("success");
	   deg_Printf("success\n");
	   OSDFrameDrawIcon(game_frame_buff,game2048_buff[GAME_PASS_TIPS].ptr,50,80,220,80);
	   g_draw_resstr_default_style_in_screen_center(game_frame_buff,R_ID_STR_GAME_PASS,RES_FONT_NUM0);

       game_over_in=1;
	}
	isf=isNotFull();//鏉╂柨娲�1鐞涖劎銇氭潻妯绘箒缁岃桨缍�
	if(isf==1 && F==1) //閺堝鈹栨担宥勭瑬閺堝些閸斻劌鍟€闂呭繑婧€
	{
	   randomdata();//閸愬秹娈㈤敓锟�?
		g2048_draw_map(game_frame_buff);
	}
	else if(isf==0)//濞屸剝婀佺粚娲？閸掓瑦鐖堕幋蹇曠波閿燂拷?
	{
//		printString("game over");
		deg_Printf("game over\n");
		OSDFrameDrawIcon(game_frame_buff,game2048_buff[GAME_PASS_TIPS].ptr,50,80,220,80);
		g_draw_resstr_default_style_in_screen_center(game_frame_buff,R_ID_STR_GAME_OVER,RES_FONT_NUM0);

		game_over_in=1;
	}


	//deg_Printf("[curx:%d][cury:%d]\r\n",curx,cury);
	memcpy(ui_draw_ctrl.bufStart,game_frame_buff,game_frame_w*game_frame_h);
	app_draw_Service(1);

//	if(game_continue==1){
//		if (game_Level==0){
//			uiWinSetResid(winItem(handle,2048_TIPS_STR_TIP),"game over");
//			deg_Printf("game over---->\n");
//  			//game_draw_str( game_frame_buff,STR_50HZ);
//		}else{
//			uiWinSetResid(winItem(handle,2048_TIPS_STR_TIP),"next");
//			deg_Printf("game pass,next---->\n");
//		    //game_draw_str( game_frame_buff,STR_NTSC);
//		}
//	}
//    if(game_over_in){
//         game_over_in=0;
//		deg_Printf("game over ---->\n");
//    }
//    if(game_continue==1){
//        game_continue=0;
//        memset(game_frame_buff,R_ID_PALETTE_Black,game_frame_w*game_frame_h);
//       2048_draw_map(game_frame_buff);
//		app_draw_Service(1);
//    }else{
//    	//MARK();
//    	deg_Printf("flush---->\n");
//		uiWinDrawUpdate();
//	}

}

//-------------------------pusbbox----------------------

/*******************************************************************************
* Function Name  : 2048OpenWin
* Description    : 2048OpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int g2048OpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	u16 w,h;
	INT8U* figure_num_temp;
	INT8U* figure_pass_tips;
	deg_Printf("g2048OpenWin\n");

	g2048_temp_icons = hal_sysMemMalloc(60*60*12+220*120);
	if(NULL == g2048_temp_icons){
		deg_Printf("mem malloc foro icon  fail--->\n");
		return -1;
	}
	//uiWinSetVisible(winItem(handle,G2048_TIPS_RECT_ID1),0);
	//uiWinSetVisible(winItem(handle,G2048_TIPS_RECT_ID2),0);
	//uiWinSetVisible(winItem(handle,G2048_TIPS_STR_TIP),0);

	 //鐠囪褰囬崶鐐垼鐠у嫭绨�
	u32 addr,len;
	figure_num_temp = g2048_temp_icons;

//	addr = res_icon_GetAddrAndSize(R_ID_ICON_PIC0,&w,&h);
//	len=w*h;
//	nv_read(addr,figure_num_0,len);

	addr = res_icon_GetAddrAndSize(R_ID_ICON_PIC2,&w,&h);
	len=w*h;
	nv_read(addr,figure_num_temp,len);	
	game2048_buff[GAME_NUM_2].ptr 		= figure_num_temp	;
	
	figure_num_temp = g2048_temp_icons +60*60*1;
	addr = res_icon_GetAddrAndSize(R_ID_ICON_PIC4,&w,&h);
	len=w*h;
	nv_read(addr,figure_num_temp,len);
	game2048_buff[GAME_NUM_4].ptr 		= figure_num_temp	;


	figure_num_temp = g2048_temp_icons +60*60*2;
	addr = res_icon_GetAddrAndSize(R_ID_ICON_PIC8,&w,&h);
	len=w*h;
	nv_read(addr,figure_num_temp,len);
	game2048_buff[GAME_NUM_8].ptr 		= figure_num_temp	;

	figure_num_temp = g2048_temp_icons +60*60*3;
	addr = res_icon_GetAddrAndSize(R_ID_ICON_PIC16,&w,&h);
	len=w*h;
	nv_read(addr,figure_num_temp,len);
	game2048_buff[GAME_NUM_16].ptr 		= figure_num_temp	;

	figure_num_temp = g2048_temp_icons +60*60*4;
	addr = res_icon_GetAddrAndSize(R_ID_ICON_PIC32,&w,&h);
	len=w*h;
	nv_read(addr,figure_num_temp,len);
	game2048_buff[GAME_NUM_32].ptr 		= figure_num_temp	;


	figure_num_temp = g2048_temp_icons +60*60*5;
	addr = res_icon_GetAddrAndSize(R_ID_ICON_PIC64,&w,&h);
	len=w*h;
	nv_read(addr,figure_num_temp,len);
	game2048_buff[GAME_NUM_64].ptr 		= figure_num_temp	;
	

	figure_num_temp = g2048_temp_icons +60*60*6;
	addr = res_icon_GetAddrAndSize(R_ID_ICON_PIC128,&w,&h);
	len=w*h;
	nv_read(addr,figure_num_temp,len);
	game2048_buff[GAME_NUM_128].ptr 		= figure_num_temp	;

	figure_num_temp = g2048_temp_icons +60*60*7;
	addr = res_icon_GetAddrAndSize(R_ID_ICON_PIC256,&w,&h);
	len=w*h;
	nv_read(addr,figure_num_temp,len);
	game2048_buff[GAME_NUM_256].ptr 		= figure_num_temp	;

	figure_num_temp = g2048_temp_icons +60*60*8;
	addr = res_icon_GetAddrAndSize(R_ID_ICON_PIC512,&w,&h);
	len=w*h;
	nv_read(addr,figure_num_temp,len);
	game2048_buff[GAME_NUM_512].ptr 		= figure_num_temp	;

	figure_num_temp = g2048_temp_icons +60*60*9;
	addr = res_icon_GetAddrAndSize(R_ID_ICON_PIC1024,&w,&h);
	len=w*h;
	nv_read(addr,figure_num_temp,len);
	game2048_buff[GAME_NUM_1024].ptr 		= figure_num_temp	;

	figure_num_temp = g2048_temp_icons +60*60*10;
	addr = res_icon_GetAddrAndSize(R_ID_ICON_PIC2048,&w,&h);
	len=w*h;
	nv_read(addr,figure_num_temp,len);
	game2048_buff[GAME_NUM_2048].ptr 		= figure_num_temp	;
	figure_pass_tips = g2048_temp_icons +60*60*11;//180*60*11;
	addr = res_icon_GetAddrAndSize(R_ID_ICON_GAME_TIPS_ICON,&w,&h);
	len=w*h;
	nv_read(addr,figure_pass_tips,len);
	game2048_buff[GAME_PASS_TIPS].ptr 		= figure_pass_tips	;


	game2048_buff[0].ptr = NULL;
//	game2048_buff[GAME_NUM_NULL].ptr 			= figure_num_0	;
//	game2048_buff[GAME_NUM_2].ptr 		= figure_num_2	;
//	game2048_buff[GAME_NUM_2+1].ptr 		= figure_num_4	;
//	game2048_buff[GAME_NUM_2+2].ptr 		= figure_num_8	;
//	game2048_buff[GAME_NUM_2+3].ptr 			= figure_num_16	;
//	game2048_buff[GAME_NUM_2+4].ptr 		= figure_num_32	;
//	game2048_buff[GAME_NUM_2+5].ptr 			= figure_num_64	;
//	game2048_buff[GAME_NUM_2+6].ptr 		= figure_num_128	;
//	game2048_buff[GAME_NUM_2+7].ptr 	= figure_num_256	;
//	game2048_buff[GAME_NUM_2+8].ptr 	= figure_num_512	;
//	game2048_buff[GAME_NUM_2+9].ptr 	= figure_num_1024;
//	game2048_buff[GAME_NUM_2+10].ptr 	= figure_num_2048;
	deg_Printf("g2048OpenWin11111\n");

	Init2048Game();
	deg_Printf("g2048OpenWin22222\n");
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameCloseWin
* Description    : nesGameCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int g2048CloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	game_over_in=0;
	hal_sysMemFree(g2048_temp_icons);
	g2048_temp_icons = NULL;
//	memset(ui_draw_ctrl.bufStart,0,game_frame_w*game_frame_h);
	SysCtrl.gui_flush_sta = 1;//GUI 閿熻闈╂嫹閿熸枻鎷烽敓鏂ゆ嫹鍒烽敓鏂ゆ嫹
	//app_draw_Service(1);
//	g_liu = 0;
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameWinChildClose
* Description    : nesGameWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int g2048WinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("g2048WinChildClose\n");

	return 0;
}
/*******************************************************************************
* Function Name  : g2048KeyMsgOk
* Description    : g2048KeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int g2048KeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		Init2048Game();
	}

	return 0;
}
/*******************************************************************************
* Function Name  : g2048KeyMsgUp
* Description    : g2048KeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int g2048KeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
        g2048_movedir(handle,KEY_EVENT_UP);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : g2048KeyMsgDown
* Description    : g2048KeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int g2048KeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
        g2048_movedir(handle,KEY_EVENT_DOWN);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : g2048KeyMsgMode
* Description    : g2048KeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int g2048KeyMsgRight(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
        g2048_movedir(handle,KEY_EVENT_RIGHT);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : g2048KeyMsgMenu
* Description    : g2048KeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int g2048KeyMsgLeft(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
        g2048_movedir(handle,KEY_EVENT_LEFT);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : g2048KeyMsgMain
* Description    : g2048KeyMsgMain
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int g2048KeyMsgMain(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;

	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
    	uiWinDestroy(&handle);
	}
	return 0;
}

static int g2048SysMsg500MS(winHandle handle,u32 parameNum,u32* parame)
{
    static u32 cnttemp;
	if(game_over_in){			
		cnttemp++;
		if(cnttemp>3)
		{
			game_over_in=0;
			uiWinDestroy(&handle);
		}
		else
		{
			g_draw_resstr_default_style_in_screen_center(game_frame_buff,R_ID_STR_GAME_OVER,RES_FONT_NUM0);
			uiWinDrawUpdate();
		}
			
		
	}else{
		cnttemp=0;
		uiWinDrawUpdate();
	}
    return 0;
}

ALIGNED(4) msgDealInfor g2048MsgDeal[]=
{
	{SYS_OPEN_WINDOW,		g2048OpenWin},
	{SYS_CLOSE_WINDOW,		g2048CloseWin},
	{SYS_CHILE_COLSE,		g2048WinChildClose},
	//{KEY_EVENT_POWER,		g2048KeyMsgPower},

	//{KEY_EVENT_OK,			g2048KeyMsgOk},
	{KEY_EVENT_UP,			g2048KeyMsgUp},
	{KEY_EVENT_DOWN,		g2048KeyMsgDown},
	{KEY_EVENT_LEFT,		g2048KeyMsgLeft},
	{KEY_EVENT_RIGHT,		g2048KeyMsgRight},
	{KEY_EVENT_POWER,		g2048KeyMsgMain},
	{SYS_EVENT_500MS,	    g2048SysMsg500MS},
	{EVENT_MAX,NULL},
};

WINDOW(g2048Window,g2048MsgDeal,g2048Win)


