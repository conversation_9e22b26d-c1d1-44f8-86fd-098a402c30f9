/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  MP3_BSIO_H
       #define  MP3_BSIO_H

#define BS_CHAR_BIT  			8

//MP3 INPUT BSIO BYTE BUF DEFINE
#define MP3_BYTE_MIN       		1024
#define MP3_BYTE_NUM        	1
#define MP3_BYTE_PEEK_SIZE  	88
//MP3 INPUT BSIO BIT BUF DEFINE
#define MP3_BIT_PEEK_SIZE    	12
#define MP3_BIT_SIZE     		(2048 + MP3_BIT_PEEK_SIZE)


#define BSIO_ABS(a)            	((a) >= 0  ? (a) : (-(a)))
#define BSIO_MAX(a,b)          	((a) > (b) ? (a) : (b))
#define BSIO_MIN(a,b)          	((a) > (b) ? (b) : (a))
#define BSIO_ALIGN(x, a)       	(((x)+(a)-1)&~((a)-1))

#define BSIO_SEEK_SET 			0
#define BSIO_SEEK_CUR 			1
#define BSIO_SEEK_END 			2
/* Bytestream I/O Context. */
typedef struct MP3_BSIO_BYTE_S {
   // unsigned char *buffer;  /**< Start of the buffer. */
   	int (*read)(int fd, void *buf, UINT size); 
	FSIZE_t (*seek)(int fd,FSIZE_t ofs, u32 op);     
    u8 *buf_root;
    u8 *buf_end; 			/**< End of the data, may be less than
                                 buffer+buffer_size if the read function returned
                                 less data than requested, e.g. for streams where
                                 no more data has been received yet. */
    u32 peek_size;
	u8 *buf_ptr; 			/**< Current position in the buffer */
    u8 (*rbuf)[MP3_BYTE_MIN];
                            /**< ring buffer pointer */
    int file;               /**< file handler */
    s32 buf_num;         	/**< total number of buffers in the ring buffer */
	s32 buf_size;           /**< Maximum buffer size */
    s32 buf_cnt;            /**< counter of valid buffers in the ring buffer */
    
	u32 len;
    s32 head;               /**< the head of the ring buffer */
    s32 head_pos;           /**< position in the file of the head buffer */
    s32 tail;               /**< the tail of the ring buffer */
    s32 tail_pos;           /**< position in the file */
    
    s32 fill_full;          /**< fill the rest of the ring buffer */
    s32 bswap32;            /**< swap the buffer data */
    s32 eof_reached;        /**< true if eof reached */
    s32 error;              /**< contains the error code or 0 if no error happened */

}MP3_BSIO_BYTE_T;

typedef struct MP3_BSIO_BIT_S {
	u8 *root_ptr;
	u8 *byte;
	//u8 *end_ptr;
	u16 cache;
	u16 left;
	u32 rest_len;
	u32 pre_len;
	u32 buf_size;
	u32 peek_size;
}MP3_BSIO_BIT_T;



/*******************************************************************************
* Function Name  : mp3_io_init
* Description    : mp3_io_init, for byte stream handle
* Input          : MP3_BSIO_T *s, void *rbuf, u32 peek_size, u32 total_num,int fd
* Output         : none
* Return         : none
*******************************************************************************/
void mp3_bsio_byte_init(MP3_BSIO_BYTE_T *byte_op, u8* buf,int fd);
/*******************************************************************************
* Function Name  : mp3_bsio_byte_read
* Description    : mp3_bsio_byte_read: read byte buff data to buf
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : none
*******************************************************************************/
u32 mp3_bsio_byte_read(u8 *buf, u32 size);
/*******************************************************************************
* Function Name  : mp3_bsio_byte_seek
* Description    : mp3_bsio_byte_seek
* Input          : int offset : seek offset
* 				   int whence: BSIO_SEEK_CUR : target offset = cur pos + offset, BSIO_SEEK_SET: target offset = offset
* Output         : none
* Return         : none
*******************************************************************************/
int mp3_bsio_byte_seek(int offset, int whence);
/*******************************************************************************
* Function Name  : mp3_bsio_byte_peek
* Description    : mp3_bsio_byte_peek: peek buf process, 
*                      if last buf remain data less than peek buf size, will cpy data to 
*                      peek buf to make buff continuous
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : return buf_ptr
*******************************************************************************/
u8 *mp3_bsio_byte_peek(void);
/*******************************************************************************
* Function Name  : mp3_bsio_byte_skip
* Description    : mp3_bsio_byte_skip: skip offset bytes in buf
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : none
*******************************************************************************/
int mp3_bsio_byte_skip(int offset);
/*******************************************************************************
* Function Name  : mp3_bsio_byte_read_u8
* Description    : mp3_bsio_byte_read_u8: read one bytes from byte buff
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : none
*******************************************************************************/
u8 mp3_bsio_byte_read_u8(void);
/*******************************************************************************
* Function Name  : mp3_bsio_byte_read_l16
* Description    : mp3_bsio_byte_read_l16: read u16 by little endian way(low addr low byte)
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : none
*******************************************************************************/
u16 mp3_bsio_byte_read_l16(void);
/*******************************************************************************
* Function Name  : mp3_bsio_byte_read_l16
* Description    : mp3_bsio_byte_read_l16: read 3 bytes by little endian way(low addr low byte)
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : none
*******************************************************************************/
u32 mp3_bsio_byte_read_l24(void);
/*******************************************************************************
* Function Name  : mp3_bsio_byte_read_l16
* Description    : mp3_bsio_byte_read_l16: read 4 bytes by little endian way(low addr low byte)
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : none
*******************************************************************************/
u32 mp3_bsio_byte_read_l32(void);
/*******************************************************************************
* Function Name  : mp3_bsio_byte_read_b16
* Description    : mp3_bsio_byte_read_b16: read 2 byte by big endian way(low addr high byte)
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : none
*******************************************************************************/
u16 mp3_bsio_byte_read_b16(void);
/*******************************************************************************
* Function Name  : mp3_bsio_byte_read_b24
* Description    : mp3_bsio_byte_read_b24: read 3 byte by big endian way(low addr high byte)
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : none
*******************************************************************************/
u32 mp3_bsio_byte_read_b24(void);
/*******************************************************************************
* Function Name  : mp3_bsio_byte_read_b32
* Description    : mp3_bsio_byte_read_b32: read 4 byte by big endian way(low addr high byte)
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : none
*******************************************************************************/
u32 mp3_bsio_byte_read_b32(void);
/*******************************************************************************
* Function Name  : mp3_bsio_bit_init
* Description    : mp3_bsio_bit_init: mp3 bit stream init
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : none
*******************************************************************************/
void mp3_bsio_bit_init(MP3_BSIO_BIT_T *bit_op, u8 *bit_buf, u32 peek_size,u32 buf_size);
/*******************************************************************************
* Function Name  : mp3_bsio_bit_init
* Description    : mp3_bsio_bit_init: mp3 bit stream init
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : none
*******************************************************************************/
void mp3_bsio_bit_reset(MP3_BSIO_BIT_T *bit_op);
/*******************************************************************************
* Function Name  : mp3_bsio_bit_add
* Description    : mp3_bsio_bit_add: mp3 bit stream add  pre len
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : none
*******************************************************************************/
void mp3_bsio_bit_add(MP3_BSIO_BIT_T *bit_op,u32 len);
/*******************************************************************************
* Function Name  : mp3_bsio_bit_fill
* Description    : mp3_bsio_bit_fill: mp3 bit stream buf fill by pre len
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : none
*******************************************************************************/
void mp3_bsio_bit_fill(MP3_BSIO_BIT_T *bit_op);
/*******************************************************************************
* Function Name  : mp3_bsio_bit_set
* Description    : mp3_bsio_bit_set: mp3 bit stream set rest len
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : none
*******************************************************************************/
bool mp3_bsio_bit_set(MP3_BSIO_BIT_T *bit_op,u32 len);
/*******************************************************************************
* Function Name  : mp3_bsio_bit_skip
* Description    : mp3_bsio_bit_skip: mp3 bit stream skip len bits
* Input          : MP3_BSIO_BIT_T *bit_op,u32 bit_len
* Output         : none
* Return         : none
*******************************************************************************/
void mp3_bsio_bit_skip(MP3_BSIO_BIT_T *bit_op,u32 bit_len);
/*******************************************************************************
* Function Name  : mp3_bsio_bit_read
* Description    : mp3_bsio_bit_read: mp3 bit stream read bit len
* Input          : MP3_BSIO_BIT_T *bit_op,u32 bit_len
* Output         : none
* Return         : none
*******************************************************************************/
u32 mp3_bsio_bit_read(MP3_BSIO_BIT_T *bit_op,u32 bit_len);

#endif
