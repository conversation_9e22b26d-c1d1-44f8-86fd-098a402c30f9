/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_ITEM_MENU_EX2_H
#define UI_WIN_ITEM_MENU_EX2_H

typedef struct
{
	uiWidgetObj widget;

	winHandle   hStr;
	winHandle   hStrSel;
	winHandle   hImageSel;
	
	u16 		select;
	uiColor 	color;
	uiColor 	selectColor;		
}uiItemMenuEx2Obj;
/*******************************************************************************
* Function Name  : uiItemCreateMenuItemEx2
* Description    : uiItemCreateMenuItemEx2
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiItemCreateMenuItemEx2(s16 x0,s16 y0,u16 width,u16 height,u16 style);

#endif
