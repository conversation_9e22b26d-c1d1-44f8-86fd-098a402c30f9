/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskGameSnakeWin.c"

#define SNAKE_Max_Long   50//蛇的最大长�??
#define  FRUIT_W   14
#define  FRUIT_H   14

static unsigned long int next = 1;

extern INT8U game_over_in;
extern u16 game_frame_w;
extern u16 game_frame_h;

typedef struct
{
	INT16U x;
	INT16U y;
	INT8U  color;
	INT8U  CurNum; //
	INT8U  NextNum;//
	INT8U  TurnNum;//
	INT8U  Move;
    //蛇结构体
    INT16S A[SNAKE_Max_Long];
	INT16S B[SNAKE_Max_Long];

    INT8U Long;//蛇的长度
	INT8U Life;//蛇的生命 0活着 1死亡
	INT8U Direction;//蛇移动的方向
}_Shape;

//食物结构�??
struct Food
{
	INT16U X;//食物�??坐标
	INT16U Y;//食物纵坐�??
	INT8U Yes;//判断�??否�?�出现�?�物的变�?? 0有�?�物 1需要出现�?�物
}food;

_Shape NewShape;

//游戏等级分数

typedef struct
{
	INT16U speed;
	INT8U  Life;
	INT32U score;
}_Game;

_Game Game={10000,1,0};

extern u8 *game_frame_buff;
static u8* gSnake_temp_icons = NULL;

void snake_game_init(void);

void snake_startgame(winHandle handle);

int rand(void) // RAND_MAX assumed to be 32767
{
    next = next * 1103515245 + 12345;
    return (unsigned int)(next/65536) % 32768;
}

void snake_game_init(void)
{
    NewShape.x = 14;
    NewShape.y = 14;
    NewShape.color = R_ID_PALETTE_Yellow;

    NewShape.CurNum = 1; //
	NewShape.NextNum = 1; //
	NewShape.TurnNum = 1; //
	NewShape.Move = 1;//0;

    NewShape.Long=4;//2//定义蛇的长度
    NewShape.Life=0;//蛇还活着
    NewShape.Direction=KEY_EVENT_RIGHT;//蛇的起�?�方向定义为�??
    Game.score=0;//分数�??0
    Game.Life=1;//4;//蛇的生命�??
    food.Yes=1;//出现新�?�物
    NewShape.A[0]=14*4;
    NewShape.B[0]=14;//28;
    NewShape.A[1]=14*3;
    NewShape.B[1]=14;//28;
    NewShape.A[2]=14*2;
    NewShape.B[2]=14;//28;
    NewShape.A[3]=14;
    NewShape.B[3]=14;//28;	
	//app_draw_Service(1);   
	SysCtrl.gui_flush_sta = 0;
   // hal_lcdSetBufYUV(hal_lcdVideoShowFrameGet(),0x00,0x80,0x80);
	memset(game_frame_buff,R_ID_PALETTE_Red,game_frame_w*game_frame_h);
	//uiWinDrawUpdate();


	//debgreg(game_frame_buff);


    //OSQPost(DisplayTaskQ, (void *) MSG_DISPLAY_TASK_EFFECT_INIT);
    //ap_display_clear_screen(Game_buff);
    //memset((INT32S*)game_frame_buff, R_ID_PALETTE_Green,tft_w*tft_h);
#if 0

    ascii_str.font_color = R_ID_PALETTE_Red;
    ascii_str.font_type = 0;
    ascii_str.buff_w = tft_w;
    ascii_str.buff_h = tft_h;

    ascii_str.pos_x = 275;
    ascii_str.pos_y = 0;
    ascii_str.str_ptr = (char *)"000";
    ap_state_resource_string_ascii_draw((INT16U *)game_frame_buff, &ascii_str);

	OSQPost(DisplayTaskQ, (void *) (MSG_DISPLAY_TASK_MJPEG_DRAW | (INT32U)game_frame_buff));
#endif

	//app_sendDrawUIMsg();
	//uiWinDrawUpdate();


}

void snake_startgame(winHandle handle)
{
	INT16U i,n;//i蛇的关节�?? n用来判断食物和蛇的身体是否重�??
	INT8U life_buf[2];
	static INT8U socre_buf[4];
    //STRING_ASCII_INFO ascii_str;
    //DISPLAY_ICONSHOW icon;

	uiRect rect;

	//deg_Printf(" NewShape.Long:%d\r\n",NewShape.Long);

    INT16U tft_w,tft_h;
    hal_lcdGetVideoResolution(&tft_w,&tft_h);
	
	g_Draw_Fill((INT16U*)game_frame_buff,14,14,308,224,R_ID_PALETTE_Black);//R_ID_PALETTE_Transparent R_ID_PALETTE_DoderBlue//BASE_Y+42*4
    if(food.Yes==1)//出现新的食物
    {
        while(1)
        {
        //�?��?�定的区域内显示食物
        //food.X=10+rand()%(240/10)*10;
        //food.Y=10+rand()%(160/10)*10;
        //srand(calendar.sec);//添加随机种子 采用的RTC时钟
        food.X=14+rand()%(294/14)*14;
        food.Y=14+rand()%(210/14)*14;
		deg_Printf(" food.X:%d,food.Y=:%d\r\n",food.X,food.Y);
        for(n=0;n<NewShape.Long;n++)
        {
        	deg_Printf(" NewShape.A[%d]:%d,NewShape.B[%d]=:%d\r\n",n,NewShape.A[n],n,NewShape.B[n]);
            if(food.X==NewShape.A[n]&&food.Y==NewShape.B[n])
                break;
            }
            if(n==NewShape.Long)
                food.Yes=0;
            break;
        }
    }

    if(food.Yes==0)//有�?�物就�?�显�??
    {
        //LCD_Fill(food.X,food.Y,food.X+10,food.Y+10,RED);

//        g_Draw_Fill((INT16U*)game_frame_buff,food.X,food.Y,food.X+10,food.Y+10,R_ID_PALETTE_Red);
		OSDFrameDrawIcon(game_frame_buff,gameSnake_buff[GAME_FRUIT].ptr,food.X,food.Y,FRUIT_W,FRUIT_H);
    }
    //取得需要重新画的蛇的节�??
    for(i=NewShape.Long-1;i>0;i--){
        NewShape.A[i]=NewShape.A[i-1];
        NewShape.B[i]=NewShape.B[i-1];
    }
    //通过按键来�?�置蛇的运动方向
    switch(NewShape.Direction){
        case KEY_EVENT_RIGHT : NewShape.A[0]+=14;break;//向右运动
        case KEY_EVENT_LEFT :  NewShape.A[0]-=14;break;//向左运动
        case KEY_EVENT_UP :    NewShape.B[0]-=14;break;//向上运动
        case KEY_EVENT_DOWN :  NewShape.B[0]+=14;break;//向下运动
    }
    for(i=0;i<NewShape.Long;i++){//画出�??
//        g_Draw_Fill((INT16U*)game_frame_buff,NewShape.A[i],NewShape.B[i],NewShape.A[i]+10,NewShape.B[i]+10,R_ID_PALETTE_Yellow);
		if(i==0)
			OSDFrameDrawIcon(game_frame_buff,gameSnake_buff[GAME_HEAD0+NewShape.Direction-KEY_EVENT_UP].ptr,NewShape.A[i],NewShape.B[i],FRUIT_W,FRUIT_H);
		else
			OSDFrameDrawIcon(game_frame_buff,gameSnake_buff[GAME_BODY].ptr,NewShape.A[i],NewShape.B[i],FRUIT_W,FRUIT_H);

    }
    //LCD_Fill(snake.X[i],snake.Y[i],snake.X[i]+10,snake.Y[i]+10,BLUE);//画蛇�??�??
    //while(pause==1){
    //    XOSTimeDly(1);
    //};
    //delay_ms(500);//延时
	

    g_Draw_Fill((INT16U*)game_frame_buff,NewShape.A[NewShape.Long-1],NewShape.B[NewShape.Long-1],NewShape.A[NewShape.Long-1]+14,NewShape.B[NewShape.Long-1]+14,R_ID_PALETTE_Black);
    //LCD_Fill(snake.X[snake.Long-1],snake.Y[snake.Long-1],snake.X[snake.Long-1]+10,snake.Y[snake.Long-1]+10,GRAY);//消除蛇身

	deg_Printf(" NewShape.A[0]:%d,NewShape.B[0]:%d\r\n",NewShape.A[0],NewShape.B[0]);

    //判断�??否撞�??
    if(NewShape.A[0]<14||NewShape.A[0]>294||NewShape.B[0]<14||NewShape.B[0]>210)
        NewShape.Life=1;//蛇�?�掉�??

    //当蛇的身体超�??3节后判断蛇自�??的�?�撞

    for(i=3;i<NewShape.Long;i++)
    {
        if(NewShape.A[i]==NewShape.A[0]&&NewShape.B[i]==NewShape.B[0])//�??�??的任一坐标值与蛇头坐标相等就�?�为�??�??�??碰撞
            Game.Life-=1;
    }
    //deg_Printf("\r\ngame:%d,shape:%d\r\n",Game.Life,NewShape.Life)
    if(NewShape.Life==1||Game.Life==0)//以上两�?�判�??以后如果蛇�?�掉了跳出内�??�??，重新开�??
    {
        //gameover();

        game_over(game_frame_buff);
        return ;
    }
    //判断蛇是否吃到了食物

    //  deg_Printf(" (x:%d,y:%d)(eat:x:%d,y:%d)\r\n",NewShape.A[0],NewShape.B[0],food.X,food.Y);
    if(NewShape.A[0]==food.X&&NewShape.B[0]==food.Y)
    {

//        g_Draw_Fill((INT16U*)game_frame_buff,food.X,food.Y,food.X+10,food.Y+10,R_ID_PALETTE_Yellow);
		OSDFrameDrawIcon(game_frame_buff,gameSnake_buff[GAME_HEAD0+NewShape.Direction-KEY_EVENT_UP].ptr,food.X,food.Y,FRUIT_W,FRUIT_H);
		OSDFrameDrawIcon(game_frame_buff,gameSnake_buff[GAME_BODY].ptr,NewShape.A[NewShape.Long-1],NewShape.B[NewShape.Long-1],FRUIT_W,FRUIT_H);
		res_music_start(R_ID_MUSIC_BOOM,0,task_com_curVolume_get());

        //LCD_Fill(food.X,food.Y,food.X+10,food.Y+10,GRAY);//把吃到的食物消除
        if(!((NewShape.Long==SNAKE_Max_Long)&&(NewShape.Long==SNAKE_Max_Long)))
            NewShape.Long++;//蛇的�??体长一�??
#if 0
        icon.icon_w  = 60;
        icon.icon_h  = 30;
        if(icon.icon_w > tft_w){
            icon.icon_w = tft_h;
        }
        icon.pos_x = 260;
        icon.pos_y = 0;

        ap_setting_rect_draw((INT16U *)game_frame_buff,0x0000,0x0000,0,&icon);	// todo

#else
//	rect.x0 = 320;
//	rect.y0 = 0;
//	rect.x1 = 320+30-1;
//	rect.y1 = 0+30-1;
//	g_Draw_Fill(game_frame_buff,rect.x0,rect.y0,rect.x1,rect.y1,R_ID_PALETTE_Transparent);
#endif

        Game.score+=1;
        //LCD_ShowString(40,165,tftlcd_data.width,tftlcd_data.height,16,socre_buf);//显示成绩
        food.Yes=1;//需要重新显示�?�物
        socre_buf[0]=Game.score/100+0x30;
        socre_buf[1]=Game.score%100/10+0x30;
        socre_buf[2]=Game.score%100%10+0x30;
        socre_buf[3]='\0';
		
       // uiWinSetResid(winItem(handle,SNAKE_GRADE_ID),socre_buf);
       // uiWinSetResid(winItem(handle,SNAKE_TIPS_ID),"SCORE:");
			
    }
    life_buf[0]=Game.Life%10+0x30;
    life_buf[1]='\0';

    //LCD_ShowString(224,165,tftlcd_data.width,tftlcd_data.height,16,life_buf);//显示生命�??
    socre_buf[0]=Game.score/100+0x30;
    socre_buf[1]=Game.score%100/10+0x30;
    socre_buf[2]=Game.score%100%10+0x30;
    socre_buf[3]='\0';    
	g_draw_str(game_frame_buff,260,12,socre_buf,RES_FONT_NUM1,R_ID_PALETTE_White,R_ID_PALETTE_Blue);	

}


void Shake_move(winHandle handle,INT32U type)
{
	static u32 cnttemp;
	if(!NewShape.Move)
	{
		return;
	}

	
    if(KEY_EVENT_END != type)// keep same diretion when move auto
    	NewShape.Direction = type;


    //deg_Printf("NewShape.Direction:%d\r\n".Direction);
    if(game_over_in==0)
	    snake_startgame(handle);

    //OSQPost(DisplayTaskQ, (void *) (MSG_DISPLAY_TASK_MJPEG_DRAW | (INT32U)game_frame_buff));// send buffer data foro display


	if(game_over_in){			
		cnttemp++;
		if(cnttemp>0/*1*/)
		{
			game_over_in=0;
			uiWinDestroy(&handle);
		}
		// else
        // {

        // }
        
			
		
	}else{
		cnttemp=0;
		memcpy(ui_draw_ctrl.bufStart,game_frame_buff,game_frame_w*game_frame_h);
		uiWinDrawUpdate();
	}
}

/*******************************************************************************
* Function Name  : nesGameSubOpenWin
* Description    : nesGameSubOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/

static int SnakeOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
    INT8U* figure_fighter_1;// man up //24*24
    INT8U* figure_fighter_2;// man down //24*24
    INT8U* figure_fighter_3;// man left//24*24
    INT8U* figure_fighter_4;// man right //24*24
    INT8U* figure_fighter_5;
    INT8U* figure_fighter_6;
    INT8U* figure_pass_tips;
    u16 w,h;
    deg_Printf("SnakeOpenWin\n");


    gSnake_temp_icons = hal_sysMemMalloc(14*14*7+220*80);
    if(NULL == gSnake_temp_icons){
    deg_Printf("mem malloc foro icon  fail--->\n");
    return -1;
    }
    //	uiWinSetVisible(winItem(handle,GFighter_TIPS_RECT_ID1),0);
    //	uiWinSetVisible(winItem(handle,GFighter_TIPS_RECT_ID2),0);
    //	uiWinSetVisible(winItem(handle,GFighter_TIPS_STR_TIP),0);

    figure_fighter_1	= gSnake_temp_icons+14*14*1;
    figure_fighter_2	= gSnake_temp_icons+14*14*2;
    figure_fighter_3	= gSnake_temp_icons+14*14*3;
    figure_fighter_4	= gSnake_temp_icons+14*14*4;
    figure_fighter_5	= gSnake_temp_icons+14*14*5;
    figure_fighter_6	= gSnake_temp_icons+14*14*6;
    figure_pass_tips	= gSnake_temp_icons+14*14*7;//180*60*7;
    //读取图标资源
    u32 addr,len;
    addr = res_icon_GetAddrAndSize(R_ID_ICON_TCS_FOOD,&w,&h);
    len=w*h;
    nv_read(addr,figure_fighter_1,len);

    addr = res_icon_GetAddrAndSize(R_ID_ICON_TCS_BODY,&w,&h);
    len=w*h;
    nv_read(addr,figure_fighter_2,len);

    addr = res_icon_GetAddrAndSize(R_ID_ICON_TCS_HEAD_U,&w,&h);
    len=w*h;
    nv_read(addr,figure_fighter_3,len);

    addr = res_icon_GetAddrAndSize(R_ID_ICON_TCS_HEAD_D,&w,&h);
    len=w*h;
    nv_read(addr,figure_fighter_4,len);

    addr = res_icon_GetAddrAndSize(R_ID_ICON_TCS_HEAD_L,&w,&h);
    len=w*h;
    nv_read(addr,figure_fighter_5,len);

    addr = res_icon_GetAddrAndSize(R_ID_ICON_TCS_HEAD_R,&w,&h);
    len=w*h;
    nv_read(addr,figure_fighter_6,len);
    addr = res_icon_GetAddrAndSize(R_ID_ICON_GAME_TIPS_ICON,&w,&h);
	len=w*h;
    nv_read(addr,figure_pass_tips,w*h);

    gameSnake_buff[0].ptr = NULL;
    gameSnake_buff[GAME_FRUIT].ptr 		= figure_fighter_1	;
    gameSnake_buff[GAME_FRUIT+1].ptr 	= figure_fighter_2	;
    gameSnake_buff[GAME_FRUIT+2].ptr 	= figure_fighter_3	;
    gameSnake_buff[GAME_FRUIT+3].ptr 	= figure_fighter_4	;
    gameSnake_buff[GAME_FRUIT+4].ptr 	= figure_fighter_5	;
    gameSnake_buff[GAME_FRUIT+5].ptr 	= figure_fighter_6	;
    gameSnake_buff[GAME_PASS_TIPS].ptr 	= figure_pass_tips	;
	

    deg_Printf("SnakeOpenWin\n");
	

    snake_game_init();
    //uiWinDrawUpdate();

    Shake_move(handle,KEY_EVENT_RIGHT);
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameSubCloseWin
* Description    : nesGameSubCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int SnakeCloseWin(winHandle handle,u32 parameNum,u32* parame)
{

	deg_Printf("SnakeCloseWin\n");
	//uiWinDestroy(&handle);
	game_over_in=0;
	hal_sysMemFree(gSnake_temp_icons);
	gSnake_temp_icons = NULL;



	SysCtrl.gui_flush_sta = 1;//GUI
//	app_draw_Service(1);

	return 0;
}
/*******************************************************************************
* Function Name  : nesGameSubWinChildClose
* Description    : nesGameSubWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int SnakeWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	//deg_Printf("nesGameSubWinChildClose\n");
	return 0;
}

static int SnakeKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		NewShape.Move = !NewShape.Move;
	}

	return 0;
}

/*******************************************************************************
* Function Name  : nesGameSubWinChildClose
* Description    : nesGameSubWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int SnakeKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//deg_Printf("SnakeKeyMsgUp\n");
		task_com_keysound_play();
		//task_com_sound_wait_end();

		if(NewShape.Direction == KEY_EVENT_DOWN)
		{
			return 0;
		}
		SysCtrl.UserlastTime = XOSTimeGet();

		Shake_move(handle,KEY_EVENT_UP);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : nesGameSubWinChildClose
* Description    : nesGameSubWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int SnakeKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
		//task_com_sound_wait_end();	
		//deg_Printf("SnakeKeyMsgDown\n");
		if(NewShape.Direction == KEY_EVENT_UP)
		{
			return 0;
		}	
		SysCtrl.UserlastTime = XOSTimeGet();
		Shake_move(handle,KEY_EVENT_DOWN);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : nesGameSubWinChildClose
* Description    : nesGameSubWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int SnakeKeyMsgLeft(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
		//task_com_sound_wait_end();
		//deg_Printf("SnakeKeyMsgLeft\n");
		if(NewShape.Direction == KEY_EVENT_RIGHT)
		{
			return 0;
		}
		SysCtrl.UserlastTime = XOSTimeGet();
		Shake_move(handle,KEY_EVENT_LEFT);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : nesGameSubWinChildClose
* Description    : nesGameSubWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int SnakeKeyMsgRight(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
		//task_com_sound_wait_end();	
		//deg_Printf("SnakeKeyMsgRight\n");
		if(NewShape.Direction == KEY_EVENT_LEFT)
		{
			return 0;
		}	
		SysCtrl.UserlastTime = XOSTimeGet();
		Shake_move(handle,KEY_EVENT_RIGHT);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : nesGameSubWinChildClose
* Description    : nesGameSubWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int SnakeKeyMsgMain(winHandle handle,u32 parameNum,u32* parame)
{
	u32 addr,len;
	static u32 *buf = NULL;

	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
//		//deg_Printf("SnakeKeyMsgMain\n");
//		buf = hal_sysMemMalloc(320*240);
//		addr = res_icon_GetAddrAndSize(R_ID_IMAGE_SNAKE1,game_frame_w,game_frame_h);
//		len=game_frame_w*game_frame_h;
//		nv_read(addr,buf,len);
//		memset(ui_draw_ctrl.bufStart,buf,game_frame_w*game_frame_h);
//		uiWinDrawUpdate();
//		
		uiWinDestroy(&handle);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : SnakeSysMsg500MS
* Description    : SnakeSysMsg500MS
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int SnakeSysMsg500MS(winHandle handle,u32 parameNum,u32* parame)
{

    Shake_move(handle,KEY_EVENT_END);
	return 0;
}

ALIGNED(4) msgDealInfor snakeMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		SnakeOpenWin},
	{SYS_CLOSE_WINDOW,		SnakeCloseWin},
	{SYS_CHILE_COLSE,		SnakeWinChildClose},
	{KEY_EVENT_OK,			SnakeKeyMsgOk},
	{KEY_EVENT_UP,	        SnakeKeyMsgUp},
	{KEY_EVENT_DOWN,	    SnakeKeyMsgDown},
	{KEY_EVENT_LEFT,	    SnakeKeyMsgLeft},
	{KEY_EVENT_RIGHT,	    SnakeKeyMsgRight},
	{KEY_EVENT_POWER,	    SnakeKeyMsgMain},
	{SYS_EVENT_USER_1S,	    SnakeSysMsg500MS},
	{EVENT_MAX,NULL},
};

WINDOW(snakeWindow,snakeMsgDeal,snakeWin)


