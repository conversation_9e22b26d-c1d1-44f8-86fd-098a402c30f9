/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"



ALIGNED(4) const NES_GAME_NAME nes_game_inner_list[]= {

	{R_ID_FILE_NES_GAME_1,  R_ID_IMAGE_GAME_1_D,  R_ID_IMAGE_GAME_1_U,	""},
	{R_ID_FILE_NES_GAME_2, 	R_ID_IMAGE_GAME_2_D,  R_ID_IMAGE_GAME_2_U,	""},
	{R_ID_FILE_NES_GAME_3, 	R_ID_IMAGE_GAME_3_D,  R_ID_IMAGE_GAME_3_U, 	""},

};
#include "taskNesGameInnerWin.c"

/*******************************************************************************
* Function Name  : taskNesGameInnerBackGroundShow
* Description    : taskNesGameInnerBackGroundShow
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
void taskNesGameInnerCurWinShow(void)
{
	if(task_nesGame_op.curIdShowUp)
	{
		task_nesGame_op.curId = nes_game_inner_list[task_nesGame_op.nesGame_inner_index].imgUpId;
	}else
	{
		task_nesGame_op.curId = nes_game_inner_list[task_nesGame_op.nesGame_inner_index].imgDnId;
	}
	res_image_show(task_nesGame_op.curId, 0);
}
/*******************************************************************************
* Function Name  : taskNesGameInnerWinChageShow
* Description    : taskNesGameInnerWinChageShow
* Input          : dir: 0: move to left, 1: move to right
* Output         : none
* Return         : none
*******************************************************************************/
void taskNesGameInnerWinChageShow(u8 dir)
{
	u32 win_type;
	u32 cur_id = task_nesGame_op.curId;
	if(dir > 0)
	{
		task_nesGame_op.nesGame_inner_index++;
		if(task_nesGame_op.nesGame_inner_index >= task_nesGame_op.nesGame_inner_indexmax)
		{
			task_nesGame_op.nesGame_inner_index = 0;
		}
		win_type = MAIN_TO_SUB_HOR_RIGHT;
	}else
	{
		if(task_nesGame_op.nesGame_inner_index <= 0)
		{
			task_nesGame_op.nesGame_inner_index = task_nesGame_op.nesGame_inner_indexmax -1;
		}else
		{
			task_nesGame_op.nesGame_inner_index--;
		}
		win_type = MAIN_TO_SUB_HOR_LEFT;
	}
	if(task_nesGame_op.curIdShowUp)
	{
		task_nesGame_op.curId = nes_game_inner_list[task_nesGame_op.nesGame_inner_index].imgUpId;
	}else
	{
		task_nesGame_op.curId = nes_game_inner_list[task_nesGame_op.nesGame_inner_index].imgDnId;
	}
	taskMainWinInit(cur_id,MEDIA_SRC_NVFS, task_nesGame_op.curId, 0,win_type);
	if(taskWinChangeProcess() < 0)
	{
		res_image_show(task_nesGame_op.curId, 0);
	}
	
}

		
		
/*******************************************************************************
* Function Name  : nesGameInnerOpenWin
* Description    : nesGameInnerOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameInnerOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("nesGameSubOpenWin\n");
	task_nesGame_op.nesGame_step  = NES_GAME_INNER;
	
	task_nesGame_op.nesGame_inner_indexmax = ARRAY_NUM(nes_game_inner_list);
	
	nesGameInnerNameShow(handle);
	res_music_start(R_ID_MUSIC_GAMES_MUSIC,0,task_com_curVolume_get());

	//task_nesGame_op.nesGame_state = NES_GAME_START;
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameInnerCloseWin
* Description    : nesGameInnerCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameInnerCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	res_music_end();
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameInnerWinChildOpen
* Description    : nesGameInnerWinChildOpen
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameInnerWinChildOpen(winHandle handle,u32 parameNum,u32* parame)
{
	res_music_end();
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameWinChildClose
* Description    : nesGameWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameInnerWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("nesGameInnerWinChildClose\n");
	nesGameInnerNameShow(handle);
	taskNesGameInnerCurWinShow();
	res_music_start(R_ID_MUSIC_GAMES_MUSIC,0,task_com_curVolume_get());
	
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameInnerKeyMsgOk
* Description    : nesGameInnerKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameInnerKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//res_music_end();
		taskNesGameStart();
	}

	return 0;
}
/*******************************************************************************
* Function Name  : nesGameInnerKeyMsgUp
* Description    : nesGameInnerKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameInnerKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{

	}
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameInnerKeyMsgDown
* Description    : nesGameInnerKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameInnerKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{

	}
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameInnerKeyMsgLeft
* Description    : nesGameInnerKeyMsgLeft
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameInnerKeyMsgLeft(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		taskNesGameInnerWinChageShow(0);
		nesGameInnerNameShow(handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameInnerKeyMsgRight
* Description    : nesGameInnerKeyMsgRight
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameInnerKeyMsgRight(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		taskNesGameInnerWinChageShow(1);
		nesGameInnerNameShow(handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameInnerKeyMsgPower
* Description    : nesGameInnerKeyMsgPower
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameInnerKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//hal_sysMemPrint();
		res_music_end();
		taskNesGameOpenList();
		if(task_nesGame_op.openListTotal != 0)
			uiOpenWindow(&nesGameWindow, 0, 0);
		else
		{
			SysCtrl.winChangeEnable = 1;
			app_taskStart(TASK_MAIN,0);
		}
			
	}
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameInnerSysMsgSD
* Description    : nesGameInnerSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameInnerSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{

	return 0;
}
/*******************************************************************************
* Function Name  : nesGameInnerSysMsgUSB
* Description    : nesGameInnerSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameInnerSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{

	return 0;
}
/*******************************************************************************
* Function Name  : nesGameInnerSysMsgBattery
* Description    : nesGameInnerSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameInnerSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{

	return 0;
}
/*******************************************************************************
* Function Name  : nesGameInnerSysMsg1S
* Description    : nesGameInnerSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameInnerSysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
	task_nesGame_op.curIdShowUp	  ^= 1;
	taskNesGameInnerCurWinShow();

	return 0;
}


ALIGNED(4) msgDealInfor nesGameInnerMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		nesGameInnerOpenWin},
	{SYS_CLOSE_WINDOW,		nesGameInnerCloseWin},
	{SYS_CHILE_OPEN,		nesGameInnerWinChildOpen},
	{SYS_CHILE_COLSE,		nesGameInnerWinChildClose},
	{KEY_EVENT_OK,			nesGameInnerKeyMsgOk},
	{KEY_EVENT_UP,			nesGameInnerKeyMsgUp},
	{KEY_EVENT_DOWN,		nesGameInnerKeyMsgDown},
	{KEY_EVENT_LEFT,		nesGameInnerKeyMsgLeft},
	{KEY_EVENT_RIGHT,		nesGameInnerKeyMsgRight},
	{KEY_EVENT_POWER,		nesGameInnerKeyMsgPower},
	{SYS_EVENT_SDC,			nesGameInnerSysMsgSD},
	{SYS_EVENT_USBDEV,		nesGameInnerSysMsgUSB},
	{SYS_EVENT_BAT,			nesGameInnerSysMsgBattery},
	{SYS_EVENT_500MS,		nesGameInnerSysMsg1S},

	{EVENT_MAX,NULL},
};

WINDOW(nesGameInnerWindow,nesGameInnerMsgDeal,nesGameInnerWin)


