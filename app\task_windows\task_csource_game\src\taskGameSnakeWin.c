/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

enum
{
	SNAKE_MODE_ID=0,
	SNAKE_BG_ID,
	SNAKE_BG1_ID,
	SNAKE_TIPS_ID,
	SNAKE_GRADE_ID,
	SNAKE_BATERRY_ID,

	SNAKE_MAX_ID
};
typedef struct{
	INT16U w;
	INT16U h;
	INT8U *ptr;
}ICON_INFOR;

enum {
	GAME_FRUIT = 1,//
	GAME_BODY,
	GAME_HEAD0 ,
	GA<PERSON>_HEAD1,
	GAME_HEAD2,
	GAME_HEAD3,//
	GAME_PASS_TIPS,
	GAME_SNAKE_ICON_MAX
};

ICON_INFOR  gameSnake_buff[GAME_SNAKE_ICON_MAX]={
    {GAME_SNAKE_ICON_MAX,GAME_SNAKE_ICON_MAX,NULL},
    {14,14,NULL},
    {14,14,NULL},
    {14,14,NULL},
    {14,14,NULL},
    {14,14,NULL},
    {14,14,NULL},
	{180,60,NULL},
};

UNUSED ALIGNED(4) const widgetCreateInfor snakeWin[] =
{
	createFrameWin( 		    Rx(0),   Ry(0),   Rw(320), Rh(240),R_ID_PALETTE_Red,WIN_ABS_POS|WIN_NOT_ZOOM),
	createRect(SNAKE_BG_ID,	    Rx(10),   Ry(10),   Rw(300),  Rh(220),R_ID_PALETTE_Black),
	//createRect(SNAKE_BG1_ID,	Rx(100), Ry(0),   Rw(100),  Rh(28),R_ID_PALETTE_Black),
	//createStringIcon(SNAKE_TIPS_ID,  	Rx(120),  Ry(0),   Rw(40),  Rh(16),	RAM_ID_MAKE("SCORE: "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_Green,	DEFAULT_FONT),
	//createStringIcon(SNAKE_GRADE_ID,  	Rx(160),  Ry(0),   Rw(40),  Rh(16),	RAM_ID_MAKE("000"),		ALIGNMENT_LEFT, 	R_ID_PALETTE_Green,	DEFAULT_FONT),
	//createImageIcon(SNAKE_MODE_ID,      	Rx(10),   Ry(6),   Rw(24),  Rh(24),  R_ID_ICON_MTPLAY2, 	ALIGNMENT_CENTER),
	widgetEnd(),
};
