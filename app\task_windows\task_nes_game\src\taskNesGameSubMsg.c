/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskNesGameSubWin.c"

/*******************************************************************************
* Function Name  : nesGameSubOpenWin
* Description    : nesGameSubOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameSubOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("nesGameSubOpenWin\n");
	//nes_palette_init();
	task_nesGame_op.nesGame_state = NES_GAME_START;
	//app_draw_Service(1);
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameSubCloseWin
* Description    : nesGameSubCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameSubCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("nesGameSubCloseWin\n");
	//uiWinDestroy(&handle);
	
	app_draw_Service(1);
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameSubWinChildClose
* Description    : nesGameSubWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameSubWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	//deg_Printf("nesGameSubWinChildClose\n");
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameSubWinChildClose
* Description    : nesGameSubWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameSubKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	//deg_Printf("nesGameSubKeyMsgMode\n");
	uiWinDestroy(&handle);
	return 0;
}
ALIGNED(4) msgDealInfor nesGameSubMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		nesGameSubOpenWin},
	{SYS_CLOSE_WINDOW,		nesGameSubCloseWin},
	{SYS_CHILE_COLSE,		nesGameSubWinChildClose},
	{KEY_EVENT_POWER,	    nesGameSubKeyMsgPower},
	{EVENT_MAX,NULL},
};

WINDOW(nesGameSubWindow,nesGameSubMsgDeal,nesGameSubWin)


