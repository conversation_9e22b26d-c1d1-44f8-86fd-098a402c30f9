/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  XCFG_H
    #define  XFG_H

//------------------x os config--------------------------------------
#define X_CFG_TICK            	1000    // ticks /s

#define X_TICK                  1        // for user delay, X-TICK delay a tick 10*X_TICK ->10ms

#define X_TICK_TIME           	(1000/X_CFG_TICK)    // ms


#define X_CFG_MSGQ_USE      	1 // msg queue 

#define X_CFG_MBOX_USE      	0 // mbox

#define X_CFG_WORK_USE      	1

#define X_CFG_CRIT_USE      	1

#if X_CFG_MSGQ_USE > 0
    #define  X_CFG_MSGQ_MAX     12   // number of msg queue
#endif


#if X_CFG_MBOX_USE > 0
    #define  X_CFG_MBOX_MAX     8   // number of mbox
#endif



#if X_CFG_WORK_USE > 0
    #define  X_CFG_WORK_MAX     8   // number of work queue
#endif


#endif

