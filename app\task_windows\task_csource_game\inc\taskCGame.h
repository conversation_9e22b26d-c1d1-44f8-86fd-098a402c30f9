/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef  __TASK_C_SOUREC_GAME_H
#define  __TASK_C_SOUREC_GAME_H
extern sysTask_T taskCGame;
EXTERN_WINDOW(cGameWindow);
EXTERN_WINDOW(mazeWindow);
EXTERN_WINDOW(pushBoxWindow);
EXTERN_WINDOW(gFighterWindow);
EXTERN_WINDOW(g2048Window);
EXTERN_WINDOW(snakeWindow);




typedef enum{
	C_GAME_DISABLE = 0,
	C_GAME_INIT,
	C_GAME_START,
	C_GAME_STOP,
}TASK_C_GAME_STATE;

typedef struct CGAME_OP_S
{
	u32  cGame_state;
	u32  music_delay;
	u32  openListTotal;		
	u32  cGame_step;
	u32  cGame_inner_index;
	u32  cGame_inner_indexmax;
	u32  curIdShowUp;
	u32  curId;
}CGAME_OP_T;

typedef struct{
	u32 imgDnId;
	u32 imgUpId;
	u8  name[32];
}C_GAME_NAME;

ALIGNED(4) CGAME_OP_T task_cGame_op;

/*******************************************************************************
* Function Name  : taskCGameIsStart
* Description    : taskCGameIsStart
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
bool taskCGameIsStart(void);


#endif
