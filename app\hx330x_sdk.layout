<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<CodeBlocks_layout_file>
	<FileVersion major="1" minor="0" />
	<ActiveTarget name="Debug" />
	<File name="user_config\src\user_config_api.c" open="1" top="0" tabpos="3" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="8148" topLine="221" />
		</Cursor>
	</File>
	<File name="task_windows\task_version\src\taskVersion.c" open="1" top="0" tabpos="25" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="861" topLine="12" />
		</Cursor>
	</File>
	<File name="user_config\inc\user_config_api.h" open="1" top="0" tabpos="9" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="1206" topLine="14" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_mipi_VGA_GC030A.c" open="1" top="0" tabpos="10" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="10812" topLine="362" />
		</Cursor>
	</File>
	<File name="task_windows\task_csource_game\src\taskGameSnakeMsg.c" open="1" top="0" tabpos="32" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="18426" topLine="583" />
		</Cursor>
	</File>
	<File name="app_common\src\main.c" open="1" top="0" tabpos="2" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="311" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_csource_game\src\taskGameMazeWin.c" open="1" top="0" tabpos="12" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="615" topLine="2" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_mcu_3030B.c" open="1" top="0" tabpos="22" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="5799" topLine="300" />
		</Cursor>
	</File>
	<File name="task_windows\task_csource_game\src\taskGameMazeMsg.c" open="1" top="0" tabpos="7" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="23989" topLine="523" />
		</Cursor>
	</File>
	<File name="task_windows\task_main\src\taskMain.c" open="1" top="0" tabpos="1" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="17386" topLine="469" />
		</Cursor>
	</File>
	<File name="app_common\src\app_init.c" open="1" top="0" tabpos="30" split="0" active="1" splitpos="0" zoom_1="-4" zoom_2="0">
		<Cursor>
			<Cursor1 position="669" topLine="12" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_mipi_VGA_GC033A.c" open="1" top="0" tabpos="4" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="19593" topLine="508" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\mMenuSettingWin.c" open="1" top="0" tabpos="20" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="5734" topLine="145" />
		</Cursor>
	</File>
	<File name="..\dev\key\src\key_api.c" open="1" top="0" tabpos="16" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="4324" topLine="112" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\inc\sensor_api.h" open="1" top="0" tabpos="13" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="3622" topLine="51" />
		</Cursor>
	</File>
	<File name="task_windows\task_csource_game\src\taskCGame.c" open="1" top="0" tabpos="8" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="552" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuItemMsg.c" open="1" top="0" tabpos="37" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="360" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_record_photo\src\taskRecordPhoto.c" open="1" top="0" tabpos="14" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="6537" topLine="203" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinManage.h" open="1" top="0" tabpos="34" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="6520" topLine="170" />
		</Cursor>
	</File>
	<File name="task_windows\task_csource_game\src\taskGame2048Msg.c" open="1" top="0" tabpos="39" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="12351" topLine="512" />
		</Cursor>
	</File>
	<File name="task_windows\task_sd_update\src\taskSdUpdateWin.c" open="1" top="0" tabpos="33" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="6707" topLine="150" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_mipi_VGA_BF20A1.c" open="1" top="0" tabpos="17" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="19899" topLine="486" />
		</Cursor>
	</File>
	<File name="resource\user_res.h" open="1" top="0" tabpos="19" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="8337" topLine="270" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_VGA_OV7675.c" open="1" top="0" tabpos="27" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="1329" topLine="37" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\inc\lcd_api.h" open="1" top="0" tabpos="31" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="2069" topLine="0" />
		</Cursor>
	</File>
	<File name="user_config\inc\user_config_typedef.h" open="1" top="0" tabpos="36" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="1320" topLine="35" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\inc\lcd_typedef.h" open="1" top="0" tabpos="23" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="3815" topLine="110" />
		</Cursor>
	</File>
	<File name="task_windows\task_csource_game\src\taskCGameWin.c" open="1" top="0" tabpos="6" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="456" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuDelAllMsg.c" open="1" top="0" tabpos="29" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="1025" topLine="62" />
		</Cursor>
	</File>
	<File name="task_windows\task_api.c" open="1" top="0" tabpos="26" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="1990" topLine="72" />
		</Cursor>
	</File>
	<File name="task_windows\task_sd_update\src\taskSdUpdate.c" open="1" top="0" tabpos="28" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="5511" topLine="171" />
		</Cursor>
	</File>
	<File name="task_windows\task_bat_charge\src\taskBatChargeMsg.c" open="1" top="0" tabpos="15" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="7387" topLine="213" />
		</Cursor>
	</File>
	<File name="user_config\src\user_config_tab.c" open="1" top="0" tabpos="24" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="325" topLine="1" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_mipi_VGA_GC0339.c" open="1" top="0" tabpos="5" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="8591" topLine="344" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_spi.c" open="1" top="0" tabpos="18" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="14995" topLine="543" />
		</Cursor>
	</File>
	<File name="..\multimedia\image\image_decode.c" open="1" top="0" tabpos="35" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="6838" topLine="252" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuSreenBrightMsg.c" open="1" top="0" tabpos="38" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="2202" topLine="61" />
		</Cursor>
	</File>
	<File name="task_windows\task_common\src\sys_common_msg.c" open="1" top="0" tabpos="21" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="2716" topLine="65" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\inc\menu_api.h" open="1" top="0" tabpos="11" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="707" topLine="8" />
		</Cursor>
	</File>
</CodeBlocks_layout_file>
