/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef LENS_API_H
#define LENS_API_H


#define LENS_FUNC_SUPPORT       1 //change this should updata lib file   

typedef enum
{
	LENS_TYPE_NONE = 0,
    LENS_TYPE_CYCLE,
    LENS_TYPE_DIAMOND,
    LENS_TYPE_STAR,
    LENS_TYPE_HOURGLASS,
    LENS_TYPE_MAX,
}LENS_TYPE;

typedef enum
{
	LENS_CH_NONE 	 = 0,
	LENS_CH_LCD		 = (1 << 0),    //LCD  EN
	LENS_CH_CSI		 = (1 << 1),	//CSI  EN
}LENS_CHAGE_STAT;


/*******************************************************************************
* Function Name  : lens_alter_init
* Description    : lens_alter_init
* Input          :
* Output         : none
* Return         : int 0;
*******************************************************************************/
void lens_init(void);
/*******************************************************************************
* Function Name  : lens_add_csi
* Description    : lens_add_csi
* Input          : u8 *y_buf, u8* uv_buf
* Output         : none
* Return         : int 0;
*******************************************************************************/
void lens_add_csi(u8 *y_buf, u8* uv_buf, u32 size);
/*******************************************************************************
* Function Name  : lens_add_lcd
* Description    : lens_add_lcd
* Input          : u8 *y_buf, u8* uv_buf
* Output         : none
* Return         : int 0;
*******************************************************************************/
void lens_add_lcd(u8 *y_buf, u8* uv_buf, u32 size);
/*******************************************************************************
* Function Name  : lens_create
* Description    : lens_create:
* Input          :
* Output         : none
* Return         : int 0;
*******************************************************************************/
int lens_create(u32 lens_type, u8 lcd_en, u8 csi_en);

/*******************************************************************************
* Function Name  : lens_add_lcd_check
* Description    : lens_add_lcd_check
* Input          :
* Output         : none
* Return         : int 0;
*******************************************************************************/
u32 lens_add_lcd_check(void);
/*******************************************************************************
* Function Name  : lens_add_csi_check
* Description    : lens_add_csi_check
* Input          : 
* Output         : none
* Return         : int 0;
*******************************************************************************/
u32 lens_add_csi_check(void);
#endif
