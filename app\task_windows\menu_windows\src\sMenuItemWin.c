/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	//ITEM_HIDE_RECT_ID = 0,
	//ITEM_MODE_RECT_ID,
	//ITEM_MODE_ID,
	ITEM_SELECT_ID,
};

UNUSED ALIGNED(4) const widgetCreateInfor menuItemWin[] =
{
	createFrameWin(							Rx(0),	Ry(0), Rw(320),Rh(240), R_ID_PALETTE_Transparent,WIN_ABS_POS),
	//createRect(ITEM_HIDE_RECT_ID,           Rx(10),	<PERSON>y(20),Rw(300),Rh(200),	MENU_FRAME_COLOR),
	//createRect(ITEM_MODE_RECT_ID,           Rx(40),	Ry(5), Rw(35), Rh(35),	MENU_FRAME_COLOR),
	//createImageIcon(ITEM_MODE_ID,           Rx(40), Ry(5), Rw(35), Rh(35), 	R_ID_ICON_MTSETTING, ALIGNMENT_CENTER),

	createItemManage(ITEM_SELECT_ID,        Rx(30),	Ry(36),Rw(255),Rh(170),	MENU_UNSELECT_BG_COLOR),

	widgetEnd(),
};
