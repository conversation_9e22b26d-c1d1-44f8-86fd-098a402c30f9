/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef NES_K6502_H
#define NES_K6502_H

#define K6502_CLK(a)								nes_g_wPassedClocks += (a)

// NMI Request
#define K6502_NMI_REQ()  							nes_NMI_State = 0

// IRQ Request
#define K6502_IRQ_REQ()  							nes_IRQ_State = 0

#define K6502_ReadZp(byAddr)   						(nes_RAM[ (BYTE)(byAddr)])

//#define K6502_Vector_ReadW(wAddr)					(nes_ROMBANK3[(wAddr) & 0x1fff] |(WORD)(nes_ROMBANK3[((wAddr)+1) & 0x1fff] << 8))		
/*******************************************************************************
* Function Name  : K6502_Read
* Description    : K6502_Read: Reading operation 
* Input          :  
 *    0x0000 - 0x1fff  RAM ( 0x800 - 0x1fff is mirror of 0x0 - 0x7ff )
 *    0x2000 - 0x3fff  PPU
 *    0x4000 - 0x5fff  Sound
 *    0x6000 - 0x7fff  SRAM ( Battery Backed )
 *    0x8000 - 0xffff  ROM
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
BYTE K6502_Read( WORD wAddr );

WORD K6502_ReadWord( WORD wAddr );
WORD K6502_ReadWord2( WORD wAddr );
WORD K6502_Vector_ReadW(WORD wAddr);
/*******************************************************************************
* Function Name  : K6502_Write
* Description    : K6502_Write: Writing operation 
* Input          :  
 *    0x0000 - 0x1fff  RAM ( 0x800 - 0x1fff is mirror of 0x0 - 0x7ff )
 *    0x2000 - 0x3fff  PPU
 *    0x4000 - 0x5fff  Sound
 *    0x6000 - 0x7fff  SRAM ( Battery Backed )
 *    0x8000 - 0xffff  ROM
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
void K6502_Write( WORD wAddr, BYTE byData );
void K6502_WriteWord( WORD wAddr, WORD wData );
WORD K6502_ReadZpW(BYTE byAddr);
/*******************************************************************************
* Function Name  : K6502_Init
* Description    : Initialize K6502 :You must call this function only once at first.
* Input          :  
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
//NES_TEXT_SECTION
void K6502_Init(void);
/*******************************************************************************
* Function Name  : K6502_Reset
* Description    : Reset CPU.
* Input          :  
* Output         : none                                            
* Return         : none
*******************************************************************************/
//NES_TEXT_SECTION
void K6502_Reset(void);
/*******************************************************************************
* Function Name  : K6502_Set_Int_Wiring
* Description    : Set up wiring of the interrupt pin
* Input          :  
* Output         : none                                            
* Return         : none
*******************************************************************************/
void K6502_Set_Int_Wiring(BYTE byNMI_Wiring, BYTE byIRQ_Wiring );
/*******************************************************************************
* Function Name  : K6502_Step
* Description    :  Only the specified number of the clocks execute Op.
* Input          : WORD wClocks (Read) The number of the clocks
* Output         : none                                            
* Return         : none
*******************************************************************************/
void K6502_Step( WORD wClocks );

#endif
