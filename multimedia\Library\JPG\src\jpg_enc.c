/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"

#if JPG_EXIF_EN
ALIGNED(4) const EXIF_HEADER_T exif_head = {
	.soi_maker		= JPEG_SOI,
	.app1_marker	= JPEG_APP1,  //JPEG_APP1
	.app1_size		= (((sizeof(EXIF_HEADER_T) - 4)&0xff)<<8)|((sizeof(EXIF_HEADER_T) - 4)>>8),
	.app1_tag		= {'E','x','i','f', 0, 0}, //"Exif  "
	.tiff_head		= {
		.byte_oder  		= {'I','I'},//"II" --intel small endian, "MM" - motorola big endian
		.tag_marker 		= 0x002a,
		.offset_next_ifd	= sizeof(TIFF_HEAD_T), //TIFF_HEAD_T start to first IFD
	},
	.ifd0			= {
		.entry_num		=  IFD0_ENTRY_CNT,//IFD0_ENTRY_CNT
		.entry			= {
			[0]	= {
				.tag		= EXIF_Make,	//
				.type		= IFD_COM_STRING, //IFD_COMPONENT_TYPE
				.cnt		= 8,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 10 + sizeof(IFD_ENTRY_T)*IFD0_ENTRY_CNT + 4,  //start_pos(entry0_data)- start_pos(tiff_head)
			},
			[1]	= {
				.tag		= EXIF_Model,	//
				.type		= IFD_COM_STRING, //IFD_COMPONENT_TYPE
				.cnt		= 2,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= ((u8)'H')|((u8)'D')<<8, 
			},	
			[2]	= {
				.tag		= EXIF_Orientation,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 1, 
			},	
			[3]	= {
				.tag		= EXIF_XResolution,	//
				.type		= IFD_COM_INT64U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 10 + sizeof(IFD_ENTRY_T)*IFD0_ENTRY_CNT + 4 + 8,  //start_pos(entry3_data)- start_pos(tiff_head), 
			},	
			[4]	= {
				.tag		= EXIF_YResolution,	//
				.type		= IFD_COM_INT64U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 10 + sizeof(IFD_ENTRY_T)*IFD0_ENTRY_CNT + 4 + 8 + 8,  //start_pos(entry4_data)- start_pos(tiff_head), 
			},	
			[5]	= {
				.tag		= EXIF_ResolutionUnit,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 2, 
			},		
			[6]	= {
				.tag		= EXIF_Software,	//
				.type		= IFD_COM_STRING, //IFD_COMPONENT_TYPE
				.cnt		= 16,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 10 + sizeof(IFD_ENTRY_T)*IFD0_ENTRY_CNT + 4 + 8 + 8 + 8,  //start_pos(entry6_data)- start_pos(tiff_head), 
			},	
			[7]	= {
				.tag		= EXIF_ModifyDate,	//
				.type		= IFD_COM_STRING, //IFD_COMPONENT_TYPE
				.cnt		= 20,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 10 + sizeof(IFD_ENTRY_T)*IFD0_ENTRY_CNT + 4 + 8 + 8 + 8 + 16,  //start_pos(entry7_data)- start_pos(tiff_head), 
			},	
			[8]	= {
				.tag		= EXIF_YCbCrPositioning,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 2, 
			},		
			[9]	= {
				.tag		= EXIF_ExifOffset,	//
				.type		= IFD_COM_INT32U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 10 + sizeof(IFD_ENTRY_T)*IFD0_ENTRY_CNT + 4 + 8 + 8 + 8 + 16 + 20,  //start_pos(sub_ifd0)- start_pos(tiff_head),  
			},					
		},
		.offset_next_ifd	= 0x0000, //0x0304
		.entry0_data		= {'H','X','-','T','e','c','h',' '},
		.entry3_data		= {0x48, 0x00,0x00,0x00, 0x01,0x00,0x00,0x00}, //72dpi		
		.entry4_data		= {0x48, 0x00,0x00,0x00, 0x01,0x00,0x00,0x00}, //72dpi	
		.entry6_data 		= {0},//{'H','X','3','3','0','X','-','V','0','0','1','.','0','0','1'},
		.entry7_data		= {0}, //updata date time		
	}, 
	.sub_ifd0			= {
		.entry_num		=  SUBIFD0_ENTRY_CNT,//IFD0_ENTRY_CNT
		.entry			= {
			[0]	= {
				.tag		= EXIF_ExposureTime,	//
				.type		= IFD_COM_INT64U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 8 + sizeof(EXIF_IFD_T) + 2 + sizeof(IFD_ENTRY_T)*SUBIFD0_ENTRY_CNT + 4,  //start_pos(entry0_data)- start_pos(tiff_head)
			},
			[1]	= {
				.tag		= EXIF_FNumber,	//
				.type		= IFD_COM_INT64U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 8 + sizeof(EXIF_IFD_T) + 2 + sizeof(IFD_ENTRY_T)*SUBIFD0_ENTRY_CNT + 4 + 8,  //start_pos(entry1_data)- start_pos(tiff_head) 
			},	
			[2]	= {
				.tag		= EXIF_ExposureProgram,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 2, 
			},	
			[3]	= {
				.tag		= EXIF_ISO,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 100,  //ISO-100
			},	
			[4]	= {
				.tag		= EXIF_ExifVersion,	//
				.type		= IFD_COM_UNDEFINED, //IFD_COMPONENT_TYPE
				.cnt		= 4,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0x30323230,  //"0220"
			},	
			[5]	= {
				.tag		= EXIF_DateTimeOriginal,	//
				.type		= IFD_COM_STRING, //IFD_COMPONENT_TYPE
				.cnt		= 20,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 8 + sizeof(EXIF_IFD_T) + 2 + sizeof(IFD_ENTRY_T)*SUBIFD0_ENTRY_CNT + 4 + 8 + 8,  //start_pos(entry5_data)- start_pos(tiff_head) 
			},		
			[6]	= {
				.tag		= EXIF_CreateDate,	//
				.type		= IFD_COM_STRING, //IFD_COMPONENT_TYPE
				.cnt		= 20,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 8 + sizeof(EXIF_IFD_T) + 2 + sizeof(IFD_ENTRY_T)*SUBIFD0_ENTRY_CNT + 4 + 8 + 8 + 20,  //start_pos(entry6_data)- start_pos(tiff_head) 
			},	
			[7]	= {
				.tag		= EXIF_ComponentsConfiguration,	// 	
				.type		= IFD_COM_UNDEFINED, //IFD_COMPONENT_TYPE
				.cnt		= 4,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0x00030201, //YCbCr//0:-,1:Y,2:Cb,3:Cr, 4:R, 5:G, 6:B
			},	
			[8]	= {
				.tag		= EXIF_CompressedBitsPerPixel,	//
				.type		= IFD_COM_INT64U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 8 + sizeof(EXIF_IFD_T) + 2 + sizeof(IFD_ENTRY_T)*SUBIFD0_ENTRY_CNT + 4 + 8 + 8 + 20 + 20,  //start_pos(entry8_data)- start_pos(tiff_head) 
			},		
			[9]	= {
				.tag		= EXIF_BrightnessValue,	//
				.type		= IFD_COM_INT64S, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 8 + sizeof(EXIF_IFD_T) + 2 + sizeof(IFD_ENTRY_T)*SUBIFD0_ENTRY_CNT + 4 + 8 + 8 + 20 + 20 + 8,  //start_pos(entry9_data)- start_pos(tiff_head) 
			},		
			[10]	= {
				.tag		= EXIF_ExposureCompensation,	//
				.type		= IFD_COM_INT64S, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 8 + sizeof(EXIF_IFD_T) + 2 + sizeof(IFD_ENTRY_T)*SUBIFD0_ENTRY_CNT + 4 + 8 + 8 + 20 + 20 + 8 + 8,  //start_pos(entry10_data)- start_pos(tiff_head) 
			},	
			[11]	= {
				.tag		= EXIF_MaxApertureValue,	//
				.type		= IFD_COM_INT64U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 8 + sizeof(EXIF_IFD_T) + 2 + sizeof(IFD_ENTRY_T)*SUBIFD0_ENTRY_CNT + 4 + 8 + 8 + 20 + 20 + 8 + 8 + 8,  //start_pos(entry11_data)- start_pos(tiff_head) 
			},	
			[12]	= {
				.tag		= EXIF_MeteringMode,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 4,  //0:Unknown, 1:Average, 2:Center-weighted average, 3:Spot, 4:Multi-spot, 5:Multi-segment,6:Partial,255:Other
			},		
			[13]	= {
				.tag		= EXIF_LightSource,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0,  //0:Unknown, 1:Daylight, 2:Fluorescent, 3:Tungsten (Incandescent) , 4:Flash, 
									//9:Fine Weather, 10:Cloudy, 11:Shade, 12:Daylight Fluorescent , 13:Day White Fluorescent, 
									//14:Cool White Fluorescent, 15:White Fluorescent, 16:Warm White Fluorescent, 17:Standard Light A , 18:Standard Light B, 
									//19:Standard Light C , 20:D55, 21:D65, 22:D75 , 23:D50,24:ISO Studio Tungsten, 255: Other
			},	
			[14]	= {
				.tag		= EXIF_Flash,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0,  //0:No Flash; 1:Fired; 5:Fired, Return not detected; 7:Fired, Return detected; 8: On, Did not fire; 
									//9:On, Fired; 0xd:On, Return not detected; 0xf:On, Return detected;0x10:Off, Did not fire
									//0x14:Off, Did not fire, Return not detected, 0x18:Auto, Did not fire, 0x19: Auto, Fired
									//0x1d:Auto, Fired, Return not detected; 0x1f:Auto, Fired, Return detected;
									//0x20: No flash function; 0x30: Off, No flash function; 0x41:Fired, Red-eye reduction;
									//0x45:Fired, Red-eye reduction, Return not detected; 0x47: Fired, Red-eye reduction, Return detected;
									//0x49:On, Red-eye reduction; 0x4d:On, Red-eye reduction, Return not detected;0x4f:On, Red-eye reduction, Return detected
									//0x50:Off, Red-eye reduction; 0x58:Auto, Did not fire, Red-eye reduction; 0x59:Auto, Fired, Red-eye reduction
									//0x5d:Auto, Fired, Red-eye reduction, Return not detected; 0x5f:Auto, Fired, Red-eye reduction, Return detected		
			},	
			[15]	= {
				.tag		= EXIF_FocalLength,	//
				.type		= IFD_COM_INT64U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 8 + sizeof(EXIF_IFD_T) + 2 + sizeof(IFD_ENTRY_T)*SUBIFD0_ENTRY_CNT + 4 + 8 + 8 + 20 + 20 + 8 + 8 + 8 + 8,  //start_pos(entry15_data)- start_pos(tiff_head) 
			},	 
			[16]	= {
				.tag		= EXIF_SubjectArea,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 4,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 8 + sizeof(EXIF_IFD_T) + 2 + sizeof(IFD_ENTRY_T)*SUBIFD0_ENTRY_CNT + 4 + 8 + 8 + 20 + 20 + 8 + 8 + 8 + 8 + 8,  //start_pos(entry16_data)- start_pos(tiff_head) 
			},	 
			[17]	= {
				.tag		= EXIF_FlashpixVersion,	//
				.type		= IFD_COM_UNDEFINED, //IFD_COMPONENT_TYPE
				.cnt		= 4,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0x30303130, //"0100"
			},	 
			[18]	= {
				.tag		= EXIF_ColorSpace,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0x1, //0x1 = sRGB;0x2 = Adobe RGB;0xfffd = Wide Gamut RGB;0xfffe = ICC Profile;0xffff = Uncalibrated
			},		
			[19]	= {
				.tag		= EXIF_ExifImageWidth,	//
				.type		= IFD_COM_INT32U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0, //width
			},	
			[20]	= {
				.tag		= EXIF_ExifImageHeight,	//
				.type		= IFD_COM_INT32U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0, //height
			},		
			[21]	= {
				.tag		= EXIF_InteropOffset,	//
				.type		= IFD_COM_INT32U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0x2e6, //??
			},	
			[22]	= {
				.tag		= EXIF_FileSource,	//
				.type		= IFD_COM_UNDEFINED, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 3, //1 = Film Scanner;2 = Reflection Print Scanner;3 = Digital Camera;"\x03\x00\x00\x00" = Sigma Digital Camera	
			},	
			[23]	= {
				.tag		= EXIF_SceneType,	//
				.type		= IFD_COM_UNDEFINED, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 1, //1 = Directly photographed
			},	
			[24]	= {
				.tag		= EXIF_CustomRendered,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0, // 0:normal process ,1:custom process
			},	
			[25]	= {
				.tag		= EXIF_ExposureMode,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0, // 0:auto ,1:manual ,2:auto bracket 
			},	
			[26]	= {
				.tag		= EXIF_WhiteBalance,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0, // 0:auto ,1:manual
			},	
			[27]	= {
				.tag		= EXIF_DigitalZoomRatio,	//
				.type		= IFD_COM_INT64U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 8 + sizeof(EXIF_IFD_T) + 2 + sizeof(IFD_ENTRY_T)*SUBIFD0_ENTRY_CNT + 4 + 8 + 8 + 20 + 20 + 8 + 8 + 8 + 8 + 8 + 8,  //start_pos(entry27_data)- start_pos(tiff_head) 
			},		
			[28]	= {
				.tag		= EXIF_FocalLengthIn35mmFormat,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0,//
			},	
			[29]	= {
				.tag		= EXIF_SceneCaptureType,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0,// 0:standard 1: landscape 2: portrait 3:night scene
			},	
			[30]	= {
				.tag		= EXIF_GainControl,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0,// 0:none ,1:low gain up 2:high gain up 3:low gain down ,4: low gain down
			},		
			[31]	= {
				.tag		= EXIF_Contrast,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0,// 0:normal ,1:soft ,2:hard
			},		
			[32]	= {
				.tag		= EXIF_Saturation,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0,// 0:normal ,1:low saturation ,2:high saturation
			},		
			[33]	= {
				.tag		= EXIF_Sharpness,	//
				.type		= IFD_COM_INT16U, //IFD_COMPONENT_TYPE
				.cnt		= 1,	// 存储的是 Directory Entry 对应的Component的数量
				.value_off	= 0,// 0:normal ,1:soft ,2:hard
			},			
		},
		.offset_next_ifd	= 0x00, //no link to next ifd
		.entry0_data		= {0x0a, 0x00,0x00,0x00, 0xe1,0x03,0x00,0x00}, //EXIF_ExposureTime 1/99s = 0x0a/0x3e1		
		.entry1_data		= {0x20, 0x00,0x00,0x00, 0x0a,0x00,0x00,0x00}, //EXIF_FNumber f/3.2( 3.2 = 0x20/0x0a)		
		.entry5_data		= {0}, //EXIF_DateTimeOriginal updata date time		
		.entry6_data		= {0}, //EXIF_CreateDate updata date time	
		.entry8_data		= {0x02, 0x00,0x00,0x00, 0x01,0x00,0x00,0x00}, //EXIF_CompressedBitsPerPixel
		.entry9_data		= {0x78, 0xec,0xff,0xff, 0xe8,0x03,0x00,0x00}, //EXIF_BrightnessValue = 0xFFFFEC78/0X3E8 = -5000/1000
		.entry10_data		= {0x0a, 0x00,0x00,0x00, 0x0a,0x00,0x00,0x00}, //EXIF_ExposureCompensation = 0x0a/0x0a = +1
		.entry11_data		= {0x03, 0x00,0x00,0x00, 0x02,0x00,0x00,0x00}, //EXIF_MaxApertureValue = 0x03/0x02 = +1.5
		.entry15_data		= {0x52, 0x00,0x00,0x00, 0x0b,0x00,0x00,0x00}, //EXIF_FocalLength = 0x52/0x0b = 7mm
		.entry16_data		= {0x10, 0x05,0xcc,0x03, 0x20,0x0a,0x98,0x07}, //EXIF_SubjectArea = 	
		.entry27_data		= {0x00, 0x00,0x00,0x00, 0x01,0x00,0x00,0x00}, //EXIF_DigitalZoomRatio = 0x00/0x01 = 0
	}, 
}; 
#endif
/*******************************************************************************
* Function Name  : jpg_encode
* Description    : jpg encode write
* Input          : JPG_ENC_ARG *arg
* Output         : NONE
* Return         : 0: success, <0:fail
*******************************************************************************/
int jpg_encode(JPG_ENC_ARG *arg)
{
	int ret;
	if(arg == NULL || arg->buf == NULL)
		return -1;
#if JPG_EXIF_EN	
	
	EXIF_HEADER_T *p_head = (EXIF_HEADER_T *)hal_sysMemMalloc(sizeof(EXIF_HEADER_T));
	u8 datatime[20];
	if(p_head == NULL)
	{
		//deg_Printf("JPG EXIF malloc fail\n");
		return -1;
	}
	memcpy((void*)p_head,(void*)&exif_head,sizeof(EXIF_HEADER_T));
	memcpy((void*)datatime,(void*)hal_rtcTime2String(hal_rtcTimeGet()), 20);
	datatime[4] = datatime[7] = ':';
	datatime[19] = 0;
	memcpy((void*)p_head->ifd0.entry7_data,(void*)datatime, 20);
	memcpy((void*)p_head->sub_ifd0.entry5_data,(void*)datatime, 20);
	memcpy((void*)p_head->sub_ifd0.entry6_data,(void*)datatime, 20);
	p_head->sub_ifd0.entry[19].value_off = arg->dst_width;
	p_head->sub_ifd0.entry[20].value_off = arg->dst_height;
	//deg_Printf("exif head write\n");	
	ret = fs_write(arg->fd,(const void*)p_head,sizeof(EXIF_HEADER_T));
	hal_sysMemFree((void*)p_head);
	if(ret < 0)
		return ret;
	arg->buf  += 0x14;
	arg->size -= 0x14;
	return 1;
	//deg_Printf("exif head write end\n");
#else
	return 0;
#endif
}


