/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  USER_CONFIG_API_H
    #define  USER_CONFIG_API_H
	
#include "user_config_typedef.h"

//用户需要根据自己的板子的硬件接口来include对应的 “user_hardware_cfg_xxx.h” 文件
#if CURRENT_CHIP == FPGA
	#include "user_hardware_cfg_fpga.h"
#else
	//#include "user_hardware_cfg_hx3302B_demo.h"
	#include "user_hardware_cfg_hx3302B_L100.h"
#endif

//---------file dir CFG--------------------------------------------------------------------------------------------------
#define  FILEDIR_REC 				"DCIM/"//"REC/"
#define  FILEDIR_IMG  			    "DCIM/"//"IMG/"
#define  FILEDIR_WAV  			    "WAV/"
#define  FILEDIR_MP3  				"/"//"MP3/" 
#define  FILEDIR_NES  				"GAME/"
//--------------RESOURCE LOAD CFG-----------------------------------------------------------------------------------------
#define  VERSION_LOAD_AUTO     		0 				  	//定义软件版本是否自动从资源获取

#define  SYSTEM_VERSION     	 	" "//"HX330X_KID_V1.0.0"  	//当定义 VERSION_LOAD_AUTO为0时，软件版本由此宏定义
#if CMOS_SENSOR_SWITCH_EN == 1
	#if LCD_TAG_SELECT == LCD_MCU_ST7789P3
		#define  VERSION_TIPS     	 	"7789P3-20A1-20A1 V1.0"
		#if SENSOR_MIPI_VGA_GC030A == 1
			#define  VERSION_TIPS     	 	"7789P3-030A-030A V1.0"
		#endif
	#else
		#define  VERSION_TIPS     	 	"3030B-20A1-20A1 V1.0"//"HX330X_KID_V1.0.0"  	//当定义 VERSION_LOAD_AUTO为0时，软件版本由此宏定义
		#if SENSOR_MIPI_VGA_GC030A == 1
			#define  VERSION_TIPS     	 	"3030B-030A-030A V1.0"

		#endif
	#endif
#else
	#if LCD_TAG_SELECT == LCD_MCU_ST7789P3l
		#define  VERSION_TIPS     	 	"7789P3-20A1 V1.1"
		#if SENSOR_MIPI_VGA_GC030A == 1
			#define  VERSION_TIPS     	 	"7789P3-030A V1.1"
		#endif
	#else
		#define  VERSION_TIPS     	 	"3030B-(20A1) V1.1"
		#if SENSOR_MIPI_VGA_GC030A == 1
			#define  VERSION_TIPS     	 	"3030B-0339 V1.0"
		#endif
	#endif
#endif
#define  SYSTEM_VERSION     	 "20250626"
// #define  SYSTEM_DATETIME			"20240327"
#define  KEYSOUND_LOAD_AUTO    		1  						// 定义是否从资源load按键音

#define  DATETIME_LOAD_AUTO    		0  						// 定义是否从资源获取默认的系统时间


//----------------------SYS FUNCTION CFG----------------------------------------------------------------------------------
//-------lcd  show cfg-----

 //1: 支持LCD屏显画面放大缩小; 0: 不支持LCD缩放
#define  LCDSHOW_CSI_SCALE      	1

//可配置在原有屏的基础上VIDEO层旋转：
//LCD_DISPLAY_ROTATE_NONE / LCD_DISPLAY_ROTATE_0 ：保持不变
//LCD_DISPLAY_ROTATE_90 ：逆时针旋转90度
//LCD_DISPLAY_ROTATE_180 ：逆时针旋转180度
//LCD_DISPLAY_ROTATE_270 ：逆时针旋转270度
//LCD_DISPLAY_MIRROR_NONE : 保持不变
//LCD_DISPLAY_V_MIRROR ： 水平镜像
//LCD_DISPLAY_H_MIRROR ：垂直镜像
#define  LCDSHOW_VIDEO_ROTATE_MORE_MODE		(LCD_DISPLAY_ROTATE_NONE |LCD_DISPLAY_MIRROR_NONE) 

//可配置在原有屏的基础上UI层旋转：(注意客户需要自行考虑UI)
//LCD_DISPLAY_ROTATE_NONE / LCD_DISPLAY_ROTATE_0 ：保持不变
//LCD_DISPLAY_ROTATE_90 ：逆时针旋转90度
//LCD_DISPLAY_ROTATE_180 ：逆时针旋转180度
//LCD_DISPLAY_ROTATE_270 ：逆时针旋转270度
#define  LCDSHOW_UI_ROTATE_MORE_MODE		(LCD_DISPLAY_ROTATE_NONE) 

//配置缩放比例，配置为0是表示最大比例缩放，即填满全屏 (注意客户需要自行考虑UI)
#define  LCDSHOW_RATIO_CFG					LCD_RATIO_MAKE(0, 0) 

//-------FUN CFG------------
#define  FUN_AUTOPOWEROFF_TIME				10 // 定义电池供电情况下，自动关机时间，单位：秒

#define  FUN_MOTION_DEC_TIME    			20 // 定义动态侦测启动录像时，录像时间长度，单位：秒



#define  FUN_AUDIO_RECORD_EN       			0	//1: 打开录音和播放录音任务

#define  FUN_VIDEO_SYNC_WRITE				0   //1: 定义录像文件定时回写，用于录像过程意外掉电时，录像文件仍旧能正常播放
							  
//以下定义是否支持MP3播放
#define  FUN_MP3_PLAY_EN       		        (1&MP3_EN)	//1: Enable MP3 play function		
//以下定义是否支持NES游戏
#define  FUN_NES_GAME_EN       		        (0&NES_FUNC_SUPPORT)	//1: Enable NES game function,注意NES使能时，使用了调色板128~191号颜色

//以下定义是否支持相框
//以下定义是否支持相框
#define  FUN_KID_FRAME_EN					(1&KID_FRAME_FUNC_SUPPORT)	//1: enable lcd kid frame fun



#define  FUN_RECORD_STOP_WIN_SHOW			1   //1: enable record manual stop win show
#define  FUN_RECORD_STOP_WIN_TYPE			LCDSHOW_WIN_RIGHTTOP //可配 LCDSHOW_WIN_LEFTTOP~LCDSHOW_WIN_DOWN
#define  FUN_RECORD_STOP_WIN_STEP			20  // 1~100

#define  FUN_PLAYBACK_DEL_WIN_SHOW			1   //1: enable record manual stop win show
#define  FUN_PLAYBACK_DEL_WIN_TYPE			LCDSHOW_WIN_RIGHTDONW //可配 LCDSHOW_WIN_LEFTTOP~LCDSHOW_WIN_DOWN
#define  FUN_PLAYBACK_DEL_WIN_STEP			20  // 1~100

#define  FUN_RECORD_VIDEO_MODE_EX			1   //1: SUPPORT RECORD VIDEO MORE MODE
#define  FUN_RECORD_VIDEO_SLOW_RATE			10	//1 ~ HAL_AVI_FRAMRATE, 表示慢动作比率，值越小，动作越慢。
												//如配置10，则 1秒的视频可播放3秒 (HAL_AVI_FRAMRATE/10 = 3)
#define  FUN_RECORD_VIDEO_DELAY_RATE		3	//1 ~ HAL_AVI_FRAMRATE, 表示延时摄影比率，值越大，动作越快。
												//如配置3，则 3秒的时间实际录像出来的时间为1秒

#define  FUN_VIDEO_PLAY_SPEED				0   //1: 支持文件快进或快退播放

#define  FUN_BATTERY_CHARGE_SHOW			1   //1: 电池充电时显示界面
//-------------DEV CFG(battery, gsensor,ir,key,lcd,led,sd,sensor,usb...)---------------------------------------------------
//-------dev enable and dev io cfg, please check and modify file"dev/dev_api.h"




#define  DEV_SDC_TIMEOUT					2000//定义SD卡读写出错超时时间，单位ms。在录像过程中，拔卡响应时间，建议配置1000ms

#define  DEV_SENSOR_FILTER_EN				1   //0: 滤镜功能关闭，1：滤镜功能打开
#define  DEV_SENSOR_FILTER_NUM				(SENSOR_RGBGAMMA_CLASSES - 6)   //滤镜颜色饱和度 0~(SENSOR_RGBGAMMA_CLASSES-1)

//以下定义是否支持分屏显示
#define  DEV_SENSOR_MAGIC_EN				(1&MAGIC_MIRROR_FUNC_SUPPORT)   //0: 分屏功能关闭，1：分屏功能打开

//以下定义是否支持哈哈镜特效
#define  DEV_SENSOR_LENS_EN					(1&LENS_FUNC_SUPPORT)   //0: 哈哈镜功能关闭，1：哈哈镜功能打开

#define  DEV_SENSOR_SWITCH_WIN_EN		    0   //1: SENSOR切换窗口显示
#define  DEV_SENSOR_SWITCH_STEP				20  // 1~100

#define  DEV_PREVIEW_WIN_EN		    		0   //1: SENSOR开始预览时窗口显示

#define  DEV_SENSOR_DROP_FIRST_FRAME		15//5	//有些sensor第一次出图几帧可能是有问题的，可以通过定义这个丢帧来保存出图显示正常

#define  CHARGE_OUT_OF_USER		    		0   //1: 充电不使用


/*
******以下的客户宏定义只能一个为1，或者所有为0，所有为0时就是公版
*/
 #define	CUSTOMER_ADD_HEBREW						0//客制软件 加希伯来语 删繁体中文
 #define	CUSTOMER_XLT							0//XLT客户的 加波兰语 删繁体中文
 #define	CUSTOMER_ADD_ROMANA_MAGYAR				0//添加罗马尼亚语和匈牙利语 删除日语 韩语
 #define	CUSTOMER_ADD_THAI						0//添加泰语 删除繁体中文	

#define		CUSTOMER_ADD_French						0//FUNKAMUI添加法语

#define		JAPANESE_HIRAGANA						0//日语平假名

#define		CUSTOMER_Poland							0//波兰语最后一个
#define		CUSTOMER_Croatian_Slovenian				1//克罗地亚/斯洛文尼亚

#define		No_games						    	0//1:不带游戏  0:带游戏

#define		Five_menus								0//1:五个菜单  0:六个菜单


#endif




