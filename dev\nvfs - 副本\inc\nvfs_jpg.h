/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  NVFS_JPG_H
    #define  NVFS_JPG_H

#define NV_JPG_MAX_NUM			100//55

//借鉴FAT16文件系统来管理SPI中的JPG文件
//BPB扇区
#define NV_BS_JmpBoot			0		/* x86 jump instruction (3-byte) "\xEB\xFE\x90"*/
#define NV_BS_OEMName			3		/* OEM name (8-byte)  "MSDOS5.0"*/
#define NV_BPB_BytsPerClus		12		/* Sector size [byte] (DWORD) 4096/65536 */
#define NV_BPB_TotClusSz		16		/* Volume size (32-bit) [clus] (DWORD) */
//#define NV_BPB_NumFATs			16		/* Number of FATs (DWORD) */
#define NV_BPB_FATSz32			20		/* FAT size (32-bit) [clus] (DWORD) */
#define NV_BPB_RootEntCnt		24		/* Size of root directory area for FAT [entry] (DWORD) */
#define NV_Free_Count			28		/* FAT32 FSI: Number of free clusters (DWORD) */
#define NV_Nxt_Free				32		/* FAT32 FSI: Last allocated cluster (DWORD) */
#define NV_BPB_RootFreeCnt		36		/* Size of root directory area for FAT [entry] (DWORD) */
#define NV_BS_55AA				510		/* Signature word (WORD) */
//DIR: CLUS SIZE  - 1
#define NV_DIR_Name				0		/* Short file name (4-byte) "0000"~'9999'*/ 
#define NV_DIR_CrtTime			4		/* Created time (DWORD) */
#define NV_DIR_FstClus			8		/* first cluster (DWORD) */
#define NV_DIR_FileSize			12		/* File size (DWORD) */

#define NV_SZDIRE				16		/* Size of a directory entry */
#define NV_DDEM					0xE5	/* Deleted directory entry mark set to DIR_Name[0] */
#define NV_RDDEM				0x05	/* Replacement of the character collides with DDEM */
//FAT表: WORD

#define NV_FAT_CLUS_EOC			0xFFFF	

#define NVWIN_TYPE_DIR			0
#define NVWIN_TYPE_FAT			1
#define NVWIN_TYPE_DAT			2

#define NV_CLUS_SIZE			4096//SF_SECTOR_SIZE


/* File access mode and open method flags (3rd argument of f_open) */
#define	NVFA_READ				0x01
#define	NVFA_WRITE				0x02
#define	NVFA_OPEN_EXISTING		0x00
#define	NVFA_CREATE_NEW			0x04
#define	NVFA_CREATE_ALWAYS		0x08
#define	NVFA_OPEN_ALWAYS		0x10
#define	NVFA_OPEN_APPEND		0x30
/* Additional file access control and file status flags for internal use */
#define NVFA_SEEKEND			0x20	/* Seek to end of the file on file open */
#define NVFA_MODIFIED			0x40	/* File has been modified */

#define NVJPG_MINSIZE			(100*1024)

typedef struct {
	DWORD	dptr;			/* Current dir read/write offset */
	BYTE*	dir_ptr;		/* Pointer to the directory entry in the win[]*/	
	DWORD	index;		
	DWORD	ftime;
	DWORD	sclust;
	DWORD	fsize;
} NV_DIR;
/* File object structure (FIL) */
typedef struct {
	BYTE		stat;			//0: valid 1: invalid
	BYTE		flag;			/* File status flags */
	BYTE		err;			/* Abort flag (error code) */
	BYTE		reserve;
	DWORD		fptr;			/* File read/write pointer (Zeroed on file open) */
	DWORD		clust;			/* Current cluster of fpter (invalid when fptr is 0) */
   	NV_DIR		dir;
} NVFS_FIL;

//DATA区
typedef struct {
	//BYTE	n_fats;			/* Number of FATs (1 or 2) */
	BYTE	wflagDir;		/* win0[] flag (b0:dirty) */
	BYTE	wflagFat;		/* win1[] flag (b0:dirty) */
	BYTE	wflagDat;		/* win1[] flag (b0:dirty) */
	BYTE	reserve;
	DWORD	n_rootdir;		/* Number of root directory entries (FAT12/16) */
	DWORD	free_rootdir;		/* Number of root directory entries (FAT12/16) */
	//DWORD	csize;			/* Cluster size [sectors] */
	DWORD	last_clst;		/* Last allocated cluster */
	DWORD	free_clst;		/* Number of free clusters */
	DWORD	n_fatent;		/* Number of FAT entries (number of clusters) */
	DWORD	fsize;			/* Size of an FAT [clusters] */
	DWORD	fatbase;		/* FAT base clusters */
	//DWORD	dirbase;		/* Root directory base sector/cluster */
	DWORD	database;		/* Data base clusters */
	
	DWORD	winDirclus;		/* Current clusters appearing in the win[] */
	DWORD	winFatclus;		/* Current clusters appearing in the win[] */
	DWORD	winDatclus;		/* Current clusters appearing in the win[] */
	NVFS_FIL fil;
	BYTE	winDir[NV_CLUS_SIZE];		/* Disk access window for BPB, Directory */
	BYTE	winFat[NV_CLUS_SIZE];		/* Disk access window for FAT */
	BYTE	winDat[NV_CLUS_SIZE];		/* Disk access window for FAT */
} NV_JPGFS;

typedef struct{
	u32     	startAddr;
	u32     	remainSize;	
	NV_JPGFS	*fs;
}NV_JPG_OP;

/* File function return code (FRESULT) */

typedef enum {
	NV_OK = 0,				/* (0) Succeeded */
	NV_INT_ERR,				/* (1) Assertion failed */
    NV_DISK_ERR,			/* (2) A hard error occurred in the low level disk I/O layer */
	NV_MEM_ERR,				/* (3) MEMORY failed */
	NV_NO_FILE,				/* (4) Could not find the file */
	NV_DENIED,				/* (5) Access denied due to prohibited access or directory full */
	NV_EXIST,				/* (6) Access denied due to prohibited access */
} NVRESULT;

void nv_jpg_ex_init(u32 resAddress, u32 resSize);

NVRESULT nv_jpg_init(void);
void nv_jpg_uinit(void);
NVRESULT nv_dir_readfirst(NV_DIR* dp);
NVRESULT nv_dir_readnext (NV_DIR* dp);
NVRESULT nv_jpg_open (u32 index,BYTE mode);
NVRESULT nv_jpg_close (void);
NVRESULT nv_jpgfile_read (void* buff,u32  btr,	u32* br);
NVRESULT nv_jpgfile_write (const void* buff,u32 btw,u32* bw);
NVRESULT nv_jpgfile_seek (u32 ofs,DWORD opt);
NVRESULT nv_jpgfile_delete(u32 index);
INT32U nv_jpgfile_size(void);
INT32U nvjpg_free_size(void);
INT32U nvjpg_free_dir(void);
NVRESULT nv_jpg_format(void);












#endif







