/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
static u8 usb_have_change;
static u8 user_usbdev_state;

/*******************************************************************************
* Function Name  : sysComKeyMsgAll
* Description    : sysComKeyMsgAll: OK/UP/DOWN/MENU/MODE
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int sysComKeyMsgAll(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_auto_poweroff(1);
		task_com_sreen_check(SREEN_RESET_AUTOOFF);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : sysComKeyMsgPowerOff
* Description    : sysComKeyMsgPowerOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int sysComKeyMsgPowerOff(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_auto_poweroff(1);
	#if  FUN_BATTERY_CHARGE_SHOW
		if(app_taskCurId() != TASK_BAT_CHARGE)
	#endif
		{
			deg_Printf("[SYS] deal:power off\n");
			app_taskStart(TASK_POWER_OFF,0);
		}

	}
	return 0;
}
/*******************************************************************************
* Function Name  : sysComSysMsgUSB
* Description    : sysComSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
u32 usbdev_state;
u32 off_5v;
static int sysComSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{
	u32 usbdev_state = USBDEV_STAT_MAX;
	if(parameNum == 1)
		usbdev_state = parame[0];
	if(usbdev_state >= USBDEV_STAT_MAX)
		return 0;
	task_com_sreen_check(SREEN_RESET_AUTOOFF);
	if(usbdev_state != USBDEV_STAT_NULL)  // DC IN
	{
		task_com_auto_poweroff(1);
	}
	if(app_taskCurId() != TASK_BAT_CHARGE)
	{
		if(usbdev_state == USBDEV_STAT_PC)
		{
			deg_Printf("22222222222\n");
			app_taskStart(TASK_USB_DEVICE,0);
			off_5v=0;
		}else{
			deg_Printf("111111111111111111111111\n");
			off_5v=1;

		}
	//	hal_lcd_pause_set(0);
	}


	return 0;
}
/*******************************************************************************
* Function Name  : sysComSysMsgSD
* Description    : sysComSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int sysComSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{

	u32 sd_state = SDC_STAT_MAX;
	if(parameNum == 1)
		sd_state = parame[0];
	if(sd_state >= SDC_STAT_MAX)
		return 0;
	task_com_sreen_check(SREEN_RESET_AUTOOFF);
	task_com_auto_poweroff(1);
	//if(sd_state == SDC_STAT_NORMAL)
	//{
    //   int ret;
	//	ret = fs_open((const char *)"SELFTEST.bin",FA_READ);
	//	if(ret>=0)
	//	{
	//		fs_close(ret);
	//		uiOpenWindow(&selfTestWindow,0,0);
	//	}
	//}
	return 0;
}
/*******************************************************************************
* Function Name  : sysComSysMsgBattery
* Description    : sysComSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int sysComSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{
	u32 bat_state = BATTERY_STAT_MAX;
	if(parameNum == 1)
		bat_state = parame[0];
	if(bat_state >= BATTERY_STAT_MAX)
		return 0;

	task_com_tips_show(TIPS_TYPE_POWER);
	return 0;
}
/*******************************************************************************
* Function Name  : sysComSysMsg1S
* Description    : sysComSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int sysComSysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
	taskID curTask = app_taskCurId();
	INT32U  stat = MEDIA_STAT_STOP;
	switch(curTask)
	{
		case TASK_RECORD_VIDEO: stat = videoRecordGetStatus(); break;
		case TASK_PLAY_VIDEO:	stat = videoPlaybackGetStatus(); break;
		case TASK_PLAY_MP3:		if(mp3_dec_sta() >= MP3_DEC_START)  stat = MEDIA_STAT_START; break;

		case TASK_RECORD_AUDIO: stat = audioRecordGetStatus(); break;
		case TASK_PLAY_AUDIO:   stat = audioPlaybackGetStatus(); break;
		case TASK_USB_DEVICE:
		case TASK_SD_UPDATE:	stat = MEDIA_STAT_START; break;
		//case TASK_BAT_CHARGE: break;
		//case TASK_POWER_OFF:  break;
		//case TASK_MAIN:		  break;
		//case TASK_SETTING:		break;
		//case TASK_NES_GAME:		break;
		//case TASK_RECORD_PHOTO: break;
		default: break;
	}
	if(stat == MEDIA_STAT_START  )
	{
		task_com_auto_poweroff(1);
	}
	return 0;

}
static int sysComSysMsg500MS(winHandle handle,u32 parameNum,u32* parame)
{
	/*if(usb_have_change)
	{
		usb_have_change = 0;
		if(user_usbdev_state == USBDEV_STAT_DCIN)
		{
			if(app_taskCurId() == TASK_MAIN)
				app_taskStart(TASK_BAT_CHARGE,0);
		}
	}else if(SysCtrl.dev_dusb_stat == USBDEV_STAT_DCIN)//开机状态下 切换到主界面时 插电下就要切换到充电
	{
		if(app_taskCurId() == TASK_MAIN)
			app_taskStart(TASK_BAT_CHARGE,0);

	}*/
	static i=0;
	if(off_5v)
	{
		i++;
	}else{
		i=0;
	}
	if(i>3){
		if(app_taskCurId() !=TASK_BAT_CHARGE)
		{
			app_taskStart(TASK_BAT_CHARGE,1);
		}

	}

}

ALIGNED(4) const msgDealInfor sysComMsgDeal[]=
{
	{KEY_EVENT_OK,		sysComKeyMsgAll},
	{KEY_EVENT_UP,		sysComKeyMsgAll},
	{KEY_EVENT_DOWN,	sysComKeyMsgAll},
	{KEY_EVENT_LEFT,	sysComKeyMsgAll},
	{KEY_EVENT_RIGHT,	sysComKeyMsgAll},

	{KEY_EVENT_POWER,	sysComKeyMsgAll},

	{KEY_EVENT_POWEROFF,sysComKeyMsgPowerOff},
	{SYS_EVENT_USBDEV,	sysComSysMsgUSB},
	{SYS_EVENT_SDC,		sysComSysMsgSD},
	{SYS_EVENT_BAT,		sysComSysMsgBattery},
	{SYS_EVENT_1S,		sysComSysMsg1S},
#if CHARGE_OUT_OF_USER 
	{SYS_EVENT_500MS,	 sysComSysMsg500MS},

#endif


	{EVENT_MAX,NULL},
};




