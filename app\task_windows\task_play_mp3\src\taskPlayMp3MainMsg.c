/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskPlayMp3MainWin.c"
static int resSum;
static int startShowNum;
/*******************************************************************************
* Function Name  : playMp3SubPlayLrcShow
* Description    : playMp3SubPlayLrcShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playMp3MainLrcShow(winHandle handle)
{
	int i,end,cnt;
	char* str;
	if(mp3_lrc_showOn() == 0)
	{
/*		if(mp3_title_get())
		{
			uiWinSetResidByNum(winItem(handle,PLAYMP3SUB_LRC_ID),RAM_ID_MAKE(mp3_title_get()),		0, 0);
			uiWinSetResidByNum(winItem(handle,PLAYMP3SUB_LRC_ID),RAM_ID_MAKE(mp3_orchestra_get()),	1, 0);
			uiWinSetResidByNum(winItem(handle,PLAYMP3SUB_LRC_ID),RAM_ID_MAKE(mp3_Album_get()),		2, 0);

			uiWinSetResSum(winItem(handle,PLAYMP3SUB_LRC_ID),3);
		}
		else*/
		{
			uiWinSetResidByNum(winItem(handle,PLAYMP3MAIN_LRC_ID),RAM_ID_MAKE(" "),	0, 0);
			uiWinSetResSum(winItem(handle,PLAYMP3MAIN_LRC_ID),1);
		}
		return;
	}
	for(i = startShowNum; i<=0; i++)
	{
		if(mp3_lrc_string_get(i))
			break;
	}
	//start 	= i;
	end		= i + resSum;
	cnt		= 0;
	for(;i <= end;i++)
	{
		str = (char *) mp3_lrc_string_get(i);
		if(str == NULL)
			str= " ";
		//else
		//	deg_Printf("str:%x, %s\n",(u32)str, str);
		if(i == 0)
			uiWinSetResidByNum(winItem(handle,PLAYMP3MAIN_LRC_ID),RAM_ID_MAKE(str),	cnt++, 1);
		else
			uiWinSetResidByNum(winItem(handle,PLAYMP3MAIN_LRC_ID),RAM_ID_MAKE(str),	cnt++, 0);
	}
	uiWinSetResSum(winItem(handle,PLAYMP3MAIN_LRC_ID),resSum);
}
/*******************************************************************************
* Function Name  : playMp3MainOpenWin
* Description    : playMp3MainOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	taskPlayMp3RootDirOpen();
	playMp3MainSDShow(handle);
	playMp3MainVolumeShow(handle);
	playMp3MainBaterryShow(handle);
	playMp3MainStatShow(handle, 0);
	playMp3MainFileNameShow(handle, 0);
	playMp3MainTimeShow(handle, 0);
	playMp3MainFreqShow(handle, 0);
	playMp3MainFindFileAndStart(0);
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainCloseWin
* Description    : playMp3MainCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainKeyMsgOk
* Description    : playMp3MainKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{	
		if( mp3_dec_sta() >= MP3_DEC_START ) //当前正在播放的MP3文件，可能是PAUSE或者RESUME或者START
		{
			switch(mp3_dac_sta())
			{
				case MP3_DAC_START:
				case MP3_DAC_RESUME: 	mp3_dac_pause();  playMp3MainStatShow(handle, 1); break;
				//case MP3_DAC_STOP:
				case MP3_DAC_PAUSE:		mp3_dac_resume(); playMp3MainStatShow(handle, 0); break;
				default: break;

			}
		}

	}

	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainKeyMsgUp
* Description    : playMp3MainKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		if(SysCtrl.curVolume < (task_com_Volume_MaxIndex() - 1 ))
		{
			SysCtrl.curVolume++;
			task_com_curVolume_cfg();
			mp3_dac_volume_cfg(task_com_curVolume_get());
			playMp3MainVolumeShow(handle);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainKeyMsgDown
* Description    : playMp3MainKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		if(SysCtrl.curVolume > 0)
		{
			SysCtrl.curVolume--;
			task_com_curVolume_cfg();
			mp3_dac_volume_cfg(task_com_curVolume_get());
			playMp3MainVolumeShow(handle);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainKeyMsgMode
* Description    : playMp3MainKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainKeyMsgLeft(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	int preIndex;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(SysCtrl.file_cnt <= 0)
		{
			return 0;
		}
		if(SysCtrl.file_index == 0)
			preIndex = SysCtrl.file_cnt - 1;
		else
			preIndex = SysCtrl.file_index - 1;
		preIndex = taskPlayMp3DirFindFile(preIndex, SysCtrl.file_index, 0); //try to find pre file
		if(preIndex >= 0)
		{
			task_playMp3_op.playfirstIndex = INVALID_PLAY_INDEX;	//当前音乐播放完后停止
			deg_Printf("preIndex:%d, %d\n", preIndex, SysCtrl.file_index);
			taskPlayMp3MainStart(preIndex);
		}else
		{
			if(mp3_dec_sta() == MP3_DEC_STOP )
				taskPlayMp3MainStart(SysCtrl.file_index);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainKeyMsgRight
* Description    : playMp3MainKeyMsgRight
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainKeyMsgRight(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	int nextIndex;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		if(SysCtrl.file_cnt <= 0)
		{
			return 0;
		}
		nextIndex = taskPlayMp3DirFindFile(SysCtrl.file_index + 1, SysCtrl.file_index, 1); //try to find pre file
		if(nextIndex >= 0)
		{
			task_playMp3_op.playfirstIndex = INVALID_PLAY_INDEX;	//当前音乐播放完后停止
			deg_Printf("nextIndex:%d, %d\n", nextIndex, SysCtrl.file_index);
			taskPlayMp3MainStart(nextIndex);
		}else
		{
			if(mp3_dec_sta() == MP3_DEC_STOP )
				taskPlayMp3MainStart(SysCtrl.file_index);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainKeyMsgPower
* Description    : playMp3MainKeyMsgPower
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(mp3_dec_sta() >= MP3_DEC_START)
		{
			mp3_api_stop();
		}
		SysCtrl.winChangeEnable = 1;
		app_taskStart(TASK_MAIN,0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainSysMsgSD
* Description    : playMp3MainSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("playMp3MainSysMsgSD\n");
	playMp3MainSDShow(handle);
	if((SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL))
	{
		if(mp3_dec_sta() >= MP3_DEC_START)
		{
			mp3_api_stop();
		}
		SysCtrl.file_cnt = 0;

	}
	else
	{
		deg_Printf("taskPlayMp3RootDirOpen 111 \n");
		taskPlayMp3RootDirOpen();
	}
	playMp3MainStatShow(handle, 0);
	playMp3MainFileNameShow(handle, 0);
	playMp3MainTimeShow(handle, 0);
	playMp3MainFreqShow(handle, 0);
	playMp3MainFindFileAndStart(0);

	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainSysMsgUSB
* Description    : playMp3MainSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{
	playMp3MainBaterryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainSysMsgBattery
* Description    : playMp3MainSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
		playMp3MainBaterryShow(handle);
	return 0;
}


/*******************************************************************************
* Function Name  : playMp3SubSysMsgPlay
* Description    : playMp3SubSysMsgPlay
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainSysMsgPlay(winHandle handle,uint32 parameNum,uint32* parame)
{
	u32 msg_event = UPDATE_NONE;
	if(parameNum == 1)
		msg_event = parame[0];
	if(msg_event & UPDATA_START)
	{
		resSum = uiWinGetResSum(winItem(handle,PLAYMP3MAIN_LRC_ID));
		startShowNum = 0 - resSum/2;
		playMp3MainFileNameShow(handle, 1);
		playMp3MainStatShow(handle, 0);
		playMp3MainFreqShow(handle, 1);
		playMp3MainTimeShow(handle, 1);

	}
	if(msg_event & UPDATE_LRCSHOW)
	{
		//playMp3MainLrcShow(handle);
	}

	if(msg_event & UPDATE_PLAYTIME)
	{
		playMp3MainTimeShow(handle, 1);
	}

	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainSysMsgBattery
* Description    : playMp3MainSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainSysMsgTimeUpdate(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL)
	{
		if(mp3_dec_sta() >= MP3_DEC_START)
		{
			playMp3MainFreqShow(handle, 1);
		}
	}
	return 0;
}


ALIGNED(4) msgDealInfor playMp3MainMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		playMp3MainOpenWin},
	{SYS_CLOSE_WINDOW,		playMp3MainCloseWin},
	{SYS_CHILE_COLSE,		playMp3MainOpenWin},
	{KEY_EVENT_OK,			playMp3MainKeyMsgOk},
	{KEY_EVENT_UP,			playMp3MainKeyMsgUp},
	{KEY_EVENT_DOWN,		playMp3MainKeyMsgDown},
	{KEY_EVENT_LEFT,		playMp3MainKeyMsgLeft},
	{KEY_EVENT_RIGHT,		playMp3MainKeyMsgRight},
	{KEY_EVENT_POWER,		playMp3MainKeyMsgPower},
	{SYS_EVENT_SDC,			playMp3MainSysMsgSD},
	{SYS_EVENT_USBDEV,		playMp3MainSysMsgUSB},
	{SYS_EVENT_BAT,			playMp3MainSysMsgBattery},
	{SYS_EVENT_TIME_UPDATE,	playMp3MainSysMsgTimeUpdate},
	{SYS_EVENT_PLAY,		playMp3MainSysMsgPlay},
	{EVENT_MAX,NULL},
};

WINDOW(playMp3MainWindow,playMp3MainMsgDeal,playMp3MainWin)


