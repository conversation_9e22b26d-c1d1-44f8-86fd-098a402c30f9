/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuSysVolumeWin.c"


ALIGNED(4) u32 sysVolume_save;
/*******************************************************************************
* Function Name  : getSysVolumeResInfor
* Description    : getSysVolumeResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : none
*******************************************************************************/
static u32 getSysVolumeResInfor(u32 item,u32* image,u32* str)
{
	if(image)
		*image = INVALID_RES_ID;
	if(str)
		*str   = task_com_Volume_stridByIndex(item);

	return 0;
}
/*******************************************************************************
* Function Name  : sysVolumeKeyMsgOk
* Description    : sysVolumeKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int sysVolumeKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;


	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,SYSVOLUME_SELECT_ID));
		if(item < task_com_Volume_MaxIndex())
		{
			sysVolume_save = SysCtrl.curVolume;
			task_com_curVolume_cfg();
			user_config_cfgSys(CONFIG_ID_SYSTEM_VOLUME);
			user_config_save();
		}
		uiWinDestroy(&handle);	
	}
	return 0;
}
/*******************************************************************************
* Function Name  : sysVolumeKeyMsgUp
* Description    : sysVolumeKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int sysVolumeKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,SYSVOLUME_SELECT_ID));
		SysCtrl.curVolume = uiItemManageGetCurrentItem(winItem(handle,SYSVOLUME_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : sysVolumeKeyMsgDown
* Description    : sysVolumeKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int sysVolumeKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];

	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManageNextItem(winItem(handle,SYSVOLUME_SELECT_ID));
		SysCtrl.curVolume = uiItemManageGetCurrentItem(winItem(handle,SYSVOLUME_SELECT_ID));

	}
	return 0;
}
/*******************************************************************************
* Function Name  : sysVolumeKeyMsgPower
* Description    : sysVolumeKeyMsgPower
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int sysVolumeKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : sysVolumeOpenWin
* Description    : sysVolumeOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int sysVolumeOpenWin(winHandle handle,u32 parameNum,u32* parame)
{

	deg_Printf("[WIN]formatOpenWin\n");
	//uiItemManageSetRowSum(winItem(handle,FORMAT_SELECT_ID),1,Rh(40));
	//uiItemManageSetColumnSumWithGap(winItem(handle,FORMAT_SELECT_ID),0,2,Rw(100), Rw(0));
	//uiItemManageCreateItem(		winItem(handle,FORMAT_SELECT_ID),uiItemCreateMenuOption,getformatResInfor,2);

	uiItemManageSetItemHeight(winItem(handle,SYSVOLUME_SELECT_ID),Rh(35));
	uiItemManageCreateItem(		winItem(handle,SYSVOLUME_SELECT_ID),uiItemCreateMenuOption,getSysVolumeResInfor,task_com_Volume_MaxIndex());
#if 0
	uiItemManageSetCharInfor(	winItem(handle,FORMAT_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_White);
	uiItemManageSetSelectColor(	winItem(handle,FORMAT_SELECT_ID),R_ID_PALETTE_DoderBlue);
	uiItemManageSetUnselectColor(winItem(handle,FORMAT_SELECT_ID),R_ID_PALETTE_Gray);
#else
	uiItemManageSetSelectColorEx(winItem(handle,SYSVOLUME_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER, SMENU_SELECT_FN_COLOR, SMENU_SELECT_BG_COLOR);
	uiItemManageSetUnselectColorEx(winItem(handle,SYSVOLUME_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER, SMENU_UNSELECT_FN_COLOR, SMENU_UNSELECT_BG_COLOR);
#endif


	if(SysCtrl.curVolume >= task_com_Volume_MaxIndex())
		SysCtrl.curVolume = task_com_Volume_MaxIndex() - 1;
	sysVolume_save = SysCtrl.curVolume;
	uiItemManageSetCurItem(		winItem(handle,SYSVOLUME_SELECT_ID),sysVolume_save);

	return 0;
}
/*******************************************************************************
* Function Name  : sysVolumeCloseWin
* Description    : sysVolumeCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int sysVolumeCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	SysCtrl.curVolume = sysVolume_save;
	if(SysCtrl.curVolume  >= task_com_Volume_MaxIndex())
		SysCtrl.curVolume  = task_com_Volume_MaxIndex() - 1;
	deg_Printf("[WIN]sreenBrightCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : sysVolumeWinChildClose
* Description    : sysVolumeWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int sysVolumeWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]sreenBrightWinChildClose\n");
	uiWinDestroy(&handle);
	return 0;
}


ALIGNED(4) msgDealInfor sysVolumeMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	sysVolumeOpenWin},
	{SYS_CLOSE_WINDOW,	sysVolumeCloseWin},
	{SYS_CHILE_COLSE,	sysVolumeWinChildClose},
	{KEY_EVENT_OK,		sysVolumeKeyMsgOk},
	{KEY_EVENT_UP,		sysVolumeKeyMsgUp},
	{KEY_EVENT_DOWN,	sysVolumeKeyMsgDown},
	{KEY_EVENT_LEFT,	sysVolumeKeyMsgPower},
	//{KEY_EVENT_RIGHT,	sysVolumeKeyMsgDown},
	{KEY_EVENT_POWER,	sysVolumeKeyMsgPower},
	{EVENT_MAX,NULL},
};

WINDOW(sysVolumeWindow,sysVolumeMsgDeal,sysVolumeWin)


