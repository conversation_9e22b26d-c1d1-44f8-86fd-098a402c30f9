/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../inc/app_api.h"

ALIGNED(4) static LCDSHOW_CTRL_T  app_lcdshow_ctrl;

/*******************************************************************************
* Function Name  : app_lcdVideoShowScaler_cfg
* Description    : set lcd show crop scaler(from csi img)
* Input          : int step: 0: not crop, > 0: crop plus, < 0: crop minus
* Output         : none
* Return         : int 0:success
*******************************************************************************/
int app_lcdVideoShowScaler_cfg(int step)
{
	static u16 crop_w_save = 0, crop_h_save = 0;
	u16 csi_w,csi_h,video_w,video_h;
	u16 crop_w, crop_h;
	hal_SensorResolutionGet(&csi_w,&csi_h);
	csi_w = 640;
	csi_h = 480;
	hal_lcdGetVideoRatioResolution(&video_w,&video_h);
	video_w = hx330x_min(csi_w, video_w);
	video_h = hx330x_min(csi_h, video_h);

	if(step == 0)
	{
		u32 step = hx330x_greatest_divisor(csi_w, csi_h);
		SysCtrl.lcd_scaler_level_max = (u32)video_w*video_h* 100/((u32)csi_w*csi_h);
		SysCtrl.lcd_scaler_w_step = csi_w / step * 2;//4;
		SysCtrl.lcd_scaler_h_step = csi_h / step * 2;//4;
		crop_w = csi_w;
		crop_h = csi_h;
		crop_w_save = crop_h_save = 0;
	}else if(step > 0)
	{
		crop_w = crop_w_save - SysCtrl.lcd_scaler_w_step;
		crop_h = crop_h_save - SysCtrl.lcd_scaler_h_step;
	}else{
		crop_w = crop_w_save + SysCtrl.lcd_scaler_w_step;
		crop_h = crop_h_save + SysCtrl.lcd_scaler_h_step;
	}
	//deg_Printf("%d, crop_w:%d, crop_h:%d\n", crop_index, crop_w, crop_h);
	if(crop_w < video_w || crop_h < video_h)
	{
		crop_w = video_w;
		crop_h = video_h;	
	}
	if(crop_w > csi_w || crop_h > csi_h)
	{
		crop_w = csi_w;
		crop_h = csi_h;	
	}
	if(crop_w_save == crop_w && crop_h_save == crop_h)
	{
		return -1;
	}
	crop_w_save = crop_w;
	crop_h_save = crop_h;
	SysCtrl.lcd_scaler_level  = (u32)crop_w*crop_h* 100/((u32)csi_w*csi_h);
	SysCtrl.lcd_scaler_X = (csi_w-crop_w_save)/SysCtrl.lcd_scaler_w_step;

	deg_Printf("level %d, crop_w:%d, crop_h:%d,SysCtrl.lcd_scaler_X=%d\n", SysCtrl.lcd_scaler_level, crop_w, crop_h,SysCtrl.lcd_scaler_X);
    hal_lcdSetCsiCrop((csi_w-crop_w)/2, (csi_h-crop_h)/2, (csi_w+crop_w)/2,  (csi_h+crop_h)/2);
	return 0;
}
/*******************************************************************************
* Function Name  : app_lcdVideoShowRatio_cfg
* Description    : user cfg display ratio ,only effect to video show
* Input          : u16 ratio : LCD_RATIO_MAKE(w, h)
* Output         : none
* Return         :0
*******************************************************************************/
void app_lcdVideoShowRatio_cfg(u16 ratio)
{
	hal_lcdSetRatio(ratio);
}
/*******************************************************************************
* Function Name  : app_lcdCsiVideoShowStart
* Description    : lcd video show start for showing csi, will reset scaler
* Input          : mode : LCDSHOW_WINAB_MODE
* Output         : none
* Return         : 0
*******************************************************************************/
int app_lcdCsiVideoShowStart(u8 mode)
{
	if(app_lcdshow_ctrl.video_layer_en)
		return 0;
	app_lcdshow_ctrl.video_layer_en = 1;
	hal_lcdSetRatio(hardware_setup.lcd_ratio_mode);
	if(SysCtrl.lcd_scaler_level == 100)
		app_lcdVideoShowScaler_cfg(0);

	app_lcdShowWinModeCfg(mode, 0);
	hal_lcdCsiShowStart();



	return 0;
}
/*******************************************************************************
* Function Name  : app_lcdCsiVideoShowStop
* Description    : lcd video csi show stop
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
void app_lcdCsiVideoShowStop(void)
{
	if(app_lcdshow_ctrl.video_layer_en)
		hal_lcdCsiShowStop();
	hx330x_lcdShowWaitDone();
	app_lcdshow_ctrl.video_layer_en = 0;
	app_lcdShowWinModeCfg(LCDSHOW_WIN_DISABLE, 0);
}

/*******************************************************************************
* Function Name  : app_lcdVideoIdleFrameGet
* Description    : lcd video show idle frame get
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
void* app_lcdVideoIdleFrameGet(void)
{
    return (void *) hal_lcdVideoIdleFrameMalloc();
}

/*******************************************************************************
* Function Name  : app_lcdUiShowInit
* Description    : lcd ui show init
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
int app_lcdUiShowInit(void)
{
	if(app_lcdshow_ctrl.ui_layer_en != 0)
		return 0;
	u16	ui_w, ui_h, sreen_w,sreen_h;
	u16 pos_x, pos_y;
	if(hal_lcdGetUiResolution(&ui_w,&ui_h) < 0)
		return -2;
	if(hal_lcdGetUiPosition(&pos_x,&pos_y) < 0)
			return -3;
	if(hal_lcdGetSreenResolution(&sreen_w,&sreen_h) < 0)
		return -4;
    u32 palette 	= (u32)hal_sysMemMalloc(256 * 4);
	if(palette == 0)
		return -6;
    INT32 scanmode = hal_lcdUiScanModeGet();
    // load palette
	if(res_iconGetPalette(R_ID_BIN_PALETTE, (u8*)palette) <= 0)
	{
		hal_sysMemFree((void *)palette);
		palette = 0;
	}
    hal_uiLzoInit();
	hal_lcdUiInit(UI_LAYER0,ui_w,ui_h, pos_x,pos_y,	palette,scanmode & 0x03);
#if 0
	switch(scanmode & 0x03)
	{
		case LCD_DISPLAY_ROTATE_0:		hal_lcdUiInit(UI_LAYER0,ui_w,ui_h, pos_x,		         pos_y,	               palette,	scanmode & 0x03); 	break;
		case LCD_DISPLAY_ROTATE_90:		hal_lcdUiInit(UI_LAYER0,ui_w,ui_h, pos_y,	             sreen_w-pos_x-ui_w,   palette,	scanmode & 0x03);	break;
		case LCD_DISPLAY_ROTATE_180:	hal_lcdUiInit(UI_LAYER0,ui_w,ui_h, sreen_w-pos_x-ui_w,   sreen_h-pos_y-ui_h,   palette,	scanmode & 0x03);	break;
		case LCD_DISPLAY_ROTATE_270:	hal_lcdUiInit(UI_LAYER0,ui_w,ui_h, sreen_h-pos_y-ui_h,   pos_x,	               palette,	scanmode & 0x03);	break;
		default: 				hal_sysMemFree((void *)palette); return -5;//
	}
#endif
    hal_lcdUiEnable(UI_LAYER0,1);
    hal_sysMemFree((void *)palette);
	app_lcdshow_ctrl.ui_layer_en = 1;
	return 0;
}
/*******************************************************************************
* Function Name  : app_lcdCsiVideoShowStop
* Description    : lcd video csi show stop
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
void app_lcdUiShowUinit(void)
{
	if(app_lcdshow_ctrl.ui_layer_en)
		hal_lcdUiEnable(UI_LAYER0,0);
	app_lcdshow_ctrl.ui_layer_en = 0;
}
/*******************************************************************************
* Function Name  : app_lcdNesUiShowInit
* Description    : lcd ui show init for nes
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
int app_lcdNesExitUiShowInit(void)
{
	if(app_lcdshow_ctrl.ui_layer_en == 0)
	{
		return app_lcdUiShowInit();
	}else
	{
		u32 palette 	= (u32)hal_sysMemMalloc(256 * 4);
		if(palette == 0)
		{
			return -1;
		}
		if(res_iconGetPalette(R_ID_BIN_PALETTE, (u8*)palette) <= 0)
		{
			hal_sysMemFree((void *)palette);
			return -2;
		}
		hx330x_lcdUiSetPalette(UI_LAYER0, (u32)palette);
		hal_sysMemFree((void *)palette);
		return 0;

	}
}
/*******************************************************************************
* Function Name  : app_lcdUiDrawIdleFrameGet
* Description    : lcd ui draw idle frame get
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
void* app_lcdUiDrawIdleFrameGet(void)
{
    return (void *) hal_uiDrawBufMalloc(UI_LAYER0);
}
/*******************************************************************************
* Function Name  : app_Cmos_Sensor_Switch
* Description    : app_Cmos_Sensor_Switch: video record should stop
* Input          :
* Output         : none
* Return         : int 0:success
*******************************************************************************/
int app_Cmos_Sensor_Switch(void)
{

	if(app_taskCurId() != TASK_RECORD_PHOTO && app_taskCurId() != TASK_RECORD_VIDEO)
		return -1;

	if(hardware_setup.cmos_sensor_switch_en == 0)
		return -1;

	if(videoRecordGetStatus() == MEDIA_STAT_START)
		app_taskRecordVideo_stop();
	int ret = 0;
#if DEV_SENSOR_SWITCH_WIN_EN
	//此处举例使用原sensor的一张图作为底图，用户也可以使用其他图片
	JPG_ENC_ARG jpg_arg;

	jpg_arg.fd 			= -1;
	//hal_SensorResolutionGet(&jpg_arg.dst_width,&jpg_arg.dst_height);
	hal_lcdGetVideoRatioResolution(&jpg_arg.dst_width,&jpg_arg.dst_height);
	jpg_arg.img_Q		= JPEG_Q_27;
	jpg_arg.timestamp	= 0;//user_configValue2Int(CONFIG_ID_TIMESTAMP);
	jpg_arg.buf			= NULL;
	jpg_arg.size		= 0;
	if(imageEncodeLcdToRamStart(&jpg_arg) < 0)
	{
		ret = -1;
		goto SENSOR_SWITCH_WIN_END;
	}
	if(hal_mjpDecodeParse((u8 *)jpg_arg.buf,320,240) < 0)
	{
		ret = -2;
		goto SENSOR_SWITCH_WIN_END;
	}
	hal_lcdwin_buf_cfg(jpg_arg.buf, jpg_arg.size);
SENSOR_SWITCH_WIN_END:
	if(ret < 0)
	{
		if(jpg_arg.buf)
		{
			hal_sysMemFree((void*)jpg_arg.buf);
			jpg_arg.buf = NULL;
		}
	}
#else
	ret = -1;
#endif
	app_lcdCsiVideoShowStop();
#if  FUN_KID_FRAME_EN
	app_kid_frame_ctrl(KID_FRAME_DISTROY, 0);
#endif
#if  DEV_SENSOR_MAGIC_EN
	app_sensor_magic_ctrl(SENSOR_MAGIC_CHANGE_NONE, 0);
#endif
#if DEV_SENSOR_FILTER_EN
	app_sensor_filter_ctrl(SENSOR_FILTER_CHANGE_NONE);
#endif

#if DEV_SENSOR_LENS_EN
	app_sensor_lens_ctrl(SENSOR_LENS_CHANGE_NONE, 0);
#endif
	dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_UINIT, hardware_setup.cmos_sensor_sel);
	hardware_setup.cmos_sensor_sel ^=1;
	hardware_setup.lcd_first_drop = DEV_SENSOR_DROP_FIRST_FRAME;
	dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_INIT, hardware_setup.cmos_sensor_sel);
	if(ret >= 0)
	{
		app_lcdCsiVideoShowStart(LCDSHOW_WIN_CSI_CENTER_UP);
	}else
	{
		app_lcdCsiVideoShowStart(LCDSHOW_WIN_DISABLE);
	}

#if DEV_SENSOR_SWITCH_WIN_EN
	if(ret >= 0)
	{
		while(1)
		{
			hal_wdtClear();
			if(hal_lcdwin_framesta_get() == WINAB_JFRAME_STA_END)
			{
				ret = app_lcdShowWinModeCfg(LCDSHOW_WIN_CSI_CENTER_UP, DEV_SENSOR_SWITCH_STEP);
				if(ret != 0)
				{
					break;
				}
			}
		}
		if(ret != 1)
			app_lcdShowWinModeCfg(LCDSHOW_NOT_SUB_WIN, 0);
		hal_lcdwin_buf_cfg(NULL, 0);
		if(jpg_arg.buf)
		{
			hal_sysMemFree((void*)jpg_arg.buf);
			jpg_arg.buf = NULL;
		}

	}

#endif
	return 0;
}
/*******************************************************************************
* Function Name  : app_lcdPreview_start
* Description    : app_lcdPreview_start
* Input          :
* Output         : none
* Return         : int 0:success
*******************************************************************************/
int app_lcdPreview_start(void)
{
#if DEV_PREVIEW_WIN_EN
	//此处举例使用开机画面作为底图，用户也可以使用其他图片
	u8 * buf = NULL;
	u32 size;
	int addr;
	int ret = 0;
	addr = nv_open(R_ID_IMAGE_POWER_ON);
	if(addr < 0)
	{
		ret = -1;
		goto PREVIEW_WIN_END;
	}
	size = nv_size(R_ID_IMAGE_POWER_ON);
	buf  = (u8*)hal_sysMemMalloc(size);
	if(buf == NULL)
	{
		ret = -2;
		goto PREVIEW_WIN_END;
	}
	nv_read(addr,buf,size);
	if(hal_mjpDecodeParse((u8 *)buf,320,240) < 0)
	{
		ret = -3;
		goto PREVIEW_WIN_END;
	}
	hal_lcdwin_buf_cfg(buf, size);
PREVIEW_WIN_END:
	if(ret < 0)
	{
		if(buf)
		{
			hal_sysMemFree((void*)buf);
			buf = NULL;
		}
		app_lcdCsiVideoShowStart(LCDSHOW_WIN_DISABLE);
	}else
	{
		app_lcdCsiVideoShowStart(LCDSHOW_WIN_CSI_CENTER_UP);
		while(1)
		{
			hal_wdtClear();
			if(hal_lcdwin_framesta_get() == WINAB_JFRAME_STA_END)
			{
				ret = app_lcdShowWinModeCfg(LCDSHOW_WIN_CSI_CENTER_UP, 10);
				if(ret != 0)
				{
					break;
				}
			}
		}
		if(ret != 1)
			app_lcdShowWinModeCfg(LCDSHOW_NOT_SUB_WIN, 0);
		hal_lcdwin_buf_cfg(NULL, 0);
		if(buf)
		{
			hal_sysMemFree((void*)buf);
			buf = NULL;
		}

	}

#else
	app_lcdCsiVideoShowStart(LCDSHOW_WIN_DISABLE);
#endif

	return 0;
}

/*******************************************************************************
* Function Name  : app_lcdShowWinModeCfg
* Description    : config lcdshow WINAB mode
* Input          : INT8U mode :
* Output         : none
* Return         : int 0:success
*******************************************************************************/
int app_lcdShowWinModeCfg(INT8U mode, int step)
{
    u16 video_w,video_h;
	u16 winA_x, winA_y,winA_w, winA_h;
	u8  winA_layer;
	u16 winB_x, winB_y,winB_w, winB_h;
	u8  winB_en, winB_layer;
	u16 win_sub_step_w, win_sub_step_h;
    u16 src_w, src_h;
	hal_lcdGetVideoRatioResolution(&video_w,&video_h);
	if(mode == LCDSHOW_WIN_DISABLE)
	{
		hal_lcdwin_framesta_reset(0);
		hal_lcdSetWinEnable(0);
		if(app_lcdshow_ctrl.video_layer_en)
		{
			hal_lcdSetWINAB(LCDWIN_A,LCDWIN_BOT_LAYER,0,0,video_w,video_h,WINAB_EN);
			hal_lcdSetWINAB(LCDWIN_B,LCDWIN_TOP_LAYER,0,0,video_w,video_h,WINAB_DIS);
		}

		app_lcdshow_ctrl.win_step = 100;
		return 0;
	}
	if(mode >= LCDSHOW_WIN_MAX || app_lcdshow_ctrl.video_layer_en  == 0)
	{
		hal_lcdwin_framesta_reset(0);
		return -1;
	}

	hal_lcdGetVideoRatioResolution(&video_w,&video_h);
	if((mode & LCDSHOW_WIN_CSI_TOP) == LCDSHOW_WIN_CSI_TOP)
	{
		winB_x = 0;
		winB_y = 0;
		winB_w = video_w;
		winB_h = video_h;
		winA_layer = LCDWIN_TOP_LAYER;
		winB_layer = LCDWIN_BOT_LAYER;
	}else
	{
		winA_x = 0;
		winA_y = 0;
		winA_w = video_w;
		winA_h = video_h;
		winA_layer = LCDWIN_BOT_LAYER;
		winB_layer = LCDWIN_TOP_LAYER;
	}
	//deg_Printf("app_lcdshow_ctrl.win_step:%d\n", app_lcdshow_ctrl.win_step);

	if(step == 0)
	{
		if((mode & LCDSHOW_WIN_SCALER_UP) == LCDSHOW_WIN_SCALER_UP)
		{
			app_lcdshow_ctrl.win_step = 40;
		}else
		{
			app_lcdshow_ctrl.win_step = 100;
		}
	}else
	{
		step = hx330x_abs(step);
		if((mode & LCDSHOW_WIN_SCALER_UP) == LCDSHOW_WIN_SCALER_UP)
		{
			app_lcdshow_ctrl.win_step += step;
		}else
		{
			app_lcdshow_ctrl.win_step -= step;
		}

		if(app_lcdshow_ctrl.win_step >= 100 || app_lcdshow_ctrl.win_step <= 0)
		{
			app_lcdshow_ctrl.win_step = 100;
		}
	}
	win_sub_step_w = (app_lcdshow_ctrl.win_step * video_w)/100;
	win_sub_step_h = (app_lcdshow_ctrl.win_step * video_h)/100;
	//deg_Printf("win_sub_step:%d,%d\n", win_sub_step_w,win_sub_step_h);
	if(app_lcdshow_ctrl.win_step == 100)
	{
		mode 	= LCDSHOW_NOT_SUB_WIN;
		winB_en = WINAB_DIS;
		//deg_Printf("11111111\n");
	}else
	{
		winB_en = WINAB_EN;
		if((mode & LCDSHOW_WIN_CSI_TOP) != LCDSHOW_WIN_CSI_TOP)
		{
			hal_mjpDecodeGetResolution(&src_w, &src_h);
			if(mode != LCDSHOW_NOT_SUB_WIN && ((win_sub_step_w < (src_w/32)) || (win_sub_step_h < (src_h/32))) )
			{
				mode 	= LCDSHOW_NOT_SUB_WIN;
				winB_en = WINAB_DIS;
				//app_lcdshow_ctrl.win_step = 100;
			}else
			{
				winB_en = WINAB_EN;
			}
		}else
		{
			if((mode & LCDSHOW_WIN_SCALER_UP) != LCDSHOW_WIN_SCALER_UP) //down
			{
				if(video_w - win_sub_step_w < 16 || video_h - winB_h < 4)
				{
					return 1;
				}
			}
		}
	}

	//deg_Printf("WIN:%d [%d-%d] %d\n", mode,video_w, video_h, winB_en);

	switch(mode)
	{
    	case LCDSHOW_NOT_SUB_WIN:
			winA_x = 0; winA_y = 0; winA_w = video_w; winA_h = video_h;
			winB_x = 0; winB_y = 0; winB_w = video_w; winB_h = video_h;
			app_lcdshow_ctrl.win_step = 100;
			break;
    	case LCDSHOW_WIN_LEFTTOP:
			winB_x = 0; winB_y = 0;
			winB_w = win_sub_step_w &~7;
			winB_h = win_sub_step_h &~1;

			break;
		case LCDSHOW_WIN_RIGHTTOP:
			winB_x = (video_w - win_sub_step_w)&~7;
			winB_y = 0;
			winB_w = video_w - winB_x;
			winB_h = (win_sub_step_h)&~1;

			break;
		case LCDSHOW_WIN_LEFTDOWN:
			winB_x = 0;
			winB_y = (video_h - win_sub_step_h)&~1;
			winB_w = win_sub_step_w &~7;
			winB_h = video_h - winB_y;

			break;
		case LCDSHOW_WIN_RIGHTDONW:
			winB_x = (video_w - win_sub_step_w)&~7;
			winB_y = (video_h - win_sub_step_h)&~1;
			winB_w = video_w - winB_x;
			winB_h = video_h - winB_y;

			break;
		case LCDSHOW_WIN_LEFT:
			winB_x = 0;
			winB_y = 0;
			winB_w = win_sub_step_w &~7;
			winB_h = video_h;
			break;
		case LCDSHOW_WIN_RIGHT:
			winB_x = (video_w - win_sub_step_w)&~7;
			winB_y = 0;
			winB_w = video_w - winB_x;
			winB_h = video_h;
			break;
		case LCDSHOW_WIN_TOP:
			winB_x = 0;
			winB_y = 0;
			winB_w = video_w;
			winB_h = win_sub_step_h&~1;

			break;
		case LCDSHOW_WIN_DOWN:
			winB_x = 0;
			winB_y = (video_h - win_sub_step_h)&~1;
			winB_w = video_w;
			winB_h = video_h - winB_y;

			break;
		case LCDSHOW_WIN_CENTER:
			winB_x = ((video_w - win_sub_step_w)/2)&~7;
			winB_y = ((video_h - win_sub_step_h)/2)&~1;
			winB_w = win_sub_step_w;
			winB_h = win_sub_step_h;
			break;
			//SENSOR画面逐渐放大或缩小
		case LCDSHOW_WIN_CSI_CENTER_UP:
		case LCDSHOW_WIN_CSI_CENTER_DOWN:
			winA_x = ((video_w - win_sub_step_w)/2)&~7;
			winA_y = ((video_h - win_sub_step_h)/2)&~1;
			winA_w = win_sub_step_w;
			winA_h = win_sub_step_h;
			break;
		case LCDSHOW_WIN_CSI_RIGHTTOP_UP:
			winA_x = ((video_w - win_sub_step_w))&~7;
			winA_y = 0;
			winA_w = video_w - winA_x;
			winA_h = win_sub_step_h;
			break;
		default:	return -1;
	}
	//deg_Printf("WINB  pos[%d:%d] wh[%d:%d]\n", winB_x,winB_y,winB_w,winB_h);
	if(mode == LCDSHOW_NOT_SUB_WIN)
	{
		hal_lcdWinEnablePreSet(0);
		hal_lcdwin_framesta_reset(0);
	}else
	{
		hal_lcdWinEnablePreSet(1);
		hal_lcdwin_framesta_reset(1);
	}
	//deg_Printf("WINA [%d] [%d,%d,%d,%d]\n",winA_layer,winA_x,winA_y,winA_w,winA_h);
	//deg_Printf("WINB [%d] [%d,%d,%d,%d]\n",winB_layer,winB_x,winB_y,winB_w,winB_h);
    hal_lcdSetWINAB(LCDWIN_A,winA_layer,winA_x,winA_y,winA_w,winA_h,WINAB_EN);
	hal_lcdSetWINAB(LCDWIN_B,winB_layer,winB_x,winB_y,winB_w,winB_h,winB_en);
	if(mode == LCDSHOW_NOT_SUB_WIN)
	{
		//hal_lcdSetWinEnable(0);
		return 1;
	}
	return 0;

}















