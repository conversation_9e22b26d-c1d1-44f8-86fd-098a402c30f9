/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../inc/hal.h"
typedef enum
{
	_JEC_FREE_ = 0,
	_JEC_BUSY_ = 1,	
	_JEC_DOWN_ = 2,
}JBEC_STA;

typedef struct MJPEG_ENC_OP_S
{
	u8 type;
	u8 timestamp;
	
//#if HAL_CFG_MJPEG_QULITY_AUTO>0
	s8 qulity;
    u8 q_cnt;
	u8 q_auto;
	s8 q_dir;
	u32 q_csize;
//#endif
	u16 usb_width;
	u16 usb_height;
    u16 mjpeg_width;
	u16 mjpeg_height;
	

	u32 jfcnt;
    u32 ybuffer;
	u32 uvbuffer;
	u32 mjpbuf;
	u32 mjpsize;

	u32 curBuffer;
	u32 curLen;
	u32 curLen_temp;
	Stream_Head_T vids;
	Stream_Node_T mjpegNode[MJPEG_ITEM_NUM];
}MJPB_ENC_OP_T;

ALIGNED(4) static MJPB_ENC_OP_T  mjpBEncCtrl;

/*******************************************************************************
* Function Name  : hal_mjpA_Sizecalculate
* Description    : hal layer .mjpeg output jpeg size
* Input          : u8 quailty : quality
				   u16 width : output width
				   u16 height: output height
* Output         : None
* Return         : int : size
*******************************************************************************/
u32 hal_mjpB_Sizecalculate(u8 quailty,u16 width,u16 height)
{
#if HAL_CFG_MJPEG_QULITY_AUTO > 0
	if(height <= 480)   
		return HAL_CFG_MJPEG_VGA_SIZE_MAX;
	else
		return HAL_CFG_MJPEG_720_SIZE_MAX;
#else		
	INT32U quli;
	switch(quailty)
	{
		case JPEG_Q_27 : quli = 40;break;
		case JPEG_Q_28 : quli = 45;break;
		case JPEG_Q_31 : quli = 50;break;
		case JPEG_Q_33 : quli = 65;break;
		case JPEG_Q_36 : quli = 70;break;
		case JPEG_Q_AUTO :
		case JPEG_Q_40 : quli = 80;break;
		case JPEG_Q_42 : quli = 80;break;
		case JPEG_Q_50 : quli = 90;break;
		case JPEG_Q_62 : quli = 100;break;
		case JPEG_Q_75 : quli = 110;break;
		case JPEG_Q_81 : quli = 120;break;
		default : quli = 100;break;
	}
//    quli+=20;
	return (((width*height/100)*quli)/10); // byte
#endif	
}
/*******************************************************************************
* Function Name  : hal_mjpA_QualityAjust
* Description    : hal layer .mjpeg quality ajust
* Input          : u32 len : current mjpeg size
* Output         : None
* Return         : NONE
*******************************************************************************/
UNUSED static void hal_mjpB_QualityAjust(u32 len)
{
#if HAL_CFG_MJPEG_QULITY_AUTO>0	
	if(mjpBEncCtrl.q_auto != JPEG_Q_AUTO)
		return ;
	
	if(mjpBEncCtrl.q_dir == 0)  //dir： (0) - default, (1) - Q decrease, (-1) - Q increase 
	{
		int mjpeg_throld = mjpBEncCtrl.q_csize*15/100;  // 40%
		if(len > (mjpBEncCtrl.q_csize+mjpeg_throld))
		{
			mjpBEncCtrl.q_dir = 1;
			mjpBEncCtrl.q_cnt = 0;
			//debg("HAL : [MJPEG]<INFO> size auto check %x,%x\n",len,mjpAEncCtrl.q_csize+mjpeg_throld);
		}
		else if(len < (mjpBEncCtrl.q_csize-mjpeg_throld))
		{
			mjpBEncCtrl.q_dir = -1;
			mjpBEncCtrl.q_cnt = 0;
			//debg("HAL : [MJPEG]<INFO> size auto check %x,%x\n",len,mjpAEncCtrl.q_csize-mjpeg_throld);
		}
	}
	else if(mjpBEncCtrl.q_dir > 0)
	{
		if(len < mjpBEncCtrl.q_csize)
		{
			mjpBEncCtrl.q_dir = 0;
			mjpBEncCtrl.q_cnt = 0;
		}
		else
			mjpBEncCtrl.q_cnt++;
	}
	else if(mjpBEncCtrl.q_dir < 0)
	{
		if(len > mjpBEncCtrl.q_csize)
		{
			mjpBEncCtrl.q_dir = 0;
			mjpBEncCtrl.q_cnt = 0;
		}
		else
			mjpBEncCtrl.q_cnt++;
	}

	if(mjpBEncCtrl.q_cnt>=10)
	{
		mjpBEncCtrl.qulity-= mjpBEncCtrl.q_dir;
		if(mjpBEncCtrl.qulity < 0)
			mjpBEncCtrl.qulity = 0;
		else if(mjpBEncCtrl.qulity >= MJPEG_Q_MAX)
			mjpBEncCtrl.qulity = MJPEG_Q_MAX-1; 
		
		
		hx330x_mjpB_EncodeQuilitySet(mjpegQualityTable[mjpBEncCtrl.qulity]);

		//debg("HAL : [MJPEG]<INFO> quality auto check[%d] ->%d\n",mjpAEncCtrl.q_dir,mjpAEncCtrl.qulity);
		mjpBEncCtrl.q_dir = 0;
		mjpBEncCtrl.q_cnt = 0;
	}
#endif	
}
static u8 hal_mjpB_QualityCheck(u8 quality)
{
#if HAL_CFG_MJPEG_QULITY_AUTO>0
    mjpBEncCtrl.q_auto = quality;
	
    if(quality == JPEG_Q_AUTO)
		quality = JPEG_Q_62;
	int i;
	for(i = 0;i < MJPEG_Q_MAX;i++)
	{
		if(mjpegQualityTable[i] == quality)
			break;
	}
	if(i >= MJPEG_Q_MAX)
		i = MJPEG_Q_MAX-1;
	mjpBEncCtrl.qulity = i;
	mjpBEncCtrl.q_cnt = 0;	
	mjpBEncCtrl.q_dir = 0;
    mjpBEncCtrl.q_csize = hal_mjpB_Sizecalculate(quality,mjpBEncCtrl.mjpeg_width,mjpBEncCtrl.mjpeg_height);
	mjpBEncCtrl.q_csize = mjpBEncCtrl.q_csize*55/100;
#endif	
    if(quality == JPEG_Q_AUTO)
		quality = JPEG_Q_62;	
	deg("video record B Q = %x\n",quality);
	return quality;
	//return JPEG_Q_62; //fix Q= 31;
}
/*******************************************************************************
* Function Name  : hal_jB_fcnt_mnt
* Description    : hal layer .mjpB encode frame debg
* Input          : none 
* Output         : None
* Return         : none
*******************************************************************************/
u32 hal_jB_fcnt_mnt(void)
{
	u32 temp = mjpBEncCtrl.jfcnt;
	deg_Printf("JB_fcnt:%d\n",mjpBEncCtrl.jfcnt);
	mjpBEncCtrl.jfcnt = 0;
	return temp;
}
/*******************************************************************************
* Function Name  : hal_mjpBEnc_state
* Description    : hal layer .mjpB encode state check
* Input          : none 
* Output         : None
* Return         : true: encode sucess
*******************************************************************************/
bool hal_mjpBEnc_state(int flag)
{
	if(flag & BIT(MJPEG_IRQ_OUTPAUSE)){//ODMAPAUSE
		//pause down
		if(!(flag & BIT(MJPEG_IRQ_FRAMEEND))){
			debg("mallo B j frame size err\n");
			return false;	
		}
		//frame down
		else
		{
			return true;
		}
	}	
	if(flag & (BIT(MJPEG_IRQ_OUTERR)|BIT(MJPEG_IRQ_OUTFULL))){
		return false;
	}
	return false;
}
/*******************************************************************************
* Function Name  : hal_mjpBEncodeDoneManual
* Description    : hal_mjpBEncodeDoneManual
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpBEncodeDoneCfg(void)
{
	//hal_lcd_encwin_done();
	hx330x_mjpB_Encode_manual_stop();
}
/*******************************************************************************
* Function Name  : hal_mjpBEncodeDoneManual
* Description    : hal_mjpBEncodeDoneManual
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpBEncodeDoneManual(int flag)
{
	u32 len;
	u32 mjp_sync;
	u32 mjp_sync_next;
	//deg_Printf("B done:%x\n",flag);
	if(flag & (BIT(MJPEG_IRQ_OUTPAUSE)|BIT(MJPEG_IRQ_FRAMEEND)|BIT(MJPEG_IRQ_OUTERR)|BIT(MJPEG_IRQ_OUTFULL)))
	{
		
		
		mjp_sync      = hal_auadc_stamp_out();
		mjp_sync_next = hal_auadc_stamp_next();
		len = hx330x_mjpB_EncodeLoadAddrGet()- mjpBEncCtrl.curBuffer;
		len = (len+0x1ff)&(~0x1ff);
		if(hal_mjpBEnc_state(flag))
		{
			mjpBEncCtrl.jfcnt++;
			hal_streamIn(&mjpBEncCtrl.vids,len,mjp_sync,mjp_sync_next);
			if(mjpBEncCtrl.type == MJPEG_TYPE_PHOTO)  // only need one frame
			{
				hx330x_mjpB_Encode_StartFunc_Reg(NULL);
				//hx330x_mjpB_Encode_manual_stop();
				hal_mjpBEncodeDoneCfg();
				return;
			}
		}
		else
		{
			hal_streamIn(&mjpBEncCtrl.vids,0,mjp_sync,mjp_sync_next);
		}
		//mjpBEncCtrl.curBuffer = (u32)0;	
		mjpBEncCtrl.curLen_temp = mjpBEncCtrl.curLen;
		mjpBEncCtrl.curLen      = (len > _JPGB_SIZE_MIN_DEF_)? (len*3/2) : (_JPGB_SIZE_MIN_DEF_);
		
	#if 0//HAL_CFG_MJPEG_QULITY_AUTO >0
		if(mjpBEncCtrl.q_auto != JPEG_Q_AUTO){
			if(mjpBEncCtrl.mjpeg_height <= 480){
				hx330x_mjpB_EncodeQadj(len,HAL_CFG_MJPEG_VGA_SIZE_MIN,HAL_CFG_MJPEG_VGA_SIZE_MAX);	
			}else{
				hx330x_mjpB_EncodeQadj(len,HAL_CFG_MJPEG_720_SIZE_MIN,HAL_CFG_MJPEG_720_SIZE_MAX);	
			}				
		}
		else
		{
			hal_mjpB_QualityAjust(len);
		}	
	#endif 
		hal_mjpBEncodeDoneCfg();
	}
}
/*******************************************************************************
* Function Name  : hal_mjpBEncodeKickManual
* Description    : hal_mjpBEncodeKickManual
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpBEncodeKickManual(void)
{
	u32 addr;
	u8  mode, tab_num;
	addr = hal_streamMalloc(&mjpBEncCtrl.vids,mjpBEncCtrl.curLen);
	//deg_Printf("B KICK\n");
	if(addr)
	{
		mjpBEncCtrl.curBuffer = addr;
	}
	else
	{
		deg_Printf("B");
		deg_Printf("[B:%d,%d,%d]\n",mjpBEncCtrl.vids.head_size,mjpBEncCtrl.mjpsize,mjpBEncCtrl.curLen);
		hal_streamMallocDrop(&mjpBEncCtrl.vids,mjpBEncCtrl.curLen_temp);
		mjpBEncCtrl.curLen = mjpBEncCtrl.curLen_temp;
		//return;
	}
	hx330x_mjpB_as_Encode();
	if(mjpBEncCtrl.type == MJPEG_TYPE_AVI)
	{
		mode = 1; //add 8bytes
		
	}else
	{
		mode = 0; //not add
	}
	if(mjpBEncCtrl.type == MJPEG_TYPE_PHOTO)
	{
		tab_num = 0;
	}else
	{
		tab_num = HAL_CFG_MJPEG_HIGH_QT ? 2 : 1;
	}
	hx330x_mjpB_Encodeinit(mode, mjpBEncCtrl.qulity,
							mjpBEncCtrl.usb_width, mjpBEncCtrl.usb_height,
							mjpBEncCtrl.mjpeg_width, mjpBEncCtrl.mjpeg_height,
							tab_num);	



	hx330x_recfg_mjpb_tminf();  //TIME INFO SET
	hx330x_mjpB_Encode_inlinebuf_init(mjpBEncCtrl.ybuffer,mjpBEncCtrl.uvbuffer);	
	
	
	hx330x_mjpB_Encode_output_init((u32)mjpBEncCtrl.curBuffer, (u32 )(mjpBEncCtrl.curBuffer + mjpBEncCtrl.curLen));
	//hx330x_mjpB_SFR_Printf();
	hx330x_mjpB_Encode_manual_start();

	//debg("s");
}
/*******************************************************************************
* Function Name  : hal_mjpA_MemInit
* Description    : hal layer .mjpeg memory initial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpB_LineBuf_MenInit(void)
{
	if(mjpBEncCtrl.ybuffer == 0)
	{
		mjpBEncCtrl.ybuffer = (u32)hal_sysMemMalloc(mjpBEncCtrl.usb_width*mjpBEncCtrl.usb_height*3/2);
		if(mjpBEncCtrl.ybuffer==0)
		{
			return false;
		}
		mjpBEncCtrl.uvbuffer = mjpBEncCtrl.ybuffer + mjpBEncCtrl.usb_width*mjpBEncCtrl.usb_height;
	}
	return true;
}
/*******************************************************************************
* Function Name  : hal_mjpB_LineBuf_cfg
* Description    : hal layer .mjpeg memory initial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpB_LineBuf_cfg(u32 yaddr, u32 uvaddr)
{
	mjpBEncCtrl.ybuffer 	= yaddr;
	mjpBEncCtrl.uvbuffer	= uvaddr;

}
/*******************************************************************************
* Function Name  : hal_mjpB_LineBuf_get
* Description    : hal layer .mjpeg memory initial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpB_LineBuf_get(u32 *yaddr, u32 *uvaddr)
{
	*yaddr  = mjpBEncCtrl.ybuffer;
	*uvaddr = mjpBEncCtrl.uvbuffer;

}
/*******************************************************************************
* Function Name  : hal_mjpBMemInit
* Description    : hal layer .mjpB encode frame debg
* Input          : none 
* Output         : None
* Return         : bool true: success
*******************************************************************************/
bool hal_mjpB_buf_MenInit(u8 type)
{
	if(mjpBEncCtrl.ybuffer == 0)
		return false;
    if(mjpBEncCtrl.mjpbuf == 0)
    {
		u32 size;
	    size = hal_sysMemRemain() - 8*1024;
		if(type  == MJPEG_TYPE_AVI)
		{
			size = size/3; //预留前路的mjpbuf
		}
		size &=~0x1ff;
		//size = 600*1024L;
		mjpBEncCtrl.mjpbuf = (u32)hal_sysMemMalloc(size);
		if(mjpBEncCtrl.mjpbuf == 0)
		{
			return false;
		}
		mjpBEncCtrl.mjpsize = size;
		deg_Printf("HAL :<INFO> mjpB addr = 0x%x,size = %dMB%dKB\n",mjpBEncCtrl.mjpbuf,size>>20,(size>>10)&1023);
    }
	return true;
}
/*******************************************************************************
* Function Name  : hal_mjpB_MemUninit
* Description    : hal layer .mjpeg memory uninitial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpB_MemUninit(void)
{
	if(mjpBEncCtrl.mjpbuf)
		hal_sysMemFree((void *)mjpBEncCtrl.mjpbuf);
	mjpBEncCtrl.mjpbuf = 0;
	if(mjpBEncCtrl.ybuffer)
		hal_sysMemFree((void *)mjpBEncCtrl.ybuffer);
	mjpBEncCtrl.ybuffer = 0;
	mjpBEncCtrl.uvbuffer = 0;
}
/*******************************************************************************
* Function Name  : hal_mjpB_usb_resolution_set
* Description    : hal layer .mjpeg B initial 
* Input          : u16 width, u16 height
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpB_usb_resolution_set(u16 width, u16 height)
{
	mjpBEncCtrl.usb_width 	= width;
	mjpBEncCtrl.usb_height 	= height;
}

/*******************************************************************************
* Function Name  : hal_mjpB_Enc_Start
* Description    : hal layer .mjpeg B initial 
* Input          : NONE
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpB_DecodeODMA1En(bool dma_en)
{
	hx330x_mjpB_DecodeODma1Cfg((u32)mjpBEncCtrl.ybuffer,(u32)mjpBEncCtrl.uvbuffer, dma_en);
}


/*******************************************************************************
* Function Name  : hal_mjpB_Enc_Start
* Description    : hal layer .mjpeg B initial 
* Input          : u8 type MJPEG_TYPE_VIDEO/MJPEG_TYPE_PHOTO/MJPEG_TYPE_UVC
				   u16 win_w : output width
				   u16 win_h : output height
				   u8  quality: encode quality
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpB_Enc_Start(u8 type, u16 target_w, u16 target_h, u8 quality)
{	
	mjpBEncCtrl.type 			= type;
	mjpBEncCtrl.mjpeg_width 	= target_w; 
	mjpBEncCtrl.mjpeg_height 	= target_h;  
	
	//if(type != MJPEG_TYPE_VIDEO )
	{
		husb_api_usensor_res_get(&mjpBEncCtrl.usb_width, &mjpBEncCtrl.usb_height);
		if(hal_mjpB_LineBuf_MenInit() == false)
		{
			deg_Printf("mjpB encode : linebuf malloc fail\n");
			return false;
		}

	}//else
	//{
	//	hal_lcdGetVideoResolution(&mjpBEncCtrl.usb_width, &mjpBEncCtrl.usb_height);
	//	hal_lcd_winAB_type_cfg(1);
	//}

	if(hal_mjpB_buf_MenInit(type) == false) // mjpeg memory malloc
	{
		deg_Printf("mjpB encode : mjpbuf malloc fail\n");
		return false;
	}
	

	//mjpdc pr check
	mjpBEncCtrl.qulity = hal_mjpB_QualityCheck(quality);
	
	hx330x_mjpB_EncodeISRRegister(hal_mjpBEncodeDoneManual);	
 
	hx330x_mjpB_Encode_StartFunc_Reg(hal_mjpBEncodeKickManual);//link to mjpbstart
	

	hal_streamInit(&mjpBEncCtrl.vids,mjpBEncCtrl.mjpegNode,MJPEG_ITEM_NUM,(u32)mjpBEncCtrl.mjpbuf,mjpBEncCtrl.mjpsize);

	mjpBEncCtrl.curLen =  mjpBEncCtrl.curLen_temp = mjpBEncCtrl.mjpsize;
	return true;
}
/*******************************************************************************
* Function Name  : hal_mjpB_Enc_Start
* Description    : hal layer .mjpeg B initial 
* Input          : u8 type MJPEG_TYPE_VIDEO/MJPEG_TYPE_PHOTO/MJPEG_TYPE_UVC
				   u16 win_w : output width
				   u16 win_h : output height
				   u8  quality: encode quality
				   u8  timestamp
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpB_Enc_Stop(void)
{
	hal_mjpBEncodeDoneCfg();
	hx330x_mjpB_Encode_StartFunc_Reg(NULL);
	hx330x_mjpB_EncodeISRRegister(NULL);	
	hal_mjpB_MemUninit();
}
/*******************************************************************************
* Function Name  : hal_mjpB_RawBufferfree
* Description    : hal layer.set mjpeg fram raw data
* Input          : void *buffer : frame buffer
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpB_RawBufferfree(void)
{
	hal_streamfree(&mjpBEncCtrl.vids);
}
/*******************************************************************************
* Function Name  : hal_mjpegRawBufferGet
* Description    : hal layer.check raw buffer addr &length
* Input          : u32 *len : buff len
 				   u32 *sync: current buf frame stamp
				   u32 *sync_next: current buf next frame stamp
* Output         : None
* Return         : void * : buffer addr
*******************************************************************************/
void *hal_mjpB_RawBufferGet(u32 *len,u32 *sync, u32 *sync_next)
{
	u8* buff;
	u32 size = 0;

	if(hal_streamOut(&mjpBEncCtrl.vids,(u32 *)&buff,(u32 *)&size, sync, sync_next) == false)
		return NULL;
	if(size == 0){
		hal_mjpB_RawBufferfree();
		deg_Printf("jlenB=0\n");
		return NULL;
	}
	hx330x_sysDcacheFlush((u32)buff,16);
	
	if((buff[0] != 0xff) || (buff[1] != 0xd8)){
		if((buff[8] != 0xff) || (buff[9] != 0xd8)){
			hal_mjpB_RawBufferfree();
			deg_Printf("jBhead err:");
			deg_PrintfBuf(buff,16);
			return NULL;	
		}	
	}
	if(len)
		*len = size;

	return ((void *)buff);
}
/*******************************************************************************
* Function Name  : hal_mjpB_Buffer_prefull
* Description    : hal layer.mjpeg buffer pre full(2/3) just
* Input          : None
* Output         : None
* Return         : true:mjpeg buffer is pre_full
*******************************************************************************/
bool hal_mjpB_Buffer_prefull(void)
{
	//u32 temp = mjpBEncCtrl.vids.head_size;
	//deg_Printf("[B:%d,%d]\n",temp,mjpBEncCtrl.mjpsize);
	if(mjpBEncCtrl.vids.head_size > (mjpBEncCtrl.mjpsize/2))
	{
		//deg_Printf("[B:%d,%d]\n",temp,mjpBEncCtrl.mjpsize);
		return true;
	}
	return false;
}
/*******************************************************************************
* Function Name  : hal_mjpA_RawBuffer_halffull
* Description    : hal layer.mjpeg buffer pre full(2/3) just
* Input          : None
* Output         : None
* Return         : true:mjpeg buffer is pre_full
*******************************************************************************/
bool hal_mjpB_Buffer_halffull(void)
{
	u32 temp = mjpBEncCtrl.vids.head_size;
	if(temp > (mjpBEncCtrl.mjpsize/4))
		return true;
	//deg_Printf("[B:%d,%d]\n",temp,mjpBEncCtrl.mjpsize);	
	return false;
}




