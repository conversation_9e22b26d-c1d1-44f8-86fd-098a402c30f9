/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef  __TASK_COMMON_H
#define  __TASK_COMMON_H
//0x00: check auto off, 0x01: reset auto off, 0x02:sreen off, 0x03 sreen on
typedef enum{
	SREEN_CHECK_AUTOOFF =  0x00,
	SREEN_RESET_AUTOOFF =  0x01,
	SREEN_SET_OFF		=  0x02,
	SREEN_SET_ON		=  0x03,
}COM_SREEN_OP;
typedef enum{
	COM_FILE_NEW = 0,
	COM_FILE_SAVE,
	COM_FILE_DELETE,
	COM_FILE_CHECKEXIST,
	COM_FILE_MAX,
}COM_FILE_OP;
extern const msgDealInfor sysComMsgDeal[];
extern const msgDealInfor taskComMsgDeal[];
/*******************************************************************************
* Function Name  : task_com_para_init
* Description    : task com para init
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_para_init(void);
/*******************************************************************************
* Function Name  : task_com_spijpg_Init
* Description    : APP LAYER: task_com_spijpg_Init
* Input          : 
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void task_com_spijpg_Init(u32 unit_force);
/*******************************************************************************
* Function Name  : task_com_sdlist_scan
* Description    : APP LAYER: task_com_spijpg_Init
* Input          : u8 unit_force: 1: \
				   u8 type: 0: jpg, 1:avi, 2: jpg + avi
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void task_com_sdlist_scan(u8 unit_force, u8 type);
/*******************************************************************************
* Function Name  : task_com_sdc_stat_set
* Description    : sd card stat set
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_usb_dev_out(u8 out);
/*******************************************************************************
* Function Name  : task_com_battery_res_get
* Description    : task_com_battery_res_get
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
resID task_com_battery_res_get(void);
/*******************************************************************************
* Function Name  : task_com_sdc_stat_set
* Description    : sd card stat set
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_sdc_stat_set(u32 stat);
/*******************************************************************************
* Function Name  : task_com_fs_scan
* Description    : fs scan
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_fs_scan(void);
/*******************************************************************************
* Function Name  : task_com_lcdbk_set
* Description    : lcd sreen on/off
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_lcdbk_set(u32 on);
/*******************************************************************************
* Function Name  : task_com_sreen_save
* Description    : screen save check
* Input          : u32 on: COM_SREEN_OP 0x00: check auto off, 0x01: reset auto off, 0x02:sreen off, 0x03 sreen on
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_sreen_check(u32 on);
/*******************************************************************************
* Function Name  : task_com_sreen_save
* Description    : screen save check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_sreen_save(u32 on);
/*******************************************************************************
* Function Name  : task_com_auto_poweroff
* Description    : system auto power off check
* Input          : int reset : 1:reset auto poweroff, 0:no reset
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_auto_poweroff(int reset);
/*******************************************************************************
* Function Name  : task_com_keysound_play
* Description    : task_com_keysound_play
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_keysound_play(void);
/*******************************************************************************
* Function Name  : task_com_sound_wait_end
* Description    : task_com_sound_wait_end
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_sound_wait_end(void);
/*******************************************************************************
* Function Name  : task_com_sdc_freesize_check
* Description    : com get fs free size
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
int task_com_sdc_freesize_check(void);
/*******************************************************************************
* Function Name  : task_com_sdc_freesize_modify
* Description    : com dec size from free size
* Input          : INT8S dec: >=0 add freesize, <0 minus freesize
*                  INT32U size : unit byte
* Output         : None
* Return         : None
*******************************************************************************/
int task_com_sdc_freesize_modify(INT8S dec,INT32U size);
/*******************************************************************************
* Function Name  : task_com_ir_set
* Description    : task_com_ir_set
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_ir_set(u32 on);
/*******************************************************************************
* Function Name  : task_com_powerOnTime_str
* Description    : task_com_powerOnTime_str
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_powerOnTime_str(void);
/*******************************************************************************
* Function Name  : task_com_rec_show_time_str
* Description    : task_com_rec_show_time_str
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_rec_show_time_str(void);
/*******************************************************************************
* Function Name  : task_com_rec_show_time_str
* Description    : task_com_rec_show_time_str
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_rec_remain_time_str(void);
/*******************************************************************************
* Function Name  : task_com_play_time_str
* Description    : task_com_play_time_str
* Input          : int sel: 1: total time, 0: cur play time
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_play_time_str(int sel);
/*******************************************************************************
* Function Name  : task_com_curVolume_get
* Description    : task_com_curVolume_get
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
u32 task_com_curVolume_get(void);
/*******************************************************************************
* Function Name  : task_com_curVolume_cfg
* Description    : task_com_curVolume_cfg
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_curVolume_cfg(void);
/*******************************************************************************
* Function Name  : task_com_curVolume_strid
* Description    : task_com_curVolume_strid
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
u32 task_com_curVolume_strid(void);
/*******************************************************************************
* Function Name  : task_com_Volume_stridByIndex
* Description    : task_com_Volume_stridByIndex
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
u32 task_com_Volume_stridByIndex(u32 index);
/*******************************************************************************
* Function Name  : task_com_Volume_MaxIndex
* Description    : task_com_Volume_MaxIndex
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
u32 task_com_Volume_MaxIndex(void);
/*******************************************************************************
* Function Name  : task_com_fileIndex_str
* Description    : task_com_fileIndex_str
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_fileIndex_str(void);
/*******************************************************************************
* Function Name  : task_com_sdcCap_str
* Description    : task_com_sdcCap_str
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_sdcCap_str(void);
/*******************************************************************************
* Function Name  : task_com_tips_show
* Description    : task_com_tips_show
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_tips_show(u32 type);
/*******************************************************************************
* Function Name  : task_com_scaler_str
* Description    : task_com_scaler_str
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_scaler_str(void);
/*******************************************************************************
* Function Name  : task_com_scaler_rate
* Description    : task_com_scaler_rate
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
u32 task_com_scaler_rate(void);
/*******************************************************************************
* Function Name  : task_com_lcd_brightness_cfg
* Description    : task_com_lcd_brightness_cfg
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_lcd_brightness_cfg(int step, int step_max);
/*******************************************************************************
* Function Name  : task_com_service
* Description    : system service in task com
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_service(u32 scanKey);

/*******************************************************************************
* Function Name  : taskComDecodeImg
* Description    : taskComDecodeImg
* Input          : int type: MEDIA_SRC_FS/MEDIA_SRC_NVFS/MEDIA_SRC_RAM
				   int index:  type == MEDIA_SRC_FS, index means resId
				   			   type == MEDIA_SRC_NVFS, index means filelist index
				   			   type == MEDIA_SRC_RAM, index means jpg sdram addr
				   int size:   type == MEDIA_SRC_RAM, size means jpg size
*                  u8 *yuv_buff:   decode output yuv buf
*                  u16 tar_w: tar image width
*                  u16 tar_h: tar image height
* Output         : none
* Return         : int 0: success, <0 fail
*******************************************************************************/
int taskComDecodeImg(int type, int index, int size, u8 *yuv_buff,u16 tar_w,u16 tar_h);
/*******************************************************************************
* Function Name  : taskComDecodeAddToImg
* Description    : taskComDecodeAddToImg
* Input          : lcdshow_frame_t* p_frame,int index,uiRect* tar_rect
                   int type: MEDIA_SRC_FS/MEDIA_SRC_NVFS/MEDIA_SRC_RAM
				   int index:  type == MEDIA_SRC_FS, index means resId
				   			   type == MEDIA_SRC_NVFS, index means filelist index
				   			   type == MEDIA_SRC_RAM, index means jpg sdram addr
				   int size:   type == MEDIA_SRC_RAM, size means jpg size
* Output         : none
* Return         : int 0: success, <0 fail
*******************************************************************************/
int taskComDecodeAddToImg(lcdshow_frame_t* p_frame,int type,int index,int size, uiRect* tar_rect);
/*******************************************************************************
* Function Name  : taskComDecodeFrameToImg
* Description    : taskComDecodeFrameToImg
* Input          : lcdshow_frame_t* p_frame,int index,uiRect* tar_rect
                   int type: MEDIA_SRC_FS/MEDIA_SRC_NVFS/MEDIA_SRC_RAM
				   int index:  type == MEDIA_SRC_FS, index means resId
				   			   type == MEDIA_SRC_NVFS, index means filelist index
				   			   type == MEDIA_SRC_RAM, index means jpg sdram addr
				   int size:   type == MEDIA_SRC_RAM, size means jpg size
* Output         : none
* Return         : int 0: success, <0 fail
*******************************************************************************/
int taskComDecodeFrameToImg(lcdshow_frame_t* p_frame,int type,int index,int size, uiRect* tar_rect);
#endif
