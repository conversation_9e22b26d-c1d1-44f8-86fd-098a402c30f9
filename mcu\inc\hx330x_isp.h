/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  HX330X_ISP_H
    #define  HX330X_ISP_H
	
//#define _FRAME_NUMS_  			2  //_FRAME_NUMS_ must >=5 when stream-lined	
#define _EVSTEP_TAB_LEN_  		132//68

#define _YGAMA_STEPS_BIT_      	8
#define _YGAMA_STEPS_          	(1<<(_YGAMA_STEPS_BIT_))
#define _YGAMA_VAL_BIT_        	10
#define _YGAMA_MAX_VAL_        	((1<<(_YGAMA_VAL_BIT_)) - 1)

#define _RGB_GAMA_STEPS_BIT_   	8
#define _RGB_GAMA_STEPS_       	(1<<(_RGB_GAMA_STEPS_BIT_))
#define _RGB_GAMA_VAL_BIT_     	8
#define _RGB_GAMA_MAX_VAL_     	((1<<(_RGB_GAMA_VAL_BIT_)) - 1)

#define _SET_ISP_MODE_(x,model,val)  do{x = ((x)&(~(0x03<<(model)))); x = ((x)|(((val)&(_ISP_MODE_BW_))<<(model)));}while(0);	
#define _GET_ISP_MODE_(x,model)  (((x)>>model)&_ISP_MODE_BW_)

#define _SET_BIT_(x,model,val)		do{x = ((x)&(~(1<<(model)))); x = ((x)|(((val)&(0x1))<<(model)));}while(0);
#define	_GET_BIT_(x,model)			(((x)>>model)&0x01)


#define _ISP_BIT_WIDTH_ 	2
#define _ISP_MODE_BW_ 		0x03
#define _BLC_POS_         	(0*_ISP_BIT_WIDTH_)
#define _LSC_POS_    	  	(1*_ISP_BIT_WIDTH_)
#define _DDC_POS_         	(2*_ISP_BIT_WIDTH_)
#define _AWB_POS_         	(3*_ISP_BIT_WIDTH_)
#define _CFA_POS_         	(4*_ISP_BIT_WIDTH_)
#define _CCM_POS_         	(5*_ISP_BIT_WIDTH_)
#define _DGAIN_POS_       	(6*_ISP_BIT_WIDTH_)
#define _YGAMA_POS_       	(7*_ISP_BIT_WIDTH_)
#define _RGB_GAMA_POS_    	(8*_ISP_BIT_WIDTH_)
#define _CH_POS_          	(9*_ISP_BIT_WIDTH_)
#define _VDE_POS_         	(10*_ISP_BIT_WIDTH_)
#define _EE_POS_          	(11*_ISP_BIT_WIDTH_)
#define _CFD_POS_         	(12*_ISP_BIT_WIDTH_)
#define _SAJ_POS_         	(13*_ISP_BIT_WIDTH_)
#define _AE_POS_          	(14*_ISP_BIT_WIDTH_)
#define _YUVMOD_POS_        (15*_ISP_BIT_WIDTH_)



#define _ISP_DIS_        	0x00
#define _ISP_EN_         	0x01
#define _ISP_AUTO_       	0x02
//RAW域：BLC->LSC->DPC->AWB STAT->AWB->CFA(RAW->RGB)
//RGB域：CCM->AE STAT->DGAIN->Y_GAMMA->RGB_GAMMA->CH (RGB->YUV444)
//YUV域：VDE->(YUV444->YUV420)->SHARP(Y)->CCF(CbCr)->SAJ
#define _ISP_YUV422_DIS_    0x00	 //disable
#define _ISP_YUV422_CCM_    0x01	 //从CCM到SAJ
#define _ISP_YUV422_VDE_    0x02	 //从VDE到SAJ



typedef struct {
	u32 rotate;
} Rotate_Adapt;
typedef struct {
	u32 pclk;
	u32 v_len;
	u32 step_val;
	u32 step_max;
	u32 down_fps_mode;//0,1,hvb down_fps; 2: exp down_fps,0xff: turn off down_fps
	u32 fps;
	u32 frequency;
} Hvb_Adapt;							

typedef	struct
{
	u32 ylog_cal_fnum;
	u32 exp_tag[8];
	u32 exp_ext_mod;
	u32 exp_gain;
	u32 k_br;
	u32 exp_min;
	u32 gain_max;	
	u32 frame_nums;	//exp adj frame num
	u32 ratio_range;
	u32 weight_in;
	u32 weight_out;
	u32 ev_mode;	//1:ev adj in VDE, 0:ev adj in EXP
	
}EXP_Adapt;
typedef	struct
{
	u32 allow_miss_dots;
	u32 ae_win_x0;
	u32 ae_win_x1;
	u32 ae_win_x2;
	u32 ae_win_x3;
	u32 ae_win_y0;
	u32 ae_win_y1;
	u32 ae_win_y2;
	u32 ae_win_y3;
	u32 weight_0_7;   
	u32 weight_8_15;  
	u32 weight_16_23;  
	u32 weight_24;  
	u32 hgrm_centre_weight[8]; 
	u32 hgrm_gray_weight[8];  
}HGRM_Adapt;
typedef	struct
{
	EXP_Adapt exp_adapt;
	HGRM_Adapt hgrm_adapt;
}AE_Adapt;



typedef	struct
{
	int blkl_r;
	int blkl_gr;
	int blkl_gb;
	int blkl_b;
	u8 blk_rate[8];	
	int step_len;
}BLC_Adapt;


typedef	struct
{
	u32 hot_num;
	u32 dead_num;
	u32 hot_th;
	u32 dead_th;
	u32 avg_th;
	u32 d_th_rate[8];
	u32 h_th_rate[8];
	u32 dpc_dn_en;
	u32 indx_table[8];
	int indx_adapt[8];
	u32 std_th[7];
	u32 std_th_rate;
	u32 ddc_step;
	u32 ddc_class;
}DDC_Adapt;
typedef	struct
{
	u32 seg_mode;
	u32 rg_start;
	u32 rgmin;
	u32 rgmax; // 256 -> 1 gain  500 /256 =about 1.9 gain
	u32 weight_in;
	u32 weight_mid;
	u32 ymin;
	u32 ymax;
	u32 hb_rate;
	u32 hb_class;
	u32 hr_rate;
	u32 hr_class;
	u32 awb_scene_mod;
	u32 manu_awb_gain[5];
	u32 yuv_mod_en;
	u32 cb_th[8];
	u32 cr_th[8];
	u32 cbcr_th[8];
	u32 ycbcr_th;
	u32 manu_rgain;
	u32 manu_ggain;
	u32 manu_bgain;
	u32 rgain;
	u32 ggain;
	u32 bgain;
	u16 seg_gain[8][3];
	u8 awb_tab[128];
}AWB_Adapt; //raw -> calculate

typedef	struct
{
	u16 ccm[9];
	short s41;
	short s42;
	short s43;
}CCM_Adapt;

typedef struct
{
	u32 dgain[9];
	u32 dgain_rate[8];
}RGB_DGAIN_Adapt;

typedef	struct
{
	u32 tab_num[8];  
	u32 adpt_num[8];
	//u32 gma_num[8];
	u32 gam_num0;
	u32 gam_num1;
	u32 contra_num;
	u32 br_mod;
	u32 bofst;
	u32 lofst;

	u32 pad_num;
	//u16 using_ygama[_YGAMA_STEPS_*2]; 
}YGAMA_Adapt;
typedef	struct
{
	u32 tab_num[8];  
	u32 adpt_num[8];
	u32 max_oft[8];
	u32 gam_num0;
	u32 gam_num1;
	u32 max_oft0;
	u32 br_mod;
	u32 rmin;
	u32 rmax;
	u32 gmin;
	u32 gmax;
	u32 bmin;
	u32 bmax;
	u32 fog_llimt;
	u32 fog_hlimt;
	u32 fog_dotnum;
	//u8 using_rgbgama[_RGB_GAMA_STEPS_*2];
}RGB_GAMA_Adapt;
typedef	struct
{
	u32 stage0_en;//enable r g b
	u32 stage1_en;//enable y c m
	u32 enhence[6];//enhance channel  r b g y c m
	u32 th1[6];//you can set hue width
	u32 th0[6];
	//m_x r_x y_x b_x g_r r_x
	u32 r_rate[6];//combining with sat[],you can enhance or weaken
	u32 g_rate[6];
	u32 b_rate[6];
	u32 sat[17];//16?a1
	u32 rate[8];
	u32 ch_step;
}CH_Adapt;
typedef	struct
{
	u32 contra;
	u32 bright_k; // 80 -> 1 gain
	u32 bright_oft; // bright_oft * bright_K 
	u32 hue;   
	u32 sat[9];
	u32 sat_rate[8];
	u32 vde_step;
}VDE_Adapt;
typedef	struct
{
	u16 ee_class;	
	u16 ee_step;
	u8 	ee_dn_slope[8];
	u8 	ee_sharp_slope[8];
	u8 	ee_th_adp[8];
	u8 	ee_dn_th[8]; 
	u8 	sharp_class[8];
	u8 	dn_class[8];
	
}EE_Adapt;
typedef	struct
{
	u32 rate; // (1)  rate / 16 
	u32 ymax;//  (1)  3*3
	u32 th; //   (1)  |cb -cr|
	u32 wdc_en;  // 
	u32 wclass; //ccf_start: wymin - (16<<wclass)   reduce saturation
	u32 wymin; // (2) 
	u32 mean_en; // (3)
	u32 dn_class;
	u32 ccf_en;
}CCF_Adapt;  
typedef	struct
{
	u8 sat[17];
	u8 sat_rate[8];
	u8 saj_step;
	u8 rev[2];
}SAJ_Adapt;  
typedef	struct
{
	u32 pixel_th;
	u32 num_th;
	u32 update_cnt;
	u32 win_h_start;
	u32 win_h_end;
	u32 win_v_start;
	u32 win_v_end;
}MD_Adapt;




//map to 

extern u16 	Y_GMA_Tab[_YGAMA_STEPS_];
extern u8 	RGB_GMMA_Tab[3][_RGB_GAMA_STEPS_];

extern const s8 LOG_TAB[25];
extern const s8 GAOS5X5_TAB[25];
extern const s8 GAOS3X3_TAB[9];
extern const u16 Ratio_of_Evstep[_EVSTEP_TAB_LEN_];
/*******************************************************************************
* Function Name  : hx330x_isp_mask_tab_cfg
* Description    : hx330x_isp_mask_tab_cfg
* Input          : s8 (*ee_sharp_tab)[25] : ee sharp mask tab
				   s8 (*ee_dn_tab)[25]: ee denoise mask tab
				   s8 (*ccf_dn_tab)[9]: ccf denoise mask tab
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_isp_mask_tab_cfg( s8 (*ee_sharp_tab)[25], s8 (*ee_dn_tab)[25], s8 (*ccf_dn_tab)[9]);
/*******************************************************************************
* Function Name  : hx330x_ispModeSet
* Description    : csi mode set
* Input          : u32 mode : mode flag
				   u8 en : 1-enable,0-disable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_ispModeSet(u32 mode,u8 en);
/*******************************************************************************
* Function Name  : hx330x_isp_stat_en
* Description    : hx330x_isp_stat_en
* Input          : bool en_ae : ae stastic en
				   bool en_wpt : wpt stastic en
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_isp_stat_en(bool en_ae, bool en_wpt);
/*******************************************************************************
* Function Name  : hx330x_isp_BLC_cfg
* Description    : cfg the blc
* Input          : BLC * p_blc   
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_BLC_cfg(BLC_Adapt * p_blc);
/*******************************************************************************
* Function Name  : hx330x_isp_LSC_cfg
* Description    : cfg lens shading correction and gbgr correction
* Input          : void * p_lsc_tab  :lsc table
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_LSC_cfg(void *p_lsc_tab);
/*******************************************************************************
* Function Name  : hx330x_isp_DDC_cfg
* Description    : config isp DDC
* Input          : DDC* p_ddc, u32 d_th_rate,u32 h_th_rate,u32 *indx_table
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_DDC_cfg(DDC_Adapt* p_ddc, u32 d_th_rate,u32 h_th_rate,u32 *indx_table);
/*******************************************************************************
* Function Name  : hx330x_isp_AWB_GAIN_adj
* Description    : awb gain adjust
* Input          : u32 Kb : bgain 
* 				   u32 Kg : ggain  
*                  u32 Kr : rgain 
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_AWB_GAIN_adj(u32 Kb, u32 Kg, u32 Kr);
/*******************************************************************************
* Function Name  : hx330x_isp_whtpnt_stat_cfg
* Description    : cfg wp stat
* Input          : AWB * p_awb
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_whtpnt_stat_cfg(AWB_Adapt * p_awb);
/*******************************************************************************
* Function Name  : hx330x_isp_CCM_cfg
* Description    : CCM *p_ccm
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_CCM_cfg(CCM_Adapt *p_ccm);
/*******************************************************************************
* Function Name  : hx330x_isp_RGB_DGAIN_adj
* Description    : awb gain adjust
* Input          : u32* p_rgb_dgain, p_rgb_dgain[9]
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_RGB_DGAIN_adj(u32* p_rgb_dgain );
/*******************************************************************************
* Function Name  : hx330x_isp_hist_stat_cfg
* Description    : cfg isp ae globel hist stat
* Input          : HGRM* p_hgrm_adapt,u32 *p_csi_hist
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_hist_stat_cfg(HGRM_Adapt* p_hgrm_adapt,u32 *p_csi_hist);
/*******************************************************************************
* Function Name  : hx330x_isp_YGAMMA_cfg
* Description    : hx330x_isp_YGAMMA_cfg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_YGAMMA_cfg(YGAMA_Adapt *p_ygama, u16* using_ygamma);
/*******************************************************************************
* Function Name  : hx330x_isp_ylog_ygamma_cal
* Description    : cal ylog val after ygamma
* Input          : u32* ylog
* Output         : None
* Return         : -1: fail, >=0: success
*******************************************************************************/
void hx330x_isp_ylog_ygamma_cal(u32* ylog);
/*******************************************************************************
* Function Name  : hx330x_isp_RGBGAMMA_cfg
* Description    : cfg isp ae globel hist stat
* Input          : RGB_GAMA *p_rgb_gama,  u32 max_oft
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_RGBGAMMA_cfg(RGB_GAMA_Adapt *p_rgb_gama,u8* using_rgbgamma,u32 max_oft);
/*******************************************************************************
* Function Name  : hx330x_isp_RGBGAMMA_cfg_tab
* Description    : 
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_RGBGAMMA_cfg_filter(u8* r_tab, u8* g_tab, u8* b_tab);
/*******************************************************************************
* Function Name  : hx330x_isp_CH_cfg
* Description    : cfg color enhance
* Input          : CH *p_ch, u32*p_r_rate, u32*p_g_rate, u32*p_b_rate, u32*p_sat
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_CH_cfg(CH_Adapt *p_ch, u32*p_r_rate, u32*p_g_rate, u32*p_b_rate, u32*p_sat);
/*******************************************************************************
* Function Name  : hx330x_isp_VDE_cfg
* Description    : cfg VDE
* Input          : VDE *p_vde,u32 * p_sat
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_VDE_cfg(VDE_Adapt *p_vde,u32 * p_sat);
/*******************************************************************************
* Function Name  : hx330x_isp_CR_cfg
* Description    : hx330x_isp_CR_cfg:用户自己单独配置对比度
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_isp_CR_cfg(u8 contras);
/*******************************************************************************
* Function Name  : hx330x_isp_EE_cfg
* Description    : cfg EE0 
* Input          : u32 sharp_slope_w,u32 sharp_th,u32 sharp_th_adp,u32 sharp_class, u32 dn_class
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_EE_cfg(u8 ee_dn_th,u8 ee_dn_slope,u8 ee_sharp_slope,u8 ee_th_adp,u8 sharp_class,u8 dn_class);
/*******************************************************************************
* Function Name  : hx330x_isp_CCF_cfg
* Description    : cfg CCF 
* Input          : CCF *p_ccf
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_CCF_cfg(CCF_Adapt *p_ccf);
/*******************************************************************************
* Function Name  : hx330x_isp_SAJ_cfg
* Description    : cfg SAJ
* Input          : u8* sj_sr(sj_sr[17])
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_SAJ_cfg(u8* sj_sr);
/*******************************************************************************
* Function Name  : hx330x_isp_kick_stat
* Description    : hx330x_isp_kick_stat
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_kick_stat(void);
/*******************************************************************************
* Function Name  : hx330x_isp_stat_cp_kick_st
* Description    : hx330x_isp_stat_cp_kick_st
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_stat_cp_kick_st(void);
/*******************************************************************************
* Function Name  : hx330x_isp_stat_cp_done
* Description    : hx330x_isp_stat_cp_done
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_stat_cp_done(void);
/*******************************************************************************
* Function Name  : hx330x_isp_model_cfg
* Description    : hx330x_isp_model_cfg
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_isp_model_cfg(u32 model_num,u32 val);










#endif
