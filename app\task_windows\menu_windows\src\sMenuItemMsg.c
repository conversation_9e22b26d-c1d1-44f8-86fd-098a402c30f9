/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"



typedef struct MENU_CTL_S{
	menu*	currentMenu;
	u32 	menuOpened;
}MENU_CTL_T;
ALIGNED(4) static MENU_CTL_T	menu_ctl = {NULL, 0};
#define currentPage()    	(menu_ctl.currentMenu->pPage[menu_ctl.currentMenu->curPage])
#define getItem(n)   		(currentPage().pItem[n])
#include "sMenuItemWin.c"
/*******************************************************************************
* Function Name  : menuIsOpen
* Description    : menuIsOpen
* Input          : none
* Output         : none
* Return         : u32: 0: close, 1: open
*******************************************************************************/
u32 menuWinIsOpen(void)
{
	return menu_ctl.menuOpened;
}
/*******************************************************************************
* Function Name  : getItemResInfor
* Description    : getItemResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : u32:
*******************************************************************************/
UNUSED static u32 getItemResInfor(u32 item,u32* image,u32* str)
{
	if(image)
		*image 	= getItem(item).image;
	if(str)
		*str	= getItem(item).str;
	return 0 ;
}
/*******************************************************************************
* Function Name  : getItemResInforEx
* Description    : getItemResInforEx
* Input          : u32 item,u32* image,u32* str,u32* selectImage,u32* selectStr
* Output         : none
* Return         : u32:
*******************************************************************************/
static u32 getItemResInforEx(u32 item,u32* image,u32* str,u32* selectImage,u32* selectStr)
{
	u32 config;
	u32 selImg = INVALID_RES_ID;
	u32 selStr = INVALID_RES_ID;
	menuItem* pItem = &getItem(item);
	if(pItem->str == R_ID_STR_SET_FORMAT)
	{
		selStr = RAM_ID_MAKE(task_com_sdcCap_str());
		selImg = R_ID_ICON_MTMORE;
	}else if(pItem->str == R_ID_STR_SET_SREEN_BRIGHT)
	{
		selStr = user_config_get(CONFIG_ID_SREEN_BRIGHT);
		selImg = R_ID_ICON_MTMORE;
	}else if(pItem->str == R_ID_STR_SET_SYSTEM_VOLUME)
	{
		selStr = user_config_get(CONFIG_ID_SYSTEM_VOLUME);
		selImg = R_ID_ICON_MTMORE;
	}
	else if(pItem->optionSum > 0)
	{
		if(pItem->configId == 0)
		{
			selStr   = pItem->pOption[0].str;
		}else
		{
			config = user_config_get(pItem->configId);
		#if 0
			if(pItem->optionSum==2 && (config == R_ID_STR_COM_OFF || config == R_ID_STR_COM_ON))
			{
				if(selectImage)
				{
					if(config == R_ID_STR_COM_OFF)
						selImg = R_ID_ICON_MTOFF;
					else
						selImg = R_ID_ICON_MTON;
				}
			}
			else
		#endif
			{
				selImg = R_ID_ICON_MTMORE;
				selStr = config;
			}
		}
	}else{
		selImg = R_ID_ICON_MTMORE;
	}

	if(image)
		*image	=	pItem->image;
	if(str)
		*str	=	pItem->str;
	if(selectImage)
		*selectImage = selImg;
	if(selectStr)
		*selectStr = selStr;
	return 0;

}
/*******************************************************************************
* Function Name  : menuItemKeyMsgOk
* Description    : menuItemKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	menuItem* pItem;

	u32 	  keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		pItem = &getItem(uiItemManageGetCurrentItem(winItem(handle,ITEM_SELECT_ID)));
		if(pItem->optionSum == 0)
			((menuItemProc)pItem->pOption)(handle,parameNum,parame);
		else if(pItem->optionSum > 1)
		{
		#if 0//UI_SHOW_SMALL_PANEL == 0// only 2 option and one of them is "OFF" and another is "ON"
             u32 	  config;
			config = user_config_get(pItem->configId);
			if(pItem->optionSum == 2 && (config == R_ID_STR_COM_OFF || config == R_ID_STR_COM_ON))
			{
				if(config==R_ID_STR_COM_OFF)
					user_config_set(pItem->configId,R_ID_STR_COM_ON);
				else
					user_config_set(pItem->configId,R_ID_STR_COM_OFF);
				user_config_cfgSys(pItem->configId);
				user_config_save();
				uiItemManageUpdateCurItem(winItem(handle,ITEM_SELECT_ID));
			}
			else
		#endif
				uiOpenWindow(&menuOptionWindow,0,1,pItem);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuItemKeyMsgUp
* Description    : menuItemKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum==1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,ITEM_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuItemKeyMsgDown
* Description    : menuItemKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManageNextItem(winItem(handle,ITEM_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuItemKeyMsgPower
* Description    : menuItemKeyMsgPower
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		menu_ctl.currentMenu->curPage++;
		if(menu_ctl.currentMenu->curPage < menu_ctl.currentMenu->pageSum)
		{
			//uiWinSetResid(winItem(handle,ITEM_MODE_ID),currentPage().selectImage);
			uiItemManageUpdateRes(winItem(handle,ITEM_SELECT_ID),currentPage().itemSum,0);
		}
		else
		{
			//uiWinDestroy(&handle);
			SysCtrl.winChangeEnable = 1;
			app_taskStart(TASK_MAIN,0);
			//SysCtrl.winChangeEnable = 0;
			//app_taskStart(TASK_NES_GAME,0);
		}

	}
	return 0;
}

/*******************************************************************************
* Function Name  : menuItemSysMsgSD
* Description    : menuItemSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	uiItemManageUpdateAllItem(winItem(handle,ITEM_SELECT_ID));
	return 0;
}


/*******************************************************************************
* Function Name  : menuItemOpenWin
* Description    : menuItemOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum < 1)
	{
		uiWinDestroy(&handle);
		return 0;
	}
	deg_Printf("[WIN]menuItemOpenWin\n");
	menu_ctl.currentMenu = (menu*)parame[0];
	menu_ctl.currentMenu->curPage = 0;
	uiItemManageSetHeightNotGap(winItem(handle,ITEM_SELECT_ID),Rh(34));
	uiItemManageCreateItem(		winItem(handle,ITEM_SELECT_ID),uiItemCreateMenuItemEx2,NULL,currentPage().itemSum);
	uiItemManageSetResInforFuncEx(winItem(handle,ITEM_SELECT_ID),getItemResInforEx);
#if 0
	uiItemManageSetCharInfor(	winItem(handle,ITEM_SELECT_ID),DEFAULT_FONT,ALIGNMENT_LEFT,R_ID_PALETTE_White);
	uiItemManageSetSelectColor(	winItem(handle,ITEM_SELECT_ID),R_ID_PALETTE_DoderBlue);
	uiItemManageSetUnselectColor(winItem(handle,ITEM_SELECT_ID),R_ID_PALETTE_Gray);
#else
	uiItemManageSetSelectColorEx(winItem(handle,ITEM_SELECT_ID),DEFAULT_FONT,ALIGNMENT_LEFT, MENU_SELECT_FN_COLOR, MENU_SELECT_BG_COLOR);
	uiItemManageSetUnselectColorEx(winItem(handle,ITEM_SELECT_ID),DEFAULT_FONT,ALIGNMENT_LEFT, MENU_UNSELECT_FN_COLOR, MENU_UNSELECT_BG_COLOR);
#endif

	uiItemManageNextItem(			winItem(handle,ITEM_SELECT_ID));
	//uiWinSetResid(				winItem(handle,ITEM_MODE_ID),currentPage().selectImage);
	menu_ctl.menuOpened = 1;
	return 0;
}
/*******************************************************************************
* Function Name  : menuItemCloseWin
* Description    : menuItemCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]menuItemCloseWin\n");
	menu_ctl.currentMenu = NULL;
	menu_ctl.menuOpened	 = 0;
	return 0;
}
/*******************************************************************************
* Function Name  : menuItemWinChildClose
* Description    : menuItemWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]menuItemWinChildClose\n");
	//uiWinSetResid(winItem(handle,ITEM_MODE_ID),currentPage().selectImage);
	//uiItemManageUpdateCurItem(winItem(handle,ITEM_SELECT_ID));
	uiItemManageUpdateAllItem(winItem(handle,ITEM_SELECT_ID));
	return 0;
}


ALIGNED(4) msgDealInfor menuItemMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	menuItemOpenWin},
	{SYS_CLOSE_WINDOW,	menuItemCloseWin},
	{SYS_CHILE_COLSE,	menuItemWinChildClose},
	{KEY_EVENT_OK,		menuItemKeyMsgOk},
	{KEY_EVENT_UP,		menuItemKeyMsgUp},
	{KEY_EVENT_DOWN,	menuItemKeyMsgDown},
	//{KEY_EVENT_LEFT,	menuItemKeyMsgUp},
	//{KEY_EVENT_RIGHT,	menuItemKeyMsgDown},
	{KEY_EVENT_POWER,	menuItemKeyMsgPower},
	{SYS_EVENT_SDC,		menuItemSysMsgSD},
	{EVENT_MAX,			NULL},
};

WINDOW(menuItemWindow,menuItemMsgDeal,menuItemWin)


