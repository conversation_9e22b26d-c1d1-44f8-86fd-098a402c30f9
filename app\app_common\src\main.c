/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../inc/app_api.h"

/*******************************************************************************
* Function Name  : main
* Description    :
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
u8 flash_success;

int main(void)
{
//--------------power on--------
    app_init();       // system power on configure
	flash_success = spi_flash_check_md5();

//----fireware upgrade
	

	//hal_timerPWMStart(TIMER2,TMR2_PWM_POS_PD0,10000, 30);
// start default task
	taskMainWinChangeInit();
#if FUN_BATTERY_CHARGE_SHOW
	if(SysCtrl.dev_stat_power & POWERON_FLAG_DCIN)
	{
		app_taskStart(TASK_BAT_CHARGE,1);
	}
	else
#endif
	{	
		taskSdUpdateProcess();
		SysCtrl.winChangeEnable = 1;
		app_taskStart(TASK_MAIN,0);
	}
	//XMsgQFlush(SysCtrl.sysQ);
	app_taskService();
	return 2; // for usb upgrade
}



