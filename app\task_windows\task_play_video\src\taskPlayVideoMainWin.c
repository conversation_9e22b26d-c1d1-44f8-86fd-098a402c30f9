/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	PLAYVIDEOMAIN_MODE_ID=0,
	PLAYVIDEOMAIN_SD_ID,
	PLAYVIDEOMAIN_SDVOL_ID,
	PLAYVIDEOMAIN_FILE_NAME_ID,
	PLAYVIDEOMAIN_FILE_INDEX_ID,

	PLAYVIDEOMAIN_TIMEBAR_ID,
	PLAYVIDEOMAIN_CUR_TIME_ID,
	PLAYVIDEOMAIN_TOTAL_TIME_ID,
	PLAYVI<PERSON>OMAIN_STAT_ID,
	PLAYVI<PERSON>OMAIN_RESOLUTION_ID,
	PLAYVIDEOMAIN_BATERRY_ID,
	PLAYVIDEOMAIN_ERROR_ID,
	PLAYVIDEOMAIN_MAX_ID
};

UNUSED ALIGNED(4) const widgetCreateInfor playVideoMainWin[] =
{
	createFrameWin( 								Rx(0),   Ry(0),   Rw(320), Rh(240), R_ID_PALETTE_Transparent, WIN_ABS_POS),
	createImageIcon(PLAYVIDEOMAIN_MODE_ID,      	Rx(4),   Ry(0),   Rw(32),  Rh(32),  R_ID_ICON_MTPLAY, 		ALIGNMENT_CENTER),
	createImageIcon(PLAYVIDEOMAIN_SD_ID,        	Rx(40),  Ry(0),   Rw(40),  Rh(32), 	R_ID_ICON_MTSDCNORMAL,	ALIGNMENT_CENTER),
	createStringIcon(PLAYVIDEOMAIN_SDVOL_ID,		Rx(40),  Ry(5),   Rw(36),  Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_GREEN2,	DEFAULT_FONT),

	createStringIcon(PLAYVIDEOMAIN_FILE_NAME_ID,    Rx(80),  Ry(4),   Rw(150), Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_Yellow,	DEFAULT_FONT),
	createStringIcon(PLAYVIDEOMAIN_FILE_INDEX_ID,   Rx(222), Ry(4),   Rw(90), Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_RIGHT, 	R_ID_PALETTE_Yellow,	DEFAULT_FONT),

	createStringIcon(PLAYVIDEOMAIN_CUR_TIME_ID,		Rx(40),  Ry(4),   Rw(140), Rh(24),	RAM_ID_MAKE(" "),	    ALIGNMENT_LEFT, 	R_ID_PALETTE_Red,       DEFAULT_FONT),
	createStringIcon(PLAYVIDEOMAIN_TOTAL_TIME_ID,	Rx(180), Ry(4),   Rw(136), Rh(24),	RAM_ID_MAKE(" "),	    ALIGNMENT_RIGHT, 	R_ID_PALETTE_White,     DEFAULT_FONT),
	createProgressBar(PLAYVIDEOMAIN_TIMEBAR_ID,     Rx(40),  Ry(40),  Rw(240), Rh(5),   R_ID_PALETTE_DarkGray,  R_ID_PALETTE_Blue, ALIGNMENT_LEFT),
	createImageIcon(PLAYVIDEOMAIN_STAT_ID,     		Rx(120), Ry(80),  Rw(80),  Rh(80),  R_ID_ICON_MTPLAY1,      ALIGNMENT_CENTER),
	createStringIcon(PLAYVIDEOMAIN_ERROR_ID,      	Rx(120), Ry(100), Rw(80),  Rh(40),	RAM_ID_MAKE("ERROR"),	ALIGNMENT_CENTER, 	R_ID_PALETTE_Red,	    DEFAULT_FONT),
	createStringIcon(PLAYVIDEOMAIN_RESOLUTION_ID,	Rx(240), Ry(184), Rw(72),  Rh(24),  RAM_ID_MAKE(" "),		ALIGNMENT_RIGHT, 	R_ID_PALETTE_White,	    DEFAULT_FONT),
	createImageIcon(PLAYVIDEOMAIN_BATERRY_ID,    	Rx(240), Ry(208), Rw(76),  Rh(32), 	R_ID_ICON_MTBATTERY3,	ALIGNMENT_RIGHT),
	widgetEnd(),
};
/*******************************************************************************
* Function Name  : playVideoMainSDShow
* Description    : playVideoMainSDShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainSDShow(winHandle handle, u32 visable)
{
	if(visable)
	{
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_SD_ID),1);
		if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
		{
			uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_SD_ID),R_ID_ICON_MTSDCNORMAL);
			uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_SDVOL_ID),1);
			uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_SDVOL_ID),RAM_ID_MAKE(task_com_sdcCap_str()));
			
		}	
		else{
			uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_SD_ID),R_ID_ICON_MTSDCNULL);
			uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_SDVOL_ID),0);
		}	
		
	}else
	{
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_SD_ID),0);
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_SDVOL_ID),0);
	}
		

}
/*******************************************************************************
* Function Name  : playVideoMainFileNameShow
* Description    : playVideoMainFileNameShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainFileNameShow(winHandle handle, u32 visable)
{
	deg_Printf("playVideoMainFileNameShow:%d\n", visable);
	uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_FILE_NAME_ID),visable);
	uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_FILE_INDEX_ID),visable);
	if(visable)
	{
		char* name = filelist_GetFileShortNameByIndex(playVideoOp.list,SysCtrl.file_index,NULL);
		if(name)
			uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_FILE_NAME_ID),RAM_ID_MAKE(name));
		else
			uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_FILE_NAME_ID),RAM_ID_MAKE(" "));
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_FILE_INDEX_ID),RAM_ID_MAKE(task_com_fileIndex_str()));
	}


}
/*******************************************************************************
* Function Name  : playVideoMainPlayTimeShow
* Description    : playVideoMainPlayTimeShow
* Input          : winHandle handle,u32 playTime,u32 totalTime
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainPlayTimeShow(winHandle handle, u32 visable)
{
	if(visable && !(SysCtrl.file_type & FILELIST_TYPE_AVI))
	{
		visable = 0;
	}
	uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_CUR_TIME_ID),  visable);
	uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_TOTAL_TIME_ID),visable);
	uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_TIMEBAR_ID),   visable);
	if(visable)
	{
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_CUR_TIME_ID),RAM_ID_MAKE(task_com_play_time_str(0)));
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_TOTAL_TIME_ID),RAM_ID_MAKE(task_com_play_time_str(1)));
		if(videoPlaybackGetStatus() == MEDIA_STAT_STOP)
		{
			uiWinSetProgressRate(winItem(handle,PLAYVIDEOMAIN_TIMEBAR_ID), 100);
		}else
		{
			uiWinSetProgressRate(winItem(handle,PLAYVIDEOMAIN_TIMEBAR_ID), (SysCtrl.play_cur_time *100) / SysCtrl.play_total_time);
		}
		
	}

}
/*******************************************************************************
* Function Name  : playVideoMainPlayTimeShow
* Description    : playVideoMainPlayTimeShow
* Input          : winHandle handle,u32 playTime,u32 totalTime
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainStatShow(winHandle handle, u32 visable)
{
	
	uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_STAT_ID),visable);
}
/*******************************************************************************
* Function Name  : playVideoMainPlayTimeShow
* Description    : playVideoMainPlayTimeShow
* Input          : winHandle handle,u32 playTime,u32 totalTime
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainErrorShow(winHandle handle, u32 visable)
{
	uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_ERROR_ID),visable);
}
/*******************************************************************************
* Function Name  : playVideoMainResolutionShow
* Description    : playVideoMainResolutionShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainResolutionShow(winHandle handle)
{
	u16 width,height;
	if(SysCtrl.file_type & FILELIST_TYPE_JPG)
	{
		imageDecodeGetResolution(&width,&height);
		u32 size = width*height;
		char* str;
		if(size <        100000) str = "QVGA";
		else if(size <   500000) str = "VGA";
		else if(size <  1500000) str = "1M";
		else if(size <  2500000) str = "2M";
		else if(size <  4000000) str = "3M";
		else if(size <  6500000) str = "5M";
		else if(size <  9000000) str = "8M";
		else if(size < 11000000) str = "10M";
		else if(size < 14000000) str = "12M";
		else if(size < 17000000) str = "16M";
		else if(size < 19000000) str = "18M";
		else if(size < 22000000) str = "20M";
		else if(size < 32000000) str = "24M";
		else if(size < 44000000) str = "40M";
		else					 str = "48M"; 

		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE(str));
	}
	else if(SysCtrl.file_type & FILELIST_TYPE_AVI)
	{
		AVI_DEC_ARG *arg = videoPlaybabkGetArg();
		width 	= arg->width;
		height 	= arg->height;
		switch((width<<16)|height)
		{
			case ((320<<16)|240)	:	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("QVGA"));	break;
			case ((640<<16)|480)	:	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("VGA"));	break;
			case ((1280<<16)|720)	:
			case ((1280<<16)|960)	:	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("HD"));		break;
			case ((1920<<16)|1080)	:	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("FHD"));	break;
			default:					uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE(" "));	break;
		}
	}else
	{
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE(" "));
	}

}
/*******************************************************************************
* Function Name  : playVideoMainBaterryShow
* Description    : playVideoMainBaterryShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainBaterryShow(winHandle handle)
{
	//uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_BATERRY_ID),1);
	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_BATERRY_ID),task_com_battery_res_get());

}
