/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"


u16 game_frame_w;
u16 game_frame_h;

INT8U game_over_in=0;

static inline void g_drawDrawPoint(INT8U* drawImg,INT16U startx,INT16U starty, INT8U color)
{
	
	drawImg[startx + starty*game_frame_w] = color;
}

void g_Draw_Circle(INT8U* drawImg,INT16U x0,INT16U y0,INT16U r,INT8U color)
{
	int a,b;
	int di;
	a=0;b=r;
	di=3-(r<<1);             //?卸??????位?????

	while(a<=b)
	{
		g_drawDrawPoint(drawImg,x0+a,y0-b,color);             //5
 		g_drawDrawPoint(drawImg,x0+b,y0-a,color);             //0
		g_drawDrawPoint(drawImg,x0+b,y0+a,color);             //4
		g_drawDrawPoint(drawImg,x0+a,y0+b,color);             //6
		g_drawDrawPoint(drawImg,x0-a,y0+b,color);             //1
 		g_drawDrawPoint(drawImg,x0-b,y0+a,color);
		g_drawDrawPoint(drawImg,x0-a,y0-b,color);             //2
  		g_drawDrawPoint(drawImg,x0-b,y0-a,color);             //7
		a++;
		//???Bresenham?????
		if(di<0)di +=4*a+6;
		else
		{
			di+=10+4*(a-b);
			b--;
		}
	}
}



void g_drawLine(INT8U* drawImg, INT16U startx, INT16U starty, INT16U endx, INT16U endy, INT8U color)
{
	INT16U t, distance;
	INT16S xerr=0, yerr=0, delta_x, delta_y;
	INT16S incx, incy,uRow,uCol;
	/* compute the distances in both directions */
	delta_x=endx-startx;
	delta_y=endy-starty;
	uRow=startx;
	uCol=starty;

	/* Compute the direction of the increment,
	an increment of 0 means either a horizontal or vertical
	line.
	*/
	if(delta_x>0)incx=1; //???????????
	else if(delta_x==0)incx=0;//?????
	else {incx=-1;delta_x=-delta_x;}
	if(delta_y>0)incy=1;
	else if(delta_y==0)incy=0;//????
	else{incy=-1;delta_y=-delta_y;}
	if(delta_x>delta_y) distance=delta_x;
	else distance=delta_y;

	/* draw the line */
	for(t=0; t<=distance+1; t++)
	{
		if(startx > 0 && startx < game_frame_w && starty > 0 && starty < game_frame_h){
			g_drawDrawPoint(drawImg,uRow,uCol,color);
			//drawImg[uRow + uCol*game_frame_w] = color;
		}

		xerr+=delta_x;
		yerr+=delta_y;
		if(xerr>distance) {
			xerr-=distance;
			uRow+=incx;
		}
		if(yerr>distance) {
			yerr-=distance;
			uCol+=incy;
		}
	}
}

void g_Draw_Fill(INT8U*buff,INT16U xStart,INT16U yStart,INT16U xEnd,INT16U yEnd,INT8U color)
{
//deg_Printf("xStart[%d],yStart[%d],xEnd[%d],yEnd[%d],game_frame_w[%d],game_frame_h[%d]\n",xStart,yStart,xEnd,yEnd,game_frame_w,game_frame_h);

	INT16U temp;
	INT16U Point_x,Point_y;
	INT16U x=xStart ,y=yStart;

    if(xEnd>=game_frame_w)
    {
        return;
    }
    if(yEnd>=game_frame_h)
    {
        return;
    }

    if((xStart > xEnd) || (yStart > yEnd))
    {
        return;
    }

#if 1
    xStart = xEnd - xStart + 1;
    yStart = yEnd - yStart + 1;

    while(xStart--)
    {
        temp = yStart;
        while (temp--)
        {
        //deg_Printf("temp[%d]\n",temp);
            Point_x=xStart+x ;
            Point_y=temp+y;

            g_drawDrawPoint(buff,Point_x,Point_y,color);
            //buff[xState+x + (temp+y)*game_frame_w] = color;
        }
    }
#else
	u8 *buf_temp = buff;
	u32 w,h,i,j;
	w = xEnd - xStart + 1;
	h = yEnd -yStart + 1;

	buf_temp = buff + (game_frame_w*h) + xStart;

	for(i=0;i<h;i++){
		for(j=0;j<w;j++){
			*(buf_temp + j) = color;
		}
		buf_temp+=game_frame_w;
	}
#endif
}

/*******************************************************************************
* Function Name  : uiWinDrawIcon
* Description    : res_draw_Icon
* Input          : uiRect* winRect,uiRect* drawRect,resID id,u8 alignment,uiColor bgColor
* Output         : none
* Return         : none
*******************************************************************************/
void OSDFrameDrawIcon(u8* frame,u8* icon_data,u16 x,u16 y,u16 icon_w,u16 icon_h)
{
	uiColor *frame_start = frame;  //start point of each block
	u16 i,j;
	u16 frame_w = game_frame_w;
	u16 frame_h = game_frame_h;
	if(frame == NULL)
		return;
	//deg_Printf("x y w h = %d %d %d %d \n",x,y,icon_w,icon_h);
	//debgbuf(icon_data,icon_w*icon_h);
	frame_start += (frame_w*y + x);

	for(i=0;i<icon_h;i++){
		for(j=0;j<icon_w;j++){
			*(frame_start+j) = *icon_data++;
		}
		frame_start += frame_w;
	}
}

//??????
//(x1,y1),(x2,y2):???蔚???????
void g_DrawRectangle_Color(INT8U*buff,INT16U x1, INT16U y1, INT16U x2, INT16U y2,INT8U color)
{
	g_drawLine(buff,x1,y1,x2,y1,color);
	g_drawLine(buff,x1,y1,x1,y2,color);
	g_drawLine(buff,x1,y2,x2,y2,color);
	g_drawLine(buff,x2,y1,x2,y2,color);
}

u16 g_draw_char(u8 *display_buf,u16 x,u16 y,u8 ascii_code,u8 font,u32 font_color,u8 bg_color)
{
	u16 i,j,char_w,char_h;
	u8* addr;
	u8 mask,temp;
	u8 *pStartAddr;
	
	if(NULL == display_buf){
        	return 0;
	}

	addr = res_ascii_get(ascii_code,&char_w,&char_h,font);
	if(NULL == addr){
        return 0;
	}
	pStartAddr = display_buf+(x)+((y*game_frame_w));

	for(i=0;i<char_h;i++)
	{
	    for(j=0;j<(char_w+7)/8;j++)
		{
	         temp = *addr++;
			//  deg_Printf("%x ",temp);
			 for(mask=0;mask<8;mask++)
			 {
				 if(temp&(0x80>>mask))
				 {
					 *pStartAddr++ = font_color;
				 }
				 else
				 {
				 	//if(backcolor)
					// *pStartAddr++=backcolor;
					//else
					 pStartAddr++;
				 }
			 }
		 }
		 y++;
		 pStartAddr =display_buf+(x)+((y*game_frame_w));
	}
	return char_w;  

}

u16 g_draw_str(u8 *display_buf,u16 x,u16 y,u8* str,u8 font,u32 font_color,u8 bg_color)
{
	u16 str_len;
	u16 i,j;
	u16 x_temp,y_temp,w_temp = 0;


	if(NULL == str){
		return 0;
	}

	str_len = hx330x_str_len(str);
	if(str_len>32)
		str_len = 32;//max 32 bytes
	else if(str_len == 0){
		return 0;
	}

	x_temp = x;
	y_temp = y;

	debgreg(str_len);
	
	for(i=0;i<str_len;i++){
		w_temp = g_draw_char(display_buf,x_temp,y_temp,*(str+i),font,font_color,bg_color);
		x_temp += w_temp;
		if(x_temp>(game_frame_w-32)){
			break;
		}
	}
	return x_temp;

}


void g_draw_str_in_block(u8 *display_buf,u8* str,u16 x,u16 y,u8 out_color, u8 in_color,u8 font,u8 font_color,u8 align)
{
	u16 char_w,char_h;
	u8* addr;
	u8 mask,temp;
	u8 *pStartAddr;
	u16 str_len;
	u16 total_w,total_h;
	
	if((NULL == display_buf) || (NULL == str))
		return;

	str_len = hx330x_str_len(str);
	if(str_len == 0)
		return;
	
	addr = res_ascii_get(*(str+0),&char_w,&char_h,font);// get w &h of first char in the str,asume all chars are the same width&height
	if(NULL == addr){
		return;
	}
	
	total_w = char_w*str_len;
	total_h = char_h;
	
	g_Draw_Fill(display_buf,x,y,x+total_w-1+8,y+char_h-1+8,out_color);
	g_Draw_Fill(display_buf,x+2,y+2,x+total_w-1+6,y+char_h-1+6,in_color);
	g_draw_str(display_buf,x+4,y+4,str,font,font_color,in_color);
	
}

void g_draw_str_default_style_in_screen_center(u8* osd_addr,u8* str,u8 font)
{
	u16 char_w,char_h;
	u8* addr;
	u16 str_len;
	u16 total_w,total_h;
	u16 x, y;

	
	if(NULL == str)
		return;

	str_len = hx330x_str_len(str);
	if(str_len == 0)
		return;
	
	addr = res_ascii_get(*(str+0),&char_w,&char_h,font);// get w &h of first char in the str,asume all chars are the same width&height
	if(NULL == addr){
		return;
	}
	
	total_w = char_w*str_len;
	total_h = char_h;

	x = (game_frame_w - total_w-8)/2;
	y = (game_frame_h - total_h-8)/2;


	g_Draw_Fill(osd_addr,x,y,x+total_w-1+8,y+char_h-1+8,R_ID_PALETTE_Yellow);
	g_Draw_Fill(osd_addr,x+2,y+2,x+total_w-1+6,y+char_h-1+6,R_ID_PALETTE_Blue);
	g_draw_str(osd_addr,x+4,y+4,str,font,R_ID_PALETTE_White,R_ID_PALETTE_Blue);
	
	//g_draw_str_in_block(game_frame_buff,str,10,20,R_ID_PALETTE_Yellow,R_ID_PALETTE_Blue,font,R_ID_PALETTE_Green,0);
}

u16 g_draw_reschar(u8 *display_buf,u16 x,u16 y,u8* char_buf,u16 char_w,u16 char_h,u8 font,u32 font_color,u8 bg_color)
{
	u16 i,j;
	u8* addr = char_buf;
	u8 mask,temp;
	u8 *pStartAddr;
	
	if(NULL == display_buf){
        	return 0;
	}
	if(NULL == char_buf){
        	return 0;
	}

	pStartAddr = display_buf+(x)+((y*game_frame_w));

	for(i=0;i<char_h;i++)
	{
	    for(j=0;j<(char_w+7)/8;j++)
		{
	         temp = *addr++;
			//  deg_Printf("%x ",temp);
			 for(mask=0;mask<8;mask++)
			 {
				 if(temp&(0x80>>mask))
				 {
					 *pStartAddr++ = font_color;
				 }
				 else
				 {
				 	//if(backcolor)
					// *pStartAddr++=backcolor;
					//else
					 pStartAddr++;
				 }
			 }
		 }
		 y++;
		 pStartAddr =display_buf+(x)+((y*game_frame_w));
	}
	return char_w;  

}

void g_draw_resstr_in_block(u8 *display_buf,u32 strid,u16 x,u16 y,u8 out_color, u8 in_color,u8 font,u8 font_color,u8 align)
{
	u32 strNum;	
	u16 i,temp,width = 0;
	u8* charBuff;
	u16 char_w,char_h;
	u16 str_w,str_h;
	u8 special;

	u16 start_x = x+4;
	u16 start_y = y+4;

	strNum = res_GetStringInfor(strid,&str_w,&str_h,font);
	if(strNum==0){
		deg_Printf("find string error!!!,0x%x\n",strid);
		return;
	}

	g_Draw_Fill(display_buf,x,y,x+str_w+8,y+str_h+8,out_color);
	g_Draw_Fill(display_buf,x+2,y+2,x+str_w+6,y+str_h+6,in_color);

	//deg_Printf("find string strNum = 0x%x,w=%d,h=%d\n",strid,str_w,str_h);
	
	for(i=0;i<strNum;i++){
		charBuff = res_font_GetChar(strid, i, &char_w, &char_h,&special);
		if(charBuff == NULL)
			break;
		temp = g_draw_reschar(display_buf,start_x, start_y,charBuff,char_w,char_h,font,font_color,0);
		//deg_Printf("draw char = %d,w=%d,h=%d\n",temp,char_w,char_h);
		start_x+=char_w;
		width+=char_w;
		if(width > str_w){
			break;
		}
	}
}

void g_draw_resstr_default_style_in_screen_center(u8* osd_addr,u32 strid,u8 font)
{
	u32 strNum;	
	u16 i,temp,width = 0;
	u8* charBuff;
	u16 char_w,char_h;
	u16 str_w,str_h;
	u8 special;
	u16 x, y;
	
	strNum = res_GetStringInfor(strid,&str_w,&str_h,font);
	if(strNum==0){
		deg_Printf("find string error!!!,0x%x\n",strid);
		return;
	}


	x = (game_frame_w - str_w-8)/2;
	y = (game_frame_h - str_h-8)/2;
	
	u16 start_x = x+4;
	u16 start_y = y+4;
	

	//g_Draw_Fill(osd_addr,x,y,x+str_w+8,y+str_h+8,R_ID_PALETTE_Yellow);
	//g_Draw_Fill(osd_addr,x+2,y+2,x+str_w+6,y+str_h+6,R_ID_PALETTE_Blue);

	//deg_Printf("find string strNum = 0x%x,w=%d,h=%d\n",strid,str_w,str_h);
	
	for(i=0;i<strNum;i++){
		charBuff = res_font_GetChar(strid, i, &char_w, &char_h,&special);
		if(charBuff == NULL)
			break;
		temp = g_draw_reschar(osd_addr,start_x, start_y,charBuff,char_w,char_h,font,R_ID_PALETTE_Black,0);
		//deg_Printf("draw char = %d,w=%d,h=%d\n",temp,char_w,char_h);
		start_x+=char_w;
		width+=char_w;
		if(width > str_w){
			break;
		}
	}
}


void game_over(INT8U* display_buf)
{

    //INT32U	buf_show= setting_frame_show->base + setting_frame_show->offset;

    //game_draw_str((INT16U *) display_buf,STR_60HZ);
    game_over_in=1;
	deg_Printf("!!!!!!!!!!!!!!game_over!!!!!!!!!!!!!!!!!!!\n");

    //msgQSend(ApQ, MSG_APQ_MODE, NULL, 0, MSG_PRI_NORMAL);
    //pushbox_darw_map(display_buf);

}


