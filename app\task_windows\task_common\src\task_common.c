/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

typedef struct TASK_COMMON_PARA_S{
	u32 sreen_mtime;
	u32 poweroff_mtime;
	u32 ir_led_cnt;
	u32 sdc_check_time;
	u32 sdc_scan_flag;		//BIT(1):scan fs, BIT(0):wait stable
	u32 bat_check_cnt;
	u32 usbhost_check_time;
	u32 iging_mode;			//wxn--老化模式
}TASK_COMMON_PARA_T;

ALIGNED(4) static TASK_COMMON_PARA_T tComPara;
/*******************************************************************************
* Function Name  : task_com_para_init
* Description    : task com para init
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_para_init(void)
{
	tComPara.sreen_mtime 		= XOSTimeGet();
	tComPara.poweroff_mtime 	= XOSTimeGet();
	tComPara.ir_led_cnt			= 100;
	tComPara.sdc_check_time 	= 0;
	tComPara.sdc_scan_flag  	= 0;
	tComPara.bat_check_cnt		= 0;
	tComPara.usbhost_check_time = 0;
	tComPara.iging_mode = 0;
}
/*******************************************************************************
* Function Name  : task_com_key_check
* Description    : key check value
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_key_check(void)
{
	if(SysCtrl.dev_fd_key < 0)
		return;
	int adcValue;
	if(dev_ioctrl(SysCtrl.dev_fd_key, DEV_KEY_AD_READ, (INT32U )&adcValue)>=0)
	{
		if(adcValue)
		{
			if(SysCtrl.dev_fd_lcd >= 0 && SysCtrl.dev_stat_lcd == 0)
			{
				task_com_sreen_check(SREEN_RESET_AUTOOFF);
			}else{
				XMsgQPost(SysCtrl.sysQ,(void*)adcValue);
			}
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_usbhost_check
* Description    : usb host check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_usbdev_set(u32 stat)
{
	if(SysCtrl.dev_dusb_stat != stat)
	{
		u32 temp = 0;
		SysCtrl.dev_dusb_stat = stat;
		switch(stat)
		{
			case USBDEV_STAT_NULL:
				dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_ONLINE_SET, 0); //set offline
				tComPara.bat_check_cnt 	  = 5;// wait stable
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBDEV,USBDEV_STAT_NULL));
				deg_Printf("[COM] usbdev out\n");
				break;
			case USBDEV_STAT_DCIN:
				//SysCtrl.dev_stat_battery  	= BATTERY_STAT_MAX;
				tComPara.bat_check_cnt 	  = 5;// wait stable
				//hx330x_usb20_dev_check_init();
				deg_Printf("[COM] usbdev in\n");
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBDEV,USBDEV_STAT_DCIN));
				break;
			case USBDEV_STAT_DEVIN_CHECK:
				if(SysCtrl.dev_dusb_out == 0 )
				{
					dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_HW_CON_CHECK, (u32)&temp);
					if(temp)
					{
						SysCtrl.dev_dusb_stat = USBDEV_STAT_DEVIN;
						dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_INIT, USB_DEVTYPE_MSC);
						XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBDEV,USBDEV_STAT_DEVIN));
					}else
					{
                        SysCtrl.dev_dusb_stat = USBDEV_STAT_DCIN;
					}
				}else
				{
					SysCtrl.dev_dusb_stat = USBDEV_STAT_DCIN;
				}

				break;
			case USBDEV_STAT_PC:
				SysCtrl.dev_dusb_stat = USBDEV_STAT_PC;
				dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_ONLINE_SET, 1); //set online
				deg_Printf("[COM] usbdev PC\n");
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBDEV,USBDEV_STAT_PC));
				break;
			default: return;
		}
		//SysCtrl.dev_dusb_stat = stat;

	}
}

/*******************************************************************************
* Function Name  : task_com_usbdev_check
* Description    : task_com_usbdev_check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_usbdev_check(void)
{
	int ret, temp;
	//--------------------usb detect------------------------
	ret = dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_PWR_CHECK, (INT32U)&temp);
	if(ret>=0)
    {
		//deg_Printf("[USB DEV] SysCtrl.dev_dusb_stat:%d\n", SysCtrl.dev_dusb_stat);
		if((temp == 0) && (SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)) // dc out
		{
			task_com_usbdev_set(USBDEV_STAT_NULL);
		}
		else if(temp && (SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)) // dc in
		{
			task_com_usbdev_set(USBDEV_STAT_DCIN);
			task_com_usbdev_set(USBDEV_STAT_DEVIN_CHECK);
		}
		else if(temp && (SysCtrl.dev_dusb_stat == USBDEV_STAT_DCIN)) // dc in
		{
			//deg_Printf("temp:%d,SysCtrl.dev_dusb_stat:%d\n",temp,SysCtrl.dev_dusb_stat);
			task_com_usbdev_set(USBDEV_STAT_DEVIN_CHECK);
		}
		else
		{
			temp = 0;
			dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_SW_CON_CHECK, (INT32U)&temp);
			//deg_Printf("temp=%d,SysCtrl.dev_dusb_stat=%d\n",temp,SysCtrl.dev_dusb_stat);
			if((SysCtrl.dev_dusb_stat == USBDEV_STAT_DEVIN) && temp )
			{
				task_com_usbdev_set(USBDEV_STAT_PC);
			}
				
		}
    }
}
/*******************************************************************************
* Function Name  : task_com_battery_check
* Description    : task_com_battery_check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_battery_check(void)
{
	int ret, temp;
	if((SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL) || (hardware_setup.battery_pos != ADC_CH_MVOUT))
	{
	    ret = dev_ioctrl(SysCtrl.dev_fd_battery,DEV_BATTERY_READ,(INT32U)&temp);
		if(ret>=0)
		{
			if(SysCtrl.dev_stat_battery != temp && tComPara.bat_check_cnt == 0) // need battery stable
				tComPara.bat_check_cnt = 5;//  3;
			else if(SysCtrl.dev_stat_battery == temp)
			{
				tComPara.bat_check_cnt = 0;
				return ; // no need update
			}
			if(tComPara.bat_check_cnt >0)
				tComPara.bat_check_cnt--;

			if(tComPara.bat_check_cnt == 0)
			{
				if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL && temp == BATTERY_STAT_0)
					app_taskStart(TASK_POWER_OFF,0);	
				if(SysCtrl.dev_stat_battery != BATTERY_STAT_MAX)		
				{
					if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL) //不接DC，电池只下降
					{
						if(SysCtrl.dev_stat_battery <= temp)
						{
							return;
						}
					}else //充电时，电池只上升
					{
						if(SysCtrl.dev_stat_battery >= temp)
						{
							return;		
						}
					}
				}
				SysCtrl.dev_stat_battery = temp;
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_BAT,SysCtrl.dev_stat_battery));
				
				deg_Printf("[COM]battery = %x\n",SysCtrl.dev_stat_battery);
			}
		}
	}else
	{
		SysCtrl.dev_stat_battery  	= BATTERY_STAT_MAX;
	}
}
/*******************************************************************************
* Function Name  : task_com_battery_res_get
* Description    : task_com_battery_res_get
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
resID task_com_battery_res_get(void)
{
	resID batid;
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
		batid = R_ID_ICON_MTBATTERYCHARGE;
	else{
		switch(SysCtrl.dev_stat_battery)
		{
			case BATTERY_STAT_0: batid = R_ID_ICON_MTBATTERY0; break;
			case BATTERY_STAT_1: batid = R_ID_ICON_MTBATTERY1; break;
			case BATTERY_STAT_2: batid = R_ID_ICON_MTBATTERY2; break;
			case BATTERY_STAT_3: batid = R_ID_ICON_MTBATTERY3; break;
			//case BATTERY_STAT_4: batid = R_ID_ICON_MTBATTERY4; break;
			//case BATTERY_STAT_5:
			default:
								 batid = R_ID_ICON_MTBATTERY4; break;
		}
	}
	return batid;
}
/*******************************************************************************
* Function Name  : task_com_sdc_stat_set
* Description    : sd card stat set
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_usb_dev_out(u8 out)
{
	SysCtrl.dev_dusb_out = out;
	if(out)
	{
		if(SysCtrl.dev_dusb_stat > USBDEV_STAT_DCIN)
		{
			SysCtrl.dev_dusb_stat = USBDEV_STAT_DCIN;
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_spijpg_Init
* Description    : APP LAYER: task_com_spijpg_Init
* Input          : 
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void task_com_spijpg_Init(u32 unit_force)
{
#if 0
	if(unit_force || SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
	{
		if(SysCtrl.spi_jpg_list >= 0)
		{
			nv_jpg_uinit();
			filelist_api_nodedestory(SysCtrl.spi_jpg_list);
			SysCtrl.spi_jpg_list = -1;
		}
	}else
	{
		if(SysCtrl.spi_jpg_list < 0)
		{
			nv_jpg_init();
			SysCtrl.spi_jpg_list   = filelist_api_nodecreate(NULL,FILELIST_TYPE_SPI,-1);
			filelist_api_scan(SysCtrl.spi_jpg_list);
		}
	}
#endif
}
/*******************************************************************************
* Function Name  : task_com_sdlist_scan
* Description    : APP LAYER: task_com_spijpg_Init
* Input          : u8 unit_force: 1: \
				   u8 type: 0: jpg, 1:avi, 2: jpg + avi
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void task_com_sdlist_scan(u8 unit_force, u8 type)
{
#if 0
	u8 create;
	if(unit_force || (SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL && SysCtrl.dev_stat_sdc != SDC_STAT_FULL))
	{
		create = 0;
	}else
	{
		create = 1;
	}
	if(create)
	{
		if(SysCtrl.avi_list < 0) 
		{
			int list;
			if(type == 1)
			{
				list = SysCtrl.avi_list   = filelist_api_nodecreate(FILEDIR_REC,FILELIST_TYPE_AVI,-1);
			}else if(type == 0)
			{
				list = SysCtrl.jpg_list	= filelist_api_nodecreate(FILEDIR_IMG,  FILELIST_TYPE_JPG, -1);
			}else
			{
				list = SysCtrl.jpg_list	= filelist_api_nodecreate(FILEDIR_IMG,  FILELIST_TYPE_JPG,   -1);
				SysCtrl.avi_list   = filelist_api_nodecreate(FILEDIR_REC,FILELIST_TYPE_AVI,SysCtrl.jpg_list);
				
			}
			filelist_api_scan(list);
		}	

	}else
	{
		filelist_api_nodedestory(SysCtrl.avi_list);
		filelist_api_nodedestory(SysCtrl.jpg_list);
		SysCtrl.avi_list = -1;
		SysCtrl.jpg_list = -1;
	}
#endif
}

/*******************************************************************************
* Function Name  : task_com_sdc_stat_set
* Description    : sd card stat set
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_sdc_stat_set(u32 stat)
{
	if(SysCtrl.dev_stat_sdc != stat)
	{
		SysCtrl.dev_stat_sdc = stat;
		tComPara.sdc_check_time = XOSTimeGet();
		switch(stat)
		{
			case SDC_STAT_NULL:
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SDC_STAT_NULL));
				deg_Printf("[COM] : sdc out\n");
				break;
			case SDC_STAT_UNSTABLE:
				deg_Printf("[COM] : sdc wait stable\n");
				break;
			case SDC_STAT_IN:
				deg_Printf("[COM] : sdc in\n");
				break;
			case SDC_STAT_ERROR:
				deg_Printf("[COM] : sdc error\n");
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SDC_STAT_ERROR));
				break;
			case SDC_STAT_FULL:
				deg_Printf("[COM] : sdc full\n");
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SDC_STAT_FULL));
				break;
			case SDC_STAT_NORMAL:
				deg_Printf("[COM] : sdc normal\n");
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SDC_STAT_NORMAL));
				break;
			default: break;
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_fs_scan
* Description    : fs scan
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_fs_scan(void)
{
	deg_Printf("[COM] : fs mount start.%d\n",XOSTimeGet());

	char string[16];

#if FUN_AUDIO_RECORD_EN
	hx330x_str_cpy(string, FILEDIR_WAV);
	string[hx330x_str_len(string)-1] = 0;
	fs_mkdir((char*)string);
#endif
#if FUN_MP3_PLAY_EN
	hx330x_str_cpy(string, FILEDIR_MP3);
	string[hx330x_str_len(string)-1] = 0;
	fs_mkdir((char*)string);//FILEDIR_AUDIO);
#endif

#if FUN_NES_GAME_EN
	hx330x_str_cpy(string, FILEDIR_NES);
	string[hx330x_str_len(string)-1] = 0;
	fs_mkdir((char*)string);//FILEDIR_AUDIO);
#endif
	hx330x_str_cpy(string, FILEDIR_REC);
	string[hx330x_str_len(string)-1] = 0;
	fs_mkdir((char*)string);

	hx330x_str_cpy(string, FILEDIR_IMG);
	string[hx330x_str_len(string)-1] = 0;
	fs_mkdir((char*)string);

	sd_api_Stop();

	SysCtrl.fs_clustsize = fs_getclustersize();
	deg_Printf("[COM] : fs cluster size.%d B\n",SysCtrl.fs_clustsize);
	if(SysCtrl.spi_jpg_list >= 0)
	{
		//nv_jpg_uinit();
		filelist_api_nodedestory(SysCtrl.spi_jpg_list);
		SysCtrl.spi_jpg_list = -1;
	}
#if 1	
	if(SysCtrl.avi_list < 0) // scan file list
	{
		SysCtrl.avi_list   = filelist_api_nodecreate(FILEDIR_REC,FILELIST_TYPE_AVI,-1);
		SysCtrl.jpg_list	= filelist_api_nodecreate(FILEDIR_IMG,  FILELIST_TYPE_JPG,                  SysCtrl.avi_list);
		filelist_api_scan(SysCtrl.avi_list);
	}
#endif
#if FUN_AUDIO_RECORD_EN
	if(SysCtrl.wav_list<0)
    {
		SysCtrl.wav_list = filelist_api_nodecreate(FILEDIR_WAV, FILELIST_TYPE_WAV,-1);
		filelist_api_scan(SysCtrl.wav_list);
    }
#endif
	task_com_sdc_stat_set(SDC_STAT_NORMAL);
	task_com_sdc_freesize_check();
	//task_video_record_caltime();

	sd_api_unlock();

}

/*******************************************************************************
* Function Name  : task_com_sdc_check
* Description    : sd card stat check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_sdc_check(void)
{
	int temp,res;
    if(SysCtrl.dev_fd_sdc < 0)
		return;
	if(tComPara.sdc_scan_flag & BIT(1))
	{
		task_com_fs_scan();
		tComPara.sdc_scan_flag &= ~BIT(1);
	}
	dev_ioctrl(SysCtrl.dev_fd_sdc, DEV_SDCARD_READ, (INT32U)&temp);  //temp: 1 - sd online or lock, 0 - sd  offline
	if(SysCtrl.dev_stat_sdc  <= SDC_STAT_UNSTABLE)
	{
		if(sd_api_init(hardware_setup.sdcard_bus_width)>=0)
		{
			if(!(tComPara.sdc_scan_flag & BIT(0)))
			{
				task_com_sdc_stat_set(SDC_STAT_IN);
			}
			if(SysCtrl.dev_stat_sdc == SDC_STAT_NULL)
			{
				task_com_sdc_stat_set(SDC_STAT_UNSTABLE);
			}
			if(SysCtrl.dev_stat_sdc == SDC_STAT_UNSTABLE)
			{
				if(tComPara.sdc_check_time + 500 > XOSTimeGet())// 500 ms,wait sdc stable
					return;
				task_com_sdc_stat_set(SDC_STAT_IN);
			}
			if(SysCtrl.dev_stat_sdc == SDC_STAT_IN)
			{
				res = fs_mount();
				if(res!=FR_OK)
				{
					sd_api_CardState_Set(SDC_STATE_NULL);
                    if(hardware_setup.sdcard_bus_width != SD_BUS_WIDTH1)
                    {
                        if(sd_api_init(SD_BUS_WIDTH1)>=0) // sdc intial 1line
                        {
                            res = fs_mount();
                        }
                    }
				}
				if(res<0)
				{
					task_com_sdc_stat_set(SDC_STAT_ERROR);
				}
				else
				{
					tComPara.sdc_scan_flag |= BIT(1);
				}
			}
			temp = 1;
		}
	}
	if(temp == 0) // no sdc dectcted
	{
		if(SysCtrl.dev_stat_sdc >= SDC_STAT_ERROR && SysCtrl.dev_stat_sdc <= SDC_STAT_NORMAL)
		{
			fs_nodeinit();  // initial fs node
			filelist_api_nodedestory(SysCtrl.avi_list);
			filelist_api_nodedestory(SysCtrl.jpg_list);
		#if (FUN_AUDIO_RECORD_EN == 1)
			filelist_api_nodedestory(SysCtrl.wav_list);
		#endif
		#if (FUN_MP3_PLAY_EN == 1)
			filelist_api_nodedestory(SysCtrl.mp3_list);
		#endif
		#if (FUN_NES_GAME_EN == 1)
			filelist_api_nodedestory(SysCtrl.nes_list);
		#endif
			SysCtrl.avi_list 	= -1;

			SysCtrl.jpg_list 	= -1;
			SysCtrl.wav_list 	= -1;
			SysCtrl.mp3_list	= -1;
			SysCtrl.nes_list	= -1;
			SysCtrl.spi_jpg_list = filelist_api_nodecreate(NULL,FILELIST_TYPE_SPI,-1);
			filelist_api_scan(SysCtrl.spi_jpg_list);
			task_com_sdc_stat_set(SDC_STAT_NULL);
			task_com_sdc_freesize_check();
		}
		//if(!(tComPara.sdc_scan_flag & BIT(0)))
		//{
		//	if(SysCtrl.dev_stat_sdc == SDC_STAT_NULL)
		//	{
		//		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SDC_STAT_NULL));
		//	}
		//}
		tComPara.sdc_scan_flag |= BIT(0);
	}

}
/*******************************************************************************
* Function Name  : task_common_gsensor_check
* Description    : gsensor stat check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_common_gsensor_check(void) // 10 ms
{
	int temp,ret;
	if((app_taskCurId() != TASK_RECORD_VIDEO))
		return;
	if((SysCtrl.dev_fd_gsensor > 0) && user_configValue2Int(CONFIG_ID_GSENSOR))
	{
		if(videoRecordGetStatus() == MEDIA_STAT_START)	// recording will check Gsensor
		{
			ret = dev_ioctrl(SysCtrl.dev_fd_gsensor,DEV_GSENSOR_LOCK_READ,(INT32U)&temp);
			if((ret >=0) && temp > 0)
			{
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_RECORD,MSG_RECORD_LOCK));
				deg_Printf("[COM].gsensor active\n");
			}
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_lcdbk_set
* Description    : lcd sreen on/off
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_lcdbk_set(u32 on)
{
	if(SysCtrl.dev_fd_lcd < 0)
		return;
	//deg_Printf("task_com_lcdbk_set:%d, %d\n",SysCtrl.dev_stat_lcd, on);
	if(SysCtrl.dev_stat_lcd != on)
	{
		dev_ioctrl(SysCtrl.dev_fd_lcd,DEV_LCD_BK_WRITE,on);   // screen on
		SysCtrl.dev_stat_lcd = on;
	}
	tComPara.sreen_mtime = XOSTimeGet();  //save sreen on time
}
/*******************************************************************************
* Function Name  : task_com_sreen_save
* Description    : screen save check
* Input          : u32 on: COM_SREEN_OP 0x00: check auto off, 0x01: reset auto off, 0x02:sreen off, 0x03 sreen on
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_sreen_check(u32 on)
{
	if(SysCtrl.dev_fd_lcd < 0 || app_taskCurId() == TASK_POWER_OFF)
		return;
	//deg_Printf("task_com_sreen_check:%d\n", on);
	if((on == SREEN_RESET_AUTOOFF)||(on == SREEN_SET_ON))  //0x01: reset auto off, 0x03 sreen on
	{
		task_com_lcdbk_set(1);
	}else if(on == SREEN_SET_OFF)   //0x02:sreen off
	{
		task_com_lcdbk_set(0);
	}else //0x00: check auto off SREEN_CHECK_AUTOOFF
	{
		u32 sreensave = user_configValue2Int(CONFIG_ID_SCREENSAVE)*1000;
		if(sreensave != 0)
		{
			if(tComPara.sreen_mtime + sreensave < XOSTimeGet())
			{
				//deg_Printf("tComPara.sreen_mtime:%d,%d,%d\n",tComPara.sreen_mtime,XOSTimeGet(),sreensave);
				task_com_lcdbk_set(0);
			}
		}
	}
}

/*******************************************************************************
* Function Name  : task_com_auto_poweroff
* Description    : system auto power off check
* Input          : int reset : 1:reset auto poweroff, 0:no reset
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_auto_poweroff(int reset)
{
	//if(app_taskCurId() == TASK_POWER_OFF || app_taskCurId() == TASK_BAT_CHARGE)
	//	return;
	//deg_Printf("reset:%d, tComPara.poweroff_mtime:%d,time:%d\n ",reset,tComPara.poweroff_mtime, XOSTimeGet());
	if(reset)
	{
		tComPara.poweroff_mtime = XOSTimeGet();
	}else
	{
		u32 autopowerofftime = user_configValue2Int(CONFIG_ID_AUTOOFF)*1000;
		if(autopowerofftime != 0)
		{
			if(tComPara.poweroff_mtime + autopowerofftime <= XOSTimeGet())
			{
				deg_Printf("[COM]Auto power off\n");
				app_taskStart(TASK_POWER_OFF,0);
				tComPara.poweroff_mtime = XOSTimeGet();
			}
		}
	}
	
}
/*******************************************************************************
* Function Name  : task_com_keysound_play
* Description    : task_com_keysound_play
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_keysound_play(void)
{
	if( (SysCtrl.dev_stat_keysound == 0) || // key sound off
		(audioPlaybackGetStatus() == MEDIA_STAT_PLAY)||// video/audio playing
		(app_taskCurId()  == TASK_POWER_OFF))
	{
		return;
	}
	res_keysound_play();
}
/*******************************************************************************
* Function Name  : task_com_sound_wait_end
* Description    : task_com_sound_wait_end
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_sound_wait_end(void)
{
	while(audioPlaybackGetStatus() == MEDIA_STAT_PLAY)
	{
		XOSTimeDly(1);
	}
}

/*******************************************************************************
* Function Name  : task_com_sdc_freesize_check
* Description    : com get fs free size
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
int task_com_sdc_freesize_check(void)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL)
	{
		SysCtrl.sdc_freesize = fs_free_size()>>1;  // kb
		if(SysCtrl.sdc_freesize < 32)
		{
			SysCtrl.sdc_freesize  = 0;
			SysCtrl.dev_stat_sdc = SDC_STAT_FULL;
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SDC_STAT_FULL));
			deg_Printf("[COM] : sdc normal but free space is 0.set sdc error\n");  // user need format sdc
		}
	}
	else
		SysCtrl.sdc_freesize = 0;
	deg_Printf("[COM] : fs free size = %dG%dM%dKB\n",SysCtrl.sdc_freesize>>20,(SysCtrl.sdc_freesize>>10)&0x3ff,(SysCtrl.sdc_freesize)&0x3ff);
	return SysCtrl.sdc_freesize;
}
/*******************************************************************************
* Function Name  : task_com_sdc_freesize_modify
* Description    : com dec size from free size
* Input          : INT8S dec: >=0 add freesize, <0 minus freesize
*                  INT32U size : unit byte
* Output         : None
* Return         : None
*******************************************************************************/
int task_com_sdc_freesize_modify(INT8S dec,INT32U size)
{
	if(size&(SysCtrl.fs_clustsize-1))
	{
		size = (size&(~(SysCtrl.fs_clustsize-1)))+SysCtrl.fs_clustsize;
		//size+=1024;
	}
	size>>=10;
	if(dec<0)
	{
		if(SysCtrl.sdc_freesize>size)
			SysCtrl.sdc_freesize-=size;
		else
			SysCtrl.sdc_freesize = 0;
	}
	else
	{
		SysCtrl.sdc_freesize+=size;
	}
	return SysCtrl.sdc_freesize;
}
/*******************************************************************************
* Function Name  : task_com_ir_set
* Description    : task_com_ir_set
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_ir_set(u32 on)
{
	if(SysCtrl.dev_fd_ir >= 0)
	{
		if(SysCtrl.dev_stat_ir != on)
		{
			dev_ioctrl(SysCtrl.dev_fd_ir,DEV_IR_WRITE,on);
			SysCtrl.dev_stat_ir = on;
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_ir_auto_check
* Description    : gsensor stat check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_ir_auto_check(void)
{
	if(SysCtrl.dev_fd_ir >= 0)
	{
		if(user_config_get(CONFIG_ID_IR_LED) != R_ID_STR_IR_AUTO)
			return;
		if(hal_isp_br_get() < 0xa)		// need ir
		{
			task_com_ir_set(1);
		}
		else if(hal_isp_br_get() > 0x30)
		{
			tComPara.ir_led_cnt--;
			if(tComPara.ir_led_cnt == 0)// wait for stable
			{
				task_com_ir_set(0);
				tComPara.ir_led_cnt =100;
			}
		}
	}
}

/*******************************************************************************
* Function Name  : task_com_md_check
* Description    : motion detect  check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_md_check(void)
{
	if(user_config_get(CONFIG_ID_MOTIONDECTION) == R_ID_STR_COM_ON)
	{
		if(hal_mdCheck())
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_MD,1));
	}
}
/*******************************************************************************
* Function Name  : task_com_md_check
* Description    : motion detect  check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/

static void task_com_sec(void)
{
	static u8 lastSec		= 255;
	static uint32 lastTime 	= 0;
	HAL_RTC_T* rtcTime = hal_rtcTimeGet();
	if(lastSec != rtcTime->sec)
	{
		lastSec = rtcTime->sec;
		SysCtrl.powerOnTime++;
		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_1S,0));

		res_iconBuffTimeUpdate();
	}
	if((XOSTimeGet()-lastTime)<=500)
		return;
	lastTime = XOSTimeGet();
	XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_500MS,0));
}


static void task_com_user_500MS(void)
{
	static uint32 lastTime 	= 0;
	if((XOSTimeGet()-SysCtrl.UserlastTime)<=500)
		return;
	SysCtrl.UserlastTime = XOSTimeGet();
	XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USER_500MS,0));

}

static void task_com_100ms(void)
{
	static uint32 lastTime 	= 0;
	if((XOSTimeGet()-lastTime)<=100)
		return;
	lastTime = XOSTimeGet();
	XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_100MS,0));

}
static void task_com_user_1S(void)
{
	static uint32 lastTime 	= 0;
	if((XOSTimeGet()-SysCtrl.UserlastTime)<=1000)
		return;
	SysCtrl.UserlastTime = XOSTimeGet();
	XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USER_1S,0));

}
/*******************************************************************************
* Function Name  : task_com_powerOnTime_str
* Description    : task_com_powerOnTime_str
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_powerOnTime_str(void)
{
	u32 sec = SysCtrl.powerOnTime;
	static char powerOnTimeStr[]= "00:00";
	hx330x_num2str(powerOnTimeStr, sec/3600, 2);
	powerOnTimeStr[2] = ':';
	hx330x_num2str(&powerOnTimeStr[3], (sec%3600)/60, 2);

	return powerOnTimeStr;
}
/*******************************************************************************
* Function Name  : task_com_rec_show_time_str
* Description    : task_com_rec_show_time_str
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_rec_show_time_str(void)
{
	u32 sec = SysCtrl.rec_show_time;
	static char recTimeStr[]="00:00:00";
	hx330x_num2str(recTimeStr, sec/3600, 2); //hour
	recTimeStr[2] = ':';
	hx330x_num2str(&recTimeStr[3], (sec%3600)/60, 2); //min
	recTimeStr[5] = ':';
	hx330x_num2str(&recTimeStr[6], sec%60, 2); //sec

	return recTimeStr;
}
/*******************************************************************************
* Function Name  : task_com_rec_show_time_str
* Description    : task_com_rec_show_time_str
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_rec_remain_time_str(void)
{
	static char recRemainTimeStr[]="00:00:00";
	app_taskRecordVideo_caltime();
	u32 sec = SysCtrl.rec_remain_time;
	hx330x_num2str(recRemainTimeStr, sec/3600, 2); //hour
	recRemainTimeStr[2] = ':';
	hx330x_num2str(&recRemainTimeStr[3], (sec%3600)/60, 2); //min
	recRemainTimeStr[5] = ':';
	hx330x_num2str(&recRemainTimeStr[6], sec%60, 2); //sec


	return recRemainTimeStr;
}
/*******************************************************************************
* Function Name  : task_com_play_time_str
* Description    : task_com_play_time_str
* Input          : int sel: 1: total time, 0: cur play time
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_play_time_str(int sel)
{
	char *str;
	static char playCurTimeStr[] = {"00:00:00"};
	static char playTotalTimeStr[] = {"00:00:00"};
	u32 sec;
	u32 playtime  = SysCtrl.play_cur_time/1000;
	u32 totaltime = SysCtrl.play_total_time;

	if(totaltime%1000 > 500)
		totaltime = totaltime/1000 + 1;
	else
		totaltime = totaltime/1000;
	if(sel)
	{
		sec = totaltime;
		str = playTotalTimeStr;
	}
	else
	{
		sec = playtime;
		str = playCurTimeStr;
	}
	if(sec/3600)
	{
		hx330x_num2str(str, sec/3600, 2); //hour
		str[2] = ':';
		hx330x_num2str(&str[3], (sec%3600)/60, 2); //min
		str[5] = ':';
		hx330x_num2str(&str[6], sec%60, 2); //sec
	}else
	{
		hx330x_num2str(str, (sec%3600)/60, 2); //min
		str[2] = ':';
		hx330x_num2str(&str[3], sec%60, 2); //sec
	}


	return str;
}
/*******************************************************************************
* Function Name  : task_com_curVolume_get
* Description    : task_com_curVolume_get
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
ALIGNED(4) const u32 volume_id_tab[] = {
	R_ID_STR_COM_VALUE_0,
	R_ID_STR_COM_VALUE_1,
	R_ID_STR_COM_VALUE_2,
	R_ID_STR_COM_VALUE_3,
	R_ID_STR_COM_VALUE_4,
	R_ID_STR_COM_VALUE_5,
	R_ID_STR_COM_VALUE_6,
	R_ID_STR_COM_VALUE_7,
	R_ID_STR_COM_VALUE_8,
	R_ID_STR_COM_VALUE_9,
	R_ID_STR_COM_VALUE_10,
	R_ID_STR_COM_VALUE_11,
	R_ID_STR_COM_VALUE_12,
	R_ID_STR_COM_VALUE_13,
	R_ID_STR_COM_VALUE_14,
	R_ID_STR_COM_VALUE_15,
};

u32 task_com_curVolume_get(void)
{
	u32 volume;
	//deg_Printf("SysCtrl.curVolume :%d\n",SysCtrl.curVolume );
	if(SysCtrl.curVolume >= task_com_Volume_MaxIndex())
	{
		SysCtrl.curVolume = task_com_Volume_MaxIndex() - 1;
	}
	volume = SysCtrl.curVolume * 80/*100*//(task_com_Volume_MaxIndex()-1);
	return volume;
}

/*******************************************************************************
* Function Name  : task_com_curVolume_cfg
* Description    : task_com_curVolume_cfg
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_curVolume_cfg(void)
{

	if(SysCtrl.curVolume >= task_com_Volume_MaxIndex())
	{
		SysCtrl.curVolume = task_com_Volume_MaxIndex() - 1;
	}
	user_config_set(CONFIG_ID_SYSTEM_VOLUME, volume_id_tab[SysCtrl.curVolume]);
	//user_config_cfgSys(CONFIG_ID_SYSTEM_VOLUME);
	//user_config_save();	

}
/*******************************************************************************
* Function Name  : task_com_curVolume_strid
* Description    : task_com_curVolume_strid
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
u32 task_com_curVolume_strid(void)
{
	return user_config_get(CONFIG_ID_SYSTEM_VOLUME);
}
/*******************************************************************************
* Function Name  : task_com_curVolume_stridByIndex
* Description    : task_com_curVolume_stridByIndex
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
u32 task_com_Volume_stridByIndex(u32 index)
{
	if(index <= 15)
	{
		return volume_id_tab[index];
	}else
	{
		return INVALID_RES_ID;
	}
}
/*******************************************************************************
* Function Name  : task_com_Volume_MaxIndex
* Description    : task_com_Volume_MaxIndex
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
u32 task_com_Volume_MaxIndex(void)
{
	return ARRAY_NUM(volume_id_tab);
}
/*******************************************************************************
* Function Name  : task_com_fileIndex_str
* Description    : task_com_fileIndex_str
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_fileIndex_str(void)
{
	static char fileindexstr[] = {"0000/0000"};
	u32 cnt;
	if(SysCtrl.file_cnt == 0)
	{
		fileindexstr[0] = 0;
	}else
	{
		cnt = hx330x_num2str_cnt(fileindexstr, SysCtrl.file_index + 1, 4);
		fileindexstr[cnt] = '/';
		hx330x_num2str_cnt(fileindexstr + cnt + 1, SysCtrl.file_cnt, 4);
	}
	return fileindexstr;
}
/*******************************************************************************
* Function Name  : task_com_sdcCap_str
* Description    : task_com_sdcCap_str
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_sdcCap_str(void)
{
	static char sd_cap_str[] = {"0000GB"};
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
	{
		u32 sd_cap = sd_api_Capacity()/(2*1024L);	// MB
		u32 cnt;
		sd_cap = (sd_cap + 127)&~127; //对齐128MB
		if(sd_cap < 1024)
			sd_cap = 1024;
		if(sd_cap >= 1024)
		{
			sd_cap /= 1024;
			if(sd_cap > 2 && sd_cap <= 16)
			{
				sd_cap = (sd_cap + 0x3)&~0x3; //对齐4G
			}else if(sd_cap > 16)
			{
				sd_cap = (sd_cap + 0x1f)&~0x1f; //对齐32G
			}
			cnt = hx330x_num2str_cnt(sd_cap_str, sd_cap, 4);
			sd_cap_str[cnt] = 0;//'G';
			//sd_cap_str[cnt + 1] = 'B';
			sd_cap_str[cnt + 1] = 0;
		}else
		{
			cnt = hx330x_num2str_cnt(sd_cap_str, sd_cap, 4);
			sd_cap_str[cnt] = 0;//'M';
			//sd_cap_str[cnt + 1] = 'B';
			sd_cap_str[cnt + 1] = 0;
		}
	}else{
		sd_cap_str[0] = 0;
	}
	return sd_cap_str;
}
/*******************************************************************************
* Function Name  : task_com_tips_show
* Description    : task_com_tips_show
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_tips_show(u32 type)
{
	u32 tips_id;
	switch(type)
	{
		case TIPS_TYPE_SD:
		{
			switch(SysCtrl.dev_stat_sdc)
			{
				case SDC_STAT_NULL:  tips_id = TIPS_SD_NOT_INSERT; break;
				case SDC_STAT_FULL:  tips_id = TIPS_SD_FULL;       break;
				case SDC_STAT_ERROR: tips_id = TIPS_SD_ERROR;	   break;
				default: return;
			}
			uiOpenWindow(&tipsWindow,0,2,tips_id, 2);
			break;
		}
		case TIPS_TYPE_SPI:
		{
			uiOpenWindow(&tipsWindow,0,2,TIPS_SPI_FULL, 2);
			break;
		}
		case TIPS_TYPE_POWER:
		{
			if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
			{
				if(SysCtrl.dev_stat_battery == BATTERY_STAT_1)
				{
					tips_id = TIPS_POWER_LOW;
				}else if(SysCtrl.dev_stat_battery == BATTERY_STAT_0)
				{
					tips_id = TIPS_NO_POWER;
				}else
				{
                    return;
				}
				uiOpenWindow(&tipsWindow,0,2,tips_id,4); //改为提示4秒，2秒感觉有点太快
			}
			break;
		}
		case TIPS_COM_WAITING: 	uiOpenWindow(&tipsWindow,1,2,R_ID_STR_COM_WAITING,5); break;
		case TIPS_COM_SUCCESS: 	uiOpenWindow(&tipsWindow,0,2,R_ID_STR_COM_SUCCESS,2); break;
		case TIPS_COM_FAIL:		uiOpenWindow(&tipsWindow,0,2,R_ID_STR_COM_FAILED,2);  break;
		case TIPS_NO_FILE: 		uiOpenWindow(&tipsWindow,0,2,R_ID_STR_FIL_NULL,2);	  break;
		case TIPS_SET_LOCKED:	uiOpenWindow(&tipsWindow,0,2,R_ID_STR_SET_LOCKED,2);  break;
		case TIPS_FMT_SUCCESS:	uiOpenWindow(&tipsWindow,0,2,R_ID_STR_FMT_SUCCESS,2); break;
		case TIPS_FMT_FAIL:		uiOpenWindow(&tipsWindow,0,2,R_ID_STR_FMT_FAIL,2);	  break;
		case TIPS_ERROR:		uiOpenWindow(&tipsWindow,0,2,"ERROR",2);	  break;
		case TIPS_SCREEN_PRO: 	uiOpenWindow(&tipsWindow,1,2,R_ID_STR_SET_SCREENOFF,1); break;
		
		default : break;
	}
}
/*******************************************************************************
* Function Name  : task_com_scaler_str
* Description    : task_com_scaler_str
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_scaler_str(void)
{
	static char *scaler_str[] = {"1.0X","1.2X","1.4X","1.6X","1.8X",
						"2.0X","2.2X","2.4X","2.6X","2.8X",
						"3.0X","3.2X","3.4X","3.6X","3.8X",
						"4.0X","4.2X","4.4X","4.6X","4.8X",
						"5.0X","5.2X","5.4X","5.6X","5.8X",
						"6.0X","6.1X","6.2X","6.4X","6.5X",
						"6.7X","6.8X","7.0X","7.1X","7.3X",
						"7.4X","7.5X","7.7X","7.9X","8.0X","8.0X",
								};
	if(SysCtrl.lcd_scaler_level >= 100)
	{
		//scaler_str[0] = '1';
		//scaler_str[2] = '0';
		return scaler_str[0];
	}else
	{
		//scaler_str[0] = 200/(SysCtrl.lcd_scaler_level) + '0';
		//scaler_str[2] = ((100%SysCtrl.lcd_scaler_level)*10)/SysCtrl.lcd_scaler_level + '0';
		return scaler_str[SysCtrl.lcd_scaler_X];
	}
	//return scaler_str;
}
/*******************************************************************************
* Function Name  : task_com_scaler_rate
* Description    : task_com_scaler_rate
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
u32 task_com_scaler_rate(void)
{
	return (((u32)100 - SysCtrl.lcd_scaler_level) * 100) / (100 - SysCtrl.lcd_scaler_level_max);
}
/*******************************************************************************
* Function Name  : task_com_lcd_brightness_cfg
* Description    : task_com_lcd_brightness_cfg
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_lcd_brightness_cfg(int step, int step_max)
{
	if(hardware_setup.lcd_bk_ctrl == 0)
	{
		s16 default_b = (s16)hal_lcdBrightnessGet();
		s16 step_v =  (default_b + 128) / step_max;
		s8  cfg_v = default_b - step_v * (step_max - step);
		hx330x_lcdSetVideoBrightness((u16)cfg_v);
	}else
	{
		u32 percent;
		if(step > 2)
		{	
			#if LCD_TAG_SELECT == LCD_MCU_NV3023A
			percent = step*8;
			#else
			percent = step*10;
			#endif
		}else
		{
			#if LCD_TAG_SELECT == LCD_MCU_NV3023A
			percent = 1 + step*8;
			#else
			percent = 15 + step*5;
			#endif
		}
		dev_ioctrl(SysCtrl.dev_fd_lcd,DEV_LCD_BK_ADJUST,percent);
	}

}

/*******************************************************************************
* Function Name  : task_com_service
* Description    : system service in task com
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_service(u32 scanKey)
{
	static u32 lastTime = 0;
	task_com_sec();
	task_com_100ms();
	task_com_user_1S();
	hal_ispService();

	if((XOSTimeGet()-lastTime)<=10*X_TICK)
		return ;

	lastTime = XOSTimeGet();
//--------key check -------------------
	if(SysCtrl.dev_stat_power & POWERON_FLAG_KEY) //�ȴ�power key�ͷ�
	{
		u32 adcValue;
		if(dev_ioctrl(SysCtrl.dev_fd_key, DEV_KEY_POWER_READ, (INT32U )&adcValue)>=0)
		{
			if(adcValue == 0)
			{
				SysCtrl.dev_stat_power &=~POWERON_FLAG_KEY;
			}
		}
	}else
	{
		if(scanKey)
			task_com_key_check();      // system key read
	}

	//--------usb dev check---------------------
	task_com_usbdev_check();
	task_com_battery_check();
    if(app_taskCurId() != TASK_POWER_OFF)
    {
//-------sdc card check-----------------
	    task_com_sdc_check();  // sdc state check
//--------gsensor check-----------------
	    //task_common_gsensor_check(); // gsensor state check
//--------sereen save check ------------
		task_com_sreen_check(SREEN_CHECK_AUTOOFF); // system check,no event
//--------auto power off check----------
	    task_com_auto_poweroff(0); //auto power off check
//--------ir auto check-------------
		//task_com_ir_auto_check();
//--------motion dection check----------
		//task_com_md_check();
    }else if(app_taskCurId() == TASK_BAT_CHARGE) //充满之后会熄屏
	{
		task_com_sreen_check(SREEN_CHECK_AUTOOFF); // system check,no event
	}
}

/*******************************************************************************
* Function Name  : taskComDecodeImg
* Description    : taskComDecodeImg
* Input          : int type: MEDIA_SRC_FS/MEDIA_SRC_NVFS/MEDIA_SRC_RAM
				   int index:  type == MEDIA_SRC_FS, index means resId
				   			   type == MEDIA_SRC_NVFS, index means filelist index
				   			   type == MEDIA_SRC_RAM, index means jpg sdram addr
				   int size:   type == MEDIA_SRC_RAM, size means jpg size
*                  u8 *yuv_buff:   decode output yuv buf
*                  u16 tar_w: tar image width
*                  u16 tar_h: tar image height
* Output         : none
* Return         : int 0: success, <0 fail
*******************************************************************************/
int taskComDecodeImg(int type, int index, int size, u8 *yuv_buff,u16 tar_w,u16 tar_h)
{
	JPG_DEC_ARG  jpg_arg;
	int res = 0;
	hx330x_bytes_memset((u8*)&jpg_arg, 0, sizeof(JPG_DEC_ARG));
	//deg_Printf("taskComDecodeImg\n");
	if(type == MEDIA_SRC_FS || type == MEDIA_SRC_NVJPG)
	{
		char *name;
		int  filetype;
		name = filelist_GetFileFullNameByIndex((type == MEDIA_SRC_FS)?SysCtrl.avi_list:SysCtrl.spi_jpg_list, index, &filetype);
		if(name == NULL)
			return -1;
		if((filetype & FILELIST_TYPE_MASK) == FILELIST_TYPE_AVI)
		{
			VIDEO_PARG_T video_arg;
			video_arg.avi_arg.src_type	= MEDIA_SRC_FS;
			video_arg.avi_arg.fd			= (int)fs_open(name,FA_READ);
			if((int)video_arg.avi_arg.fd < 0)
				return -1;
			video_arg.tar_width 	= tar_w;
			video_arg.tar_height 	= tar_h;
			video_arg.yout  		= yuv_buff;
        	video_arg.uvout 		= yuv_buff+((tar_w+0x1f)&(~0x1f))*tar_h;
			if(videoDecodeFirstFrame(&video_arg)<0)
			{
				deg_Printf("avi decode fail<%s>\n",name);
				fs_close(video_arg.avi_arg.fd);
				return -1;
			}
			fs_close(video_arg.avi_arg.fd);
			return 0;
		}else if((filetype & FILELIST_TYPE_MASK) == FILELIST_TYPE_JPG)
		{
			jpg_arg.fd 			= (int)fs_open(name,FA_READ);
			jpg_arg.src_type	= MEDIA_SRC_FS;
		}else if((filetype & FILELIST_TYPE_MASK) == FILELIST_TYPE_SPI)
		{
			jpg_arg.fd 			= (nv_jpg_open(filelist_GetFileIndexByIndex(SysCtrl.spi_jpg_list,index),NVFA_READ) == NV_OK) ? 0 : -1;
			jpg_arg.src_type	= MEDIA_SRC_NVJPG;
		}
		else
		{
			return -1;
		}
	}else if(type == MEDIA_SRC_NVFS)
	{
		jpg_arg.fd 			= index;
		jpg_arg.src_type	= MEDIA_SRC_NVFS;
	}else if(type == MEDIA_SRC_RAM)
	{
		jpg_arg.fd			= -1;
		jpg_arg.src_type	= MEDIA_SRC_RAM;
		jpg_arg.jpgbuf		= (u8*)index;
		jpg_arg.jpgsize		= size;
	}else{
		return -1;
	}

	jpg_arg.type 		= MEDIA_FILE_JPG;
	jpg_arg.wait 		= 1;
	jpg_arg.dst_width	= tar_w;
	jpg_arg.dst_height	= tar_h;
	//jpg_arg.jpgsize		= 0;
	jpg_arg.yout  		= yuv_buff;
	jpg_arg.uvout 		= yuv_buff+((tar_w+0x1f)&(~0x1f))*tar_h;
	jpg_arg.step_yout 	= NULL;
	jpg_arg.p_lcd_buffer = NULL;
	if(type == MEDIA_SRC_NVJPG)
	{
		res = imageDecodeSpiStart(&jpg_arg);
		nv_jpg_close();
	}	
	else
		res = imageDecodeStart(&jpg_arg);
	if(type == MEDIA_SRC_FS)
	{
		fs_close(jpg_arg.fd);
	}
	return res;
}
/*******************************************************************************
* Function Name  : taskComDecodeAddToImg
* Description    : taskComDecodeAddToImg
* Input          : lcdshow_frame_t* p_frame,int index,uiRect* tar_rect
                   int type: MEDIA_SRC_FS/MEDIA_SRC_NVFS/MEDIA_SRC_RAM
				   int index:  type == MEDIA_SRC_FS, index means resId
				   			   type == MEDIA_SRC_NVFS, index means filelist index
				   			   type == MEDIA_SRC_RAM, index means jpg sdram addr
				   int size:   type == MEDIA_SRC_RAM, size means jpg size
* Output         : none
* Return         : int 0: success, <0 fail
*******************************************************************************/
int taskComDecodeAddToImg(lcdshow_frame_t* p_frame,int type,int index,int size, uiRect* tar_rect)
{
	u16 tar_main_w 	= p_frame->ratio_w;
	u16 tar_main_h 	= p_frame->ratio_h;
	u16 tar_thumb_w	= tar_rect->x1 - tar_rect->x0 + 1;
	u16 tar_thumb_h = tar_rect->y1 - tar_rect->y0 + 1;
	u16 tar_thumb_x = tar_rect->x0;
	u16 tar_thumb_y = tar_rect->y0;
	u16 tar_thumb_stride = (tar_thumb_w + 0x1f)&~0x1f;
	u32 thumbSize;
	u8 *thumbBuf;
	if(tar_thumb_x + tar_thumb_w > tar_main_w)
	{
		deg_Printf("[DecodeAddToImg] w over \n");
		//tar_thumb_w = (tar_main_w - tar_thumb_x)&~0x1;
		return -1;
	}
	if(tar_thumb_y + tar_thumb_h > tar_main_h)
	{
		deg_Printf("[DecodeAddToImg] h over \n");
		//tar_thumb_h = (tar_main_h - tar_thumb_y)&~0x01;
		return -1;
	}
	thumbSize	= tar_thumb_stride*tar_thumb_h*3/2;
	tar_main_w  = (tar_main_w + 0x1f)&~0x1f;

	//==decode jpeg==
	thumbBuf = hal_sysMemMalloc(thumbSize);
	if(NULL == thumbBuf)
	{
		deg_Printf("[DecodeAddToImg] buf malloc fail :%d\n",thumbSize);
		return -1;
	}
	if(taskComDecodeImg(type, index, size, thumbBuf,tar_thumb_w,tar_thumb_h)<0)
	{
		deg_Printf("DecodeAddToImg] image decode fail\n");
		hal_sysMemFree(thumbBuf);
		return -1;
	}
	u8* tar_main_buf  = p_frame->y_addr + tar_thumb_y*tar_main_w + tar_thumb_x;
	u8* src_thumb_buf = thumbBuf;
	u16 i;
	//Y
	for(i = 0; i < tar_thumb_h; i++)
	{
		//deg_Printf("memcpy %x, %x, %d\n",tar_main_buf,src_thumb_buf,tar_thumb_w);
		hx330x_mcpy0_sdram2gram((void *)tar_main_buf, (void*)src_thumb_buf, tar_thumb_w);
		tar_main_buf  += tar_main_w;
		src_thumb_buf += tar_thumb_stride;
	}
	//UV
	tar_main_buf = p_frame->uv_addr + tar_thumb_y*tar_main_w/2 + tar_thumb_x;
	for(i = 0; i < tar_thumb_h/2; i++)
	{
		hx330x_mcpy0_sdram2gram((void *)tar_main_buf, (void*)src_thumb_buf, tar_thumb_w);
		tar_main_buf  += tar_main_w;
		src_thumb_buf += tar_thumb_stride;
	}
	hal_sysMemFree(thumbBuf);

	return 0;
}
/*******************************************************************************
* Function Name  : taskComDecodeFrameToImg
* Description    : taskComDecodeFrameToImg
* Input          : lcdshow_frame_t* p_frame,int index,uiRect* tar_rect
                   int type: MEDIA_SRC_FS/MEDIA_SRC_NVFS/MEDIA_SRC_RAM
				   int index:  type == MEDIA_SRC_FS, index means resId
				   			   type == MEDIA_SRC_NVFS, index means filelist index
				   			   type == MEDIA_SRC_RAM, index means jpg sdram addr
				   int size:   type == MEDIA_SRC_RAM, size means jpg size
* Output         : none
* Return         : int 0: success, <0 fail
*******************************************************************************/
int taskComDecodeFrameToImg(lcdshow_frame_t* p_frame,int type,int index,int size, uiRect* tar_rect)
{
	//static u8 cnt = 6;
	#define FRAME_YMIN_THD 				0x3B
	#define FRAME_UVMIN_THD 			0x10
	u16 tar_main_w 	= p_frame->ratio_w;
	u16 tar_main_h 	= p_frame->ratio_h;
	u16 tar_thumb_w	= tar_rect->x1 - tar_rect->x0 + 1;
	u16 tar_thumb_h = tar_rect->y1 - tar_rect->y0 + 1;
	u16 tar_thumb_x = tar_rect->x0;
	u16 tar_thumb_y = tar_rect->y0;
	u16 tar_thumb_stride = (tar_thumb_w + 0x1f)&~0x1f;
	u32 thumbSize;
	u8 *thumbBuf;
	if(tar_thumb_x + tar_thumb_w > tar_main_w)
	{
		deg_Printf("[DecodeAddToImg] w over \n");
		//tar_thumb_w = (tar_main_w - tar_thumb_x)&~0x1;
		return -1;
	}
	if(tar_thumb_y + tar_thumb_h > tar_main_h)
	{
		deg_Printf("[DecodeAddToImg] h over \n");
		//tar_thumb_h = (tar_main_h - tar_thumb_y)&~0x01;
		return -1;
	}
	
	thumbSize	= tar_thumb_stride*tar_thumb_h*3/2;
	tar_main_w  = (tar_main_w + 0x1f)&~0x1f;

	//==decode jpeg==
	thumbBuf = hal_sysMemMalloc(thumbSize);
	if(NULL == thumbBuf)
	{
		deg_Printf("[DecodeAddToImg] buf malloc fail :%d\n",thumbSize);
		return -1;
	}
	if(taskComDecodeImg(type, index, size, thumbBuf,tar_thumb_w,tar_thumb_h)<0)
	{
		deg_Printf("DecodeAddToImg] image decode fail\n");
		hal_sysMemFree(thumbBuf);
		return -1;
	}
	u8* tar_main_ybuf  = p_frame->y_addr + tar_thumb_y*tar_main_w + tar_thumb_x;
	u8* tar_main_uvbuf = p_frame->uv_addr + tar_thumb_y*tar_main_w/2 + tar_thumb_x;
	u8* src_thumb_ybuf = thumbBuf;
	u8* src_thumb_uvbuf = thumbBuf + tar_thumb_stride*tar_thumb_h;

	u32 i, j,cnty, cntuv;
	u32 start, cpy_len, cpy_flag;
	//Y
	//hx330x_sysDcacheFlush((u32)p_frame->y_addr,p_frame->buf_size);
	hx330x_sysDcacheFlush((u32)thumbBuf,thumbSize);
	//if(cnt > 0)
	//{
	//	deg_Printf("cnt = %d----------------------------------\n", cnt);
	//	deg_Printf("[%d,%d] %d %d %d %d\n",tar_thumb_w,tar_thumb_h, tar_rect->x1,tar_rect->x0,tar_rect->y1,tar_rect->y0);
	//	for(i = 0; i < tar_thumb_h; i += 2)
	//	{
	//		deg_Printf("i = %d\n", i);
	//		debgbuf((u8*)&src_thumb_ybuf[i*tar_thumb_stride], tar_thumb_w);
	//		deg_Printf("-----------");
	//		debgbuf((u8*)&src_thumb_ybuf[(i+1)*tar_thumb_stride], tar_thumb_w);
	//		deg_Printf("-----------");
	//		debgbuf((u8*)&src_thumb_uvbuf[(i/2)*tar_thumb_stride], tar_thumb_w);
	//	}
	//	//cnt--;
	//}
	//debgbuf(src_thumb_ybuf,32);
	//debgbuf(src_thumb_uvbuf,32);
	for(i = 0; i < tar_thumb_h; i += 2)
	{
		hal_wdtClear();
		//Y
		//hx330x_sysDcacheInvalid((u32)tar_main_ybuf, (u32)tar_thumb_w);
		//hx330x_sysDcacheInvalid((u32)tar_main_ybuf + tar_main_w, tar_thumb_w);
		//hx330x_sysDcacheInvalid((u32)src_thumb_ybuf, tar_thumb_stride * 2);
		//UV
		//hx330x_sysDcacheInvalid((u32)tar_main_uvbuf, tar_thumb_w);
		//hx330x_sysDcacheInvalid((u32)src_thumb_uvbuf, tar_thumb_stride);
		start = cpy_len = cpy_flag = 0;
		for(j = 0; j < tar_thumb_w; j+=2)
		{
			cnty  = cntuv = 0;
		#if 0
			if(src_thumb_ybuf[j] <= FRAME_YMIN_THD)
			{
				cnty++;
			}else{
				tar_main_ybuf[j] = src_thumb_ybuf[j];
			}
			if(src_thumb_ybuf[j+1] <= FRAME_YMIN_THD)
			{
				cnty++;
			}else{
				tar_main_ybuf[j+1] = src_thumb_ybuf[j+1];
			}
			if(src_thumb_ybuf[j+tar_thumb_stride] <= FRAME_YMIN_THD)
			{
				cnty++;
			}else
			{
				tar_main_ybuf[j+tar_main_w] = src_thumb_ybuf[j+tar_thumb_stride];
			}
			if(src_thumb_ybuf[j + tar_thumb_stride+1] <= FRAME_YMIN_THD)
			{
				cnty++;
			}else
			{
				tar_main_ybuf[j+tar_main_w+1] = src_thumb_ybuf[j+tar_thumb_stride+1];
			}	
			if(cnty <= 2 )
			{
				tar_main_uvbuf[j] = src_thumb_uvbuf[j];
				tar_main_uvbuf[j+1] = src_thumb_uvbuf[j+1];
				//if(cnty != 0 && cntuv != 0)
				//{
				//	deg_Printf("Y:%x,%x,%x,%x\n", src_thumb_ybuf[j], src_thumb_ybuf[j+1],src_thumb_ybuf[j+tar_thumb_stride],src_thumb_ybuf[j+tar_thumb_stride+1]);
				//	deg_Printf("UV:%x,%x\n",  src_thumb_uvbuf[j], src_thumb_uvbuf[j+1]);
				//}
			}
		#elif 0
			if(src_thumb_ybuf[j] <= FRAME_YMIN_THD) cnty++;
			if(src_thumb_ybuf[j+1] <= FRAME_YMIN_THD) cnty++;
			if(src_thumb_ybuf[j+tar_thumb_stride] <= FRAME_YMIN_THD) cnty++;
			if(src_thumb_ybuf[j+tar_thumb_stride+1] <= FRAME_YMIN_THD) cnty++;
			if(src_thumb_uvbuf[j] >= (0x80 - FRAME_UVMIN_THD) && src_thumb_uvbuf[j] <= (0x80 + FRAME_UVMIN_THD)) cntuv++;
			if(src_thumb_uvbuf[j+1] >= (0x80 - FRAME_UVMIN_THD) && src_thumb_uvbuf[j+1] <= (0x80 + FRAME_UVMIN_THD)) cntuv++;
			if(cnty == 0 || cntuv <= 0 )
			{

				tar_main_ybuf[j] = src_thumb_ybuf[j];
				tar_main_ybuf[j+1] = src_thumb_ybuf[j+1];
				tar_main_ybuf[j+tar_main_w] = src_thumb_ybuf[j+tar_thumb_stride];
				tar_main_ybuf[j+tar_main_w+1] = src_thumb_ybuf[j+tar_thumb_stride+1];
				tar_main_uvbuf[j] = src_thumb_uvbuf[j];
				tar_main_uvbuf[j+1] = src_thumb_uvbuf[j+1];
			}
		#else
			if(src_thumb_ybuf[j] <= FRAME_YMIN_THD) cnty++;
			if(src_thumb_ybuf[j+1] <= FRAME_YMIN_THD) cnty++;
			if(src_thumb_ybuf[j+tar_thumb_stride] <= FRAME_YMIN_THD) cnty++;
			if(src_thumb_ybuf[j+tar_thumb_stride+1] <= FRAME_YMIN_THD) cnty++;
			if(src_thumb_uvbuf[j] >= (0x80 - FRAME_UVMIN_THD) && src_thumb_uvbuf[j] <= (0x80 + FRAME_UVMIN_THD)) cntuv++;
			if(src_thumb_uvbuf[j+1] >= (0x80 - FRAME_UVMIN_THD) && src_thumb_uvbuf[j+1] <= (0x80 + FRAME_UVMIN_THD)) cntuv++;
			if(cnty == 0 || cntuv <= 1 )
			{
				if(cpy_len == 0)
				{
					start = j;
				}
				cpy_len += 2;
			}else if(cpy_len) 
			{
				cpy_flag = 1;
			}
			if(cpy_len && j >= (tar_thumb_w - 2 ))
			{
				cpy_flag = 1;
			}
			if(cpy_flag) 
			{
				//if(cnt > 0)
				//	deg_Printf("111 start:%d, cpy_len:%d, i:%d, j:%d\n", start, cpy_len,i, j);
				if(/*start != 0 &&*/ cpy_len > 4)
				{
					start += 2;
					cpy_len -= 4;
				}	
				hx330x_bytes_memcpy(&tar_main_ybuf[start], &src_thumb_ybuf[start], cpy_len);
				hx330x_bytes_memcpy(&tar_main_ybuf[start+tar_main_w], &src_thumb_ybuf[start+tar_thumb_stride], cpy_len);
				hx330x_bytes_memcpy(&tar_main_uvbuf[start], &src_thumb_uvbuf[start], cpy_len);
				cpy_len = 0;
				cpy_flag = 0;
			}
		#endif
		}
		//hx330x_sysDcacheWback((u32)tar_main_ybuf, tar_thumb_w);
		//hx330x_sysDcacheWback((u32)tar_main_ybuf + tar_main_w, tar_thumb_w);
		//hx330x_sysDcacheWback((u32)tar_main_uvbuf, tar_thumb_w);
		tar_main_ybuf 	+= tar_main_w*2;
		tar_main_uvbuf 	+= tar_main_w;
		src_thumb_ybuf 	+= tar_thumb_stride*2;
		src_thumb_uvbuf += tar_thumb_stride;
	}
	//hx330x_sysDcacheWback((u32)p_frame->y_addr,p_frame->buf_size);
	hal_sysMemFree(thumbBuf);
	//if(cnt > 0)
	//{
	//	cnt--;
	//}
	return 0;
}

void task_com_set_aging(u32 mode){
	tComPara.iging_mode = mode;

	return;
}
u32 task_com_get_aging(){

	return tComPara.iging_mode;
}
void task_com_pwmPlus(){
	u8 i ,value;
	value = user_configValue2Int(CONFIG_ID_SREEN_BRIGHT);
	u32 percent;
	if(value > 2)
	{
		#if LCD_TAG_SELECT == LCD_MCU_NV3023A
		percent = value*8;
		#else
		percent = value*10;
		#endif
	}else
	{
		#if LCD_TAG_SELECT == LCD_MCU_NV3023A
		percent = 1 + value*8;
		#else
		percent = 15 + value*5;
		#endif
	}	
	for( i = 0;i<percent;i++){
		dev_ioctrl(SysCtrl.dev_fd_lcd,DEV_LCD_BK_ADJUST,i); 			
	}
}
void task_com_pwmMinus(){
	u8 i ,value;
	value = user_configValue2Int(CONFIG_ID_SREEN_BRIGHT);
	u32 percent;
	if(value > 2)
	{
		percent = value*10;
	}else
	{
		percent = 15 + value*5;
	}	
	for( i = percent;i>0;i--){
		dev_ioctrl(SysCtrl.dev_fd_lcd,DEV_LCD_BK_ADJUST,i); 			
	}

}


char *task_com_pres_get(u32 resid)
{
	static char *pres_str[] = {"VGA","1M","2M","3M","5M",
						"8M","10M","12M","16M","18M",
						"20M","24M","40M","48M",
								};
	switch(resid)
	{
		case R_ID_STR_RES_VGA:	return pres_str[0];
		case R_ID_STR_RES_1M:	return pres_str[1];
		case R_ID_STR_RES_2M:	return pres_str[2];
		case R_ID_STR_RES_3M:	return pres_str[3];
		case R_ID_STR_RES_5M:	return pres_str[4];
		case R_ID_STR_RES_8M:	return pres_str[5];
		case R_ID_STR_RES_10M:	return pres_str[6];
		case R_ID_STR_RES_12M:	return pres_str[7];
		case R_ID_STR_RES_16M:	return pres_str[8];
		case R_ID_STR_RES_18M:	return pres_str[9];
		case R_ID_STR_RES_20M:	return pres_str[10];
		case R_ID_STR_RES_24M:	return pres_str[11];
		case R_ID_STR_RES_40M:	return pres_str[12];
		case R_ID_STR_RES_48M:	return pres_str[13];

	}
}

char *task_com_datetime_from_version()
{
	static char *datetime = "20230101";
	hx330x_str_ncpy(datetime,SysCtrl.version_str,8);
	return &datetime[0];
}