/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../hal/inc/hal.h"

#if MEDIA_IMAGE_ENCODE_EN
/*******************************************************************************
* Function Name  : imageEncodeInit
* Description	 : image encode initial
* Input 		 : none
* Output		 : none 										   
* Return		 : int 0
*******************************************************************************/
int imageEncodeInit(void)
{
    //hal_mjpA_EncodeInit();
	hal_jpg_watermark_init();
    hal_csiEnable(1);
	
	return 0;
}
/*******************************************************************************
* Function Name  : imageEncodeUninit
* Description	 : image encode uninitial
* Input 		 : none
* Output		 : none 										   
* Return		 : int 0 success
*******************************************************************************/
int imageEncodeUninit(void)
{
	hal_jpg_watermark_uinit();
	return 0;
}
/*******************************************************************************
* Function Name  : imageEncodeSaveRam
* Description	 : take a photo to ram
* Input 		 : JPG_ENC_ARG *arg
* Output		 : none 										   
* Return		 : int : 0 success
                                -1 fail
*******************************************************************************/
static int imageEncodeSubImg(JPG_ENC_ARG *arg)
{
    INT32S res = 0;
    INT32U timeout;
	u8* buf = NULL;
	u32 size = 0;
	if(hal_mjpAEncodePhotoResumeRam() < 0)
	{
		res = -1;
		goto OUT;
	}
    timeout = XOSTimeGet();
    while(1)
    {
		hal_wdtClear();
		buf = hal_mjpA_RawBufferGet(&size,NULL, NULL);
		if(buf && size)
	    {
			//hal_mjpA_RawBufferfree();
			break;
	    }
		if((timeout+2000)<XOSTimeGet())
		{
			deg_Printf("image encode : timeout 2-second.\n");
			res = -1;
			break;
		}
    }
	if(res >= 0)
	{
		u32 file_offset = fs_size(arg->fd);
		buf[size - 12] = 0xFF;
		buf[size - 11] = 0xFE;
		buf[size - 10] = 0x00;
		buf[size - 9] = 0x0A;
		buf[size - 8] = (file_offset >> 24 )&0xff;
		buf[size - 7] = (file_offset >> 16 )&0xff;
		buf[size - 6] = (file_offset >> 8 )&0xff;
		buf[size - 5] = (file_offset >> 0 )&0xff;
		buf[size - 4] = 0x55;
		buf[size - 3] = 0xAA;
		buf[size - 2] = 0x55;
		buf[size - 1] = 0xAA;

		res = fs_write(arg->fd,(const void*)buf,size);
		if(res != size)
		{
			res = -12;
			deg_Printf("image encode : write err\n");
			goto OUT;
		}

		hal_mjpA_linebufUninit();
		arg->buf = (u8*)hal_sysMemMalloc(size);
		arg->size = size;
		if(arg->buf == NULL)
		{
			res = -2;
		}else
		{
			hx330x_mcpy0_sdram2gram_nocache((void *)arg->buf, (void *)buf, size);
			hx330x_sysDcacheInvalid((u32)arg->buf,size);
		}
		hal_mjpA_RawBufferfree();

	} 
OUT:
	if(res < 0)
	{
		arg->buf = NULL;
		arg->size = 0;	
	}
	return res;
}
/*******************************************************************************
* Function Name  : imageEncodeStart
* Description	 : take a photo
* Input 		 : JPG_ENC_ARG *arg
* Output		 : none 										   
* Return		 : int : 0 success
                                -1 fail
*******************************************************************************/
int imageEncodeStart(JPG_ENC_ARG *arg, u8 save_ram)
{
    INT32S res = 0;
    INT32U timeout;
	INT32U sync_flag = 0;
	
#if MJPEG_PHOTO_ENC_TYPE ==	MJPEG_ENC_SRC_CSI
	if(hal_mjpA_Enc_Photo_Start(arg->dst_width,arg->dst_height,arg->img_Q,arg->timestamp) == false)
		return -1;
#else
	if(hal_mjpA_EncLcd_Photo_Start(arg->dst_width,arg->dst_height,arg->img_Q,arg->timestamp) == false)
		return -1;
#endif
    timeout = XOSTimeGet();
	u8 enc_mode = hal_mjpA_photo_encode_mode();
	if(enc_mode == ENC_MODE_NORMAL)
	{
		deg_Printf("ENC_MODE_NORMAL\n");
		while(1)
		{
			hal_wdtClear();
			arg->buf = hal_mjpA_RawBufferGet(&arg->size,NULL, NULL);
			if(arg->buf && arg->size)
			{
				res = jpg_encode(arg);
				if(res < 0)
				{
					res = -1;
					deg_Printf("image encode : error\n");
					goto OUT;
				}
				res = fs_write(arg->fd,(const void*)arg->buf,arg->size);	
				if(res != arg->size)
				{
					res = -2;
					deg_Printf("image encode : write err\n");
					goto OUT;
				}
				hal_mjpA_RawBufferfree();
				res = 0;
				goto OUT;
			}
			if((timeout+2000)<XOSTimeGet())
			{
				deg_Printf("image encode : timeout.\n");
				res = -3;
				goto OUT;
			}
		}
	}else if(enc_mode == ENC_MODE_PKG)
	{
		deg_Printf("ENC_MODE_PKG\n");
		while(1)
    	{
			hal_wdtClear();

			arg->buf = hal_mjpA_RkgBufferGet(&arg->size,&sync_flag, NULL);
			if(arg->buf && arg->size)
		    {
				if(sync_flag & JPG_PKG_START)
				{
					if((arg->buf[0] != 0xff) || (arg->buf[1] != 0xd8))
					{
						deg_Printf("Encode head not ffd8\n");
						res = -4;
						goto OUT;
					}else
					{
						res = jpg_encode(arg);
						if(res < 0)
						{
							res = -5;
							deg_Printf("image encode : error\n");
							goto OUT;
						}	
						if(res >= 0 )
						{
							res = fs_write(arg->fd,(const void*)arg->buf,arg->size);	
						}
					}

				}else
				{
					res = fs_write(arg->fd,(const void*)arg->buf,arg->size);
				}
				if(res < 0)
				{
					res = -6;
					deg_Printf("image encode : write err\n");
					goto OUT;
				}	
				hal_mjpA_RawBufferfree();

				if(sync_flag & JPG_PKG_END)
				{
					res = 0;
					goto OUT;
				}
				sync_flag = 0;
				hal_mjpAEncodePhotoResumePKG();
		    }
			if((timeout+2000)<XOSTimeGet())
			{
				deg_Printf("image encode : timeout.\n");
				res = -7;
				goto OUT;
			}
    	}
	}else if(enc_mode == ENC_MODE_LLPKG)
	{
		deg_Printf("ENC_MODE_LLPKG\n");
		while(1)
    	{
			hal_wdtClear();

			arg->buf = hal_mjpA_RkgBufferGet(&arg->size,&sync_flag, NULL);
			if(arg->buf && arg->size)
		    {
				MJP_LINK_MAP* link = hal_mjp_enle_tab_get();
				if(link == NULL || link->addr == NULL || link->len == 0)
				{
					res = -8;
					deg_Printf("Encode LLPKG link err\n");
					break;
				}
			#if MJPEG_LLPKG_DOUBLEBUF
				hal_mjpAEncodePhotoResumeLLPKG();
			#endif
				if(sync_flag & JPG_PKG_START)
				{
					if((arg->buf[0] != 0xff) || (arg->buf[1] != 0xd8))
					{
						deg_Printf("Encode head not ffd8\n");
						res = -9;
					}else
					{
						res = jpg_encode(arg);
						if(res < 0)
						{
							res = -10;
							deg_Printf("image encode : error\n");
							goto OUT;
						}
						if(res > 0)
						{
							link->addr += 0x14;
							link->len  -= 0x14;		
						}	
					}
				}
				while(link->addr && link->len)
				{
					//deg_Printf("link:%x,%x\n", link->addr, link->len);
					//debgbuf(link->addr + link->len -2, 2);
					hal_wdtClear();
					res = fs_write(arg->fd,(const void*)link->addr,link->len);	
					if(res != link->len)
					{
						res = -11;
						deg_Printf("image encode : write err\n");
						goto OUT;
					}
					link++;
				}
				hal_mjpA_RawBufferfree();
				if(sync_flag & JPG_PKG_END)
				{
					break;
				}
				sync_flag = 0;
			#if MJPEG_LLPKG_DOUBLEBUF == 0
				hal_mjpAEncodePhotoResumeLLPKG();
			#endif
		    }else if(sync_flag & JPG_PKG_ERR)
			{
				deg_Printf("image encode : PKG ERR.\n");
				res = -20;
				goto OUT;
			}

			if((timeout+4000)<XOSTimeGet())
			{
				deg_Printf("image encode : timeout.\n");
				res = -12;
				goto OUT;
			}
		}
		deg_Printf("TAKE PHOTO TIME:%dms\n",XOSTimeGet()-timeout);
	}

OUT:
	if(res >= 0)
	{
		res = imageEncodeSubImg(arg); //
		if(res >= 0 && save_ram == 0)
		{
			if(arg->buf)
			{
				hal_sysMemFree(arg->buf);
				arg->buf = NULL;
				arg->size = 0;
			}
		}
	}
	hal_mjpA_Enc_Stop();
    if(arg->timestamp)
	    hal_jpg_watermarkStart(0,0,0);  // disable
    
	return res;
}
/*******************************************************************************
* Function Name  : imageEncodeStart
* Description	 : take a photo
* Input 		 : JPG_ENC_ARG *arg
* Output		 : none 										   
* Return		 : int : 0 success
                                -1 fail
*******************************************************************************/
int imageEncodeToRamStart(JPG_ENC_ARG *arg)
{
    INT32S res = 0;
    INT32U timeout;
	u8* buf = NULL;
	u32 size = 0;
	if(hal_mjpA_Enc_Photo_Start( arg->dst_width,arg->dst_height,arg->img_Q,arg->timestamp) == false)
		return -1;
	if(hal_mjpA_photo_encode_mode() != ENC_MODE_NORMAL)
	{
		res = -2;
		goto OUT;
	}	
    timeout = XOSTimeGet();
    while(1)
    {
		hal_wdtClear();
		buf = hal_mjpA_RawBufferGet(&size,NULL, NULL);
		if(buf && size)
	    {
			//hal_mjpA_RawBufferfree();
			break;
	    }
		if((timeout+2000)<XOSTimeGet())
		{
			deg_Printf("image encode : timeout 2-second.\n");
			res = -1;
			break;
		}
    }
	if(res >= 0)
	{
		hal_mjpA_linebufUninit();
		arg->buf = (u8*)hal_sysMemMallocLast(size);
		arg->size = size;
		if(arg->buf == NULL)
		{
			res = -2;
		}else
		{
			hx330x_mcpy0_sdram2gram_nocache((void *)arg->buf, (void *)buf, size);
			hx330x_sysDcacheInvalid((u32)arg->buf,size);
		}
		hal_mjpA_RawBufferfree();

	}
OUT:
	hal_mjpA_Enc_Stop();
    if(arg->timestamp)
	    hal_jpg_watermarkStart(0,0,0);  // disable
    
	return res;
}
/*******************************************************************************
* Function Name  : imageEncodeLcdToRamStart
* Description	 : take a photo
* Input 		 : JPG_ENC_ARG *arg
* Output		 : none 										   
* Return		 : int : 0 success
                                -1 fail
*******************************************************************************/
int imageEncodeLcdToRamStart(JPG_ENC_ARG *arg)
{
    INT32S res = 0;
    INT32U timeout;
	u8* buf = NULL;
	u32 size = 0;
	if(hal_mjpA_EncLcd_Photo_Start( arg->dst_width,arg->dst_height,arg->img_Q,arg->timestamp) == false)
		return -1;
	if(hal_mjpA_photo_encode_mode() != ENC_MODE_NORMAL)
	{
		res = -2;
		goto OUT;
	}	
    timeout = XOSTimeGet();
	hal_lcdCsiFrameKickOnce();
    while(1)
    {
		hal_wdtClear();
		buf = hal_mjpA_RawBufferGet(&size,NULL, NULL);
		if(buf && size)
	    {
			//hal_mjpA_RawBufferfree();
			break;
	    }
		if((timeout+2000)<XOSTimeGet())
		{
			deg_Printf("image encode : timeout 2-second.\n");
			res = -1;
			break;
		}
    }
	if(res >= 0)
	{
		hal_mjpA_linebufUninit();
		arg->buf = (u8*)hal_sysMemMallocLast(size);
		arg->size = size;
		if(arg->buf == NULL)
		{
			res = -2;
		}else
		{
			hx330x_mcpy0_sdram2gram_nocache((void *)arg->buf, (void *)buf, size);
			hx330x_sysDcacheInvalid((u32)arg->buf,size);
		}
		hal_mjpA_RawBufferfree();

	}
OUT:
	hal_mjpA_Enc_Stop();
    if(arg->timestamp)
	    hal_jpg_watermarkStart(0,0,0);  // disable
    
	return res;
}
/*******************************************************************************
* Function Name  : imageEncodeToSpi
* Description	 : take a photo
* Input 		 : JPG_ENC_ARG *arg
* Output		 : none 										   
* Return		 : int : 0 success
                                -1 fail
*******************************************************************************/
int imageEncodeToSpi(JPG_ENC_ARG *arg, u8 save_ram)
{
    INT32S res = 0;
    INT32U timeout;
	if(arg->fd < 0)
	{
		return -1;
	}
	
	if(hal_mjpA_EncLcd_Photo_Start(arg->dst_width,arg->dst_height,arg->img_Q,arg->timestamp) == false)
		return -2;
	if(hal_mjpA_photo_encode_mode() != ENC_MODE_NORMAL)
	{
		deg_Printf("imageEncodeToSpi fail\n");
		res = -3;
		goto OUT;
	}	
    timeout = XOSTimeGet();
	while(1)
	{
		hal_wdtClear();
		arg->buf = hal_mjpA_RawBufferGet(&arg->size,NULL, NULL);
		
		if(arg->buf && arg->size)
		{
			u32 size = 0;
			hx330x_sysDcacheFlush((u32)arg->buf,arg->size);
			deg_Printf("SPI JPG SIZE:%dkb, remain_size:%dkb\n",arg->size/1024, nvjpg_free_size()/1024);
			if(NV_OK != nv_jpgfile_write ((const void*)arg->buf,arg->size,&size) || size != arg->size)
			{
				deg_Printf("image encode : error\n");
				res = -4;
				goto OUT;
			}
			if(save_ram)
			{
				u8* buf; 
				hal_mjpA_linebufUninit();
				buf = (u8*)hal_sysMemMalloc(arg->size);
				if(buf == NULL)
				{
					res = -4;
				}else
				{
					hx330x_mcpy0_sdram2gram_nocache((void *)buf, (void *)arg->buf, arg->size);
					hx330x_sysDcacheFlush((u32)buf,size);
				}
				arg->buf = buf;
	
			}else
			{
				arg->buf = NULL;
				arg->size = 0;
			}
			hal_mjpA_RawBufferfree();
			break;
		}
		if((timeout+2000)<XOSTimeGet())
		{
			deg_Printf("image encode : timeout 2-second.\n");
			res = -1;
			break;
		}
	
	}
OUT:
	hal_mjpA_Enc_Stop();
    if(arg->timestamp)
	    hal_jpg_watermarkStart(0,0,0);  // disable
    
	return res;
}
#endif








