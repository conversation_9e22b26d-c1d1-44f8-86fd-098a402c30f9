/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if SENSOR_MIPI_720P_H62 > 0
#define MIPI_CLK_SEL					1	//1:180, 2:240, 3:300, 4: 330， 5：360

#define MIPI_DPHY_PLL					57600000L
#define MIPI_CSI_DPHY_PCLK				36000000L
SENSOR_INIT_SECTION const u8 H62InitTable[] = 
{	
    //sensor inittab 初始化表
	0x12, 0x40, //sys start
	0x0c, 0x00,
	//0x0c, 0x01,  //10 bit walking '1' pattern
	0x0D, 0x50,
#if MIPI_CLK_SEL == 1 // pll 180Mhz
	0x0e, 0x11, //pll control1
	0x0f, 0x09, //pll control2
	0x10, 0x1e, //pll control3
	0x11, 0x80,  //clk
	0x19, 0x68, //luminance control
	#define FRAME_WIDTH		(1708)
	#define FRAME_HEIGHT	(789)		
//	0x20, 0xac, // sensor frame time width [7:0]
	0x20, FRAME_WIDTH&0XFF, // sensor frame time width [7:0]
	0x21, FRAME_WIDTH>>8,  // sensor frame time width [15:8] :1920
	0x22, FRAME_HEIGHT&0XFF, // sensor frame time height [7:0]
	//0x22, 0x20, // sensor frame time height [7:0]
	0x23, FRAME_HEIGHT>>8, // sensor frame time height [15:8] 750
	
//	0x20, 0x20, // sensor frame time width [7:0]
//	0x21, 0x05,  // sensor frame time width [15:8] :1920
	//0x22, 0x15, // sensor frame time height [7:0]
//	0x22, 0x10, // sensor frame time height [7:0]
//	0x23, 0x03, // sensor frame time height [15:8] 750
	
//	0x20,0x90,//;60  
//	0x21,0x09,//;09
//	0x22,0x40,
//	0x23,0x06,  	
	
	0x24, 0x00, //HWin [7:0]
	0x25, 0xd0, //VWin [7:0]
	0x26, 0x25, //HVWin
#elif MIPI_CLK_SEL == 2// 240MHz
	0x0e, 0x11, //pll control1 PLL_Pre_Ratio = 1 + [1:0] = 2
	0x0f, 0x09, //pll control2 Mipi Pclk divider = [0] = 1( 1/10 for raw10), DAC Clock = VCO/(1+PLL2[5:3]) = VCO/2 = 180
	0x10, 0x28, //pll control3 VCO = Input clock*PLL3[7:0]/PLL_Pre_Ratio = Input clock * 30/2 = 360
	0x11, 0x80, //clk CLK[7]: System clock option. “1”: system clock use PLLclk directly
	0x19, 0x68, //luminance control
	0x20, 0x00, // sensor frame time width [7:0]
	0x21, 0x08,  // sensor frame time width [15:8] :1920
	0x22, 0x15, // sensor frame time height [7:0]
	0x23, 0x03, // sensor frame time height [15:8] 750
	0x24, 0x00, //HWin [7:0]
	0x25, 0xd0, //VWin [7:0]
	0x26, 0x25, //HVWin
#elif MIPI_CLK_SEL == 3//300MHz
	0x0e, 0x11, //pll control1
	0x0f, 0x09, //pll control2
	0x10, 0x32, //pll control3
	0x11, 0x80,  //clk
	0x19, 0x68, //luminance control
//	0x20, 0x7e, // sensor frame time width [7:0]
//	0x21, 0x12,  // sensor frame time width [15:8] :1920
//	0x22, 0x3f, // sensor frame time height [7:0]
//	0x23, 0x09, // sensor frame time height [15:8] 750
	0x20, 0x00, // sensor frame time width [7:0]
	0x21, 0x0b,  // sensor frame time width [15:8] :1920
	0x22, 0x15, // sensor frame time height [7:0]
	0x23, 0x03, // sensor frame time height [15:8] 750
	0x24, 0x00, //HWin [7:0]
	0x25, 0xd0, //VWin [7:0]
	0x26, 0x25, //HVWin
#elif MIPI_CLK_SEL == 4 //330MHz
	0x0e, 0x11, //pll control1
	0x0f, 0x09, //pll control2
	0x10, 0x37, //pll control3
	0x11, 0x80,  //clk
	0x19, 0x68, //luminance control
//	0x20, 0x7e, // sensor frame time width [7:0]
//	0x21, 0x12,  // sensor frame time width [15:8] :1920
//	0x22, 0x3f, // sensor frame time height [7:0]
//	0x23, 0x09, // sensor frame time height [15:8] 750
	0x20, 0x00, // sensor frame time width [7:0]
	0x21, 0x0e,  // sensor frame time width [15:8] :1920
	0x22, 0x15, // sensor frame time height [7:0]
	0x23, 0x03, // sensor frame time height [15:8] 750
	0x24, 0x00, //HWin [7:0]
	0x25, 0xd0, //VWin [7:0]
	0x26, 0x25, //HVWin
#elif MIPI_CLK_SEL == 5//360MHz	
	0x0e, 0x11, //pll control1
	0x0f, 0x09, //pll control2
	0x10, 0x3c, //pll control3
	0x11, 0x80,  //clk
	0x19, 0x68, //luminance control
//	0x20, 0x7e, // sensor frame time width [7:0]
//	0x21, 0x12,  // sensor frame time width [15:8] :1920
//	0x22, 0x3f, // sensor frame time height [7:0]
//	0x23, 0x09, // sensor frame time height [15:8] 750
	0x20, 0x00, // sensor frame time width [7:0]
	0x21, 0x0d,  // sensor frame time width [15:8] :1920
	0x22, 0x15, // sensor frame time height [7:0]
	0x23, 0x03, // sensor frame time height [15:8] 750
	0x24, 0x00, //HWin [7:0]
	0x25, 0xd0, //VWin [7:0]
	0x26, 0x25, //HVWin
#endif
	
	0x27,0xd4,
	0x28,0x15,
	0x29,0x02,//*///
	0x2a, 0x63, 
	0x2b, 0x21,
	0x2c, 0x08,
	0x2d, 0x01,
	0x2e, 0xbc,
	0x2f, 0xc0,
	
	
	0x41, 0x88,
	0x42, 0x12,
	0x39, 0x90,
	0x1d, 0x00,
	0x1e, 0x04,
	0x7a, 0x4c, //DPHY2 mipi interface :normal operation
	0x70, 0x49, //Mipi1: timing control [7:5]Tlpx
	0x71, 0x2a, //Mipi2: [7:5] Ths-zero [4:0] RSVD
	0x72, 0x48, //Mipi3 :[7:5] Ths-prepare
	0x73, 0x33, //Mipi4 [6:4] Ths-trail
	0x74, 0x52, //Mipi5: [7] mipi_sleep off, [6] mipi continus mode
	0x75, 0x2b, //Mipi6: mipi data type ID
	0x76, 0x40, //Mipi word count LSBs
	0x77, 0x06,//Mipi word count MSBs
	0x78, 0x18,//Mipi9 [6:0] RSVD
	0x66, 0x38,
	0x1f, 0x20,//GLat rsvd
	0x30, 0x90,
	0x31, 0x0c,
	0x32, 0xff,
	0x33, 0x0c,
	0x34, 0x4b,
	0x35, 0xa3,
	0x36, 0x06,
	0x38, 0x40,
	0x3a, 0x08,
	0x56, 0x02,
	0x60, 0x01,
	0x0d, 0x50,
	0x57, 0x80,
	0x58, 0x33,
	0x5a, 0x04,
	0x5b, 0xb6,
	0x5c, 0x08,
	0x5d, 0x67,
	0x5e, 0x04,
	0x5f, 0x08,
	0x66, 0x28,
	0x67, 0xf8,
	0x68, 0x00,
	0x69, 0x74,
	0x6a, 0x1f,
	0x63, 0x82,
	0x6c, 0xc0,
	0x6e, 0x5c,
	0x82, 0x01,
	

	0x46, 0xc2,
	0x48, 0x7e,
	0x62, 0x40,
	0x7d, 0x57,
	0x7e, 0x28,
	0x80, 0x00,
	0x4a, 0x05,
	0x4C, 0x08,
	0x4D, 0x08,
	0x49, 0x08,//0x10,
	0x13, 0x81,
	0x59, 0x9C,//0x97,
	0x12, 0x00, //sleep out
	0x47, 0x47,
	//sleep 500
 	0x0b, 0x62,
	0x0b, 0x62,
	0x0b, 0x62,
	0x0b, 0x62,
	0x0b, 0x62,
	0x0b, 0x62,
	0x0b, 0x62,
	0x0b, 0x62,
	0x0b, 0x62,
	0x0b, 0x62,
	0x0b, 0x62,
	0x0b, 0x62,
	
	0x47, 0x44, 
	0x1f, 0x21, 
	//0x17, 0x00, 
	//0x16, 0x20,
//	0xc0, 0x01,
//	0xc1, 0xaa,
//	0xc2, 0x02,
//	0xc3, 0x03,
//	0xc4, 0x00,
//	0xc5, 0x20,
//	0x1f, 0x80,
	0x02, 0x02,
	0x01, 0x20,
	0x00, 0x03,
//	0x13, 0x80,
//	0x14, 0x40,
	
	SENSOR_TAB_END
};



SENSOR_LSC_TAB_SECTION const u16 h62_lsc_tab[572]={
	437,368,322,294,283,280,289,315,362,433,544,
	414,349,305,282,273,271,276,299,342,410,513,
	396,335,294,274,266,262,269,287,328,393,489,
	385,325,286,270,260,258,264,279,317,382,477,
	379,318,283,267,257,253,260,275,312,374,468,
	375,316,281,265,255,252,259,274,310,371,460,
	376,317,281,265,257,254,260,275,311,373,465,
	382,321,283,268,259,257,263,278,316,378,470,
	392,330,290,272,263,261,268,285,324,388,482,
	407,343,301,279,270,269,275,295,337,404,506,
	431,361,315,289,279,277,286,309,355,424,526,
	459,383,335,306,291,291,302,329,378,451,560,
	466,388,339,310,295,293,305,334,382,458,567,

	437,368,322,294,283,280,289,315,362,433,544,
	414,349,305,282,273,271,276,299,342,410,513,
	396,335,294,274,266,262,269,287,328,393,489,
	385,325,286,270,260,258,264,279,317,382,477,
	379,318,283,267,257,253,260,275,312,374,468,
	375,316,281,265,255,252,259,274,310,371,460,
	376,317,281,265,257,254,260,275,311,373,465,
	382,321,283,268,259,257,263,278,316,378,470,
	392,330,290,272,263,261,268,285,324,388,482,
	407,343,301,279,270,269,275,295,337,404,506,
	431,361,315,289,279,277,286,309,355,424,526,
	459,383,335,306,291,291,302,329,378,451,560,
	466,388,339,310,295,293,305,334,382,458,567,

	437,368,322,294,283,280,289,315,362,433,544,
	414,349,305,282,273,271,276,299,342,410,513,
	396,335,294,274,266,262,269,287,328,393,489,
	385,325,286,270,260,258,264,279,317,382,477,
	379,318,283,267,257,253,260,275,312,374,468,
	375,316,281,265,255,252,259,274,310,371,460,
	376,317,281,265,257,254,260,275,311,373,465,
	382,321,283,268,259,257,263,278,316,378,470,
	392,330,290,272,263,261,268,285,324,388,482,
	407,343,301,279,270,269,275,295,337,404,506,
	431,361,315,289,279,277,286,309,355,424,526,
	459,383,335,306,291,291,302,329,378,451,560,
	466,388,339,310,295,293,305,334,382,458,567,

	437,368,322,294,283,280,289,315,362,433,544,
	414,349,305,282,273,271,276,299,342,410,513,
	396,335,294,274,266,262,269,287,328,393,489,
	385,325,286,270,260,258,264,279,317,382,477,
	379,318,283,267,257,253,260,275,312,374,468,
	375,316,281,265,255,252,259,274,310,371,460,
	376,317,281,265,257,254,260,275,311,373,465,
	382,321,283,268,259,257,263,278,316,378,470,
	392,330,290,272,263,261,268,285,324,388,482,
	407,343,301,279,270,269,275,295,337,404,506,
	431,361,315,289,279,277,286,309,355,424,526,
	459,383,335,306,291,291,302,329,378,451,560,
	466,388,339,310,295,293,305,334,382,458,567
};
// static void mipi_H62_rotate(u32 r)
// {
// 	//u8 flip = (r&0x2)>>1;
// 	u8 flip = r&0x1;
// 	u8 mirror = r&0x1;
// 	r = ((mirror<<1)|flip)<<4;
// 	u8 i,iicbuf[][2] = {
// 		{0x12,(0x00|r)},
// 		{0x27,0xd4+flip},
// 		{0x28,0x15+mirror}
// 		}; //init table value
// 	for(i=0;i<sizeof(iicbuf)/2;i++)
// 		sensor_iic_write((u8 *)&iicbuf[i][0]);
// }
// static void mipi_H62_hvblank(u32 h,u32 v)
// {
// 	u32 i,frame_rate;
// 	u16 hbuffer[] = {0xd5a,0xc05/*0x1800*/,0x960,0x803};//frame_rate 20 , 25 ,30
// 	frame_rate = 48000000 / (h*v);
// 	h= hbuffer[(frame_rate-15) / 5];
// 	const u8 t[][3] = {
// 		{0x23,v >> 8},
// 		{0x22,v & 0xff},
// 		{0x21,h >> 8},
// 		{0x20,h & 0xff},
// 	};
// 	for(i=0;i<4;i++){
// 		sensor_iic_write((u8 *)&t[i][0]);
// 	}
// }
// /*static u32 sensor_h62_exp_rd(void)
// {
// 	return 0; 
// }*/
// static void mipi_sensor_h62_exp_wr(u32 EV)
// {
// 	u8 iicbuf[4] = {0x02,0x00,0x01,0x00};
// 	iicbuf[1] = ((EV >> 8) & 0xff);
// 	iicbuf[3] = (EV & 0xff);
// //	sensor_iic_enable();
// //	sensor_iic_info();		
// 	sensor_iic_write(iicbuf);

// 	//Delay_MS(1);
// 	sensor_iic_write(iicbuf+2);	
// //  sensor_iic_disable();
// }
// static void sensor_h62_gain_wr(u32 EV)
// {
// 	u32 i;
// 	u8 iicbuf[2] = {0x00,0x00};
// 	u32 rough_gain[7] = {256,512,1024,2048,4096,8192,16385};
// //	sensor_iic_enable();
// //	sensor_iic_info();	
// 	//EV = EV*2;
// 	for(i=0;i<6;i++){
// 		if((EV >= rough_gain[i])&&(EV < rough_gain[i+1])){
// 			iicbuf[1] |= (i<<4);
// 			iicbuf[1] |= (((EV<<8) / rough_gain[i]-256)>>4);
// 			break;
// 		}
// 	}
// 	//debg("gain:%x\n",iicbuf[1]);
// 	sensor_iic_write(iicbuf);	
// //  sensor_iic_disable();	
// }
// static void sensor_h62_exp_gain_wr(u32 exp,u32 gain){
// 	sensor_h62_exp_wr(exp);
// 	sensor_h62_gain_wr(gain);
// }

SENSOR_OP_SECTION const Sensor_Adpt_T  h62_adpt = 
{
	.typ 				= CSI_TYPE_RAW10|CSI_TYPE_MIPI,// csi type: 10; 8	

#if  (CURRENT_CHIP == FPGA)
	.mclk 				= 24000000,			//mclk set
	.mclk_src			= MCLK_SRC_SYSPLL,  //mclk src: MCLK_SRC_SYSPLL, MCLK_SRC_USBPLL
	.pclk_dig_fir_step 	= 0,				//pclk digital filter :0 - disable filter, 1 - enable 2 steps filter,2 - enable 3 steps filter, 3 - disable PCLK OUTPUT
	.pclk_ana_fir_step	= 0,				//pclk analog filter :4'b0xxx： diable， 4'b1xxx: enable
	.pclk_inv_en 		= 0,				//pclk invert: 0 - not invert, 1 - invert
	.csi_tun 			= 0,				//csi clk tune: 0x00~0x0f: + 1~ +16steps, 0x10~0x1f: -1 ~ -16 steps
#else
	.mclk 				= 24000000,			//mclk set
	.mclk_src			= MCLK_SRC_SYSPLL,	//mclk src: MCLK_SRC_SYSPLL, MCLK_SRC_USBPLL
	.pclk_dig_fir_step 	= 0,				//pclk digital filter :0 - disable filter, 1 - enable 2 steps filter,2 - enable 3 steps filter, 3 - disable PCLK OUTPUT
	.pclk_ana_fir_step	= 0,				//pclk analog filter :4'b0xxx： diable， 4'b1xxx: enable
	.pclk_inv_en 		= 0,				//pclk invert: 0 - not invert, 1 - invert
	.csi_tun 			= 0,				//csi clk tune: 0x00~0x0f: + 1~ +16steps, 0x10~0x1f: -1 ~ -16 steps
#endif
	//sensor input -> sensor crop -> csi input， CSI_TYPE_MIPI 下不支持
	.senPixelw          = 1280, 			//sensor input width
	.senPixelh          = 720,				//sensor input height
	.senCropW_St        = 0,				//sensor crop width start
	.senCropW_Ed        = 1280,				//sensor crop width end
	.senCropH_St        = 0,				//sensor crop height start
	.senCropH_Ed        = 720,				//sensor crop height end
	.senCropMode        = CSI_PASS_MODE,	//sensor crop mode: CSI_PASS_MODE, CSI_CROP_MODE , CSI_DIV2_MODE, CSI_CROP_DIV2_MODE

	.pixelw 			= 1280,				//csi input width
	.pixelh				= 720,				//csi input height
	.hsyn 				= 1,				//1: hsync valid high, 0: hsync valid low
	.vsyn 				= 1,				//1: vsync valid high, 0: vsync valid low
	.colrarray 			= CSI_PRIORITY_BGGR,//RAW: CSI_PRIORITY_RGGB, CSI_PRIORITY_GRBG, CSI_PRIORITY_BGGR, CSI_PRIORITY_GBRG
											//YUV422: CSI_PRIORITY_CBY0CRY1, CSI_PRIORITY_CRY0CBY1, CSI_PRIORITY_Y0CBY1CR, CSI_PRIORITY_Y0CRY1CB
											
	.sensorCore			= SYS_VOL_V2_8,		//VDDSENCORE: SYS_VOL_V1_2 ~ SYS_VOL_V3_3
	.sensorIo			= SYS_VOL_V2_8,		//VDDSENIO: SYS_VOL_V1_2 ~ SYS_VOL_V3_56

	
	.mipi_adapt			= {
		.lanes			= 1,			//mipi lane num
		.raw_bit		= 10,			//10/8: RAW10/RAW8
		.dphy_pll		= MIPI_DPHY_PLL,
		.csi_pclk		= MIPI_CSI_DPHY_PCLK,
		.tclk_settle	= 17,			//TCLK_SETTLE_TIME  = tclk_settle*(1/dphy_pll)
		.tclk_miss		= 4,			//TCLK_MISS_TIME	= tclk_miss*(1/dphy_pll)
		.tclk_prepare	= 2,			//TCLK_PREPARE_TIME = tclk_prepare*(1/dphy_pll)
		.ths_settle		= 2,			//THS_SETTLE_TIME  	= ths_settle*(1/dphy_pll)
		.ths_skip		= 6,			//THS_SKIP_TIME		= ths_skip*(1/dphy_pll)
		.ths_dtermen	= 4,			//THS_DTERMEN_TIME 	= ths_dtermen*(1/dphy_pll)
        //Controller Timing
		.hsa			= 10,			//HSA_TIME			= hsa*(1/csi_pclk)
		.hbp			= 20,			//h back porch			= hbp*(1/csi_pclk)
		.hsd			= 200,			//HSD_TIME			= hsd*(1/csi_pclk)
		.hlines			= 30,           //hsa + hbp + video zone + hfp
		.vsa_lines		= 3,
		.vbp_lines		= 5,            //back porch
		.vfp_lines		= 7,            //front porch
		.vactive_lines	= 0x50
	},
	.rotate_adapt 		= {0},

	.hvb_adapt = {
		.pclk			= 48000000,			//csi pclk input	
		.v_len			= 800,				//sensor v_len = height + vblank
		.step_val		= 0,				//auto cal
		.step_max		= 0,				//auto cal
		.down_fps_mode	= 0xff,				//0,1,hvb down_fps; 2: exp down_fps, 0xff: turn off down_fps
#if  (CURRENT_CHIP == FPGA)
		.fps			= 20,				//sensor fps set
#else
		.fps			= 25,				//sensor fps set
#endif
		.frequency		= 0					//0: 50hz, 1: 60hz
	},
	//_ISP_DIS_,_ISP_EN_,  _ISP_AUTO_
	.isp_all_mod =  (_ISP_EN_  <<_BLC_POS_ | _ISP_EN_  <<_LSC_POS_  | _ISP_AUTO_<<_DDC_POS_   | _ISP_AUTO_<<_AWB_POS_  \
					|_ISP_DIS_  <<_CCM_POS_ | _ISP_DIS_<<_AE_POS_   | _ISP_DIS_<<_DGAIN_POS_ | _ISP_AUTO_<<_YGAMA_POS_ \
					| _ISP_AUTO_<<_RGB_GAMA_POS_ | _ISP_DIS_<<_CH_POS_\
					|_ISP_DIS_<<_VDE_POS_ | _ISP_DIS_<<_EE_POS_   | _ISP_DIS_<<_CFD_POS_    |_ISP_DIS_<<_SAJ_POS_
					|_ISP_YUV422_DIS_ << _YUVMOD_POS_),
	.blc_adapt = {	//when _BLC_POS_ set _ISP_EN_ or _ISP_AUTO_
		.blkl_r		= -5,					//BLC red adjust //signed 10bit
		.blkl_gr	= -5,					//BLC green(red) adjust //signed 10bit
		.blkl_gb	= -5,					//BLC green(blue) adjust //signed 10bit
		.blkl_b		= -5,					//BLC blue adjust //signed 10bit
		.blk_rate 	= {0,2,3,4,5,6,7,8},	//_ISP_AUTO_ use, [AE statistic YLOGA/step_len] to adj BLC para, 8 means 1 rate
		.step_len	= 5,					//_ISP_AUTO_ use
	},
	.ddc_adapt = {	//when _DDC_POS_ set _ISP_EN_ or _ISP_AUTO_
		.hot_num 		= 2,				//亮点：目标点比周围24个点中的(24 - (8- hot_num))个点 都亮，差值 >((h_th_rate*p[2][2])/16 + hot_th)
		.dead_num		= 2,				//暗点：目标点比周围24个点中的(24 - (8-dead_num))个点 都暗，差值 >(d_th_rate*AVG/16 + dead_th), AVG为P[2][2]周围8个点平均值
		.hot_th			= 0,				//亮点：判断亮点的阈值，0~1023
		.dead_th		= 0,				//暗点：判断暗点的阈值，0~1023
		.avg_th			= 16,				//暗点/亮点替换：差值平均值的阈值， 0~255
		.d_th_rate		= {4,4,4,4,4,4,4,4},//_ISP_AUTO_时，根据cur_br获取d_th_rate， default使用 d_th_rate[7] , 16 means 1 rate
		.h_th_rate		= {8,8,8,8,8,8,8,8},//_ISP_AUTO_时，根据cur_br获取 h_th_rate， default使用 h_th_rate[7] , 16 means 1 rate
		.dpc_dn_en		= 1,				//1:开启pre_denoise，滤波系数与坐标距离，像素点差值正相关
		.indx_table		= {2,0,0,0,0,0,0,0},//pre_denoise: 取值范围0~7，配置 dn_idx_table, 值越大，滤波开的越大
		.indx_adapt		= {2,1,1,1,0,0,0,0},//_ISP_AUTO_ use：根据yloga/ddc_step查表获得的值，来调整indx_table 表中的值
		.std_th			= {6,20,30,40,50,80,120}, //差值对比表，对应用于获得indx_table的值
		.std_th_rate	= 0,				//用于调整 std_th ，std_th_rate * avg_val / 16;
		.ddc_step		= 7,				//_ISP_AUTO_ use
		.ddc_class		= 7,				//预留用
	},	
	.awb_adapt = {	////when _AWB_POS_ set _ISP_EN_ or _ISP_AUTO_
		.seg_mode		= 0x03,		//AWBStatistic，取值 0~3，根据Y值划分为 (1 << seg_mode)个统计区域
		.rg_start		= 191,		//AWBStatistic yuv_mod_en = 0使用，rgain (g*256/r)起始范围
		.rgmin			= 240,		//AWBStatistic yuv_mod_en = 0 使用，rgain比较的最小值，当rgain落在[rgmin,rgmax]范围内，则落在统计范围内
		.rgmax			= 485, 		//AWBStatistic yuv_mod_en = 0， rgain比较的最大值 // 256 -> 1 gain  500 /256 =about 1.9 gain
		.weight_in		= 3,		//AWBStatistic yuv_mod_en = 0，g 在 [bgain_in_low,bgain_in_high]的统计权重值（+1）
		.weight_mid		= 2,		//AWBStatistic yuv_mod_en = 0，g 在 [bgain_out_low,bgain_out_high]的统计权重值（+1）
		.ymin			= 0x0a,		//AWBStatistic 统计的Y值区域的最小值
		.ymax			= 0xd0,		//AWBStatistic 统计的Y值区域的最大值
		.hb_rate		= 0xff,		//AWB ADJ bgain <256时使用
		.hb_class		= 0x00,		//AWB ADJ 取值范围 0~3 , bgain <256时使用，为 0 时不用，th = 1024 - (1 <<(6+hb_class))
		.hr_rate		= 0xff,		//AWB ADJ rgain <256时使用
		.hr_class		= 0x00,		//AWB ADJ 取值范围 0~3 , rgain <256时使用，为 0 时不用，th = 1024 - (1 <<(6+hr_class))
		.awb_scene_mod	= 0,		//当前使用的AWB RGB GAIN，用于查表manu_awb_gain[]
		.manu_awb_gain	= { 		//定义不同的AWB GAIN表
		//(bgain << 20) | (ggain<< 10) | (rgain<< 0),
			(400 << 20) | (256<< 10) | (380<< 0), 
			(368 << 20) | (256<< 10) | (350<< 0),
			(465 << 20) | (256<< 10) | (225<< 0),
			(370 << 20) | (256<< 10) | (385<< 0),
			(370 << 20) | (256<< 10) | (385<< 0)
		},
		.yuv_mod_en		= 0,										 //1:base Y, 0: Gray World
		.cb_th			= {0x5,0x0a,0x0f,0x14,0x19,0x1e,0x23,0x28},  //AWBStatistic yuv_mod_en = 1, 对应不同的Y分区的ABS(CB)阈值，取值范围 0~127
		.cr_th			= {0x5,0x0a,0x0f,0x14,0x19,0x1e,0x23,0x28},	 //AWBStatistic yuv_mod_en = 1 ,对应不同的Y分区的ABS(CR)阈值，取值范围 0~127 
		.cbcr_th		= {0x8,0x0f,0x16,0x1e,0x24,0x2d,0x34,0x3c},  //AWBStatistic yuv_mod_en = 1,对应不同的Y分区的ABS(CB)+ABS(CR)阈值，取值范围 0~255 
		.ycbcr_th		= 0x0a,										 //AWBStatistic yuv_mod_en = 1,对应不同的Y分区的y阈值(y-ABS(CB)-ABS(CR))，取值范围 0~255 
		.manu_rgain		= 0,										 //manual AWB时记录配置的rgain
		.manu_ggain		= 0,										 //manual AWB时记录配置的ggain	
		.manu_bgain		= 0,										 //manual AWB时记录配置的bgain
		.rgain			= 0,										 //auto AWB时记录配置的rgain
		.ggain			= 0,										 //auto AWB时记录配置的ggain
		.bgain			= 0,										 //auto AWB时记录配置的bgain
		.seg_gain		= {{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0}}, //AUTO AWB时记录不同Y分区的RGB GAIN值
		.awb_tab		= {	//AWBStatistic yuv_mod_en = 0 用于根据(rgain-rg_start)查表获得目标g值，以16位单位（16*32 = 512）
			183,186,187,184,181,179,177,174,171,168,165,163,161,159,158,155,153,150,148,144,139,131,123,117,109,104, 98, 92, 86, 80, 73, 66, //bgain_out_high
			183,180,177,174,171,168,165,161,157,154,151,147,144,141,138,136,134,133,130,120,112,106,101, 99, 97, 95, 91, 86, 81, 76, 71, 66, //bgain_in_high
			183,170,164,158,153,151,146,142,140,136,130,126,125,124,117,115,110, 99, 93, 89, 88, 87, 86, 82, 84, 84, 78, 76, 73, 70, 68, 65, //bgain_in_low  
			181,157,150,143,138,133,130,127,122,118,115,113,110,102, 97, 90, 84, 81, 74, 72, 70, 68, 68, 68, 68, 69, 70, 69, 68, 67, 66, 65 //bgain_out_low
		}		
	},					
	.ccm_adapt = {	//when _CCM_POS_ set _ISP_EN_ or _ISP_AUTO_
		//注意 CCM TAB排列顺序如下，即 竖着看，第一列为调整R， 第二列调整G，第三列调整B
		// RR,  GR, BR,
		// RG,  GG, BG,
		// RB,  GB, BB,
		//R:  (RR*R+RG*G + RB*B)/256 + s41
		//G:  (GR*R+GG*G + GB*B)/256 + s42
		//B:  (BR*R+BG*G + BB*B)/256 + s43
		.ccm	= {	//signed 10bit, -512~511, 
			0x100,	0x000,	0x000,  
			0x000,	0x100,	0x000,  
			0x00,	0x00,	0x100   
		},
		.s41	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
		.s42	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
		.s43	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
	},	
	.ae_adapt = {	//when _AE_POS_ set _ISP_EN_ or _ISP_AUTO_
		.exp_adapt = {	//AE auto adj时使用的参数
			.ylog_cal_fnum	= 4,		//_ISP_AUTO_使用：AE统计的frame num，最大32，计算获得ylog_avg 和yloga
			.exp_tag		= {50,60,70,80,90,95,100,136}, //_ISP_AUTO_使用：根据cur_br查表获得目标ylog
			.exp_ext_mod	= 3,		//_ISP_AUTO_使用：低照度下的最小ylog值：exp_ext_mod*8
			.exp_gain		= 195*256,	//当前exp*gain的值
			.k_br			= 12,		//_ISP_AUTO_使用：用于从ylog换算cur_br的系数，值越大，换算的cur_br越大
			.exp_min		= 4,		//限制最小exp值：当exp_gain比较小时，调整gain
			.gain_max		= 1024*12,	//限制最大gain值：当exp_gain比较大时，调整exp
			.frame_nums		= 2,		//_ISP_AUTO_使用：曝光相关调整的帧数间隔
			.ratio_range	= 16,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar 范围不在[32-ratio_range/2,32 + ratio_range]时，加快调整速度
			.weight_in		= 1,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar <= 32时，使用weight_in系数(即目标照度需要降低时)
			.weight_out		= 4,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar > 32时，使用weight_out系数(即目标照度需要提高时)
			.ev_mode		= 1,	    //外部调整整体亮度用：1:在VDE模块调整bright_oft，0：在AE调整中使用
		},
		.hgrm_adapt = { 
			//AE统计配置的参数，AE统计将整幅图划分为5*5的块进行灰度（Y值）统计
			//X[0 - WIN_X0 - WIN_X1 - WIN_X2 - WIN_X3 - WIDTH]
			//Y[0 - WIN_Y0 - WIN_Y1 - WIN_Y2 - WIN_Y3 - HEIGHT]
			.allow_miss_dots	= 256,	//预留用
			.ae_win_x0			= 160,	//
			.ae_win_x1			= 320,
			.ae_win_x2			= 960,
			.ae_win_x3			= 1120,
			.ae_win_y0			= 200,
			.ae_win_y1			= 400,
			.ae_win_y2			= 640,
			.ae_win_y3			= 680,
			.weight_0_7			= 0x11111112,//每4bit 对应每个区域的统计权重，区域 0~7
			.weight_8_15		= 0x11114111,//每4bit 对应每个区域的统计权重，区域 8~15
			.weight_16_23		= 0x88811888,//每4bit 对应每个区域的统计权重，区域 16~23
			.weight_24			= 0x01,		 //每4bit 对应每个区域的统计权重，区域 24
			.hgrm_centre_weight	= {15,14,13,12,11,10,9,8}, //用于根据cur_br调整中间区域，即区域12的权重值
			.hgrm_gray_weight	= {8,8,9,9,10,10,11,12},   //_ISP_AUTO_使用：根据Y值划分区域调整统计的值
		},
	},		
	.rgbdgain_adapt = { //when _DGAIN_POS_ set _ISP_EN_ or _ISP_AUTO_
		.dgain		= {64,64,64,64,64,64,64,64,64},	//配置寄存器：根据Y值的大小划分8个区域来调整
		.dgain_rate	= {64,64,64,64,64,64,64,64}, 	//_ISP_AUTO_使用：根据cur_br获得调整rate，用于调整dgain[]
	},	
	.ygama_adapt = {	//when _YGAMA_POS_ set _ISP_EN_ or _ISP_AUTO_
		.tab_num		= {5,7,9,11,13,14,15,16}, //根据 tab_num[i]的值来选择sensor_ygamma_tab[tab_num[i]]
		.adpt_num		= {1,4,7,7,7,7,7,7},	  //_ISP_AUTO_: 根据cur_br取adpt_num[]值，取的值用于查表tab_num，然后根据查表的值选中对应的sensor_ygamma_tab[]表
		.gam_num0		= 16,					  //当前使用的gamma表index0, 对应sensor_ygamma_tab[index0]
		.gam_num1		= 16,					  //当前使用的gamma表index1, 对应sensor_ygamma_tab[index1]
		.br_mod			= 0,					  //根据br_mod来从index0和index1表中加权平均获得目标的ygamma值
		.bofst			= 0,					  //ymin值 = bosfst << (10 - 8)
		.lofst			= 0xff,					  //ymax值 = lofst << (10 - 8)
		.pad_num		= 1,					  //配置寄存器用，不为0，微调经过ygamma的RGB值
	},
	.rgbgama_adapt = { //when _RGB_GAMA_POS_ set _ISP_EN_ or _ISP_AUTO_
		.tab_num		= {0,1,2,3,4,5,6,7},	//根据 tab_num[i]的值来选择sensor_rgb_gamma[tab_num[i]] 
		.adpt_num		= {3,2,1,1,1,1,1,1},	//_ISP_AUTO_: 根据cur_br取adpt_num[]值，取的值用于查表tab_num，然后根据查表的值选中对应的sensor_rgb_gamma[]表
		.max_oft		= {16,12,12,8,4,0,0,0}, //_ISP_AUTO_: 根据cur_br查表获得当前的max_oft0值
		.gam_num0		= 3,					//当前使用的gamma表index0, 对应sensor_rgb_gamma[index0]
		.gam_num1		= 3,					//当前使用的gamma表index1, 对应sensor_rgb_gamma[index1]
		.max_oft0		= 0,					//用于加大rgbgamma的值
		.br_mod			= 0,					//根据br_mod来从index0和index1表中加权平均获得目标的rgbgamma值
		.rmin			= 0,					//限制最小r值
		.rmax			= 0xff, 				//限制最大r值
		.gmin			= 0,					//限制最小g值
		.gmax			= 0xff,					//限制最大g值
		.bmin			= 0,					//限制最小b值
		.bmax			= 0xff,					//限制最大b值
		.fog_llimt		= 64,					//_ISP_AUTO_: 根据ylog动态调整的 rmin/gmin/bmin的最大值
		.fog_hlimt		= 224,					//_ISP_AUTO_: 根据ylog动态调整的 rmax/gmax/bmax的最小值
		.fog_dotnum		= 4000,					//_ISP_AUTO_: 亮度统计值的目标值，用于计算获得ylog_low和ylog_high
	},
	.ch_adapt = {	//when _CH_POS_ set _ISP_EN_ or _ISP_AUTO_
		.stage0_en	= 1,//enable r g b
		.stage1_en	= 1,//enable y c m
		.enhence	= {0,1,0,1,0,0},//enhance channel  r g b y c m
		//r: >th1[0] && < th0[0], g: [th0[1],th1[1]], b: [th0[2],th1[2]],
		//y(r+g): [th0[3], th1[3]], c(g+b):[th0[4],th1[4]], m(b+r):[th0[5],th1[5]]
		.th1		= {320,192,320,128,256,384},//you can set hue width
		.th0		= {64,  64,192,  0,128,256},//you can set hue width
		//m_x c_x y_x b_x g_r r_x
		.r_rate		= {0,0,0,5,0,0},//[0]~[5]:r,g,b,y,c,m
		.g_rate		= {0,14,0,5,0,0},//[0]~[5]:r,g,b,y,c,m
		.b_rate		= {0,0,0,0,0,0},//[0]~[5]:r,g,b,y,c,m
		.sat		= {16,16,18,20,20,20,20,20,20,20,20,20,20,18,16,16,16}, //根据饱和度S按16划分为16个区域进行调整的rate表
		.rate		= {0,16,16,16,16,16,16,16}, //_ISP_AUTO_使用：根据yloga/ch_step查表获得rate，用于调整r_rate，g_rate，b_rate，sat表
		.ch_step	= 7,						//_ISP_AUTO_使用
	},
	.vde_adapt = {	//when _VDE_POS_ set _ISP_EN_ or _ISP_AUTO_
		.contra		= 0x80,	//取值范围0~255，对比度调节系数 (contra-128)/128, 配置为0x80时不调节
		.bright_k	= 0x80, //取值范围0~255，亮度调节系数 (bright_k-128)/128, 配置为0x80时不调节
		.bright_oft	= 0x80, //取值范围0~255，亮度增加值： (bright_oft-128), 配置为0x80时不调节
		.hue		= 0x80, //取值范围0~255，色度（UV）调节系数：配置为0x80时不调节
		.sat		= {64,70,86,96,96,96,96,84,80}, //饱和度调节表（调节UV），根据Y值划分为32间隔的8个区域进行取值，64表示1
		.sat_rate	= {10,10,16,16,16,16,16,16}, //_ISP_AUTO_使用：根据yloga/vde_step选择sat_rate，用于调整sat[]表的值
		.vde_step	= 6,	//_ISP_AUTO_使用
	},
	.ee_adapt = {	//when _EE_POS_ set _ISP_EN_ or _ISP_AUTO_
		//锐化或降噪的差值区间[ee_dn_th-> ee_keep_th-> ee_sharp_th]
		//ee_dn_th = ee_dn_th + ee_th_adp *avg/256;
		//ee_keep_th = ee_dn_th + (1<<ee_dn_slope);
		//ee_sharp_th = ee_keep_th + (1<<ee_sharp_slope);
		.ee_class		= 1,	//预留用	
		.ee_step		= 6,	//_ISP_AUTO_使用：预留ylog 调整用
		.ee_dn_slope	= {1,1,1,1,1,1,1,1},	//_ISP_AUTO_使用：取值范围0~7，根据cur_br查表获得ee_dn_slope
		.ee_sharp_slope	= {5,5,4,4,3,3,3,3},	//_ISP_AUTO_使用：取值范围0~7，根据cur_br查表获得ee_sharp_slope	
		.ee_th_adp		= {8,8,8,8,8,8,8,8},	//_ISP_AUTO_使用：取值范围0~15，根据cur_br查表获得ee_th_adp	
		.ee_dn_th		= {8,6,4,2,2,2,2,2}, //_ISP_AUTO_使用：取值范围0~63，根据cur_br查表获得ee_dn_th	
		.sharp_class	= {0x7,0x9,0xa,0xa,0xa,0xa,0xa,0xa}, //_ISP_AUTO_使用：取值范围0~31，根据cur_br查表获得sharp_class,用于配置 ee_sharp_mask[12] = 32-sharp_class
		.dn_class		= {0,0,0,0,0,0,0,0},	//_ISP_AUTO_使用：取值范围0~31，根据cur_br查表获得dn_class,用于选择不同的dn_mask表，目前固定用0
	},
	.cfd_adapt = {	//when _EE_POS_ set _ISP_EN_， and _CFD_POS_ set _ISP_EN_ or _ISP_AUTO_ 
		//根据Y值划分区域，
		//(1) Y < ccf_start的区域，mean_en = 1时，进行高斯滤波处理
		//(2) ccf_start < y < ccf_white_ymin, 使用 (ccf_white_ymin - y)/(16<<wclass)为系数调整UV
		//(3) ccf_white_ymin <= y < ymax的区域，直接配置UV 为128
		//(4) y > ymax同时 UV差值大于 th的区域，使用 rate/16 为系数调整UV
		.rate		= 4, 		// UV调整rate，取值范围0~15，
		.ymax		= 0xe0,		// 强光区 ymax配置，取值范围 0~255
		.th			= 0x20, 	// 配置(ABS(U) + ABS(V))阈值，取值范围 0~127
		.wdc_en		= 1, 		// 1：使能(2)(3)区域的调整	
		.wclass		= 1, 		//ccf_start: wymin - (16<<wclass)   reduce saturation
		.wymin		= 0xff, 	//ccf_white_ymin 
		.mean_en	= 1, 		//ccf_mean: 配置为1，使能(1)区域的调整
		.dn_class	= 0,		//选择ccf_cd_mask[9]表，目前固定配置为0
		.ccf_en		= 1,		//配置为1时，使能(4)区域的调整
	},
	.saj_adapt = {	//when _SAJ_POS_ set _ISP_EN_， and _CFD_POS_ set _ISP_EN_ or _ISP_AUTO_ 
		.sat		= {12,12,13,14,16,18,18,20,20,20,18,18,16,16,14,12,12}, //取值范围0~31，饱和度调节率表，色饱和度[0,255]划分为16个区域，
		.sat_rate	= {8,14,16,16,16,16,16,16}, //_ISP_AUTO_使用：根据yloga/saj_step查表用于调节sat[]表, 16为单位
		.saj_step	= 6,		//_ISP_AUTO_使用：
	},
	.md_adapt = {	
		.pixel_th		= 20,
		.num_th			= 20,
		.update_cnt		= 1,
		.win_h_start	= (1280/4)*1,	
		.win_h_end		= (1280/4)*3,
		.win_v_start	= (720/4)*1,
		.win_v_end		= (720/4)*3,
	}, 

	.p_fun_adapt = {
		.fp_rotate		= 0,//H62_rotate,
		.fp_hvblank		= 0,//H62_hvblank,
		.fp_exp_gain_wr	= 0//sensor_h62_exp_gain_wr
	},

};
SENSOR_HEADER_ITEM_SECTION const Sensor_Ident_T h62_init =
{
	.sensor_struct_addr   	= (u32 *)&h62_adpt,     
	.sensor_struct_size   	= sizeof(Sensor_Adpt_T),
	.sensor_init_tab_adr  	= (u32 *)H62InitTable,     
	.sensor_init_tab_size 	= sizeof(H62InitTable),
	.lsc_tab_adr 			= (u32 *)h62_lsc_tab,     
	.lsc_tab_size 			= sizeof(h62_lsc_tab), 
	.sensor_name	  		= "H62_720p",
	.w_cmd            		= 0x60,                   
	.r_cmd            		= 0x61,                   
	.addr_num         		= 0x01,                   
	.data_num         		= 0x01,   
	.id               		= 0x62, 
	.id_reg           		= 0x0b,  
};

#endif