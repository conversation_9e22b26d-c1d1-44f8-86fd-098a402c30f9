/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../inc/hal.h"

typedef struct MJPEG_DEC_OP_S {
	u16 height;		/* Image height in unit of pixel */
	u16 width;		/* Image width in unit of pixel */
	u8  msx;		/* y sampling factor hor */
	u8  msy;		/* y sampling factor vert*/
    u8  yuvfmt;     /* yuv format,0:yuv420,1:yuv422*/
	u8  qtid[3];	/* dequantizer table ID for y component */
					/* dequantizer table ID for u component */
					/* dequantizer table ID for v component */
	u8  hfid[3][2]; /* [y/u/v][AC/DC],[0][0]:yAC,[0][1]:yDC*/
	u16 nrst;		/* DRI interval */
	u16 bs_ofs;		/* bit stream offset to jpeg start */
	u16 qt_ofs[3];  /* dequantizer table offset for id=0 component*/
                    /* dequantizer table offset for id=1 component*/
                    /* dequantizer table offset for id=2 component*/
    u32 comp_table_id;
    u32 qt_addr[3];
	u8 *bsptr;		/* bit stream start */

	u8 *dqt[4];
	u8 *dht[4];
	u8 *valptr[4];
	u16 *minicode[4];
} MJPEG_DEC_OP_T;

ALIGNED(4) static GRAM_SECTION u8 mjp_decode_buf[1024] ;


ALIGNED(4) static MJPEG_DEC_OP_T  mjpDecCtrl;




/*******************************************************************************
* Function Name  : hal_mjpQTTable
* Description    : hal layer.mjpeg decode qt table caculation
* Input          : u8 * seg : buffer
				   u32 len  : length
* Output         : None
* Return         : None
*******************************************************************************/
static int hal_mjpQTTable(u8 * seg, u32 len)
{
	u8 d, *p;
	while(len)
	{
		d = seg[0];
		if (d & 0xF0)
			return -1;				/* Err: not 8-bit resolution */
		p = mjpDecCtrl.dqt[d & 3];	/* Get table ID */

		memcpy((u8*)p,(u8*)&seg[1],64);
		mjpDecCtrl.qt_addr[d & 3] = (u32)&seg[1];
		if(len < 65)
		{
			return -1;
		}
		seg += 65;
		len -= 65;
	}

    if(mjpDecCtrl.qt_addr[2])
        return -1;

	return 0;
}
/*******************************************************************************
* Function Name  : hal_mjpHuffmanTable
* Description    : hal layer.mjpeg decode huffman table caculation
* Input          : u8 * seg : buffer
				   u32 len  : length
* Output         : None
* Return         : None
*******************************************************************************/
static int hal_mjpHuffmanTable(u8 * seg,u32 len)
{
	int counter,i;
	u8  order,j;
	u16 code,d;
	while(len)
	{
		hal_wdtClear();
		d = *seg++; len--;
		d = ((d & 0x10) >> 3) + (d & 0x01);

		counter = code = 0;

		for(i=0,order = 15; i<16; i++)
		{
			j = *seg++;
			mjpDecCtrl.minicode[d][i] = ((code >> order) & 0xffff);
			mjpDecCtrl.valptr[d][i]   = counter;
			counter += j;
			code += j << order;
			order--;
		}
		if(len < (16 + counter))
		{
			return -1;
		}
		len -= 16;

		for(i=0; i < counter; i++)
		{
			mjpDecCtrl.dht[d][i] = *seg++;
		}
		len -= counter;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : hal_mjpDecodeSize
* Description    : hal layer.mjpeg decode get jpeg file header
* Input          : u8 * p_jpeg_header : buffer
* Output         : u16 *width
				   u16 *height
* Return         : None
*******************************************************************************/
static int hal_mjpDecodeSize(u8 * p_jpeg_header,u16 *width,u16 *height)
{
	u8 *seg;
	u32 i;
	int res = -1;


	seg = p_jpeg_header;

	if (LDB_WORD(seg) != 0xFFD8)
	   return -1;

	seg += 2;

	for(i = 512;i > 0;i -= 2)
	{
		if(LDB_WORD(seg) == 0xffc0)
		{
			seg += 4;
			*width = LDB_WORD(seg+3);
			*height = LDB_WORD(seg+1);
			res = 0;
			break;
		}
		seg += 2;
	}

	return res;
}
/*******************************************************************************
* Function Name  : hal_mjpHeaderParse
* Description    : hal layer.mjpeg decode parse jpeg file
* Input          : u8 * p_jpeg_header : buffer
* Output         : None
* Return         : None
*******************************************************************************/
int hal_mjpHeaderParse(u8 * p_jpeg_header)
{
	u8 *seg, b;
	u16 marker;
	u32 ofs,i, len;
	int res=0;
	//u8 *temp_buf = mjp_decode_buf;


//	u8 * temp_buf = tempbuf_malloc(1024);
	mjpDecCtrl.dqt[0] 		= &mjp_decode_buf[0];
	mjpDecCtrl.dqt[1] 		= &mjp_decode_buf[0x40];
	mjpDecCtrl.dqt[2] 		= &mjp_decode_buf[80];
	mjpDecCtrl.dqt[3] 		= &mjp_decode_buf[0xc0];

	mjpDecCtrl.minicode[0] 	= (u16 *)&mjp_decode_buf[0x100];
	mjpDecCtrl.minicode[1] 	= (u16 *)&mjp_decode_buf[0x120];
	mjpDecCtrl.minicode[2] 	= (u16 *)&mjp_decode_buf[0x140];
	mjpDecCtrl.minicode[3] 	= (u16 *)&mjp_decode_buf[0x160];

	mjpDecCtrl.valptr[0] 	= &mjp_decode_buf[0x180];
	mjpDecCtrl.valptr[1] 	= &mjp_decode_buf[0x190];
	mjpDecCtrl.valptr[2] 	= &mjp_decode_buf[0x1a0];
	mjpDecCtrl.valptr[3] 	= &mjp_decode_buf[0x1b0];

	mjpDecCtrl.dht[0] 		= &mjp_decode_buf[0x1c0];
	mjpDecCtrl.dht[1] 		= &mjp_decode_buf[0x1cc];
	mjpDecCtrl.dht[2] 		= &mjp_decode_buf[0x1d8];
	mjpDecCtrl.dht[3] 		= &mjp_decode_buf[0x27a];

	seg = p_jpeg_header;
	if (LDB_WORD(seg) != 0xFFD8)
	{
		debgbuf(p_jpeg_header-8,32);
		res = -1;
		goto out;
	}
	ofs = 2;

	for (;;)
	{
		hal_wdtClear();
		/* Get a JPEG marker */
		seg = p_jpeg_header + ofs;
		marker = LDB_WORD(seg);		/* Marker */
		if(marker == 0xffff){
			ofs += 1;
			continue;
		}
		len = LDB_WORD(seg + 2);	/* Length field */
		if (len <= 2 || (marker >> 8) != 0xFF)
		{
			res = -2;
			goto out;
		}
		len -= 2;		/* Content size excluding length field */
		ofs += 4 + len;	/* Number of bytes loaded */
		seg += 4;

		switch (marker & 0xFF)
		{
			case 0xC0:	/* SOF0 (baseline JPEG) */
				mjpDecCtrl.width = LDB_WORD(seg+3);		/* Image width in unit of pixel */
				mjpDecCtrl.height = LDB_WORD(seg+1);		/* Image height in unit of pixel */
				if (seg[5] != 3) //3: YCrCb
				{
					res = -3;
					goto out;
				}

				/* Check three image components */
				for (i = 0; i < 3; i++)
				{
					b = seg[7 + 3 * i];							/* Get sampling factor */
					if (!i)
					{	/* Y component */
						if (/*b != 0x11 &&*/ b != 0x22 && b != 0x21)/* Check sampling factor */
						{
							res = -4;
							goto out;
						}
						mjpDecCtrl.msx = b >> 4;			/* Size of MCU [blocks] */
						mjpDecCtrl.msy = b & 15;
					}
					else
					{	/* Cb/Cr component */
						if (b != 0x11)
						{
							res = -4;
							goto out;
						}
					}
					b = seg[8 + 3 * i];							/* Get dequantizer table ID for this component */
					if (b > 3)
					{
						res = -4;
						goto out;
					}
					mjpDecCtrl.qtid[i] = b;
				}
				break;

			case 0xDD:	/* DRI */
				/* Get restart interval (MCUs) */
				mjpDecCtrl.nrst = LDB_WORD(seg);
				break;

			case 0xC4:	/* DHT */
				/* Create huffman tables */
				if(hal_mjpHuffmanTable(seg, len) < 0)
				{
					res = -10;
					goto out;
				}
				break;

			case 0xDB:	/* DQT */
			/* Create de-quantizer tables */
				if (hal_mjpQTTable(seg, len) <0)
				{
					res = -7;
					goto out;
				}
				break;

			case 0xDA:	/* SOS */
				if (!mjpDecCtrl.width || !mjpDecCtrl.height)
				{
					res = -8;
					goto out;
				}

				if (*seg++ != 3)
				{
					res = -9;
					goto out;
				}

				/* Check if all tables corresponding to each components have been loaded */
				for (i = 0; i < 3; i++)
				{
					mjpDecCtrl.hfid[seg[0] - 1][0] = seg[1] & 0xf; /* AC */
					mjpDecCtrl.hfid[seg[0] - 1][1] = seg[1] >> 4;  /* DC */
					seg += 2;
				}

				mjpDecCtrl.bsptr = seg + len - 7;
				mjpDecCtrl.bs_ofs = mjpDecCtrl.bsptr - p_jpeg_header;
				for(i=0;i<2;i++)
				{
					mjpDecCtrl.qt_ofs[i] = (u16)((u32)mjpDecCtrl.qt_addr[i] - (u32)p_jpeg_header);
				}

			    hx330x_mjpB_DecodeInitTable((u32)mjp_decode_buf);

				goto out;		/* Initialization succeeded. Ready to decompress the JPEG image. */

			default:	/* Unknown segment (comment, exif or etc..) */
				break;
		}
	}

out:
	if(res)
		deg_Printf("mjpHeaderParse fail:%d\n",res);
//	tempbuf_free(temp_buf);
	return res;
}
/*******************************************************************************
* Function Name  : hal_mjpDecodeInit
* Description    : hal layer.mjpeg decode initial
* Input          : u8 * p_jpeg_start : buffer
				   u8 * p_out_y : out ybuffer
				   u8 * p_out_uv: out uvbuffer
				   u16 dst_width: out size width
				   u16 dst_height:out size height
* Output         : None
* Return         : None
*******************************************************************************/
static int hal_mjpDecodeInit(u8 * p_jpeg_start,u8 * p_out_y,u8 * p_out_uv,
                               u16 dst_width,u16 dst_height,u32 stride)
{
	static const u8 yuv_format_map[] = {0,0,2,1,0};
	int ret;
	u32 comp_table_id;

	memset((u8*)&mjpDecCtrl,0,sizeof(MJPEG_DEC_OP_T));

	ret = hal_mjpHeaderParse(p_jpeg_start);
	if(ret<0)
	{
		deg_Printf("HAL: <MJPEG>[ERROR] jpeg header parse fail.%d\n",ret);
		return ret;
	}
    mjpDecCtrl.comp_table_id =
    comp_table_id = (mjpDecCtrl.hfid[2][0]<<29)|//AC entropy coding table for comp 3
                    (mjpDecCtrl.hfid[2][1]<<28)|//DC entropy coding table for comp 3
                    (mjpDecCtrl.qtid[2]<<26)|//quant talbe for comp 3
                    (1<<23)|//vertical sampling for comp 3
                    (1<<20)|//horizontal sampling for comp 3
                    (mjpDecCtrl.hfid[1][0]<<19)|//AC entropy coding table for comp 2
                    (mjpDecCtrl.hfid[1][1]<<18)|//DC entropy coding table for comp 2
                    (mjpDecCtrl.qtid[1]<<16)|//quant talbe for comp 2
                    (1<<13)|//vertical sampling for comp 2
                    (1<<10)|//horizontal sampling for comp 2
                    (mjpDecCtrl.hfid[0][0]<<9)| //AC entropy coding table for comp 1
                    (mjpDecCtrl.hfid[0][1]<<8)| //DC entropy coding table for comp 1
                    (mjpDecCtrl.qtid[0]<<6)| //quant talbe for comp 1
                    (mjpDecCtrl.msy<<3)| //vertical sampling for comp 1
                    (mjpDecCtrl.msx<<0); //horizontal sampling for comp 1

    mjpDecCtrl.yuvfmt = yuv_format_map[mjpDecCtrl.msy+mjpDecCtrl.msx];

    hx330x_mjpB_DecodeInit(mjpDecCtrl.yuvfmt);
	hx330x_mjpB_DecodeDCTimeSet(1,mjpDecCtrl.height);
    hx330x_mjpB_DecodeSetSize(mjpDecCtrl.width,mjpDecCtrl.height,dst_width,dst_height,stride);
    hx330x_mjpB_DecodeOutputSet((u32)p_out_y,(u32)p_out_uv);
	hx330x_mjpB_DecodeInputSet((u32)mjpDecCtrl.bsptr,0);
	hx330x_mjpB_DecodeDriSet(mjpDecCtrl.nrst);
	hx330x_mjpB_DecodeCompressSet(comp_table_id);
	hx330x_mjpB_DecodeISRRegister(hal_lcd_decwin_done);
	return 0;
}
/*******************************************************************************
* Function Name  : hal_mjpDecodeIsYUV422
* Description    : hal_mjpDecodeIsYUV422
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u8 hal_mjpDecodeIsYUV422(void)
{
	return (u8)(mjpDecCtrl.yuvfmt);
}
/*******************************************************************************
* Function Name  : hal_mjpDecodeGetResolution
* Description    : hal layer.mjpeg decode get jpeg size
* Input          :  u16 width: out size width
                      u16 height:out size height
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpDecodeGetResolution(u16 *width,u16 *height)
{
	if(width)
		*width = mjpDecCtrl.width;
	if(height)
		*height = mjpDecCtrl.height;
}
/*******************************************************************************
* Function Name  : hal_mjpDecodeSetResolution
* Description    : hal layer.mjpeg decode Set jpeg size
* Input          : u16 width: out size width
				   u16 height:out size height
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpDecodeSetResolution(u16 width,u16 height)
{
	mjpDecCtrl.width 	= width;
	mjpDecCtrl.height 	= height;
}
/*******************************************************************************
* Function Name  : hal_mjpDecodePicture
* Description    : hal layer.mjpeg decode one picture
* Input          : u8 * p_jpeg_start : buffer
				   u8 * p_out_y : out ybuffer
				   u8 * p_out_uv: out uvbuffer
				   u16 dst_width: out size width
				   u16 dst_height:out size height
* Output         : None
* Return         : true:success
*******************************************************************************/
bool hal_mjpDecodePicture(u8 * p_jpeg_start,u8 * p_out_y,u8 * p_out_uv,u16 dst_width,u16 dst_height)
{
    hal_mjpDecodeReset();
	if(hal_mjpDecodeInit(p_jpeg_start,p_out_y,p_out_uv,dst_width,dst_height,0)<0)
		return false;
    hx330x_mjpB_DecodeEnable(1);
	return true;
}
/*******************************************************************************
* Function Name  : hal_mjpegDecodePicture_noisr
* Description    : hal layer.mjpeg decode one picture without isr
* Input          : u8 * p_jpeg_start : buffer
				   u8 * p_out_y : out ybuffer
				   u8 * p_out_uv: out uvbuffer
				   u16 dst_width: out size width
				   u16 dst_height:out size height
* Output         : None
* Return         : true:success
*******************************************************************************/
bool hal_mjpegDecodePicture_noisr(u8 * p_jpeg_start,u8 * p_out_y,u8 * p_out_uv,u16 dst_width,u16 dst_height)
{
    hal_mjpDecodeReset();
	if(hal_mjpDecodeInit(p_jpeg_start,p_out_y,p_out_uv,dst_width,dst_height,0)<0)
		return false;
    hx330x_mjpB_DecodeKick();
	return true;
}
/*******************************************************************************
* Function Name  : hal_mjpegDecodePicture_packet
* Description    : hal layer.mjpeg decode one picture packet by packet without isr
* Input          : u8 * p_jpeg_start : buffer
				   u8 * p_out_y : out ybuffer
				   u8 * p_out_uv: out uvbuffer
				   u16 dst_width: out size width
				   u16 dst_height:out size height
* Output         : None
* Return         : true:success
*******************************************************************************/
bool hal_mjpegDecodePicture_packet(u8 * p_jpeg_start,u8*pkg_end, u8 * p_out_y,u8 * p_out_uv,u16 dst_width,u16 dst_height)
{
    hal_mjpDecodeReset();
	if(hal_mjpDecodeInit(p_jpeg_start,p_out_y,p_out_uv,dst_width,dst_height,0)<0)
		return false;
	hx330x_mjpB_DecodeInputSet((u32)mjpDecCtrl.bsptr,(u32)pkg_end|(1<<29));
    hx330x_mjpB_DecodeKick();
	return true;
}
/*******************************************************************************
* Function Name  : hal_mjpDecodeBusyCheck
* Description    : hal layer.mjpeg decode get status
* Input          :
* Output         : None
* Return         : true : busy
*******************************************************************************/
bool hal_mjpDecodeBusyCheck(void)
{
    return hx330x_mjpB_DecodeBusyCheck();
}
/*******************************************************************************
* Function Name  : hal_mjpDecodeErrorCheck
* Description    : hal layer.mjpeg decode get error
* Input          :
* Output         : None
* Return         : 0 : no error
*******************************************************************************/
u32 hal_mjpDecodeErrorCheck(void)
{
    return hx330x_mjpB_DecodeCheck();
}
/*******************************************************************************
* Function Name  : hal_mjpDecodeStop
* Description    : hal layer.mjpeg decode stop
* Input          :
* Output         : None
* Return         :
*******************************************************************************/
void hal_mjpDecodeStop(void)
{
    hx330x_mjpB_DecodeStop();
}
/*******************************************************************************
* Function Name  : hal_mjpDecodeReset
* Description    : hal layer.mjpeg decoder reset
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpDecodeReset(void)
{
    hx330x_mjpB_reset();
    hx330x_lcdWinReset(WIN_SRC_B);
	//hal_lcd_encwin_done();
}
/*******************************************************************************
* Function Name  : hal_mjpegDecodeParse
* Description    : hal layer.mjpeg decode stop
* Input          :  u8 * p_jpeg_start : buffer
                       u16 dst_width : out width
                       u16 dst_height: out height
* Output         : None
* Return         : 0 : success
                      <0 : fail
*******************************************************************************/
int hal_mjpDecodeParse(u8 * p_jpeg_start,u16 dst_width,u16 dst_height)
{
	return hal_mjpDecodeInit(p_jpeg_start,NULL,NULL,dst_width,dst_height,0);
}
/*******************************************************************************
* Function Name  : hal_mjpegDecodeOneFrame
* Description    : hal layer.mjpeg decode one frame,video file using
* Input          : u8 * p_jpeg_start : buffer
				   u8 * p_out_y : out ybuffer
				   u8 * p_out_uv:out uvbuffer
* Output         : None
* Return         : 0 : success
                      <0 : fail
*******************************************************************************/
void hal_mjpDecodeOneFrame(u8 * p_jpeg_start,u8 * p_out_y,u8 * p_out_uv)
{
	mjpDecCtrl.bsptr = p_jpeg_start + mjpDecCtrl.bs_ofs;
	hx330x_mjpB_yuvfmt_set(mjpDecCtrl.yuvfmt);
    hx330x_mjpB_DecodeOutputSet((u32)p_out_y,(u32)p_out_uv);
    hx330x_mjpB_DecodeInputSet((u32)mjpDecCtrl.bsptr,0);
	hx330x_mjpB_DecodeEnable(1);
}
/*******************************************************************************
* Function Name  : hal_mjpegDecodeOneFrame_ext
* Description    : hal layer.mjpeg decode one frame,
* Input          : u8 * p_jpeg_start : buffer
				   u8 * p_out_y : out ybuffer
				   u8 * p_out_uv:out uvbuffer
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpDecodeOneFrame_Ext(u8 * p_jpeg_start,u8 * p_out_y,u8 * p_out_uv)
{
	u8 *qt[3],i;
	for(i=0;i<2;i++)
	{
		qt[i] = p_jpeg_start+mjpDecCtrl.qt_ofs[i];
	}
	mjpDecCtrl.bsptr = p_jpeg_start + mjpDecCtrl.bs_ofs;
	hx330x_mjpB_yuvfmt_set(mjpDecCtrl.yuvfmt);
    hx330x_mjpB_DecodeQDTCfg(qt);
    hx330x_mjpB_DecodeOutputSet((u32)p_out_y,(u32)p_out_uv);
    hx330x_mjpB_DecodeInputSet((u32)mjpDecCtrl.bsptr,0);
	hx330x_mjpB_DecodeEnable(1);
}
/*******************************************************************************
* Function Name  : hal_mjpegDecodeRestart_Ext
* Description    : hal layer.mjpeg decode one frame,use last configration.
* Input          :  u8 * p_jpeg_start : buffer
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpDecodeRestart_Ext(u8 * p_jpeg_start)
{
	u8 *qt[3],i;
	for(i=0;i<2;i++)
	{
		qt[i] = p_jpeg_start+mjpDecCtrl.qt_ofs[i];
	}
	mjpDecCtrl.bsptr = p_jpeg_start + mjpDecCtrl.bs_ofs;

    hx330x_mjpB_DecodeQDTCfg(qt);
    hx330x_mjpB_DecodeInputSet((u32)mjpDecCtrl.bsptr,0);
	hx330x_mjpB_DecodeEnable(1);
}
/*******************************************************************************
* Function Name  : hal_mjpDecodeOneFrame_Fast
* Description    : quickly mjpeg decoder initial and kick decoder run
*                  NEED call hal_mjpDecodeInit before call this function
* Input          : u8 * p_jpeg_start : buffer
                   u8 * p_out_y : out ybuffer
                   u8 * p_out_uv: out uvbuffer
                   u16 dst_width: out size width
                   u16 dst_height:out size height
                   u16 stride :
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpDecodeOneFrame_Fast(u8 * p_jpeg_start, u8* p_jpeg_end,
                                  u8 * p_out_y,u8 * p_out_uv,
                                  u16 dst_width,u16 dst_height,
                                  u16 stride)
{
	u8 *qt[3],i;
	for(i=0;i<2;i++)
	{
		qt[i] = p_jpeg_start+mjpDecCtrl.qt_ofs[i];
	}
	mjpDecCtrl.bsptr = p_jpeg_start + mjpDecCtrl.bs_ofs;


    hx330x_mjpB_DecodeInit(mjpDecCtrl.yuvfmt);
	hx330x_mjpB_DecodeDCTimeSet(1,mjpDecCtrl.height);
    hx330x_mjpB_DecodeSetSize(mjpDecCtrl.width,mjpDecCtrl.height,dst_width,dst_height,stride);
	hx330x_mjpB_DecodeDriSet(mjpDecCtrl.nrst);
    hx330x_mjpB_DecodeCompressSet(mjpDecCtrl.comp_table_id);
    hx330x_mjpB_DecodeQDTCfg(qt);
    hx330x_mjpB_DecodeOutputSet((u32)p_out_y,(u32)p_out_uv);
    hx330x_mjpB_DecodeInputSet((u32)mjpDecCtrl.bsptr,(u32)p_jpeg_end);
	hx330x_mjpB_DecodeEnable(1);
}
/*******************************************************************************
* Function Name  : hal_mjpDecodeMiniSize
* Description    : hal layer.mjpeg decode mini size caculation
* Input          :
* Output         : None
* Return         : < 0 fail
*******************************************************************************/
int hal_mjpDecodeMiniSize(u8 * p_jpeg_start,u16 *min_width,u16 *min_height)
{
	static u16 minsize[][2] = {M(640,480),M(1280,720),M(1920,1080),M(2560,1440),M(4000,3000)};
	u16 src_w;
	u16 src_h;
	if(hal_mjpDecodeSize(p_jpeg_start,&src_w,&src_h)<0)
	{
		debg("HAL : <MJPEG>[ERROR] jpeg head parse err\n");
		return -1;
	}

	u32 check = (src_w<<16)|(src_h);
	switch(check)
	{
		case (640 << 16)|480   : *min_width = minsize[0][0];*min_height = minsize[0][1];break;
		case (1280 << 16)|720  : *min_width = minsize[1][0];*min_height = minsize[1][1];break;
		case (1920 << 16)|1080 : *min_width = minsize[2][0];*min_height = minsize[2][1];break;
		case (2560 << 16)|1440 : *min_width = minsize[3][0];*min_height = minsize[3][1];break;
		case (4000 << 16)|3000 : *min_width = minsize[4][0];*min_height = minsize[4][1];break;
		default:
			*min_width  = (ROUND_UP(src_w,31) + 1) & ~1;
			*min_height = (ROUND_UP(src_h,31) + 1) & ~1;
			break;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : hal_mjpDecodeODma1Cfg
* Description    : config output dma1(no block-scaler),call this function before init jpeg-decoder
*                  hal_mjpDecodeStop will disable DMA1
* Input          : u32 y_addr
*                  u32 uv_addr
*                  bool dma_en
* Output         : none
* Return         : none
*******************************************************************************/
void hal_mjpDecodeODma1Cfg(u32 y_addr,u32 uv_addr,bool dma_en)
{
    hx330x_mjpB_DecodeODma1Cfg(y_addr,uv_addr,dma_en);
}
/*******************************************************************************
* Function Name  : hal_BackRecDecodeStatusCheck
* Description    : check back decode status per second, for back usensor online check
* Input          : NONE
* Output         : none
* Return         : TRUE: USENSOR ONLINE AND RECIEVE DATA SUCESS
*******************************************************************************/
bool hal_BackRecDecodeStatusCheck(void)
{
	static u32 ftmout = 0;
	if(hx330x_mjpB_DecodeBusyCheck())
	{
		//debg("XSFR_JPEG1_FLAG:%x\n",XSFR_JPEG1_FLAG);
		ftmout++;
		if(ftmout>6)//6frame time out
		{
			ftmout = 0;
			hal_mjpDecodeReset();
			hx330x_mjpB_DecodeStop();

			//deg_Printf("hal_mjpegDecodeStatusCheck: <time out>\n");

		}
		return false;
	}
	else
	{
		ftmout = 0;
		return true;
	}
}
