/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  MULTIMEDIA_API_H
    #define  MULTIMEDIA_API_H
	
//#include "../hal/inc/hal.h"



#define  AVI_TYPE_CFG       				MEDIA_AVI_ODML  // AVI FILE TYPE .MEDIA_AVI_ODML/MEDIA_AVI_STD
#define  AVI_FILE_PREMALLOC					0				//0: not pre malloc file, 1: pre malloc file
#define  AVI_IFRAME_REAL					0				//1: insert real frame, 0: insert only index
//-------------debug config----------------------------------------------------------------------
#define  DBG_VIDEO_REC_EN					(1&HAL_CFG_EN_DBG)   // for video debug print  record
#define  DBG_VIDEO_PLY_EN					(1&HAL_CFG_EN_DBG)   // for video debug print  playback
#define  DBG_AUIDO_REC_EN					(1&HAL_CFG_EN_DBG)   // for audio debug print  record
#define  DBG_AUDIO_PLY_EN					(1&HAL_CFG_EN_DBG)   // for audio debug print  playback


//---------------module enable config------------------------------------------------------------
#define  MEDIA_IMAGE_ENCODE_EN				1
#define  MEDIA_IMAGE_DECODE_EN				1
#define  MEDIA_AUDIO_ENCODE_EN				1
#define  MEDIA_AUDIO_DECODE_EN				1
#define  MEDIA_VIDEO_ENCODE_EN				1
#define  MEDIA_VIDEO_DECODE_EN				1

//---------------func  config------------------------------------------------------------
//time sync
#define  MEDIA_CFG_SYNC_TIMER_EN       		1   // video record & play sync by timer
#define  MEDIA_CFG_TIMER                	HAL_CFG_SYNC_TIMER
//audio
#define  MEDIA_CFG_AUDSRATE       			AUADC_16K
#define  MEDIA_CFG_MIC_VOLUME				(MIC_VOLUME_MAX/2)
#define  MEDIA_CFG_AUDSSIZE					(((AUADC_16K*2/PCM_REC_SYNC)+0x1ff)&~0x1ff)

#define  AUDIO_CFG_BUFFER_SIZE          	(MEDIA_CFG_AUDSSIZE*2)   // audio play buffer size
#define  AUDIO_CFG_BUFFER_NUM             	3// HAL_CFG_PCM_BUFFER_NUM       // audio play buffer number

//audio rec
#define  AUDIO_CFG_REC_LOOP					1	//audio loop rec enable
#define  AUIDO_CFG_REC_TIME					30*60L	//audio rec time(sec)


//video
#define  VIDEO_CFG_JUMP_SAMEFRAME      		1    // jump of the same frame of vids
#define  VIDEO_CFG_BUFFER_NUM             	HAL_CFG_LCD_BUFFER_NUM       // video play buffer number

#define  JPEG_CFG_CACHE_NUM                	2
#define  JPEG_CFG_CACHE_SIZE              	(300*1024L)  // 200k//(HAL_CFG_MJPEG_SIZE/2) // 0x80000   // 1-m,for jpeg file cache

#define  AVI_CFG_IDX1_BUFF_SIZE           	(32768L) // 8192  // avi idx1 cache size 

#define  AUDIO_CACHE_SIZE           		(32768L) // 8192  // avi idx1 cache size 



enum
{
	VIDEO_CH = 0,
	VIDEO_CH_A = 0,
	VIDEO_CH_B,
	VIDEO_CH_MAX
};



enum// player state
{
	MEDIA_STAT_STOP = 0,
	MEDIA_STAT_START,
	MEDIA_STAT_READY,
	MEDIA_STAT_PLAY = 1,
	MEDIA_STAT_PAUSE,	
	//MEDIA_STAT_FIRST_PAUSE,
	MEDIA_STAT_FILE_FULL,
	MEDIA_STAT_ERROR
};
enum //sync_stat
{
	MEDIA_SYNC_INIT		= 0,
	MEDIA_SYNC_PRIOR	= (1 << 0),
	MEDIA_SYNC_RESTART 	= (1 << 1),
	MEDIA_SYNC_WRITING	= (1 << 2),
	MEDIA_SYNC_JUNK		= (1 << 3),
	MEDIA_SYNC_FULL		= (1 << 4),
	MEDIA_SYNC_PRERESTART 	= (1 << 5),
	
};

enum // video encode type
{
	VIDEO_CODE_MJPEG=0,
	VIDEO_CODE_H264,

	VIDEO_CODE_ERROR
};

enum  // command
{
	CMD_COM_ERROR=0,
	CMD_COM_CHECK,
	CMD_COM_LOOPTIME,
	CMD_COM_LOOPREC,
	CMD_COM_MDTIME,
	CMD_COM_TIMESTRAMP,
	CMD_COM_AUDIOEN,
	CMD_COM_RESOLUTIONN,
	CMD_COM_QUALITY,
	CMD_COM_FPS,
	CMD_COM_DELAY,
	
	
	CMD_VIDEO_RECORD_INIT,
	CMD_VIDEO_RECORD_UNINIT,
	CMD_VIDEO_RECORD_START,
	CMD_VIDEO_RECORD_STOP,
	CMD_VIDEO_RECORD_PAUSE,
	CMD_VIDEO_RECORD_RESUME,
		
	CMD_AUDIO_RECORD_INIT,
	CMD_AUDIO_RECORD_UNINIT,
	CMD_AUDIO_RECORD_START,
	CMD_AUDIO_RECORD_STOP,
	CMD_AUDIO_RECORD_PAUSE,
	CMD_AUDIO_RECORD_RESUME,

	CMD_PHOTO_RECORD_INIT,
	CMD_PHOTO_RECORD_UNINIT,
	CMD_PHOTO_RECORD_START,
	CMD_PHOTO_RECORD_STOP,

	CMD_VIDEO_PLAYBACK_INIT,
	CMD_VIDEO_PLAYBACK_UNINIT,
	CMD_VIDEO_PLAYBACK_START,
	CMD_VIDEO_PLAYBACK_STOP,
	CMD_VIDEO_PLAYBACK_PAUSE,
	CMD_VIDEO_PLAYBACK_RESUME,
	
	CMD_AUDIO_PLAYBACK_INIT,
	CMD_AUDIO_PLAYBACK_UNINIT,
	CMD_AUDIO_PLAYBACK_START,
	CMD_AUDIO_PLAYBACK_STOP,
	CMD_AUDIO_PLAYBACK_PAUSE,
	CMD_AUDIO_PLAYBACK_RESUME,


};
enum // video status
{
	STATUS_OK 		= 0,
	STATUS_FAIL 	= -1,
};

typedef enum{
	VIDEOPLAY_NOT_END 	= 0,
	VIDEOPLAY_IDX1_END	= (1<<0),
	VIDEOPLAY_VIDS_END	= (1<<1),
	VIDEOPLAY_AUDS_END	= (1<<2),
	VIDEOPLAY_ALL_END	= (7<<0),
}VIDEO_PLAY_END_T;
 // 0 :stop,1:start,2:dac auto stop,3:decode auto stop,4:video first frame
typedef enum{
	PLAY_DACSTA_STOP 		= 0x00,
	PLAY_DACSTA_START		= 0x01,
	PLAY_DACSTA_AUTOSTOP	= 0x02,
	PLAY_DACSTA_WAITSTOP	= 0x03,
	PLAY_DACSTA_VFIRST		= 0x10,  //video first frame, not play dac
	PLAY_DACSTA_MASK		= 0x0f,
	PLAY_DACSTA_SPEED		= 0x20,  //video fast speed mode
}VIDEO_PLAY_DACSTA_T;

#define MEDIA_NULL_CHECK(p,r)   if(p == NULL)return r


#include "Library/api_multimedia.h"

#include "audio/audio_record.h"
#include "audio/audio_playback.h"

#include "image/image_encode.h"
#include "image/image_decode.h"


#include "video/video_record.h"
#include "video/video_playback.h"	

#endif