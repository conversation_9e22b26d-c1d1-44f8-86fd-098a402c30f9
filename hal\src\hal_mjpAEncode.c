/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../inc/hal.h"


typedef struct MJPEG_ENC_OP_S
{
	u8 enc_mode;
	u8 timeStamp;
	u8 manual_pkg_start;
	u8 manual_kick_noisr;
	u8 lens_add_en;
	u8 encode_lcd_mode;
//#if HAL_CFG_MJPEG_QULITY_AUTO>0	
	s8 qulity;
    u8 q_cnt;
	u8 q_auto;
	s8 q_dir;
	u32 q_csize;
//#endif
	u16 csi_width;
	u16 csi_height;
	u16 lcd_width;
	u16 lcd_height;
    u16 mjpeg_width;
	u16 mjpeg_height;
	u16 line_width;
	u16 line_height;
	u32 jfcnt;
    u32 ybuffer;
	u32 uvbuffer;
	u32 mjpbuf;
	u32 mjpsize;

	u32 curBuffer;
	u32 curLen;
	u32 curLen_temp;
	u32 mjp_flg;

	MAGIC_SRC_WIN_T magic_win;
	Stream_Head_T vids;
	Stream_Node_T mjpegNode[MJPEG_ITEM_NUM];
}MJPA_ENC_OP_T;


ALIGNED(4) static MJPA_ENC_OP_T  mjpAEncCtrl;


#if HAL_CFG_MJPEG_QULITY_AUTO>0
ALIGNED(4) const u8 mjpegQualityTable[MJPEG_Q_MAX] = {JPEG_Q_27,JPEG_Q_28,JPEG_Q_31,JPEG_Q_33,JPEG_Q_36,JPEG_Q_40,JPEG_Q_42,JPEG_Q_50,JPEG_Q_62,JPEG_Q_75,JPEG_Q_81};
#endif

/*******************************************************************************
* Function Name  : hal_mjpA_Sizecalculate
* Description    : hal layer .mjpeg output jpeg size
* Input          : u8 quailty : quality
				   u16 width : output width
				   u16 height: output height
* Output         : None
* Return         : int : size
*******************************************************************************/
u32 hal_mjpA_Sizecalculate(u8 quailty,u16 width,u16 height)
{
#if HAL_CFG_MJPEG_QULITY_AUTO > 0
	if(height <= 720)
		return HAL_CFG_MJPEG_720_SIZE_MAX;
	else
		return HAL_CFG_MJPEG_1080_SIZE_MAX;
#else
	INT32U quli;
	switch(quailty)
	{
		case JPEG_Q_27 : quli = 40;break;
		case JPEG_Q_28 : quli = 45;break;
		case JPEG_Q_31 : quli = 50;break;
		case JPEG_Q_33 : quli = 65;break;
		case JPEG_Q_36 : quli = 70;break;
		case JPEG_Q_AUTO :
		case JPEG_Q_40 : quli = 80;break;
		case JPEG_Q_42 : quli = 80;break;
		case JPEG_Q_50 : quli = 90;break;
		case JPEG_Q_62 : quli = 100;break;
		case JPEG_Q_75 : quli = 110;break;
		case JPEG_Q_81 : quli = 120;break;
		default : quli = 100;break;
	}
//    quli+=20;
	return (((width*height/100)*quli)/10); // byte
#endif
}
/*******************************************************************************
* Function Name  : hal_mjpA_QualityAjust
* Description    : hal layer .mjpeg quality ajust
* Input          : u32 len : current mjpeg size
* Output         : None
* Return         : NONE
*******************************************************************************/
static void hal_mjpA_QualityAjust(u32 len)
{
#if HAL_CFG_MJPEG_QULITY_AUTO>0
	if(mjpAEncCtrl.q_auto!=JPEG_Q_AUTO)
		return ;

	if(mjpAEncCtrl.q_dir == 0)  //dir： (0) - default, (1) - Q decrease, (-1) - Q increase
	{
		int mjpeg_throld = mjpAEncCtrl.q_csize*15/100;  // 40%
		if(len > (mjpAEncCtrl.q_csize+mjpeg_throld))
		{
			mjpAEncCtrl.q_dir = 1;
			mjpAEncCtrl.q_cnt = 0;
			//deg_Printf("HAL : [MJPEG]<INFO> size auto check %x,%x\n",len,mjpAEncCtrl.q_csize+mjpeg_throld);
		}
		else if(len < (mjpAEncCtrl.q_csize-mjpeg_throld))
		{
			mjpAEncCtrl.q_dir = -1;
			mjpAEncCtrl.q_cnt = 0;
			//deg_Printf("HAL : [MJPEG]<INFO> size auto check %x,%x\n",len,mjpAEncCtrl.q_csize-mjpeg_throld);
		}
	}
	else if(mjpAEncCtrl.q_dir > 0)
	{
		if(len < mjpAEncCtrl.q_csize)
		{
			mjpAEncCtrl.q_dir = 0;
			mjpAEncCtrl.q_cnt = 0;
		}
		else
			mjpAEncCtrl.q_cnt++;
	}
	else if(mjpAEncCtrl.q_dir < 0)
	{
		if(len > mjpAEncCtrl.q_csize)
		{
			mjpAEncCtrl.q_dir = 0;
			mjpAEncCtrl.q_cnt = 0;
		}
		else
			mjpAEncCtrl.q_cnt++;
	}

	if(mjpAEncCtrl.q_cnt>=10)
	{
		mjpAEncCtrl.qulity-= mjpAEncCtrl.q_dir;
		if(mjpAEncCtrl.qulity < 0)
			mjpAEncCtrl.qulity = 0;
		else if(mjpAEncCtrl.qulity >= MJPEG_Q_MAX)
			mjpAEncCtrl.qulity = MJPEG_Q_MAX-1;


		hx330x_mjpA_EncodeQuilitySet(mjpegQualityTable[mjpAEncCtrl.qulity]);

		//deg_Printf("HAL : [MJPEG]<INFO> quality auto check[%d] ->%d\n",mjpAEncCtrl.q_dir,mjpAEncCtrl.qulity);
		mjpAEncCtrl.q_dir = 0;
		mjpAEncCtrl.q_cnt = 0;
	}
#endif
}
/*******************************************************************************
* Function Name  : hal_mjpA_QualityCheck
* Description    : hal layer .mjpeg quality check
* Input          : u8 quality : user set quality
* Output         : None
* Return         : u8
*******************************************************************************/
static u8 hal_mjpA_QualityCheck(u8 quality)
{
#if HAL_CFG_MJPEG_QULITY_AUTO>0
    mjpAEncCtrl.q_auto = quality;

    if(quality == JPEG_Q_AUTO)
		quality = JPEG_Q_40;
	int i;
	for(i = 0;i < MJPEG_Q_MAX;i++)
	{
		if(mjpegQualityTable[i] == quality)
			break;
	}
	if(i >= MJPEG_Q_MAX)
		i = MJPEG_Q_MAX-1;
	mjpAEncCtrl.qulity = i;
	mjpAEncCtrl.q_cnt = 0;
	mjpAEncCtrl.q_dir = 0;
    mjpAEncCtrl.q_csize = hal_mjpA_Sizecalculate(quality,mjpAEncCtrl.mjpeg_width,mjpAEncCtrl.mjpeg_height);
	mjpAEncCtrl.q_csize = mjpAEncCtrl.q_csize*55/100;
#endif
    if(quality == JPEG_Q_AUTO)
		quality = JPEG_Q_40;
	deg_Printf("video record Q = %x\n",quality);
	return quality;
}

/*******************************************************************************
* Function Name  : hal_mjpA_EncState
* Description    : hal layer .mjpA encode state check
* Input          : u8 flag:
* Output         : None
* Return         : bool true : encode success
*******************************************************************************/
bool hal_mjpA_EncState(int flag)
{
	if(flag & BIT(MJPEG_IRQ_OUTPAUSE)){//ODMAPAUSE
		if(!(flag & BIT(MJPEG_IRQ_FRAMEEND))){
			//deg_Printf("mallo j frame size err\n");
			return false;
		}
	}
	if((flag & BIT(MJPEG_IRQ_OUTERR))){
		return false;
	}
	return true;
}

/*******************************************************************************
* Function Name  : hal_jA_fcnt_mnt
* Description    : hal layer .mjpA encode frame debg
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
u32 hal_jA_fcnt_mnt(void)
{
	u32 temp = mjpAEncCtrl.jfcnt;
	deg_Printf("JA_fcnt:%d\n",mjpAEncCtrl.jfcnt);
	mjpAEncCtrl.jfcnt = 0;
	return temp;
}
/*******************************************************************************
* Function Name  : hal_mjpAEncodeVideoKickManual
* Description    : hal layer .mjpeg callback function for mjpeg irq encode
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_mjpAEncodeVideoKickManual(void)
{
 	//只有等MJP ENCODE DOWN时才重新CSI DMA EN（当csi 比mjp快时，会冲掉正在encode 的mjp linebuf），不过会导致encode帧率下降
	if(mjpAEncCtrl.curBuffer)
		return;
	mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
	if(mjpAEncCtrl.curBuffer)
	{
		//if(mjpAEncCtrl.magic_win.config_en)
		//{
		//	magic_mirror_csi_add((u8*)mjpAEncCtrl.ybuffer);
		//}
		//else
		{
			kid_frame_csi_add((u8*)mjpAEncCtrl.ybuffer);	//always use ch 1
		}		
		hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curBuffer+mjpAEncCtrl.curLen);
		hx330x_mjpA_Encode_manual_on();
		hx330x_intEnable(IRQ_JPGA,1); // enable jpegirq
		//debg("M");
	}
}
/*******************************************************************************
* Function Name  : hal_mjpAEncodeVideoKickManual
* Description    : hal layer .mjpeg callback function for mjpeg irq encode
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_mjpAEncodePhotoKickManual(void)
{
 	//只有等MJP ENCODE DOWN时才重新CSI DMA EN（当csi 比mjp快时，会冲掉正在encode 的mjp linebuf），不过会导致encode帧率下降
	if(mjpAEncCtrl.curBuffer)
		return;
	mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
	if(mjpAEncCtrl.curBuffer)
	{
		hx330x_csiMJPEGDmaEnable(0);
		if(mjpAEncCtrl.encode_lcd_mode)
		{
			if(mjpAEncCtrl.lens_add_en)
			{
				lens_add_lcd((u8*)mjpAEncCtrl.ybuffer, (u8*)mjpAEncCtrl.uvbuffer, mjpAEncCtrl.line_width*mjpAEncCtrl.line_height*3/2);
			}
			else if(mjpAEncCtrl.magic_win.config_en)
			{
				magic_mirror_lcd_add((u8*)mjpAEncCtrl.ybuffer);
			}
			else
			{
				kid_frame_lcd_add((u8*)mjpAEncCtrl.ybuffer);	//always use ch 1
			}	
		}else
		{
			if(mjpAEncCtrl.lens_add_en)
			{
				lens_add_csi((u8*)mjpAEncCtrl.ybuffer, (u8*)mjpAEncCtrl.uvbuffer, mjpAEncCtrl.line_width*mjpAEncCtrl.line_height*3/2);
			}
			else if(mjpAEncCtrl.magic_win.config_en)
			{
				magic_mirror_csi_add((u8*)mjpAEncCtrl.ybuffer);
			}
			else
			{
				kid_frame_csi_add((u8*)mjpAEncCtrl.ybuffer);	//always use ch 1
			}	
		}

		if(mjpAEncCtrl.enc_mode == ENC_MODE_LLPKG)
		{
			hal_mjp_enle_manual_kickstart(mjpAEncCtrl.ybuffer, mjpAEncCtrl.uvbuffer, mjpAEncCtrl.curBuffer, mjpAEncCtrl.curLen);
		}else
		{
			//hx330x_mjpA_EncodeDriModeSet(0);
			hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curBuffer+mjpAEncCtrl.curLen);
			hx330x_mjpA_Encode_manual_on();
		}	
		hx330x_intEnable(IRQ_JPGA,1); // enable jpegirq
		//debg("M");
	}
	//deg_Printf("hal_mjpAEncodePhotoKickManual kick\n");
}
/*******************************************************************************
* Function Name  : hal_mjpAEncodeVideoKickManual
* Description    : hal layer .mjpeg callback function for mjpeg irq encode
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_mjpAEncodePhotoKickLcd(u32 y_addr, u32 uv_addr)
{
	u16 width, height;
 	//只有等MJP ENCODE DOWN时才重新CSI DMA EN（当csi 比mjp快时，会冲掉正在encode 的mjp linebuf），不过会导致encode帧率下降
	if(mjpAEncCtrl.curBuffer)
		return;
	if(mjpAEncCtrl.encode_lcd_mode == 0)
		return;
	mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
	if(mjpAEncCtrl.curBuffer)
	{
		hx330x_mjpA_EncodeLcdKickRegister(NULL);
		if(mjpAEncCtrl.enc_mode != ENC_MODE_NORMAL || mjpAEncCtrl.timeStamp == 1)
		{
			width = (mjpAEncCtrl.mjpeg_width > mjpAEncCtrl.csi_width) ? mjpAEncCtrl.csi_width: mjpAEncCtrl.mjpeg_width;
			height = (mjpAEncCtrl.mjpeg_height > mjpAEncCtrl.csi_height) ? mjpAEncCtrl.csi_height: mjpAEncCtrl.mjpeg_height;
			hx330x_mjpA_EncodeInit(1,  0);
			hx330x_mjpA_EncodeQuilitySet(JPEG_Q_95);
			hx330x_mjpA_EncodeInfoSet(0);
			if(mjpAEncCtrl.enc_mode == ENC_MODE_NORMAL)
			{
				hal_jpg_watermarkStart(mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height,mjpAEncCtrl.timeStamp);
				mjpAEncCtrl.timeStamp = 0;
			}else
			{
				hal_jpg_watermarkStart(mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height,0);
			}
			
			hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.lcd_width,mjpAEncCtrl.lcd_height, mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height);
			hx330x_mjpA_Encode_inlinebuf_init((u32)y_addr,(u32)uv_addr);
			hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curBuffer+mjpAEncCtrl.curLen);
			hx330x_mjpA_Encode_manual_on();
			if(hx330x_mjpA_Encode_check() == false)
			{
				deg_Printf("ENC LCD STEP1 fail\n");
				hal_streamIn(&mjpAEncCtrl.vids, 0, 0, 0);
				return;
			}
			hal_mjpHeaderParse(mjpAEncCtrl.curBuffer);
			if(hal_mjpegDecodePicture_noisr(mjpAEncCtrl.curBuffer,mjpAEncCtrl.ybuffer,mjpAEncCtrl.uvbuffer,width,height) == false)
			{
				deg_Printf("ENC LCD STEP2 fail\n");
				hal_streamIn(&mjpAEncCtrl.vids, 0, 0, 0);
				return;
			}
			if(hx330x_mjpB_Decode_check() == false)
			{
				deg_Printf("ENC LCD STEP3 fail\n");
				hal_streamIn(&mjpAEncCtrl.vids, 0, 0, 0);
				return;
			}
			hx330x_mjpA_EncodeInit(1,  0);
			hx330x_mjpA_EncodeQuilitySet(mjpAEncCtrl.qulity);
			hx330x_mjpA_EncodeInfoSet(0);
			
			if(mjpAEncCtrl.enc_mode == ENC_MODE_LLPKG)
			{
				hal_mjp_enle_manual_kickstart(mjpAEncCtrl.ybuffer, mjpAEncCtrl.uvbuffer, mjpAEncCtrl.curBuffer, mjpAEncCtrl.curLen);
			}else
			{
				hal_jpg_watermarkStart(mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height,mjpAEncCtrl.timeStamp);
				hx330x_mjpA_EncodeSizeSet(width,height, mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height);
				hx330x_mjpA_Encode_inlinebuf_init((u32)mjpAEncCtrl.ybuffer,(u32)mjpAEncCtrl.uvbuffer);
				hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curBuffer+mjpAEncCtrl.curLen);
				hx330x_mjpA_Encode_manual_on();
			}	
		}else
		{
			hx330x_mcpy0_sdram2gram_nocache((void*)mjpAEncCtrl.ybuffer, (void*)y_addr, mjpAEncCtrl.line_width * mjpAEncCtrl.line_height);
			hx330x_mcpy0_sdram2gram_nocache((void*)mjpAEncCtrl.uvbuffer, (void*)uv_addr, mjpAEncCtrl.line_width * mjpAEncCtrl.line_height/2);
			hx330x_mjpA_EncodeInit(1,  0);
			hx330x_mjpA_EncodeQuilitySet(mjpAEncCtrl.qulity);
			hx330x_mjpA_EncodeInfoSet(0);
			hal_jpg_watermarkStart(mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height,mjpAEncCtrl.timeStamp);
			hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.lcd_width, mjpAEncCtrl.lcd_height, mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height);
			hx330x_mjpA_Encode_inlinebuf_init((u32)mjpAEncCtrl.ybuffer,(u32)mjpAEncCtrl.uvbuffer);
			hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curBuffer+mjpAEncCtrl.curLen);
			hx330x_mjpA_Encode_manual_on();
		}	
		hx330x_intEnable(IRQ_JPGA,1); // enable jpegirq
		//debg("M");
	}
	//deg_Printf("hal_mjpAEncodePhotoKickManual kick\n");
}
/*******************************************************************************
* Function Name  : hal_mjpAEncodeVideoDoneManual
* Description    : hal layer .mjpeg manual encode done callback
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpAEncodeVideoDoneManual(int flag)
{
	u32 len;
	u32 mjp_sync;
	u32 mjp_sync_next;
	if(flag&((1<<MJPEG_IRQ_OUTPAUSE)|(1<<MJPEG_IRQ_FRAMEEND)))
	{
		len = hx330x_mjpA_EncodeLoadAddrGet()- mjpAEncCtrl.curBuffer;
		len = (len+0x1ff)&(~0x1ff);	
		mjp_sync      = hal_auadc_stamp_out();
		mjp_sync_next = hal_auadc_stamp_next();
		if(hal_mjpA_EncState(flag))
		{
			mjpAEncCtrl.jfcnt++;
			hal_streamIn(&mjpAEncCtrl.vids,len,mjp_sync,mjp_sync_next);
		}
		else
		{
			hal_streamIn(&mjpAEncCtrl.vids,0,mjp_sync,mjp_sync_next);
		}	
		mjpAEncCtrl.curBuffer = (u32)0;
		mjpAEncCtrl.curLen    = (len > _JPGA_SIZE_MIN_DEF_)? (len*2) : (_JPGA_SIZE_MIN_DEF_*2);	
	#if HAL_CFG_MJPEG_QULITY_AUTO >0
		if(mjpAEncCtrl.q_auto != JPEG_Q_AUTO){
			if(mjpAEncCtrl.mjpeg_height <= 720){
				hx330x_mjpA_EncodeQadj(len,HAL_CFG_MJPEG_720_SIZE_MIN,HAL_CFG_MJPEG_720_SIZE_MAX);
			}else{
				hx330x_mjpA_EncodeQadj(len,HAL_CFG_MJPEG_1080_SIZE_MIN,HAL_CFG_MJPEG_1080_SIZE_MAX);
			}
		}
		else
		{
			hal_mjpA_QualityAjust(len);
		}
	#endif	
		hx330x_mjpA_Flag_Clr(flag);				
		
	}else{
		deg_Printf("ERR:%x\n",flag);
		hx330x_mjpA_Flag_Clr(flag);
	}

}
/*******************************************************************************
* Function Name  : hal_mjpAEncodePhotoDoneManual
* Description    : hal layer .mjpeg manual encode done callback
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpAEncodePhotoDoneManual(int flag)
{
	u32 len;
	u32 mjp_sync = 0;
	//static u32 size = 0;
	if(flag&((1<<MJPEG_IRQ_OUTPAUSE)|(1<<MJPEG_IRQ_FRAMEEND)))
	{
		
		len = hx330x_mjpA_EncodeLoadAddrGet() - hx330x_mjpA_EncodeStartAddrGet();
		len = (len+3)&(~3);
	
		if(mjpAEncCtrl.enc_mode == ENC_MODE_LLPKG ) //encode pkg
		{
			int res;
			if(hal_mjpA_EncState(flag) == false)
			{
				hal_streamIn(&mjpAEncCtrl.vids,0,0,0); // sync as frame done flag
				
				hx330x_mjpA_EncodeEnable(0);
				deg_Printf("encode llpkg fail 1\n");
			}else
			{
				//size += len;
				res = hal_mjp_enle_manual_done(len);
				if(res  < 0)
				{
					deg_Printf("encode llpkg fail :%d\n",res);
					hal_streamIn(&mjpAEncCtrl.vids,0,JPG_PKG_ERR,0); // sync as frame done flag
				}else if(res > 0)
				{
					if(mjpAEncCtrl.manual_pkg_start)
					{
						mjp_sync |= JPG_PKG_START;
						mjpAEncCtrl.manual_pkg_start = 0;
					}
					if(res == 2)
					{
						mjp_sync |= JPG_PKG_END;
					}else
					{
						mjpAEncCtrl.manual_kick_noisr = 1;
					}	
					
					hx330x_sysDcacheFlush(mjpAEncCtrl.curBuffer, mjpAEncCtrl.curLen);
					if(hal_mjp_enle_buf_mdf() < 0)
					{
						hal_streamIn(&mjpAEncCtrl.vids,0,JPG_PKG_ERR,0); // sync as frame done flag
						hx330x_mjpA_EncodeEnable(0);
					}else
					{
						//hal_streamIn(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen,mjp_sync,0);	
						hal_streamIn(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen,mjp_sync,0);	
					}	
					//if(mjp_sync & JPG_PKG_END)
					//{
					//	//deg_Printf("size:%d\n", size);
					//	//size = 0;
					//}		
					
				}
			}	
		}
		else if(mjpAEncCtrl.enc_mode == ENC_MODE_PKG ) //encode pkg
		{
			
			if(mjpAEncCtrl.manual_pkg_start)
			{
				mjp_sync |= JPG_PKG_START;
				mjpAEncCtrl.manual_pkg_start = 0;
				//deg_Printf("S");
			}
			if(hal_mjpA_EncState(flag)) // frame done
			{
				//deg_Printf("E");
				hal_streamIn(&mjpAEncCtrl.vids,len,mjp_sync|JPG_PKG_END,0); // sync as frame done flag
				hx330x_mjpA_EncodeEnable(0);
				return;
			}
			//PKG PAUSE
			hal_streamIn(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen,mjp_sync,0);
			mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
			if(mjpAEncCtrl.curBuffer)
			{
				//deg_Printf("A");
				hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curBuffer+mjpAEncCtrl.curLen);
				//kick
				hx330x_mjpA_Flag_Clr(flag);
				
			}else
			{
				//deg_Printf("B");
				hx330x_intEnable(IRQ_JPGA,0);
				mjpAEncCtrl.mjp_flg = flag;
				mjpAEncCtrl.manual_kick_noisr = 1;
			}

		}else //ENC_MODE_NORMAL
		{
			len += 12;
			if(hal_mjpA_EncState(flag))
			{
				//mjpAEncCtrl.jfcnt++;
				hal_streamIn(&mjpAEncCtrl.vids,len,0,0);
				hx330x_mjpA_EncodeEnable(0);
			}
			else
			{
				hal_streamIn(&mjpAEncCtrl.vids,0,0,0);
				hx330x_csiMJPEGDmaEnable(1);
			}	
			mjpAEncCtrl.curBuffer = (u32)0;		
		#if HAL_CFG_MJPEG_QULITY_AUTO >0
			if(mjpAEncCtrl.q_auto != JPEG_Q_AUTO){
				if(mjpAEncCtrl.mjpeg_height <= 720){
					hx330x_mjpA_EncodeQadj(len,HAL_CFG_MJPEG_720_SIZE_MIN,HAL_CFG_MJPEG_720_SIZE_MAX);
				}else{
					hx330x_mjpA_EncodeQadj(len,HAL_CFG_MJPEG_1080_SIZE_MIN,HAL_CFG_MJPEG_1080_SIZE_MAX);
				}
			}
			else
			{
				hal_mjpA_QualityAjust(len);
			}
		#endif	
			hx330x_mjpA_Flag_Clr(flag);			
		}
	}else{
		deg_Printf("ERR:%x\n",flag);
		hx330x_mjpA_Flag_Clr(flag);
	}

}
/*******************************************************************************
* Function Name  : hal_mjpAEncodePhotoResumePKG
* Description    : hal layer .mjpeg callback function for mjpeg irq encode, only one frame
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
int hal_mjpAEncodePhotoResumePKG(void)
{
	
	HAL_CRITICAL_INIT();
	HAL_CRITICAL_ENTER();
	volatile u8 kick = mjpAEncCtrl.manual_kick_noisr;
	HAL_CRITICAL_EXIT();
	if(kick == 0)
	{
		return 0;
	}
	mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
	if(mjpAEncCtrl.curBuffer)
	{
		//deg_Printf("C");
		mjpAEncCtrl.manual_kick_noisr = 0;
		hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curBuffer+mjpAEncCtrl.curLen);
		hx330x_mjpA_Flag_Clr(mjpAEncCtrl.mjp_flg);
		hx330x_intEnable(IRQ_JPGA,1);
		return 0;
	}
	return -1;
}
/*******************************************************************************
* Function Name  : hal_mjpAEncodePhotoResumeLLPKG
* Description    : hal layer .mjpeg callback function for mjpeg irq encode, only one frame
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
int hal_mjpAEncodePhotoResumeLLPKG(void)
{
	HAL_CRITICAL_INIT();
	HAL_CRITICAL_ENTER();
	volatile u8 kick = mjpAEncCtrl.manual_kick_noisr;
	HAL_CRITICAL_EXIT();
	if(kick == 0)
	{
		return 0;
	}
	//deg_Printf("hal_mjpAEncodePhotoResumeLLPKG\n");
	mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
	if(mjpAEncCtrl.curBuffer)
	{
		
		mjpAEncCtrl.manual_kick_noisr = 0;
		hal_mjp_enle_manual_kickstart(mjpAEncCtrl.ybuffer, mjpAEncCtrl.uvbuffer, mjpAEncCtrl.curBuffer, mjpAEncCtrl.curLen);
		//deg_Printf("END\n");
		return 0;
	}
	
	return -1;
}
/*******************************************************************************
* Function Name  : hal_mjpAEncodePhotoResumeRam
* Description    : hal layer .mjpeg callback function for mjpeg irq encode, only one frame
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
int hal_mjpAEncodePhotoResumeRam(void)
{
	if(mjpAEncCtrl.enc_mode == ENC_MODE_PKG)
	{
		mjpAEncCtrl.curLen *= 2;
	}
	mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
	if(mjpAEncCtrl.curBuffer)
	{
		mjpAEncCtrl.enc_mode 			= ENC_MODE_NORMAL;
		//mjpAEncCtrl.qulity 				= hal_mjpA_QualityCheck(JPEG_Q_27);
		//hx330x_mjpA_EncodeQuilitySet(mjpAEncCtrl.qulity);
		//if(mjpAEncCtrl.lens_add_en)
		//{
		//#if LENS_USE_TAB
		//	hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height, mjpAEncCtrl.csi_width*2,mjpAEncCtrl.csi_height*2);
		//#else
		//	hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height, mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height);
		//#endif
		//	hal_jpg_watermarkStart(mjpAEncCtrl.csi_width*2,mjpAEncCtrl.csi_height*2,mjpAEncCtrl.timeStamp);
		//}else
		//if(mjpAEncCtrl.encode_lcd_mode)
		//{
		//	hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.lcd_width,mjpAEncCtrl.lcd_height, mjpAEncCtrl.lcd_width,mjpAEncCtrl.lcd_width);
		//	hal_jpg_watermarkStart(mjpAEncCtrl.lcd_width,mjpAEncCtrl.lcd_width,mjpAEncCtrl.timeStamp);		
		//}
		//else
		{
			hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height, mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height);
			hal_jpg_watermarkStart(mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height,mjpAEncCtrl.timeStamp);		
		}

		hx330x_mjpA_Encode_inlinebuf_init((u32)mjpAEncCtrl.ybuffer,(u32)mjpAEncCtrl.uvbuffer);
		hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curBuffer+mjpAEncCtrl.curLen);
		hx330x_mjpA_EncodeDriModeSet(0);
		hx330x_mjpA_Encode_manual_on();
		hx330x_intEnable(IRQ_JPGA,1);
		return 0;
	}
	return -1;
}


/*******************************************************************************
* Function Name  : hal_mjpA_Start
* Description    : hal layer .mjpeg start.for sync to frame,this function will be callback by csi frame end
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_Start(void)
{
	hx330x_mjpA_EncodeEnable(1);
	hx330x_csiISRRegiser(CSI_IRQ_SEN_STATE_INT,NULL); // only need execute once
}
/*******************************************************************************
* Function Name  : hal_mjpA_Restart
* Description    : hal layer .mjpeg restart
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_Restart(void)
{
	hx330x_mjpA_EncodeEnable(0);
	hx330x_csiISRRegiser(CSI_IRQ_SEN_STATE_INT,hal_mjpA_Start);
}


/*******************************************************************************
* Function Name  : hal_mjpA_EncodeInit
* Description    : hal layer .mjpeg initial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_EncodeInit(void)
{
	mjpAEncCtrl.uvbuffer = 0;//(u32)halSysMemMJPEGBuffer.uv_buffer;
	mjpAEncCtrl.ybuffer  = 0;//(u32)halSysMemMJPEGBuffer.y_buffer;
	mjpAEncCtrl.mjpbuf   = 0;//&halSysMemMJPEGBuffer.mjpeg_buffer;
	hal_SensorResolutionGet(&mjpAEncCtrl.csi_width,&mjpAEncCtrl.csi_height);
	hx330x_mjpA_EncodeLcdKickRegister(NULL);
	hal_mjp_enle_init();
}
/*******************************************************************************
* Function Name  : hal_mjpA_EncodeUninit
* Description    : hal layer .mjpeg uninitial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_EncodeUninit(void)
{
	//if(mjpAEncCtrl.mjpbuf == 0)
	//	return;
	hx330x_mjpA_EncodeEnable(0);
	hx330x_csiISRRegiser(CSI_IRQ_JPG_FRAME_END,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_SEN_STATE_INT,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_JBF_DMAWR_ERR,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_WIFIBF_ERR,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_WIFI_FRAME_END,NULL);
	hx330x_csiISRRegiser(CSI_JE_LB_OVERFLOW_ERR,NULL);
	hx330x_mjpA_EncodeISRRegister(NULL);
	hx330x_mjpA_EncodeLcdKickRegister(NULL);
	hal_mjpA_MemUninit();
}

/*******************************************************************************
* Function Name  : hal_mjpA_MemInit
* Description    : hal layer .mjpeg memory initial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpA_LineBuf_MenInit(u16 width,u16 height)
{
	if(mjpAEncCtrl.encode_lcd_mode)
	{
		mjpAEncCtrl.line_width = mjpAEncCtrl.csi_width;
		mjpAEncCtrl.line_height = mjpAEncCtrl.csi_height;
	}else
	{
		mjpAEncCtrl.line_width = (mjpAEncCtrl.csi_width > width) ? width : mjpAEncCtrl.csi_width;
		mjpAEncCtrl.line_height = (mjpAEncCtrl.csi_height > height) ? height : mjpAEncCtrl.csi_height;		
	}

	deg_Printf("MJPA LINE W:%d,H:%d\n",mjpAEncCtrl.line_width,mjpAEncCtrl.line_height);

	if(mjpAEncCtrl.ybuffer == 0)
	{
		mjpAEncCtrl.ybuffer = (u32)hal_sysMemMallocLast((u32)mjpAEncCtrl.line_width*mjpAEncCtrl.line_height*3/2);
		if(mjpAEncCtrl.ybuffer == 0)
		{
			return false;
		}
		mjpAEncCtrl.uvbuffer = mjpAEncCtrl.ybuffer + (u32)mjpAEncCtrl.line_width*mjpAEncCtrl.line_height;

		deg_Printf("mjpAEncCtrl.ybuffer:%x\n",mjpAEncCtrl.ybuffer);
	}
	return true;
}

/*******************************************************************************
* Function Name  : hal_mjpA_MemInit
* Description    : hal layer .mjpeg memory initial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpA_buf_MenInit(void)
{
	u32 size;
	if(mjpAEncCtrl.ybuffer == 0)
		return false;
	if(mjpAEncCtrl.mjpbuf == 0)
	{
		size = hal_sysMemRemain() - 16*1024; //预留给创建文件用buf
		size &=~0x1ff;
		//size = 1200*1024L;
		mjpAEncCtrl.mjpbuf = (u32)hal_sysMemMalloc(size);
		if(mjpAEncCtrl.mjpbuf == 0)
		{
			return false;
		}
		mjpAEncCtrl.mjpsize = size;
		deg_Printf("HAL :<INFO> mjpA addr = 0x%x,size = %dMB%dKB\n",mjpAEncCtrl.mjpbuf,size>>20,(size>>10)&1023);
	}
	return true;
}
/*******************************************************************************
* Function Name  : hal_mjpA_MemUninit
* Description    : hal layer .mjpeg memory uninitial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_linebufUninit(void)
{
	if(mjpAEncCtrl.ybuffer)
		hal_sysMemFree((void *)mjpAEncCtrl.ybuffer);
	mjpAEncCtrl.ybuffer = 0;
	mjpAEncCtrl.uvbuffer = 0;
}
/*******************************************************************************
* Function Name  : hal_mjpA_MemUninit
* Description    : hal layer .mjpeg memory uninitial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_MemUninit(void)
{
	hal_mjp_enle_unit();
	if(mjpAEncCtrl.mjpbuf)
		hal_sysMemFree((void *)mjpAEncCtrl.mjpbuf);
	mjpAEncCtrl.mjpbuf = 0;
	mjpAEncCtrl.encode_lcd_mode = 0;
	hal_mjpA_linebufUninit();
}

/*******************************************************************************
* Function Name  : hal_mjpA_Enc_Video_Start
* Description    : hal layer .mjpeg initial
* Input          : u8 type MJPEG_TYPE_AVI/MJPEG_TYPE_UVC
				   u16 win_w : output width
				   u16 win_h : output height
				   u8  quality: encode quality
				   u8  timestamp
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpA_Enc_Video_Start(u8 type, u16 target_w, u16 target_h, u8 quality, u8 timestamp)
{
	hx330x_bytes_memset((u8*)&mjpAEncCtrl, 0, sizeof(mjpAEncCtrl));
	//mjpAEncCtrl.encode_lcd_mode = 0;
	if(type != MJPEG_TYPE_AVI && type != MJPEG_TYPE_UVC)
	{
		return false;
	}
	hal_SensorResolutionGet(&mjpAEncCtrl.csi_width,&mjpAEncCtrl.csi_height);
	hal_lcdGetVideoRatioResolution(&mjpAEncCtrl.lcd_width,&mjpAEncCtrl.lcd_height);
	if(hx330x_csiMJPEGScaler(target_w,target_h,0,0,mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height) == false)
	{
		deg_Printf("MJPEGScaler CFG err\n");
		return false;
	}
	if((mjpAEncCtrl.csi_width > target_w) && (mjpAEncCtrl.csi_height > target_h)) //scaler down
	{
		deg_Printf("mjpA Encode : I2J Scaler down fail\n");
		return false;
	}
	if(hal_mjpA_LineBuf_MenInit(target_w,target_h) == false)
	{
		deg_Printf("mjpA encode : linebuf malloc fail\n");
		return false;
	}
	if(hal_mjpA_buf_MenInit() == false) // mjpeg memory malloc
	{
		hal_mjpA_linebufUninit();
		deg_Printf("mjpA encode : mjpbuf malloc fail\n");
		return false;
	}
	hal_streamInit(&mjpAEncCtrl.vids,mjpAEncCtrl.mjpegNode,MJPEG_ITEM_NUM,(u32)mjpAEncCtrl.mjpbuf,mjpAEncCtrl.mjpsize);

	mjpAEncCtrl.mjpeg_width 	= target_w;
	mjpAEncCtrl.mjpeg_height 	= target_h;
	mjpAEncCtrl.qulity 			= hal_mjpA_QualityCheck(quality);
	//mjpAEncCtrl.magic_win.config_en = 0;
	mjpAEncCtrl.enc_mode   		= ENC_MODE_NORMAL;
	//mjpAEncCtrl.encode_lcd_mode   = 0;
	//mjpAEncCtrl.manual_pkg_start  = 0;
	//mjpAEncCtrl.manual_kick_noisr = 0;	
	//mjpAEncCtrl.curBuffer = 0;
	mjpAEncCtrl.curLen = mjpAEncCtrl.mjpsize/2;

	deg_Printf("mjpsize:%x,curLen:%x\n",mjpAEncCtrl.mjpsize, mjpAEncCtrl.curLen );

	hx330x_csiMJPEGFrameSet(mjpAEncCtrl.ybuffer,mjpAEncCtrl.uvbuffer,mjpAEncCtrl.line_height,mjpAEncCtrl.line_width);
	hx330x_mjpA_EncodeInit(1,(HAL_CFG_MJPEG_HIGH_QT?2:1));
	hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height, mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height);
	hx330x_mjpA_EncodeQuilitySet(mjpAEncCtrl.qulity);
	hx330x_mjpA_EncodeInfoSet((type == MJPEG_TYPE_AVI)?1:0);
	hal_jpg_watermarkStart(mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height,timestamp);

	hx330x_mjpA_Encode_inlinebuf_init((u32)mjpAEncCtrl.ybuffer,(u32)mjpAEncCtrl.uvbuffer);

	hx330x_csiISRRegiser(CSI_IRQ_JPG_FRAME_END,	hal_mjpAEncodeVideoKickManual);
	hx330x_csiISRRegiser(CSI_IRQ_SEN_STATE_INT,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_JBF_DMAWR_ERR,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_WIFIBF_ERR,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_WIFI_FRAME_END,NULL);
	hx330x_csiISRRegiser(CSI_JE_LB_OVERFLOW_ERR,NULL);

	hx330x_mjpA_EncodeISRRegister(hal_mjpAEncodeVideoDoneManual);
	hx330x_csiMJPEGDmaEnable(1);	
	hx330x_csiEnable(1);
	return true;
}
/*******************************************************************************
* Function Name  : hal_mjpA_Enc_Photo_Start
* Description    : hal layer .mjpeg initial
* Input          : 
				   u16 win_w : output width
				   u16 win_h : output height
				   u8  quality: encode quality
				   u8  timestamp
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpA_Enc_Photo_Start(u16 target_w, u16 target_h, u8 quality, u8 timestamp)
{
	hx330x_bytes_memset((u8*)&mjpAEncCtrl, 0, sizeof(mjpAEncCtrl));
	hal_SensorResolutionGet(&mjpAEncCtrl.csi_width,&mjpAEncCtrl.csi_height);
	hal_lcdGetVideoRatioResolution(&mjpAEncCtrl.lcd_width,&mjpAEncCtrl.lcd_height);
	mjpAEncCtrl.lens_add_en = lens_add_csi_check();
	//mjpAEncCtrl.encode_lcd_mode = 0;
	if(hx330x_csiMJPEGScaler(target_w,target_h,0,0,mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height) == false)
	{
		deg_Printf("MJPEGScaler CFG err\n");
		return false;
	}		

	int res = hal_mjp_enle_check(mjpAEncCtrl.csi_width, mjpAEncCtrl.csi_height, target_w, target_h, quality,timestamp,MJPEG_LLPKG_DOUBLEBUF);
	deg_Printf("enle:%d\n", res);
	if(res < 0)
	{
		deg_Printf("mjpA Encode : I2J Scaler down fail\n");
		return false;
	}
	if(res > 0)
	{
		mjpAEncCtrl.enc_mode = ENC_MODE_LLPKG;
	}else if(target_w > mjpAEncCtrl.csi_width || target_h > mjpAEncCtrl.csi_height)
	{	
		mjpAEncCtrl.enc_mode   = ENC_MODE_PKG;
	}else
	{
		mjpAEncCtrl.enc_mode   = ENC_MODE_NORMAL;
	}
	

	mjpAEncCtrl.timeStamp 			= timestamp;
	mjpAEncCtrl.manual_pkg_start 	= 1;
	//mjpAEncCtrl.manual_kick_noisr 	= 0;
	mjpAEncCtrl.qulity 				= hal_mjpA_QualityCheck(quality);

	mjpAEncCtrl.mjpeg_width 		= target_w;
	mjpAEncCtrl.mjpeg_height 		= target_h;
	//mjpAEncCtrl.jfcnt				= 0;
	//mjpAEncCtrl.mjp_flg 			= 0;
	deg_Printf("enc_mode:%d\n", mjpAEncCtrl.enc_mode);
	//mjpAEncCtrl.magic_win.config_en = 0;
	if(hal_mjpA_LineBuf_MenInit(target_w,target_h) == false)
	{
		deg_Printf("mjpA encode : linebuf malloc fail\n");
		return false;
	}
	if(hal_mjpA_buf_MenInit() == false) // mjpeg memory malloc
	{
		hal_mjpA_linebufUninit();
		deg_Printf("mjpA encode : mjpbuf malloc fail\n");
		return false;
	}
	int magic_sta = magic_mirror_csi_updata(&mjpAEncCtrl.magic_win);
	deg_Printf("[Magic] [%d][%d,%d,%d,%d][%d,%d,%d,%d]\n", magic_sta,
	mjpAEncCtrl.magic_win.dst_w,mjpAEncCtrl.magic_win.dst_h, mjpAEncCtrl.magic_win.dst_pos_x, mjpAEncCtrl.magic_win.dst_pos_y,
	mjpAEncCtrl.magic_win.src_crop_sx, mjpAEncCtrl.magic_win.src_crop_sy, mjpAEncCtrl.magic_win.src_crop_ex, mjpAEncCtrl.magic_win.src_crop_ey);
	if( magic_sta > 0 )
	{
		mjpAEncCtrl.magic_win.config_en = 1;
	}
	hal_streamInit(&mjpAEncCtrl.vids,mjpAEncCtrl.mjpegNode,MJPEG_ITEM_NUM,(u32)mjpAEncCtrl.mjpbuf,mjpAEncCtrl.mjpsize);

	mjpAEncCtrl.curBuffer = 0;
	if(mjpAEncCtrl.enc_mode == ENC_MODE_PKG)
		mjpAEncCtrl.curLen = mjpAEncCtrl.mjpsize/2;
	else
		mjpAEncCtrl.curLen = mjpAEncCtrl.mjpsize;
#if  MJPEG_LLPKG_DOUBLEBUF
	if(mjpAEncCtrl.enc_mode == ENC_MODE_LLPKG)
		mjpAEncCtrl.curLen = mjpAEncCtrl.mjpsize/2;
#endif
	deg_Printf("mjpsize:%x,curLen:%x\n",mjpAEncCtrl.mjpsize, mjpAEncCtrl.curLen );

	hx330x_csiMJPEGFrameSet(mjpAEncCtrl.ybuffer,mjpAEncCtrl.uvbuffer,mjpAEncCtrl.line_height,mjpAEncCtrl.line_width);
	hx330x_mjpA_EncodeInit(1,  0);
	hx330x_mjpA_EncodeQuilitySet(mjpAEncCtrl.qulity);
	hx330x_mjpA_EncodeInfoSet(0);
	if(mjpAEncCtrl.enc_mode != ENC_MODE_LLPKG)
	{
		hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height, mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height);
		hal_jpg_watermarkStart(mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height,timestamp);
		hx330x_mjpA_Encode_inlinebuf_init((u32)mjpAEncCtrl.ybuffer,(u32)mjpAEncCtrl.uvbuffer);
	}
	hx330x_csiISRRegiser(CSI_IRQ_JPG_FRAME_END,	hal_mjpAEncodePhotoKickManual);
	hx330x_csiISRRegiser(CSI_IRQ_SEN_STATE_INT,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_JBF_DMAWR_ERR,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_WIFIBF_ERR,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_WIFI_FRAME_END,NULL);
	hx330x_csiISRRegiser(CSI_JE_LB_OVERFLOW_ERR,NULL);
	hx330x_mjpA_EncodeISRRegister(hal_mjpAEncodePhotoDoneManual);
	hx330x_csiMJPEGDmaEnable(1);	
	hx330x_csiEnable(1);
	return true;
}
/*******************************************************************************
* Function Name  : hal_mjpA_EncLcd_Photo_Start
* Description    : hal layer .mjpeg initial
* Input          : 
				   u16 win_w : output width
				   u16 win_h : output height
				   u8  quality: encode quality
				   u8  timestamp
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpA_EncLcd_Photo_Start(u16 target_w, u16 target_h, u8 quality, u8 timestamp)
{
	u16 video_w, video_h;
	hx330x_bytes_memset((u8*)&mjpAEncCtrl, 0, sizeof(mjpAEncCtrl));
	hal_SensorResolutionGet(&mjpAEncCtrl.csi_width,&mjpAEncCtrl.csi_height);
	hal_lcdGetVideoRatioResolution(&mjpAEncCtrl.lcd_width,&mjpAEncCtrl.lcd_height);
	mjpAEncCtrl.lcd_width = (mjpAEncCtrl.lcd_width + 0x1f)&~0x1f;
	
	mjpAEncCtrl.lens_add_en = lens_add_csi_check();
	mjpAEncCtrl.encode_lcd_mode = 1;
	int res = hal_mjp_enle_check(mjpAEncCtrl.csi_width, mjpAEncCtrl.csi_height, target_w, target_h, quality,timestamp,MJPEG_LLPKG_DOUBLEBUF);
	deg_Printf("enle:%d\n", res);
	if(res < 0)
	{
		deg_Printf("mjpA Encode : I2J Scaler down fail\n");
		return false;
	}
	if(res > 0)
	{
		mjpAEncCtrl.enc_mode = ENC_MODE_LLPKG;
	}else if(target_w > mjpAEncCtrl.csi_width || target_h > mjpAEncCtrl.csi_height)
	{	
		mjpAEncCtrl.enc_mode   = ENC_MODE_PKG;
	}else
	{
		mjpAEncCtrl.enc_mode   = ENC_MODE_NORMAL;
	}
	
	mjpAEncCtrl.timeStamp 			= timestamp;
	mjpAEncCtrl.manual_pkg_start 	= 1;
	//mjpAEncCtrl.manual_kick_noisr 	= 0;
	mjpAEncCtrl.qulity 				= hal_mjpA_QualityCheck(quality);

	mjpAEncCtrl.mjpeg_width 		= target_w;
	mjpAEncCtrl.mjpeg_height 		= target_h;
	//mjpAEncCtrl.jfcnt				= 0;
	//mjpAEncCtrl.mjp_flg 			= 0;
	deg_Printf("enc_mode:%d\n", mjpAEncCtrl.enc_mode);
	deg_Printf("encode_lcd_mode:%d\n", mjpAEncCtrl.encode_lcd_mode);
	mjpAEncCtrl.magic_win.config_en = 0;
	if(hal_mjpA_LineBuf_MenInit(target_w,target_h) == false)
	{
		deg_Printf("mjpA encode : linebuf malloc fail\n");
		return false;
	}
	if(hal_mjpA_buf_MenInit() == false) // mjpeg memory malloc
	{
		hal_mjpA_linebufUninit();
		deg_Printf("mjpA encode : mjpbuf malloc fail\n");
		return false;
	}

	hal_streamInit(&mjpAEncCtrl.vids,mjpAEncCtrl.mjpegNode,MJPEG_ITEM_NUM,(u32)mjpAEncCtrl.mjpbuf,mjpAEncCtrl.mjpsize);

	//mjpAEncCtrl.curBuffer = 0;
	if(mjpAEncCtrl.enc_mode == ENC_MODE_PKG)
		mjpAEncCtrl.curLen = mjpAEncCtrl.mjpsize/2;
	else
		mjpAEncCtrl.curLen = mjpAEncCtrl.mjpsize;
#if  MJPEG_LLPKG_DOUBLEBUF
	if(mjpAEncCtrl.enc_mode == ENC_MODE_LLPKG)
		mjpAEncCtrl.curLen = mjpAEncCtrl.mjpsize/2;
#endif

	deg_Printf("mjpsize:%x,curLen:%x\n",mjpAEncCtrl.mjpsize, mjpAEncCtrl.curLen );

	//hx330x_csiMJPEGFrameSet(mjpAEncCtrl.ybuffer,mjpAEncCtrl.uvbuffer,mjpAEncCtrl.line_height,mjpAEncCtrl.line_width);
	//hx330x_mjpA_EncodeInit(1,  0);
	//hx330x_mjpA_EncodeQuilitySet(mjpAEncCtrl.qulity);
	//hx330x_mjpA_EncodeInfoSet(0);
	
	//hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.lcd_width,mjpAEncCtrl.lcd_height, mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height);
	//hal_jpg_watermarkStart(mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height,timestamp);
	//hx330x_mjpA_Encode_inlinebuf_init((u32)mjpAEncCtrl.ybuffer,(u32)mjpAEncCtrl.uvbuffer);
	
	hx330x_csiISRRegiser(CSI_IRQ_JPG_FRAME_END,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_SEN_STATE_INT,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_JBF_DMAWR_ERR,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_WIFIBF_ERR,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_WIFI_FRAME_END,NULL);
	hx330x_csiISRRegiser(CSI_JE_LB_OVERFLOW_ERR,NULL);
	hx330x_mjpA_EncodeISRRegister(hal_mjpAEncodePhotoDoneManual);
	hx330x_mjpA_EncodeLcdKickRegister(hal_mjpAEncodePhotoKickLcd);
	//hx330x_csiMJPEGDmaEnable(1);	
	hx330x_csiEnable(1);
	return true;
}
/*******************************************************************************
* Function Name  : hal_mjpA_photo_encode_mode
* Description    : hal layer.set mjpeg fram raw data
* Input          : void *buffer : frame buffer
* Output         : None
* Return         : None
*******************************************************************************/
u32 hal_mjpA_photo_encode_mode(void)
{
	return mjpAEncCtrl.enc_mode;
}
/*******************************************************************************
* Function Name  : hal_mjpegRawBufferfree
* Description    : hal layer.set mjpeg fram raw data
* Input          : void *buffer : frame buffer
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_RawBufferfree(void)
{
	hal_streamfree(&mjpAEncCtrl.vids);
}
/*******************************************************************************
* Function Name  : hal_mjpA_RawBufferGet
* Description    : hal layer.check raw buffer addr &length
* Input          : u32 *len : buff len
 				   u32 *sync: current buf frame stamp
				   u32 *sync_next: current buf next frame stamp
* Output         : None
* Return         : void * : buffer addr
*******************************************************************************/
void *hal_mjpA_RawBufferGet(u32 *len,u32 *sync, u32 *sync_next)
{
	u8* buff;
	u32 size = 0;

	if(hal_streamOut(&mjpAEncCtrl.vids,(u32 *)&buff,(u32 *)&size, sync, sync_next) == false)
		return NULL;
	if(size == 0){
		hal_mjpA_RawBufferfree();
		deg_Printf("jlen=0\n");
		return NULL;
	}
	hx330x_sysDcacheFlush((u32)buff,16);

	if((buff[0] != 0xff) || (buff[1] != 0xd8)){
		if((buff[8] != 0xff) || (buff[9] != 0xd8)){
			hal_mjpA_RawBufferfree();
			deg_Printf("jhead err:");
			deg_PrintfBuf(buff,16);
			return NULL;
		}
	}
	if(len)
		*len = size;

	return ((void *)buff);
}
/*******************************************************************************
* Function Name  : sync_flag
* Description    : hal layer.check raw buffer addr &length
* Input          : u32 *len : buff len
 				   u32 *sync: current buf frame stamp
				   u32 *sync_next: current buf next frame stamp
* Output         : None
* Return         : void * : buffer addr
*******************************************************************************/
void *hal_mjpA_RkgBufferGet(u32 *len,u32 *sync, u32 *sync_next)
{
	u8* buff;
	u32 size = 0;

	if(hal_streamOut(&mjpAEncCtrl.vids,(u32 *)&buff,(u32 *)&size, sync, sync_next) == false)
		return NULL;
	if(size == 0){
		hal_mjpA_RawBufferfree();
		deg_Printf("jlen=0\n");
		return NULL;
	}
	hx330x_sysDcacheFlush((u32)buff,16);
	if(len)
		*len = size;

	return ((void *)buff);
}
/*******************************************************************************
* Function Name  : hal_mjpA_Buffer_prefull
* Description    : hal layer.mjpeg buffer pre full(2/3) just
* Input          : None
* Output         : None
* Return         : true:mjpeg buffer is pre_full
*******************************************************************************/
bool hal_mjpA_Buffer_prefull(void)
{
	//if(mjpAEncCtrl.vids.head_size > (mjpAEncCtrl.mjpsize/5*2))
	//	return true;
	u32 temp = mjpAEncCtrl.vids.head_size;

	//deg_Printf("[A:%d,%d]\n",temp,mjpAEncCtrl.mjpsize);
	if(temp > ((mjpAEncCtrl.mjpsize)/2))
	{
		//deg_Printf("[A:%d,%d]\n",temp,mjpAEncCtrl.mjpsize);
		return true;
	}
	return false;
}
/*******************************************************************************
* Function Name  : hal_mjpA_Buffer_halffull
* Description    : hal layer.mjpeg buffer pre full(2/3) just
* Input          : None
* Output         : None
* Return         : true:mjpeg buffer is half_full
*******************************************************************************/
bool hal_mjpA_Buffer_halffull(void)
{
	//if(mjpAEncCtrl.vids.head_size > (mjpAEncCtrl.mjpsize/5))
	//	return true;
	u32 temp = mjpAEncCtrl.vids.head_size;
	if(temp > ((mjpAEncCtrl.mjpsize)/4))
		return true;
	//deg_Printf("[A:%d,%d]\n",temp,mjpAEncCtrl.mjpsize);
	return false;
}
