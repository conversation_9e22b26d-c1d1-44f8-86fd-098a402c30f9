/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	LOCKCUR_RECT_ID = 0,
	LOCKCUR_TIPS_ID,
	LOCKCUR_SELECT_ID
};

UNUSED ALIGNED(4) const widgetCreateInfor lockCurWin[] =
{
	createFrameWin(						Rx(58),	Ry(58), Rw(204),Rh(124),SMENU_FRAME_COLOR,WIN_ABS_POS),
	createRect(LOCKCUR_RECT_ID,         Rx(2),	<PERSON>y(2),  <PERSON>w(200),<PERSON>h(120),SMENU_UNSELECT_BG_COLOR),
	createStringIcon(LOCKCUR_TIPS_ID,	Rx(2),	Ry(2), 	Rw(200),Rh(40),R_ID_STR_SET_LOCKCUR,ALIGNMENT_CENTER, SMENU_UNSELECT_FN_COLOR,DEFAULT_FONT),
	createItemManage(LOCKCUR_SELECT_ID,	Rx(2),	Ry(42), Rw(200),Rh(80),	SMENU_UNSELECT_BG_COLOR),
	widgetEnd(),
};


