/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef __MENU_API_H
#define __MENU_API_H
#include "menu_typedef.h"

#define  MENU_FRAME_COLOR           R_ID_PALETTE_Gray
#define  MENU_SELECT_BG_COLOR       R_ID_PALETTE_LightYellow
#define  MENU_SELECT_FN_COLOR       R_ID_PALETTE_Black

#define  MENU_UNSELECT_BG_COLOR     R_ID_PALETTE_Transparent
#define  MENU_UNSELECT_FN_COLOR     R_ID_PALETTE_White

#define  SMENU_FRAME_COLOR          R_ID_PALETTE_Gray
#define  SMENU_SELECT_BG_COLOR      R_ID_PALETTE_LightYellow
#define  SMENU_SELECT_FN_COLOR      R_ID_PALETTE_Black
#define  SMENU_UNSELECT_BG_COLOR    R_ID_PALETTE_LightGreen
#define  SMENU_UNSELECT_FN_COLOR    R_ID_PALETTE_White
EXTERN_MENU(setting);

EXTERN_WINDOW(menuItemWindow);
EXTERN_WINDOW(menuOptionWindow);
EXTERN_WINDOW(dateTimeWindow);
EXTERN_WINDOW(defaultWindow);
EXTERN_WINDOW(sreenBrightWindow);
EXTERN_WINDOW(sysVolumeWindow);
EXTERN_WINDOW(delWindow);
EXTERN_WINDOW(delAllWindow);
EXTERN_WINDOW(delCurWindow);
EXTERN_WINDOW(formatWindow);
EXTERN_WINDOW(lockCurWindow);
EXTERN_WINDOW(unlockAllWindow);
EXTERN_WINDOW(unlockCurWindow);
EXTERN_WINDOW(versionWindow);
EXTERN_WINDOW(noFileWindow);
EXTERN_WINDOW(selfTestWindow);

EXTERN_WINDOW(tipsWindow);

EXTERN_WINDOW(versionWindow);
EXTERN_WINDOW(VersionWindow);//task version

/*******************************************************************************
* Function Name  : menuWinIsOpen
* Description    : menuWinIsOpen
* Input          : none
* Output         : none
* Return         : u32: 0: close, 1: open
*******************************************************************************/
u32 menuWinIsOpen(void);

#endif
