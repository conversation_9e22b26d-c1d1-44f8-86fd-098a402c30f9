/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#ifndef  SENSOR_API_H
    #define  SENSOR_API_H

#include "sensor_typedef.h"

extern const Sensor_Header_T  RES_SensorHeader;
extern const Sensor_Adpt_T test_img_adpt;
extern const Sensor_Ident_T test_img_init;

extern const u16 sensor_ygamma_tab[SENSOR_YGAMMA_CLASSES][256];
extern const u8 sensor_rgb_gamma[SENSOR_RGBGAMMA_CLASSES][256];
extern const u8 sensor_rgb_gamma_inverse[256];
extern const u8 sensor_rgb_gamma_raw[256];
#define SENSOR_720P_SUPPORT        			1 //注意720P下不支持同时接后拉
#define SENSOR_VGA_SUPPORT         			1

#define SENSOR_DVP_720P_BF3016   			(1&SENSOR_720P_SUPPORT)
#define SENSOR_DVP_720P_FPX1002   			(1&SENSOR_720P_SUPPORT)
#define SENSOR_DVP_720P_GC1004   			(1&SENSOR_720P_SUPPORT)
#define SENSOR_DVP_720P_GC1034   			(1&SENSOR_720P_SUPPORT)
#define SENSOR_DVP_720P_GC1054   			(0&SENSOR_720P_SUPPORT)
#define	SENSOR_DVP_720P_GC1064				(1&SENSOR_720P_SUPPORT)
#define SENSOR_DVP_720P_H42      			(1&SENSOR_720P_SUPPORT)
#define SENSOR_DVP_720P_H62      			(0&SENSOR_720P_SUPPORT)
#define SENSOR_DVP_720P_H65      			(1&SENSOR_720P_SUPPORT)
#define SENSOR_DVP_720P_H7640    			(1&SENSOR_720P_SUPPORT)
#define SENSOR_DVP_720P_NT99141  			(1&SENSOR_720P_SUPPORT)
#define SENSOR_DVP_720P_OV9710   			(1&SENSOR_720P_SUPPORT)
#define SENSOR_DVP_720P_OV9732   			(1&SENSOR_720P_SUPPORT)
#define SENSOR_DVP_720P_SC1045   			(1&SENSOR_720P_SUPPORT)
#define SENSOR_DVP_720P_SC1243				(1&SENSOR_720P_SUPPORT)
#define SENSOR_DVP_720P_SC1345				(1&SENSOR_720P_SUPPORT)
#define SENSOR_DVP_720P_SP1409   			(1&SENSOR_720P_SUPPORT)
#define SENSOR_DVP_720P_SP140A				(1&SENSOR_720P_SUPPORT)


#define SENSOR_DVP_VGA_BF2013   			(1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_BF3703   			(1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_BF3A03   			(1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_GC0307   			(1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_GC0308   			(1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_GC0309   			(1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_GC0328   			(1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_GC0329               (1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_HM1055   			(1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_IT03A1   			(1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_NT99142  			(1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_OV7670   			(1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_OV7725   			(1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_OV7736   			(1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_OV7675				(1&SENSOR_VGA_SUPPORT)

#define SENSOR_DVP_VGA_SIV100B  			(1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_SIV120B  			(1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_SIV121DS 			(1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_SC030IOT 			(1&SENSOR_VGA_SUPPORT)
#define SENSOR_DVP_VGA_BF20A6 			    (1&SENSOR_VGA_SUPPORT)



#define SENSOR_MIPI_720P_H62  				(1&SENSOR_720P_SUPPORT)
#define SENSOR_MIPI_720P_GC1054  			(1&SENSOR_720P_SUPPORT)
#define SENSOR_MIPI_720P_OV9714  			(1&SENSOR_720P_SUPPORT)
#define SENSOR_MIPI_VGA_GC030A				(1&SENSOR_VGA_SUPPORT)
#define SENSOR_MIPI_VGA_GC033A				(1&SENSOR_VGA_SUPPORT)
#define SENSOR_MIPI_VGA_GC0339				(1&SENSOR_VGA_SUPPORT)
#define SENSOR_MIPI_VGA_SP0A09				(1&SENSOR_VGA_SUPPORT)
#define SENSOR_MIPI_VGA_BF20A1 			    (1&SENSOR_VGA_SUPPORT)






	
/*******************************************************************************
* Function Name  : sensor_iic_enable
* Description    : enable senor iic for write/read
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
#define sensor_iic_enable()				hal_iic0Init()
/*******************************************************************************
* Function Name  : sensor_iic_disable
* Description    : disable senor iic for write/read
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
#define sensor_iic_disable()			hal_iic0Uninit()
/*******************************************************************************
* Function Name  : sensor_iic_write
* Description    : senor iic  write
* Input          : INT8U *data : data & addr
* Output         : none
* Return         : none
*******************************************************************************/
void sensor_iic_write(INT8U *data);
/*******************************************************************************
* Function Name  : sensor_iic_read
* Description    : senor iic  read
* Input          : INT8U *data : data & addr
* Output         : none
* Return         : none
*******************************************************************************/
INT32U sensor_iic_read(INT8U *data);


/*******************************************************************************
* Function Name  : sensor_rgbgamma_tab_load
* Description    : u32 rgbgma_num
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void sensor_rgbgamma_tab_load(u32 rgbgma_num0, u32 rgbgamma_num1);
/*******************************************************************************
* Function Name  : sensor_filter_rgbgamma_tab_load
* Description    : u32 rgbgma_num
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void sensor_filter_rgbgamma_tab_load(u32 rgbgma_num);
/*******************************************************************************
* Function Name  : sensor_ygamma_tab_load
* Description    : u32 ygamma_num
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void sensor_ygamma_tab_load(u32 ygamma_num0,u32 ygamma_num1);
/*******************************************************************************
* Function Name  : sensor_lsc_tab_load
* Description    : none
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void sensor_lsc_tab_load(void);
/*******************************************************************************
* Function Name  : SensorGetName
* Description    : get Sensor name
* Input          :
* Output         : none
* Return         : char *
*******************************************************************************/
char *SensorGetName(void);

/*******************************************************************************
* Function Name  : dev_sensor_init
* Description    : initial cmos sensor
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
int dev_sensor_init(void);
/*******************************************************************************
* Function Name  : dev_sensor_ioctrl
* Description    : dev_sensor_ioctrl
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
int dev_sensor_ioctrl(INT32U op,INT32U para);

#endif


