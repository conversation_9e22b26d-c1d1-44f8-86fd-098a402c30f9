/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuOptionWin.c"

ALIGNED(4) static menuItem* currentItem = NULL;
/*******************************************************************************
* Function Name  : getMenuOptionResInfor
* Description    : getMenuOptionResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : u32: 
*******************************************************************************/
static u32 getMenuOptionResInfor(u32 item,u32* image,u32* str)
{
	if(image)
		*image 	= currentItem->pOption[item].image;
	if(str)
		*str	= currentItem->pOption[item].str;
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionKeyMsgOk
* Description    : menuOptionKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int menuOptionKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(currentItem)
		{
			user_config_set(currentItem->configId,currentItem->pOption[uiItemManageGetCurrentItem(winItem(handle,OPTION_SELECT_ID))].str);
			user_config_cfgSys(currentItem->configId);
			user_config_save();
		}
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionKeyMsgUp
* Description    : menuOptionKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int menuOptionKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,OPTION_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionKeyMsgDown
* Description    : menuOptionKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int menuOptionKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManageNextItem(winItem(handle,OPTION_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionKeyMsgMenu
* Description    : menuOptionKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int menuOptionKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionSysMsgSD
* Description    : menuOptionSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int menuOptionSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{

	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionOpenWin
* Description    : menuOptionOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int menuOptionOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	u32 itemNum,config;
	if(parameNum < 1)
	{
		uiWinDestroy(&handle);
		return 0;
	}
	currentItem = (menuItem*)parame[0];
	deg_Printf("[WIN]menuOptionOpenWin\n");
	itemNum = uiItemManageSetHeightNotGap(winItem(handle,OPTION_SELECT_ID),Rh(32));

	uiItemManageCreateItem(		winItem(handle,OPTION_SELECT_ID),uiItemCreateMenuOption,getMenuOptionResInfor,currentItem->optionSum);

#if 0
	uiItemManageSetCharInfor(	winItem(handle,OPTION_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_White);
	uiItemManageSetSelectColor(	winItem(handle,OPTION_SELECT_ID),R_ID_PALETTE_DoderBlue);
	uiItemManageSetUnselectColor(winItem(handle,OPTION_SELECT_ID),R_ID_PALETTE_Black);
#else
	uiItemManageSetSelectColorEx(winItem(handle,OPTION_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER, SMENU_SELECT_FN_COLOR, SMENU_SELECT_BG_COLOR);
	uiItemManageSetUnselectColorEx(winItem(handle,OPTION_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER, SMENU_UNSELECT_FN_COLOR, SMENU_UNSELECT_BG_COLOR);
#endif

	config  = user_config_get(currentItem->configId);
	itemNum = 0;
	while(itemNum < currentItem->optionSum)
	{
		if(currentItem->pOption[itemNum].str == config)
			break;
		itemNum++;
	}
	if(itemNum >= currentItem->optionSum)
		itemNum = 0;
	uiItemManageSetCurItem(winItem(handle,OPTION_SELECT_ID),itemNum);
	//uiWinSetResid(winItem(handle,OPTION_TITLE_ID),currentItem->str);
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionCloseWin
* Description    : menuOptionCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int menuOptionCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]menuOptionCloseWin\n");
	currentItem = NULL;
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionCloseWin
* Description    : menuOptionCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int menuOptionWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]menuOptionWinChildClose\n");
	return 0;
}


ALIGNED(4) msgDealInfor menuOptionMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	menuOptionOpenWin},
	{SYS_CLOSE_WINDOW,	menuOptionCloseWin},
	{SYS_CHILE_COLSE,	menuOptionWinChildClose},
	{KEY_EVENT_OK,		menuOptionKeyMsgOk},
	{KEY_EVENT_UP,		menuOptionKeyMsgUp},
	{KEY_EVENT_DOWN,	menuOptionKeyMsgDown},
	{KEY_EVENT_LEFT,	menuOptionKeyMsgPower},
	//{KEY_EVENT_RIGHT,	menuOptionKeyMsgRight},
	{KEY_EVENT_POWER,	menuOptionKeyMsgPower},
	{SYS_EVENT_SDC,		menuOptionSysMsgSD},
	{EVENT_MAX,			NULL},
};

WINDOW(menuOptionWindow,menuOptionMsgDeal,menuOptionWin)


