/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	//NESGAMEINNER_UP_RECT_ID = 0,
	//NESGAMEINNER_DOWN_RECT_ID,
	NESGAMEINNER_LEFT_ID,
	NESGAMEINNER_RIGHT_ID,
	//NESGAMEINNER_NAME_ID
};
UNUSED ALIGNED(4) const widgetCreateInfor nesGameInnerWin[] =
{
	createFrameWin( 							Rx(0),   Ry(0),   Rw(320), Rh(240), R_ID_PALETTE_Transparent,     WIN_ABS_POS),
	//createStringIcon(NESGAMEINNER_NAME_ID,	    Rx(0),   Ry(10),  Rw(320), Rh(40),	RAM_ID_MAKE(" "),	 ALIGNMENT_CENTER, 	R_ID_PALETTE_Red,RES_FONT_NUM2),
	//createRect(NESGAMEINNER_UP_RECT_ID,         Rx(100), Ry(60),  Rw(120), Rh(120),  R_ID_PALETTE_Transparent),
	//createRect(NESGAMEINNER_DOWN_RECT_ID,       Rx(100), Ry(80),  Rw(120), Rh(120),  R_ID_PALETTE_Transparent),
	createImageIcon(NESGAMEINNER_LEFT_ID,    	Rx(0),   Ry(100), Rw(40),  Rh(40),  R_ID_ICON_MTLEFT, 	ALIGNMENT_LEFT),
	createImageIcon(NESGAMEINNER_LEFT_ID,    	Rx(280), Ry(100), Rw(40),  Rh(40),  R_ID_ICON_MTRIGHT, 	ALIGNMENT_RIGHT),
	widgetEnd(),
};
/*******************************************************************************
* Function Name  : playAudioSubVolumeShow
* Description    : playAudioSubVolumeShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void nesGameInnerNameShow(winHandle handle)
{
	//char *name = (char*)nes_game_inner_list[task_nesGame_op.nesGame_inner_index].name;
	//uiWinSetResid(winItem(handle,NESGAMEINNER_NAME_ID),RAM_ID_MAKE(name));
}
