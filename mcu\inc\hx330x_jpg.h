/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  HX330X_MJPEG_H
    #define  HX330X_MJPEG_H

enum
{
	JPEG_Q_27 = 0xf,// 1.875
	JPEG_Q_28 = 0xe,// 1.75
	JPEG_Q_31 = 0xD,// 1.625
	JPEG_Q_33 = 0xC,// 1.5
	JPEG_Q_36 = 0xB,// 1.375
	JPEG_Q_40 = 0xA, // 1.25
	JPEG_Q_42 = 0x9,//1.125
	JPEG_Q_50 = 0x0,//0.00	
	JPEG_Q_62 = 0x6,//0.75
	JPEG_Q_75 = 0x4,//0.50
	JPEG_Q_81 = 0x3,//0.375 
	JPEG_Q_90 = 0x2,
	JPEG_Q_95 = 0x1,
	JPEG_Q_AUTO = 0xff,
};


typedef enum
{
	MJPEG_IRQ_OUTPAUSE=0,
	MJPEG_IRQ_INPAUSE,
	MJPEG_IRQ_OUTERR = 3,
	MJPEG_IRQ_OUTFULL = 7,	
	MJPEG_IRQ_OUTLOAD = 4,
	MJPEG_IRQ_ERROR,
	MJPEG_IRQ_HUFFERR,
	MJPEG_IRQ_FRAMEEND = 6,
	MJPEG_IRQ_TINFO2END = 11,
	MJPEG_IRQ_TINFO1END = 10,
	MJPEG_IRQ_TINFO0END = 9,
	
	MJPEG_IRQ_DECODE=16,
	MJPEG_IRQ_ENCODE,
	MJPEG_IRQ_MAX
}MJPEG_IRQ_E;



extern const u32 bic_coef_tab[];
extern const u32 bic_coef_tabL[];
extern const u32 bic_coef_tabR[];
extern const int alpha_table_luma[16];
extern const int belta_table_luma[16];
extern const int c_table_luma[16];
extern const int alpha_table_chroma[16];
extern const int belta_table_chroma[16];
extern const int c_table_chroma[16];

/*******************************************************************************
* Function Name  : hx330x_mjpA_table_init
* Description    : initialize jpeg quility table
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_mjpA_table_init(u8 tab_num);

/*******************************************************************************
* Function Name  : hx330x_mjpB_table_init
* Description    : initialize jpeg B quility table
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_mjpB_table_init(u8 tab_num);

/*******************************************************************************
* Function Name  : hx330x_mjpA_EncodeISRRegister
* Description    : jpegA encode isr register
* Input          : void (*isr)(int flag)
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpA_EncodeISRRegister(void (*isr)(int flag));
/*******************************************************************************
* Function Name  : hx330x_mjpA_EncodeISRRegister
* Description    : jpegA encode isr register
* Input          : void (*isr)(int flag)
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpA_EncodeLcdKickRegister(void (*isr)(u32 linebuf_y, u32 line_uv));
/*******************************************************************************
* Function Name  : hx330x_mjpB_Encode_StartFunc_call
* Description    : 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpA_EncodeLcdKick_Func_call(u32 linebuf_y, u32 line_uv);
/*******************************************************************************
* Function Name  : hx330x_mjpB_Encode_StartFunc_Check
* Description    : hx330x_mjpB_Encode_StartFunc_Check
* Input          : void 
* Output         : none
* Return         : none
*******************************************************************************/
bool hx330x_mjpA_EncodeLcdKick_Func_Check(void);
/*******************************************************************************
* Function Name  : hx330x_mjpB_EncodeISRRegister
* Description    : mjpB encode isr register
* Input          : void (*isr)(int flag)
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_EncodeISRRegister(void (*isr)(int flag));
/*******************************************************************************
* Function Name  : hx330x_mjpA_isr_check
* Description    : check if mjpA isr is register
* Input          : none
* Output         : none
* Return         : bool: true - mjpA isr is regitered, false - mjpA isr is not registered
*******************************************************************************/
bool hx330x_mjpA_isr_check(void);
/*******************************************************************************
* Function Name  : hx330x_mjpB_DecodeISRRegister
* Description    : mjpB decode isr register
* Input          : void (*isr)(int flag)
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_DecodeISRRegister(void (*isr)(int flag));
/*******************************************************************************
* Function Name  : hx330x_mjpB_Encode_StartFunc_Check
* Description    : hx330x_mjpB_Encode_StartFunc_Check
* Input          : void 
* Output         : none
* Return         : none
*******************************************************************************/
bool hx330x_mjpB_Encode_StartFunc_Check(void);
/*******************************************************************************
* Function Name  : hx330x_mjpB_Encode_StartFunc_Reg
* Description    : mjpB encode start func register
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_Encode_StartFunc_Reg(void (*fp_fun)(void));
/*******************************************************************************
* Function Name  : hx330x_mjpB_Encode_StartFunc_call
* Description    : 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_Encode_StartFunc_call(void);
/*******************************************************************************
* Function Name  : hx330x_mjpA_reset
* Description    : mjpA  module reset
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpA_reset(void);
/*******************************************************************************
* Function Name  : hx330x_mjpB_reset
* Description    : mjpB  module reset
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_reset(void);
/*******************************************************************************
* Function Name  : hx330x_mjpA_EncodeSizeSet
* Description    : jpeg encode size set
* Input          : u32 src_w : source width
*                  u32 src_h : source height
*                  u32 tar_w : target width
*                  u32 tar_h : target height
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpA_EncodeSizeSet(u32 src_w,u32 src_h,u32 tar_w,u32 tar_h);
/*******************************************************************************
* Function Name  : hx330x_mjpA_EncodeSizeSet
* Description    : jpeg encode size set
* Input          : u32 src_w : source width
*                  u32 src_h : source height
*                  u32 tar_w : target width
*                  u32 tar_h : target height
* Output         : 
* Return         : none
*******************************************************************************/
void hx330x_mjpA_EncodeSizeSet2(u32 src_w,u32 src_h,u32 tar_w,u32 tar_h, u8 add_mode);
/*******************************************************************************
* Function Name  : hx330x_mjpA_EncodeQuilitySet
* Description    : jpeg encode quility set
* Input          : u8 qulity 
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpA_EncodeQuilitySet(u8 qulity);
/*******************************************************************************
* Function Name  : hx330x_mjpA_EncodeInfoSet
* Description    : jpeg encode info mode set
* Input          : u8 mode 
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpA_EncodeInfoSet(u8 mode);
/*******************************************************************************
* Function Name  : hx330x_mjpA_EncodeBufferSet
* Description    : jpeg encode set buffer
* Input          : u32 saddr : start address
*                  u32 eddr : end address
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpA_EncodeBufferSet(u32 saddr,u32 eaddr);
/*******************************************************************************
* Function Name  : hx330x_mjpA_EncodeLoadAddrGet
* Description    : jpeg encode get load addr
* Input          : u8 mode 
* Output         : none
* Return         : none
*******************************************************************************/
u32 hx330x_mjpA_EncodeLoadAddrGet(void);
/*******************************************************************************
* Function Name  : hx330x_mjpA_EncodeLoadAddrGet
* Description    : jpeg encode get load addr
* Input          : u8 mode 
* Output         : none
* Return         : none
*******************************************************************************/
u32 hx330x_mjpA_EncodeStartAddrGet(void);
/*******************************************************************************
* Function Name  : hx330x_mjpA_Encode_inlinebuf_init
* Description    : mjpA encode linebuf cfg
* Input          : u8 mode 
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpA_Encode_inlinebuf_init(u32 yaddr, u32 uvaddr);
/*******************************************************************************
* Function Name  : hx330x_mjpA_Encode_manual_on
* Description    : pre jpeg encode manual start
* Input          : u8 mode 
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpA_Encode_manual_on(void);
/*******************************************************************************
* Function Name  : hx330x_mjpA_Encode_manual_stop
* Description    : pre jpeg encode manual stop
* Input          : u8 mode 
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpA_Encode_manual_stop(void);
/*******************************************************************************
* Function Name  : hx330x_mjpA_EncodeEnable
* Description    : pre mjp auto mode encode enable
* Input          : u8 ne : 1-enable,0-disable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpA_EncodeEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_mjpegEncodeQadj
* Description    : jpeg encode quality ajust                      
* Input          : u32 jsize : last jpeg size
                      u32 min_size : limited min size
                      u32 max_size: limited max size
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpA_EncodeQadj(u32 jsize,u32 min_size,u32 max_size);
/*******************************************************************************
* Function Name  : hx330x_mjpA_EncodeInit
* Description    : pre jpeg encode initial
* Input          : bool manual : 0: auto mode, 1: manual mode
				   u8 tab_num  : 0: pic encode, 1: video encode, 2: video high qt
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpA_EncodeInit(bool manual, u8 tab_num);
/*******************************************************************************
* Function Name  : hx330x_mjpA_EncodeDriTabGet
* Description    : pre jpeg encode initial
* Input          : bool manual : 0: auto mode, 1: manual mode
				   u8 tab_num  : 0: pic encode, 1: video encode, 2: video high qt
* Output         : none
* Return         : none
*******************************************************************************/
u32* hx330x_mjpA_EncodeDriTabGet(void);
/*******************************************************************************
* Function Name  : hx330x_mjpA_EncodeDriModeSet
* Description    : pre jpeg encode initial
* Input          : bool manual : 0: auto mode, 1: manual mode
				   u8 tab_num  : 0: pic encode, 1: video encode, 2: video high qt
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpA_EncodeDriModeSet(u8 skip);
/*******************************************************************************
* Function Name  : hx330x_cal_jA_1s_size
* Description    : cal mjpA encode size per 1 second
* Input          : none				   
* Output         : none
* Return         : size
*******************************************************************************/
u32 hx330x_cal_jASize(void);
/*******************************************************************************
* Function Name  : hx330x_mjpeg_encode_check
* Description    : check if mjpA encode finish
* Input          : u8 mode 
* Output         : none
* Return         : none
*******************************************************************************/
bool hx330x_mjpA_Encode_check(void);
/*******************************************************************************
* Function Name  : hx330x_mjpA_IRQHandler
* Description    : 
* Input          : none				   
* Output         : none
* Return         : size
*******************************************************************************/
void hx330x_mjpA_IRQHandler(void);
/*******************************************************************************
* Function Name  : hx330x_mjpA_Flag_Clr
* Description    : 
* Input          : none				   
* Output         : none
* Return         : size
*******************************************************************************/
void hx330x_mjpA_Flag_Clr(u32 jflag);
/*******************************************************************************
* Function Name  : hx330x_mjpA_EncodeQuilitySet
* Description    : jpeg encode quility set
* Input          : u8 qulity 
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_EncodeQuilitySet(u8 qulity);
/*******************************************************************************
* Function Name  : hx330x_mjpegEncodeQadj
* Description    : jpeg encode quality ajust                      
* Input          : u32 jsize : last jpeg size
                      u32 min_size : limited min size
                      u32 max_size: limited max size
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_EncodeQadj(u32 jsize,u32 min_size,u32 max_size);
/*******************************************************************************
* Function Name  : hx330x_mjpB_Encodeinit
* Description    : initialize the uart
* Input          : u8 tab_num  : 0: pic encode, 1: video encode, 2: video high qt
* Output         : None
* Return         : None
******************************************************************************/
void hx330x_mjpB_Encodeinit(u8 mod, u8 quality, u32 src_w,  u32 src_h, u32 obj_w, u32 obj_h, u8 tab_num);
/*******************************************************************************
* Function Name  : hx330x_cal_jA_1s_size
* Description    : cal mjpA encode size per 1 second
* Input          : none				   
* Output         : none
* Return         : size
*******************************************************************************/
u32 hx330x_cal_jBSize(void);
/*******************************************************************************
* Function Name  : hx330x_mjpB_Encode_inlinebuf_init
* Description    : mjpB encode linebuf cfg
* Input          : u8 mode 
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_Encode_inlinebuf_init(u32 yaddr, u32 uvaddr);
/*******************************************************************************
* Function Name  : hx330x_mjpB_Encode_output_init
* Description    : mjpB encode linebuf cfg
* Input          : u8 mode 
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_Encode_output_init(u32 start, u32 end);
/*******************************************************************************
* Function Name  : hx330x_mjpB_Encode_manual_stop
* Description    : back jpeg encode manual stop
* Input          : u8 mode 
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_Encode_manual_stop(void);

/*******************************************************************************
* Function Name  : hx330x_mjpB_Encode_manual_start
* Description    : back jpeg encode manual start
* Input          : u8 mode 
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_Encode_manual_start(void);
/*******************************************************************************
* Function Name  : hx330x_mjpA_EncodeLoadAddrGet
* Description    : jpeg encode get load addr
* Input          : u8 mode 
* Output         : none
* Return         : none
*******************************************************************************/
u32 hx330x_mjpB_EncodeLoadAddrGet(void);
/*******************************************************************************
* Function Name  : hx330x_mjpB_as_Encode
* Description    : mjpB flag set as encode
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_as_Encode(void);
/*******************************************************************************
* Function Name  : hx330x_mjpB_DecodeScalerCal
* Description    : jpeg decode scaler caculate
* Input          : u32 src_w : source width
*                  u32 tar_w : source height
* Output         : none
* Return         : none
*******************************************************************************/
u32 hx330x_mjpB_DecodeScalerCal(u32 src_w,u32 tar_w);
/*******************************************************************************
* Function Name  : hx330x_mjpB_DecodeSetSize
* Description    : jpeg decode size set
* Input          : u32 src_w : source width
*                  u32 src_h : source height
*                  u32 tar_w : target width
*                  u32 tar_h : target height
*                  u32 stride: 0:disable stride function
*                             -1:don't change stride
*                              other:target stride,for dma0
* Output         : none
* Return         : bool: true: sucess, false: fail
*******************************************************************************/
bool hx330x_mjpB_DecodeSetSize(u32 src_w,u32 src_h,u32 tar_w,u32 tar_h,u32 stride);
/*******************************************************************************
* Function Name  : hx330x_mjpB_DecodeOutputSet
* Description    : jpeg decode output buffer set
* Input          : u32 yaddr : yaddress
*                  u32 uvaddr: uv address
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_DecodeOutputSet(u32 yaddr,u32 uvaddr);
/*******************************************************************************
* Function Name  : hx330x_mjpB_DecodeInputSet
* Description    : jpeg decode input set
* Input          : u32 saddr : start address
*                  u32 eaddr : end address
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_DecodeInputSet(u32 saddr,u32 eaddr);
/*******************************************************************************
* Function Name  : hx330x_mjpB_DecodeDriSet
* Description    : jpeg decode dri set
* Input          : u32 dri
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_DecodeDriSet(u32 dri);
/*******************************************************************************
* Function Name  : hx330x_mjpB_DecodeCompressSet
* Description    : jpeg decode compress set
* Input          : u32 table : compress value
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_DecodeCompressSet(u32 table);
/*******************************************************************************
* Function Name  : hx330x_mjpB_DecodeInitTable
* Description    : jpeg decode huffman table
* Input          : u32 tab : table address
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_DecodeInitTable(u32 tab);
/*******************************************************************************
* Function Name  : hx330x_mjpB_yuvfmt_set
* Description    : hx330x_mjpB_yuvfmt_set
* Input          : u8 yuvfmt : 0:yuv420,1:yuv422
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_yuvfmt_set(u8 yuvfmt);
/*******************************************************************************
* Function Name  : hx330x_mjpB_DecodeInit
* Description    : jpeg decode initial
* Input          : u8 yuvfmt : 0:yuv420,1:yuv422
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_DecodeInit(u8 yuvfmt);
/*******************************************************************************
* Function Name  : hx330x_mjpB_DecodeDCTimeSet
* Description    : jpeg decode dc time over set
* Input          : u8 en,u32 dctimeout ： ms
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_DecodeDCTimeSet(u8 en, u16 height);
/*******************************************************************************
* Function Name  : hx330x_mjpB_DecodeEnable
* Description    : jpeg decode enable
* Input          : u8 en : 1-enable,0-disable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_DecodeEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_mjpB_DecodeKick
* Description    : jpeg decode kick ， no isr
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_DecodeKick(void);
/*******************************************************************************
* Function Name  : hx330x_mjpB_DecodeStop
* Description    : jpeg decode stop
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_DecodeStop(void);
/*******************************************************************************
* Function Name  : hx330x_mjpB_DecodeQDTCfg
* Description    : jpeg decode config qdt table
* Input          : u8 *qdt[3] : table
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_DecodeQDTCfg(u8 *qdt[2]);
/*******************************************************************************
* Function Name  : hx330x_mjpB_DecodeBusyCheck
* Description    : jpeg decode get status
* Input          : none
* Output         : none
* Return         : s32 : true-busy,false-idle
*******************************************************************************/
bool hx330x_mjpB_DecodeBusyCheck(void);
/*******************************************************************************
* Function Name  : hx330x_mjpB_DecodeCheck
* Description    : jpeg decode error check
* Input          : none
* Output         : none
* Return         : u32 : 0-no error.other-error
*******************************************************************************/
u32 hx330x_mjpB_DecodeCheck(void);
/*******************************************************************************
* Function Name  : hx330x_mjpB_DecodeODma1Cfg
* Description    : config output dma1(no block-scaler),call this function before init jpeg-decoder
*                  hx330x_mjpB_DecodeStop will disable DMA1
* Input          : u32 y_addr
*                  u32 uv_addr
*                  bool dma_en
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_DecodeODma1Cfg(u32 y_addr,u32 uv_addr,bool dma_en);
/*******************************************************************************
* Function Name  : hx330x_mjpB_Decode_check
* Description    : mjpB decode check
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool hx330x_mjpB_Decode_check(void);
/*******************************************************************************
* Function Name  : hx330x_mjpB_Decode_InResume
* Description    : mjpB decode check
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_mjpB_Decode_InResume(void);
/*******************************************************************************
* Function Name  : hx330x_mjpB_Decode_check
* Description    : mjpB decode check
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
int hx330x_mjpB_DecodePacket_check(void);
/*******************************************************************************
* Function Name  : hx330x_mjpegIRQHandler
* Description    : jpeg irq handler
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mjpB_IRQHandler(void);



#endif
