/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuUnlockAllWin.c"
/*******************************************************************************
* Function Name  : getunlockAllResInfor
* Description    : getunlockAllResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : u32: 
*******************************************************************************/
static u32 getunlockAllResInfor(u32 item,u32* image,u32* str)
{
	if(item == 0)
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = R_ID_STR_COM_OK;
	}
	else if(item == 1)
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = R_ID_STR_COM_CANCEL;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockAllKeyMsgOk
* Description    : unlockAllKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockAllKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	int i,cnt;
	char *name;
	INT32S list;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,UNLOCKALL_SELECT_ID));
		if(item == 0)
		{
			if(app_taskCurId()  == TASK_PLAY_VIDEO)
			{
				if(SysCtrl.spi_jpg_list >= 0)
				{
					task_com_tips_show(TIPS_COM_FAIL);
					return 0;
				}
				list = SysCtrl.avi_list;
			}else if(app_taskCurId()  == TASK_PLAY_AUDIO)
			{
				list = SysCtrl.wav_list;
			}else if(app_taskCurId()  == TASK_NES_GAME)
			{
				list = SysCtrl.nes_list;
			}else 
			{
				return 0;
			}
			cnt = filelist_api_CountGet(list);
			if(cnt > 0)
			{
				task_com_tips_show(TIPS_COM_WAITING);
				for(i = 0;i < cnt;i++)
				{
					if(filelist_fnameChecklockByIndex(list,i) > 0) // > 0: lock, 0: AVI and unlock, <0: lock invalid
					{
						name = filelist_GetFileFullNameByIndex(list, i, NULL); 
						hx330x_str_cpy(SysCtrl.file_fullname,name);
						filenode_filefullnameUnlock(name);
						deg_Printf("unlock : %s -> %s.",SysCtrl.file_fullname,name);
						if(f_rename(SysCtrl.file_fullname,name)==FR_OK)  // rename in file system
						{
							deg_Printf("->ok\n");
							filenode_fnameUnlockByIndex(list,i);
						}
						else
							deg_Printf("->fail\n");
					}
				}
				task_com_tips_show(TIPS_COM_SUCCESS);
			}
			else
				task_com_tips_show(TIPS_NO_FILE);
		}
		else
			uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockAllKeyMsgUp
* Description    : unlockAllKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockAllKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,UNLOCKALL_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockAllKeyMsgDown
* Description    : unlockAllKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockAllKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		 uiItemManageNextItem(winItem(handle,UNLOCKALL_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockAllKeyMsgPower
* Description    : unlockAllKeyMsgPower
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockAllKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockAllOpenWin
* Description    : unlockAllOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockAllOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]unlockAllOpenWin\n");
	//uiItemManageSetRowSum(winItem(handle,UNLOCKALL_SELECT_ID),1,Rh(40));
	//uiItemManageSetColumnSumWithGap(winItem(handle,UNLOCKALL_SELECT_ID),0,2,Rw(100), Rw(0));
	//uiItemManageCreateItem(		winItem(handle,UNLOCKALL_SELECT_ID),uiItemCreateMenuOption,getunlockAllResInfor,2);
	
	uiItemManageSetItemHeight(winItem(handle,UNLOCKALL_SELECT_ID),Rh(35));
	uiItemManageCreateItem(		winItem(handle,UNLOCKALL_SELECT_ID),uiItemCreateMenuOption,getunlockAllResInfor,2);

#if 0
	uiItemManageSetCharInfor(	winItem(handle,UNLOCKALL_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_White);
	uiItemManageSetSelectColor(	winItem(handle,UNLOCKALL_SELECT_ID),R_ID_PALETTE_DoderBlue);
	uiItemManageSetUnselectColor(winItem(handle,UNLOCKALL_SELECT_ID),R_ID_PALETTE_Gray);
#else
	uiItemManageSetSelectColorEx(winItem(handle,UNLOCKALL_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER, SMENU_SELECT_FN_COLOR, SMENU_SELECT_BG_COLOR);
	uiItemManageSetUnselectColorEx(winItem(handle,UNLOCKALL_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER, SMENU_UNSELECT_FN_COLOR, SMENU_UNSELECT_BG_COLOR);
#endif


	uiItemManageSetCurItem(		winItem(handle,UNLOCKALL_SELECT_ID),1);
	
	return 0;
}
/*******************************************************************************
* Function Name  : unlockAllCloseWin
* Description    : unlockAllCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockAllCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]unlockAllCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : unlockAllCloseWin
* Description    : unlockAllCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockAllWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]unlockAllWinChildClose\n");
	uiWinDestroy(&handle);
	return 0;
}


ALIGNED(4) msgDealInfor unlockAllMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	unlockAllOpenWin},
	{SYS_CLOSE_WINDOW,	unlockAllCloseWin},
	{SYS_CHILE_COLSE,	unlockAllWinChildClose},
	{KEY_EVENT_OK,		unlockAllKeyMsgOk},
	{KEY_EVENT_UP,		unlockAllKeyMsgUp},
	{KEY_EVENT_DOWN,	unlockAllKeyMsgDown},
	{KEY_EVENT_LEFT,	unlockAllKeyMsgPower},
	//{KEY_EVENT_DOWN,	NULL},
	{KEY_EVENT_POWER,	unlockAllKeyMsgPower},
	{EVENT_MAX,			NULL},
};

WINDOW(unlockAllWindow,unlockAllMsgDeal,unlockAllWin)


