/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#if FUN_AUDIO_RECORD_EN
#include "taskRecordAudioWin.c"

/*******************************************************************************
* Function Name  : audioKeyMsgOk
* Description    : audioKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int audioKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(audioRecordGetStatus() == MEDIA_STAT_START)
			audioRecordStop(0);
		else
			audioRecordStart();
		audioRecTimeShow(handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : audioKeyMsgMode
* Description    : audioKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int audioKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(audioRecordGetStatus() != MEDIA_STAT_START)
		{
			SysCtrl.winChangeEnable = 1;
			app_taskStart(TASK_MAIN,0);
		}
			
	}
	return 0;
}
/*******************************************************************************
* Function Name  : audioRecSysMsgTimeUpdate
* Description    : audioRecSysMsgTimeUpdate
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int audioRecSysMsgTimeUpdate(winHandle handle,u32 parameNum,u32* parame)
{
	audioRecTimeShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsgSD
* Description    : videoSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int audioRecSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL && audioRecordGetStatus() == MEDIA_STAT_START) // sdc out when recording
	{
		audioRecordStop(1);
	}
	audioRecTimeShow(handle);

	task_com_tips_show(TIPS_TYPE_SD);

	return 0;
}
/*******************************************************************************
* Function Name  : audioOpenWin
* Description    : audioOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int audioOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	return 0;
}
/*******************************************************************************
* Function Name  : audioCloseWin
* Description    : audioCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int audioCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	return 0;
}
/*******************************************************************************
* Function Name  : audioWinChildClose
* Description    : audioWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int audioWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	return 0;
}




ALIGNED(4) msgDealInfor recordAudioMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		audioOpenWin},
	{SYS_CLOSE_WINDOW,		audioCloseWin},
	{SYS_CHILE_COLSE,		audioWinChildClose},
	{KEY_EVENT_OK,			audioKeyMsgOk},
	{KEY_EVENT_MODE,		audioKeyMsgMode},
	{SYS_EVENT_TIME_UPDATE,	audioRecSysMsgTimeUpdate},
	{SYS_EVENT_SDC,         audioRecSysMsgSD},
	{EVENT_MAX,NULL},
};

WINDOW(RecordAudioWindow,recordAudioMsgDeal,RecordAudioWin)


#endif