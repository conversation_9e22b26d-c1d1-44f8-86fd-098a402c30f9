/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	//C_MODE_ID ,
	//C_SD_ID,
	//C_BATERRY_ID,
	C_SELECT_ID= 0,
	C_SELECT_LEFT_ID,
	C_SELECT_RIGHT_ID,
	
};

UNUSED ALIGNED(4) const widgetCreateInfor cGameWin[] =
{
	createFrameWin( 							Rx(0),   Ry(0),   Rw(320), Rh(240),R_ID_PALETTE_Transparent,WIN_ABS_POS|WIN_NOT_ZOOM),
	
	createImageIcon(C_SELECT_LEFT_ID,Rx(0),   Ry(100),   Rw(48), Rh(48),R_ID_ICON_MTLEFT,ALIGNMENT_LEFT),
	createImageIcon(C_SELECT_LEFT_ID,Rx(272),   Ry(100),   Rw(48), Rh(48),R_ID_ICON_MTRIGHT,ALIGNMENT_RIGHT),
	
	widgetEnd(),
};
