/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_DAC_H
    #define HX330X_DAC_H

#define  DAC_INT_PEND     	1
#define  DAC_INT_HALF     	2
#define  DAC_INT_EMPTY   	4


#define  DAC_SAMPLE_RATE_8000       10
#define  DAC_SAMPLE_RATE_11025      9
#define  DAC_SAMPLE_RATE_12000      8
#define  DAC_SAMPLE_RATE_16000      6
#define  DAC_SAMPLE_RATE_22050      5
#define  DAC_SAMPLE_RATE_24000      4
#define  DAC_SAMPLE_RATE_32000      2
#define  DAC_SAMPLE_RATE_44100      1
#define  DAC_SAMPLE_RATE_48000      0


typedef enum
{
	HP_VDD_2_6V=0,
	HP_VDD_2_7V,
	HP_VDD_2_8V,
	HP_VDD_2_9V,
	HP_VDD_3_0V,
	HP_VDD_3_1V,
	HP_VDD_3_2V,
	HP_VDD_3_3V
}HP_VDD_E;
#define G_14DB		642
#define G_13DB		572
#define G_12DB		510
#define G_11DB		454

#define G_10DB		405
#define G_9DB		361
#define G_8DB		322
#define G_7DB		287
#define G_6DB		255
#define G_5DB		228
#define G_4DB		203
#define G_3DB		81 
#define G_2DB		161
#define G_1DB		144
#define G_0DB		128
#define G_M1DB      114
#define G_M2DB      102
#define G_M3DB      91 
#define G_M4DB		80 
#define G_M5DB		72 
#define G_M6DB		64 
#define G_M7DB		57 
#define G_M8DB		51 
#define G_M9DB		45 
#define G_M10DB     40 
#define G_M11DB     36 
#define G_M12DB     32 
#define G_M13DB     29 
#define G_M14DB     26 
#define G_M15DB     23 
#define G_M16DB     20 






//------------------------------------------------------------------------------
// DAC Internal SFR Address Defines              
//------------------------------------------------------------------------------
#define SFR_DAC_SIR_CON                 0
#define SFR_DAC_SIR_STATUS                 1
#define SFR_DAC_SIR_PND                 2
#define SFR_DAC_SIR_VOL                 3
#define SFR_DAC_SIR_VOLCON                4
#define SFR_DAC_SIR_TRIM               5
#define SFR_DAC_SIR_TRIML_DATA                 6
#define SFR_DAC_SIR_TRIMR_DATA                 7
#define SFR_DAC_SIR_MIX_CON0              8
#define SFR_DAC_SIR_MIX_CON1              9
#define SFR_DAC_SIR_EQGAINTAB0             10
#define SFR_DAC_SIR_EQGAINTAB1             11
#define SFR_DAC_SIR_EQGAINTAB2             12
#define SFR_DAC_SIR_EQGAINTAB3             13
#define SFR_DAC_SIR_EQGAINTAB4             14
#define SFR_DAC_SIR_EQTABCFGADDR             15
#define SFR_DAC_SIR_EQTABCFGVALUE                16
/*******************************************************************************
* Function Name  : hx330x_dacIRQHandler
* Description    : dac irq handler
* Input          :  
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dacIRQHandler(void);
/*******************************************************************************
* Function Name  : hx330x_dacInit
* Description    : initial dac
* Input          :  u8 src: 0: upll, 1: syspll
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dacInit(u8 src);
/*******************************************************************************
* Function Name  : hx330x_dacTypeCfg
* Description    : hx330x_dacTypeCfg: hx330x_dacInit后，hx330x_dacStart 前
* Input          : u8 type: 0: class D, 1: class AB
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dacTypeCfg(u8 type);
/*******************************************************************************
* Function Name  : hx330x_dacSampleRateSet
* Description    : set dac samlerate
* Input          :  u32 sample_rate : sample rate
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dacSampleRateSet(u32 sample_rate);
/*******************************************************************************
* Function Name  : hx330x_dacEnable
* Description    : enable dac
* Input          :  u8 en
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dacEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_dacVolumeSet
* Description    : set dac volume
* Input          :  u16 volume : volume
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dacVolumeSet(u16 volume);
/*******************************************************************************
* Function Name  : hx330x_dacBufferSet
* Description    : set dac play buffer
* Input          : u32 obuf_addr : out buffer addr
				   u32 obuf_size  : out buffer size
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dacBufferSet(u32 obuf_addr,u32 obuf_size);
/*******************************************************************************
* Function Name  : hx330x_dacBufferFlush
* Description    : dac play reset
* Input          :  
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dacBufferFlush(u32 obuf_size);
/*******************************************************************************
* Function Name  : hx330x_dacReset
* Description    : dac play reset
* Input          :  
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dacReset(void);
/*******************************************************************************
* Function Name  : hx330x_dacStart
* Description    : dac play start
* Input          :  
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dacStart(u32 obuf_addr,u32 obuf_size);
/*******************************************************************************
* Function Name  : hx330x_dacStop
* Description    : dac play stop
* Input          :  
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dacStop(void);
/*******************************************************************************
* Function Name  : hx330x_check_dacstop
* Description    : hx330x_check_dacstop
* Input          : None
* Output         : None
* Return         : none
*******************************************************************************/
//SDRAM_TEXT_SECTION
void hx330x_check_dacstop(void);
/*******************************************************************************
* Function Name  : hx330x_dacISRRegister
* Description    : register irq service
* Input          :  void (*isr)(u8 flag)
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dacISRRegister(void (*isr)(u8 flag));
/*******************************************************************************
* Function Name  : hx330x_dacHPSet
* Description    : dac HP VDD output set
* Input          : u8 en : enable.1->enable,0-disable
				   u32 level : vdd level.SEE HP_VDD_E
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dacHPSet(u8 en,u32 level);
/*******************************************************************************
* Function Name  : eq_coeff_init
* Description    : eq_coeff_init
* Input          : None
* Output         : None
* Return         : none
*******************************************************************************/
void eq_coeff_init(void);
/*******************************************************************************
* Function Name  : eq_gain_init
* Description    : eq_gain_init
* Input          : None
* Output         : None
* Return         : none
*******************************************************************************/
void eq_gain_init(void);

#endif
