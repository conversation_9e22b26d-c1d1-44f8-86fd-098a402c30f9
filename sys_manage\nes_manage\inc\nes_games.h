/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef NES_GAMES_H
#define NES_GAMES_H

/*******************************************************************************
* Function Name  : nes_games_open
* Description    : The main loop of InfoNES
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
int nes_games_open(int fd, u32 src_type);
/*******************************************************************************
* Function Name  : nes_games_read
* Description    : The main loop of InfoNES
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
int nes_games_read(int fd,void *buff,UINT len);

/*******************************************************************************
* Function Name  : nes_games_service
* Description    : nes_games_service
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
//void nes_games_service(void);
#endif
