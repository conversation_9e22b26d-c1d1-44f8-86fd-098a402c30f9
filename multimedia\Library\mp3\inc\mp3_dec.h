/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  MP3_DEC_H
       #define  MP3_DEC_H

/*****************MP3 TAG***************************/
#define MAKE_TAG(a,b,c,d) 			((a<<24)|(b<<16)|(c<<8)|(d))
#define MP3_TAG_AENC				MAKE_TAG('A','E','N','C')	//Audioencryption
#define MP3_TAG_APIC				MAKE_TAG('A','P','I','C')	//Attached picture
#define MP3_TAG_COMM				MAKE_TAG('C','O','M','M')	//Comments
#define MP3_TAG_COMR				MAKE_TAG('C','O','M','R')	//Commercial
#define MP3_TAG_ENCR				MAKE_TAG('E','N','C','R')	//Encryptionmethod registration
#define MP3_TAG_EQUA				MAKE_TAG('E','Q','U','A')	//Equalization
#define MP3_TAG_ETCO				MAKE_TAG('E','T','C','O')	//Event timingcodes
#define MP3_TAG_GEOB				MAKE_TAG('G','E','O','B')	//Generalencapsulated object
#define MP3_TAG_GRID				MAKE_TAG('G','R','I','D')	//Groupidentification registration
#define MP3_TAG_IPLS				MAKE_TAG('I','P','L','S')	//Involvedpeople list
#define MP3_TAG_LINK				MAKE_TAG('L','I','N','K')	//Linkedinformation
#define MP3_TAG_MCDI				MAKE_TAG('M','C','D','I')	//Music CDidentifier
#define MP3_TAG_MLLT				MAKE_TAG('M','L','L','T')	//MPEGlocationlookup table
#define MP3_TAG_OWNE				MAKE_TAG('O','W','N','E')	//Ownership
#define MP3_TAG_PRIV				MAKE_TAG('P','R','I','V')	//Private
#define MP3_TAG_PCNT				MAKE_TAG('P','C','N','T')	//Playcounter
#define MP3_TAG_POPM				MAKE_TAG('P','O','P','M')	//Popularimeter
#define MP3_TAG_POSS				MAKE_TAG('P','O','S','S')	//Positionsynchronisation
#define MP3_TAG_RBUF				MAKE_TAG('R','B','U','F')	//Recommendedbuffer size
#define MP3_TAG_RVAD				MAKE_TAG('R','V','A','D')	//Relativevolume adjustment
#define MP3_TAG_RVRB				MAKE_TAG('R','V','R','B')	//RVRB：Reverb
#define MP3_TAG_SYLT				MAKE_TAG('S','Y','L','T')	//SYLT：Synchronizedlyric/text
#define MP3_TAG_SYTC				MAKE_TAG('S','Y','T','C')	//SYTC：Synchronizedtempo codes
#define MP3_TAG_TALB				MAKE_TAG('T','A','L','B')	//TALB：Album/Movie/Showtitle
#define MP3_TAG_TBPM				MAKE_TAG('T','B','P','M')	//TBPM：BPM(beats perminute)
#define MP3_TAG_TCOM				MAKE_TAG('T','C','O','M')	//TCOM：Composer
#define MP3_TAG_TCON				MAKE_TAG('T','C','O','N')	//TCON：Content type
#define MP3_TAG_TCOP				MAKE_TAG('T','C','O','P')	//TCOP：Copyrightmessage
#define MP3_TAG_TDAT				MAKE_TAG('T','D','A','T')	//TDAT：Date
#define MP3_TAG_TDLY				MAKE_TAG('T','D','L','Y')	//TDLY：Playlistdelay
#define MP3_TAG_TENC				MAKE_TAG('T','E','N','C')	//TENC：Encoded by
#define MP3_TAG_TEXT				MAKE_TAG('T','E','X','T')	//TEXT：Lyricist/Textwriter
#define MP3_TAG_TFLT				MAKE_TAG('T','F','L','T')	//TFLT：Filetype
#define MP3_TAG_TIME				MAKE_TAG('T','I','M','E')	//TIME：Time
#define MP3_TAG_TIT1				MAKE_TAG('T','I','T','1')	//TIT1：Content groupdeion
#define MP3_TAG_TIT2				MAKE_TAG('T','I','T','2')	//TIT2：Title/songname/contentdeion
#define MP3_TAG_TIT3				MAKE_TAG('T','I','T','3')	//TIT3：Subtitle/Deionrefinement
#define MP3_TAG_TKEY				MAKE_TAG('T','K','E','Y')	//TKEY：Initial key
#define MP3_TAG_TLAN				MAKE_TAG('T','L','A','N')	//TLAN：Language(s)
#define MP3_TAG_TLEN				MAKE_TAG('T','L','E','N')	//TLEN：Length
#define MP3_TAG_TMED				MAKE_TAG('T','M','E','D')	//TMED：Media type
#define MP3_TAG_TOAL				MAKE_TAG('T','O','A','L')	//TOAL：Originalalbum/movie/show title
#define MP3_TAG_TOFN				MAKE_TAG('T','O','F','N')	//TOFN：Originalfilename
#define MP3_TAG_TOLY				MAKE_TAG('T','O','L','Y')	//TOLY：Originallyricist(s)/text writer(s)
#define MP3_TAG_TOPE				MAKE_TAG('T','O','P','E')	//TOPE：Originalartist(s)/performer(s)
#define MP3_TAG_TORY				MAKE_TAG('T','O','R','Y')	//TORY：Originalrelease year
#define MP3_TAG_TOWN				MAKE_TAG('T','O','W','N')	//TOWN：Fileowner/licensee
#define MP3_TAG_TPE1				MAKE_TAG('T','P','E','1')	//TPE1：Leadperformer(s)/Soloist(s)
#define MP3_TAG_TPE2				MAKE_TAG('T','P','E','2')	//TPE2：Band/orchestra/accompaniment
#define MP3_TAG_TPE3				MAKE_TAG('T','P','E','3')	//TPE3：Conductor/performerrefinement
#define MP3_TAG_TPE4				MAKE_TAG('T','P','E','4')	//TPE4：Interpreted,remixed, or otherwise modified by
#define MP3_TAG_TPOS				MAKE_TAG('T','P','O','S')	//TPOS：Partof a set
#define MP3_TAG_TPUB				MAKE_TAG('T','P','U','B')	//TPUB：Publisher
#define MP3_TAG_TRCK				MAKE_TAG('T','R','C','K')	//TRCK：Tracknumber/Position in set
#define MP3_TAG_TRDA				MAKE_TAG('T','R','D','A')	//TRDA：Recordingdates
#define MP3_TAG_TRSN				MAKE_TAG('T','R','S','N')	//TRSN：Internetradio station name
#define MP3_TAG_TRSO				MAKE_TAG('T','R','S','O')	//TRSO：Internetradio station owner
#define MP3_TAG_TSIZ				MAKE_TAG('T','S','I','Z')	//TSIZ：Size
#define MP3_TAG_TSRC				MAKE_TAG('T','S','R','C')	//TSRC：ISRC(internationalstandard recording code)
#define MP3_TAG_TSSE				MAKE_TAG('T','S','S','E')	//TSSE：Software/Hardwareand settings used for encoding
#define MP3_TAG_TYER				MAKE_TAG('T','Y','E','R')	//TYER：Year
#define MP3_TAG_TXXX				MAKE_TAG('T','X','X','X')	//TXXX：Userdefinedtext information
#define MP3_TAG_UFID				MAKE_TAG('U','F','I','D')	//UFID：Unique fileidentifier
#define MP3_TAG_USER				MAKE_TAG('U','S','E','R')	//USER：Terms of use
#define MP3_TAG_USLT				MAKE_TAG('U','S','L','T')	//USLT：Unsychronizedlyric/text tranion
#define MP3_TAG_WCOM				MAKE_TAG('W','C','O','M')	//WCOM：Commercialinformation
#define MP3_TAG_WCOP				MAKE_TAG('W','C','O','P')	//WCOP：Copyright/Legalinformation
#define MP3_TAG_WOAF				MAKE_TAG('W','O','A','F')	//WOAF：Officialaudio file webpage
#define MP3_TAG_WOAR				MAKE_TAG('W','O','A','R')	//WOAR：Officialartist/performer webpage
#define MP3_TAG_WOAS				MAKE_TAG('W','O','A','S')	//WOAS：Officialaudio source webpage
#define MP3_TAG_WORS				MAKE_TAG('W','O','R','S')	//WORS：Officialinternet radio station homepage
#define MP3_TAG_WPAY				MAKE_TAG('W','P','A','Y')	//WPAY：Payment
#define MP3_TAG_WPUB				MAKE_TAG('W','P','U','B')	//WPUB：Publishersofficial webpage
#define MP3_TAG_WXXX				MAKE_TAG('W','X','X','X')	//WXXX：UserdefinedURL link

/*****************MP3 HEADER***************************/

typedef struct MP3_HEADER_TAG_S{
	char Header[3];    /*必须为“ID3”否则认为标签不存在*/
	char Ver;         /*版本号ID3V2.3 就记录3*/
	char Revision;     /*副版本号此版本记录为0*/
	char Flag;        /*标志字节，只使用高三位，其它位为0 */
	char Size[4];      /*标签大小*/
}MP3_HEADER_TAG;
#define ID3v2_HEADER_SIZE 			sizeof(MP3_HEADER_TAG)

typedef enum{
	FRAME_FLAG_TAG_PROTECT 		= (1 << 15),
	FRAME_FLAG_FILE_PROTECT 	= (1 << 14),
	FRAME_FLAG_READONLY 		= (1 << 13),
	FRAME_FLAG_COMPRESS 		= (1 <<	7),
	FRAME_FLAG_ENCYPT 			= (1 << 6),
	FRAME_FLAG_GROUP 			= (1 << 5),
}FRAME_TAG_FLAG;
typedef struct MP3_FRAME_TAG_S{
	u32  id; 	/*标识帧，说明其内容，例如作者/标题等*/
	u32  size; 	/*帧内容的大小，不包括帧头，不得小于1, 低地址存高bytes*/
	u16  Flags; //标志帧，使用每个字节的高三位，其他位均为0(abc00000B xyz00000B)
				//a -- 标签保护标志，设置时认为此帧作废
				//b -- 文件保护标志，设置时认为此帧作废
				//c -- 只读标志，设置时认为此帧不能修改
				//x -- 压缩标志，设置时一个字节存放两个BCD 码表示数字
				//y-- 加密标志
				//z-- 组标志，设置时说明此帧和其他的某帧是一组
}MP3_FRAME_TAG;

typedef enum{
	MP3_DEC_UINT = 0,	
	MP3_DEC_STOP,
	MP3_DEC_START,
	MP3_DEC_WAITSTOP,
}MP3_DEC_STA;	


typedef struct MP3_RAM_S{
	u8 bsio_byte_buf[MP3_BYTE_PEEK_SIZE + MP3_BYTE_NUM*MP3_BYTE_MIN];
	u8 bsio_bit_buf[MP3_BIT_SIZE];
	u8 allocation[2][32];
	u8 scfsi[2][32];
	u8 scalefactor[2][32][3];
	mad_fixed_t xr[2][576];
	mad_fixed_t mp3_tmp_data[576];	
	mad_fixed_t III_main_overlap[2 * 32 * 18*2];
	mad_fixed_t output[36];
}MP3_RAM_T;
typedef struct MP3_DEC_BUF_S
{
	u8 *buf;
	u32 len;
	u32 combo_index;
	u8  *cur_buf;
}MP3_DEC_BUF_T;
	
typedef struct MP3_DEC_CTRL_S 
{
	MP3_BSIO_BYTE_T byte_op;
	MP3_BSIO_BIT_T 	head_bit_op;
	MP3_BSIO_BIT_T	frame_bit_op;	
	MP3_DEC_BUF_T 	pcm;
	MP3_DEC_BUF_T 	tit2;
	MP3_DEC_BUF_T 	tpe1;
	MP3_DEC_BUF_T 	talb;
	MP3_DEC_BUF_T 	apic;
	MP3_FRAME_T	   	frame;
	MP3_SYNTH_T		synth;
} MP3_DEC_CTRL_T;
/*******************************************************************************
* Function Name  : mp3_id3v2_parse
* Description    : mp3_id3v2_parse: 
* Input          : u8 *buf
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
void mp3_dec_stop(void);
/*******************************************************************************
* Function Name  : mp3_id3v2_parse
* Description    : mp3_id3v2_parse: 
* Input          : u8 *buf
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
int mp3_dec_frame(void);
/*******************************************************************************
* Function Name  : mp3_id3v2_parse
* Description    : mp3_id3v2_parse: 
* Input          : u8 *buf
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
int mp3_dec_start(int fd);

#endif
