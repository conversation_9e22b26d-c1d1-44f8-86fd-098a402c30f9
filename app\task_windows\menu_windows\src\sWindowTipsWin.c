/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	TIP_STRING_ID=0,
	TIP_RECT_ID,
};
UNUSED ALIGNED(4) const widgetCreateInfor tipsWin[] =
{
	createFrameWin(					Rx(78),	Ry(98), Rw(164),Rh(44),	R_ID_PALETTE_Yellow,	WIN_ABS_POS),
	createRect(TIP_RECT_ID,         Rx(2),	<PERSON>y(2),  <PERSON>w(160),<PERSON>h(40), R_ID_PALETTE_LightGreen),
	createStringIcon(TIP_STRING_ID,	Rx(2),	<PERSON>y(2), 	Rw(160),Rh(40),	" ",ALIGNMENT_CENTER, R_ID_PALETTE_Yellow,DEFAULT_FONT),
	widgetEnd(),
};
