/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskGameFighterWin.c"

#define  PLANE_W   20
#define  PLANE_H   24//22
INT display  ; //记录被子弹击中的敌机编号
typedef struct
{
    int x;
    int y;
} BULLET;

typedef struct
{
    int x;
    int y;
    int speed;
    int life;
    BULLET bullet[10]; //子弹数量
    int num;           //子弹计数
} PLANE;

typedef struct
{
    int x;
    int y;
    int life;
	int colortype;
    int move_step;
} ENEMY;

typedef struct
{
    int num;              //敌机�?�?
    int produce_interval; //敌机产生间隔
    int produce_step;
    int move_interval; //控制敌机移动
} ENEMY_DATA;

static INT8U curx,cury;
//INT8U g_liu=0; //推�?�子关卡

//static INT8U game_continue;
static INT8U game_pause;

// static INT8U game_Level;
static u8* gFighter_temp_icons = NULL;
static u8* gFighter_buffer_string = NULL;

extern INT8U game_over_in;

extern u8 *game_frame_buff;

extern u16 game_frame_w;
extern u16 game_frame_h;

//	int a[30][120];
	
static	int score;	//分数
//	
//	int escape; //�?击落的敌机数
	
	PLANE plane;
	
	ENEMY_DATA en_da;
	
	ENEMY enemy[10];



#if 1//defined(  ICON_SAVE_DEBUG)
	//---------------------------------game--------------------------------------

#endif

//随机数字
//void randomdata()
//{
//	int r,c,x;
//	x = (rand()%2) *2 + 2;
//	do
//	{
//		r = rand()%N;
//	    c = rand()%N;
//	}while(grid[r][c]!=0);
//	grid[r][c]=x;
//	deg_Printf("x:%d,y:%d,value:%d\n",r,c,x);
//
//}
//void zeroDataArray(void)
//{
//    int i,j;
//	for(i=0;i<N;i++)
//	for(j=0;j<N;j++)
//		grid[i][j] = 0;
//}
void create_enemy() //创建敌机
{
    INT16U tft_w = game_frame_w;
    INT16U tft_h = game_frame_h;
	deg_Printf("create_enemy---\n");
    enemy[en_da.num].x = 5;

//    do
//    {
        enemy[en_da.num].y = rand() % (tft_h-PLANE_H);
	enemy[en_da.num].colortype = rand() % 3;


//    } while (enemy[en_da.num].x == 0 || enemy[en_da.num].x == WIGHT); //防�?�生成的敌机位于边界

    enemy[en_da.num].life = 5;//1~4显示坠毁�?5显示飞机，且能移动�?0表示消失�?
    if (en_da.num == 9)
        en_da.num = 0;
    else
        en_da.num++;
}


void initialize_fighter_data() //初�?�化数据
{
    INT16U tft_w = game_frame_w;
    INT16U tft_h = game_frame_h;
	int i ;
	
    plane.life = 3;
    plane.speed = 1;
    plane.x = tft_w - PLANE_W;//200
    plane.y = PLANE_H * 4;

    for ( i = 0; i < 10; i++)  //子弹
    {
        plane.bullet[i].x = (tft_w - PLANE_W);
        plane.bullet[i].y = 0;
    }

    en_da.produce_step = 0;
    en_da.produce_interval = 15;//控制敌机的数�?
    en_da.num = 0;

    for ( i = 0; i < 10; i++) //初�?�化敌机
    {
        enemy[i].life = 0;
        enemy[i].move_step = 0;
        enemy[i].x = 0;
    }

    en_da.move_interval = 1;
    plane.num = 0;
    score = 0;
//    escape = 0;
    create_enemy();

}

void InitFighterGame(void)
{
    INT16U tft_w = game_frame_w;
    INT16U tft_h = game_frame_h;
	game_over_in=0;
	game_pause = 0;


	 
	//g_liu= 1;//ap_state_config_Fighter_get();
	//memset(game_frame_buff,R_ID_PALETTE_Black,tft_w*tft_h);
	SysCtrl.gui_flush_sta = 0;

	initialize_fighter_data(); //初�?�化数据

	gFighter_draw_map(game_frame_buff);
	app_draw_Service(1);
}

//void pass(u8 * buf_show,u32 line)
//{
//	INT8U i,j;
//	game_continue=1;
//    deg_Printf("pass---->%d\r\n",line);
//
//	for(i=0;i<12;i++){
//		if(game_continue==0)
//			break;
//		for(j=0;j<12;j++){
//			if(level[g_liu][j][i]==3||level[g_liu][j][i]==7){
//				if(level_temp[i][j]==7){
//					game_continue=1;
//				}else{
//					game_continue=0;
//					break;
//				}
//			}
//		}
//	}
//	if(game_continue==1){
//		if(g_liu < 9){
//			g_liu += 1;
//			game_Level=0;
//			//show_game_state(0);
//		}else{
//			g_liu=0;
//			game_Level=1;
//			//show_game_state(1);
//		}
//        game_over_in=1;
//        //ap_state_config_Fighter_set(g_liu);
//        //Fighter();
//
//        //show_game_state(val);
//        //XOSTimeDly(80);
//
//        //Fighter_draw_map(buf_show);
//
//        //LCD_WriteNumlen(126,280,g+1,2,RED,BLACK,1);
//        //LCD_ShowxNum1(111,304,g_liu+1,2,16,0,rgb(20,106,222),RED);
//	}
//}

static void game_over(winHandle handle,INT8U* display_buf)
{
	ENTER();
	//INT32U	buf_show= setting_frame_show->base + setting_frame_show->offset;
//	uiWinSetResid(winItem(handle,GFighter_TIPS_STR_TIP),"game over");
	//game_draw_str( display_buf,STR_60HZ);
	game_over_in=1;
	//uiWinDestroy(&handle);
	//msgQSend(ApQ, MSG_APQ_MODE, NULL, 0, MSG_PRI_NORMAL);
	//gFighter_draw_map(display_buf);
}

//void image_darwbg(INT8U *buf_show,INT8U* icon_stream,INT8U cow,INT8U col,INT16U w,INT16U h)
//{
//#if 1
//	DISPLAY_ICONSHOW	icon;
//	INT16U tft_w = game_frame_w;
//	INT16U tft_h = game_frame_h;
//	INT16U x,y;
//	x =w*cow+w;
//	y= h*col+h;
//
//	if((x>=tft_w)||(y>=tft_h)){
//		return;
//	}
//
//	if(g_liu==0){
//		x+=w;
//    }else if(g_liu<7){
//		x+=w*3;
//	}
//	#if 0
//	icon.transparent=0x87c1;
//	icon.icon_w=w;
//	icon.icon_h=h;
//	icon.pos_x=x;
//	icon.pos_y=y;
//	ap_setting_special_icon_draw(buf_show, icon_stream, &icon);
//	//OSQPost(DisplayTaskQ, (void *) (MSG_DISPLAY_TASK_MJPEG_DRAW | (INT32U)display_frame));
//	#else
//	OSDFrameDrawIcon(buf_show,icon_stream,x,y,w,h);
//	#endif
//#else
//	OSDFrameDrawIcon(buf_show,icon_stream,cow,col,w,h);
//#endif
//}

#define  BASE_X   32
#define  BASE_Y   32

//static void image_draw_picture(INT8U *buf_show,INT8U* icon_stream,INT8U cow,INT8U col,INT16U w,INT16U h)
//{
//#if 1
//	INT16U x,y;
//	INT16U tft_w = game_frame_w;
//	INT16U tft_h = game_frame_h;
//
//    x =w*cow+BASE_X;
//    y= h*col+BASE_Y;
//    if((x>=tft_w)||(y>=tft_h)){
//        return;
//    }
////	if(g_liu==0){
////		x+=w;
////	}else if(g_liu<7){
////		x+=w*3;
////	}
//	//deg_Printf("x:%d  y:%d\r\n",x,y);
//	#if 0
//	DISPLAY_ICONSHOW	icon;
//	icon.transparent=0x87c1;
//	icon.icon_w=w;
//	icon.icon_h=h;
//	icon.pos_x=x;
//	icon.pos_y=y;
//	ap_display_icon_draw(buf_show, icon_stream, &icon);
//	//ap_setting_icon_draw(buf_show,icon_stream, &icon, SETTING_ICON_NORMAL_DRAW);
//	#else
//	OSDFrameDrawIcon(buf_show,icon_stream,x,y,w,h);
//	#endif
//#else
//	OSDFrameDrawIcon(buf_show,icon_stream,cow,col,w,h);
//#endif
//}





void gFighter_draw_map(INT8U * buf_show)
{
    INT8U i,j,iconIndex;
    INT16U tft_w = game_frame_w;
    INT16U tft_h = game_frame_h;
	static INT8U socre_buf[5];
//    curx=0;
//    cury=0;
    //deg_Printf("gFighter_draw_map\r\n");
	//0 空地 1 ??2背景 3 �?�???4 箱子 5 ??
	g_Draw_Fill(buf_show,0,0,tft_w-1,tft_h-1,R_ID_PALETTE_White);
//飞机
	OSDFrameDrawIcon(buf_show,gameFighter_buff[GAME_FIGHTER].ptr,plane.x ,plane.y,PLANE_W,PLANE_H);

    //子弹刷新
    for ( i = 0; i < 10; i++)
    {
        if (plane.bullet[i].x<plane.x) //子弹
        {
			OSDFrameDrawIcon(buf_show,gameFighter_buff[GAME_BUTTLE].ptr,plane.bullet[i].x ,plane.bullet[i].y,PLANE_W,PLANE_H);
        }
    }

//敌机
    for ( i = 0; i <= 10; i++)
    {
        if (enemy[i].life == 5)
        {
			OSDFrameDrawIcon(buf_show,gameFighter_buff[GAME_ENEMY1+enemy[i].colortype].ptr,enemy[i].x ,enemy[i].y,PLANE_W,PLANE_H);
        }
		else if((enemy[i].life < 5)&&(enemy[i].life > 0)){
			OSDFrameDrawIcon(buf_show,gameFighter_buff[GAME_FALL].ptr,enemy[i].x ,enemy[i].y,PLANE_W,PLANE_H);
			enemy[display].x = 500;
			enemy[display].y = 500;
			display = -1;
		}
    }
    socre_buf[0]=score/1000+0x30;
    socre_buf[1]=score/100%10+0x30;
    socre_buf[2]=score/10%10+0x30;
	socre_buf[3]=score%10+0x30;
    socre_buf[4]='\0';    
	g_draw_str(game_frame_buff,250,12,socre_buf,RES_FONT_NUM1,R_ID_PALETTE_Red,R_ID_PALETTE_Blue);	

	if(game_over_in==1)
	{
		//g_draw_str_default_style_in_screen_center(game_frame_buff,"game over!",RES_FONT_NUM1);
		OSDFrameDrawIcon(game_frame_buff,gameFighter_buff[GAME_ENEMY1+6].ptr ,50,80,220,80);
		g_draw_resstr_default_style_in_screen_center(game_frame_buff,R_ID_STR_GAME_OVER,RES_FONT_NUM0);
	}
}

int  BulletInteval;
void update() //更新数据
{
    INT16U tft_w = game_frame_w;
    INT16U tft_h = game_frame_h;
	int i,j;
	
	//deg_Printf("update---\n");
    if (en_da.produce_step == en_da.produce_interval) //产生敌机
    {
        create_enemy();
        en_da.produce_step = 0;
		en_da.produce_interval = en_da.produce_interval ;//- score/100;
    }
    else
        en_da.produce_step++;


    //子弹位置刷新

    for ( i = 0; i < 10; i++)
    {
        if ((plane.bullet[i].x >= 0)&&(plane.bullet[i].x<(tft_w - PLANE_W))) //子弹移动
        {
	        plane.bullet[i].x -= PLANE_W;
			if(plane.bullet[i].x<0)
				plane.bullet[i].x = (tft_w - PLANE_W);//子弹消失
        }
    }

	BulletInteval++;
	if(BulletInteval%2 == 0)
	{
		plane.bullet[plane.num].y = plane.y;	
		plane.bullet[plane.num].x = plane.x - PLANE_W;
		plane.num++;	
		if (plane.num == 10)	
			plane.num = 0;
	}

	

    //敌机位置刷新
    for ( i = 0; i <= 10; i++)
    {
        if (enemy[i].x <= (tft_w-PLANE_W) && enemy[i].life == 5)
        {
            if (enemy[i].move_step == en_da.move_interval)
            {
                enemy[i].x += 10;
                enemy[i].move_step = 0;
				if(enemy[i].y +PLANE_H*2 <=plane.y)
					enemy[i].y += 2;				
				else if(enemy[i].y<plane.y)
					enemy[i].y++;
				else if(enemy[i].y>=(plane.y+PLANE_H*2))
					enemy[i].y -=2;
				else if(enemy[i].y>plane.y)
					enemy[i].y--;
            }
            else
                enemy[i].move_step++;

			
        }
		else if((enemy[i].life < 5)&&(enemy[i].life > 0))
			enemy[i].life--;
    }

    //碰到飞机或是敌机越界后�?�机减�?�

    for ( i = 0; i <= 10; i++)
    {
        if (enemy[i].x > (tft_w-PLANE_W)) //越界
        {
            enemy[i].x = 0;//敌机消失
            enemy[i].life = 0;
        }

        //碰到飞机
//        if ((enemy[i].x == (plane.x- PLANE_W) && enemy[i].y == plane.y) ||
//            ((enemy[i].x == plane.x - PLANE_W/2) && ((enemy[i].y+PLANE_H) > plane.y) &&((enemy[i].y) < (plane.y+PLANE_H))))
        if ((enemy[i].x >= (plane.x- PLANE_W)) &&   (enemy[i].x <= plane.x - PLANE_W/2) 
            && ((enemy[i].y+PLANE_H) > plane.y) &&(enemy[i].y < (plane.y+PLANE_H)))
        {
			game_over_in=1;
            plane.life = 0;
            enemy[i].life = 0;
			OSDFrameDrawIcon(game_frame_buff,gameFighter_buff[GAME_ENEMY1+6].ptr ,50,80,220,80);
			g_draw_resstr_default_style_in_screen_center(game_frame_buff,R_ID_STR_GAME_OVER,RES_FONT_NUM0);
        }
    }

    for ( i = 0; i < 10; i++)//子弹数量
    {
        for ( j = 0; j <= 10; j++)//敌机数量
        {
			if(plane.bullet[i].x<(tft_w - PLANE_W)){
				if ((enemy[j].x >= (plane.bullet[i].x- PLANE_W)) &&   (enemy[j].x <= plane.bullet[i].x - PLANE_W/2) 
					&& ((enemy[j].y+PLANE_H/2) > plane.bullet[i].y) &&(enemy[j].y < (plane.bullet[i].y+PLANE_H/2))
					&& enemy[j].life == 5)
	            {
	                enemy[j].life = 4;//1~4显示坠毁�?5显示飞机，且能移动�?0表示消失�?
					display = j;
	                plane.bullet[i].x = plane.x;//子弹消失
	                score ++;
					res_music_start(R_ID_MUSIC_3660,0,task_com_curVolume_get());
	            }
			}

        }

    }
}










#if 0
void game_draw_str(INT16U* display_buf,INT16U str_idx)
{
	STRING_INFO 			str = { 0 };
	DISPLAY_ICONSHOW		icon;
	t_STRING_TABLE_STRUCT	str_res;
	INT16U tft_w = game_frame_w;
	INT16U tft_h = game_frame_h;


    str.font_type = 0;
	str.str_idx = str_idx;//???????
	str.buff_w = tft_w;
	str.buff_h = tft_h;
	str.language = ap_state_config_language_get() - 1;
	ap_state_resource_string_resolution_get(&str, &str_res);
	str.pos_x = (tft_w - str_res.string_width) >> 1;
	str.pos_y = (tft_h - str_res.string_height) >> 1;
	str.font_color = R_ID_PALETTE_Red;
/*
	icon.icon_w = 216;
	icon.icon_h = 144;
	icon.pos_x = (tft_w - icon.icon_w) / 2;
	icon.pos_y = (tft_h - icon.icon_h) / 2;
*/
	icon.transparent = R_ID_PALETTE_Transparent;

	str.pos_x +=1;
	str.pos_y +=1;
	icon.icon_w = str_res.string_width+12;
	icon.icon_h = str_res.string_height+12;
	icon.pos_x = (tft_w-icon.icon_w)>>1;
	icon.pos_y = (tft_h-icon.icon_h)>>1;
	ap_setting_rect_draw(display_buf, 0x2595,0xffe0,1, &icon);
	ap_state_resource_string_draw( display_buf, &str);
}
#endif




//s8 printString(const char *pszStr)
//{
//	int length;
//	length = hx330x_str_len(pszStr);
//		
//	if(NULL == gFighter_buffer_string)
//		gFighter_buffer_string = hal_sysMemMalloc(16*32*length);	
//	
//	if(NULL == gFighter_buffer_string){
//		deg_Printf("mem malloc printString fail--->\n");
//		return -1;
//	}
//
//	hal_WartermarkFill(pszStr,gFighter_buffer_string,16*length,RES_FONT_NUM0);//RES_FONT_NUM2
////	image_draw_picture(game_frame_buff,gFighter_buffer_string,i,j,gameFighter_buff[iconIndex].w,gameFighter_buff[iconIndex].h);
//	OSDFrameDrawIcon(game_frame_buff,gFighter_buffer_string,32,32,16*length,16);
//
//
//}


void gFighter_movedir(winHandle handle,INT32U type)
{
   INT16U tft_w = game_frame_w;
   INT16U tft_h = game_frame_h;
   if(game_pause)
   {
		return;
   }

   if(game_over_in==0){
	   if(type==KEY_EVENT_LEFT){//KEY_EVENT_UP
		  	if(plane.x>=PLANE_W*2)
				plane.x -= PLANE_W;	
	   	}else if(type==KEY_EVENT_RIGHT){//KEY_EVENT_DOWN
			if(plane.x < (tft_w - PLANE_W))
				plane.x += PLANE_W; 
	  	}else if(type==KEY_EVENT_UP){//KEY_EVENT_LEFT
		  	if(plane.y>=PLANE_H)
				plane.y -= PLANE_H;		
			
		}else if(type==KEY_EVENT_DOWN){//KEY_EVENT_RIGHT
			 if(plane.y <PLANE_H*9)
				 plane.y += PLANE_H;	 
		}
   }



}

//-------------------------pusbbox----------------------

/*******************************************************************************
* Function Name  : FighterOpenWin
* Description    : FighterOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int gFighterOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	INT8U* figure_fighter_1;  // man up //24*24
	INT8U* figure_fighter_2;// man down //24*24
	INT8U* figure_fighter_3;// man left//24*24
	INT8U* figure_fighter_4;// man right //24*24
	INT8U* figure_fighter_5;
	INT8U* figure_fighter_6;
	INT8U* figure_pass_tips;
	u16 w,h;
	deg_Printf("gFighterOpenWin\n");
	
	//hal_lcdSetBufYUV(hal_lcdVideoShowFrameGet(),0xFF,0x80,0x80);
	memset(game_frame_buff,R_ID_PALETTE_White,game_frame_w*game_frame_h);
	gFighter_temp_icons = hal_sysMemMalloc(20*24*7+220*80);
	if(NULL == gFighter_temp_icons){
		deg_Printf("mem malloc foro icon  fail--->\n");
		return -1;
	}
//	uiWinSetVisible(winItem(handle,GFighter_TIPS_RECT_ID1),0);
//	uiWinSetVisible(winItem(handle,GFighter_TIPS_RECT_ID2),0);
//	uiWinSetVisible(winItem(handle,GFighter_TIPS_STR_TIP),0);

	figure_fighter_1	= gFighter_temp_icons+20*24*1;
	figure_fighter_2	= gFighter_temp_icons+20*24*2;
	figure_fighter_3	= gFighter_temp_icons+20*24*3;
	figure_fighter_4	= gFighter_temp_icons+20*24*4;
	figure_fighter_5	= gFighter_temp_icons+20*24*5;
	figure_fighter_6	= gFighter_temp_icons+20*24*6;
	figure_pass_tips	= gFighter_temp_icons+20*24*7;

	 //读取图标资源
	u32 addr,len;
	addr = res_icon_GetAddrAndSize(R_ID_ICON_PLANE_DIJI1,&w,&h);
	len=w*h;
	nv_read(addr,figure_fighter_1,len);

	addr = res_icon_GetAddrAndSize(R_ID_ICON_PLANE_DIJI2,&w,&h);
	len=w*h;
	nv_read(addr,figure_fighter_2,len);

	addr = res_icon_GetAddrAndSize(R_ID_ICON_PLANE_DIJI3,&w,&h);
	len=w*h;
	nv_read(addr,figure_fighter_3,len);

	addr = res_icon_GetAddrAndSize(R_ID_ICON_PLANE_FIGHT,&w,&h);
	len=w*h;
	nv_read(addr,figure_fighter_4,len);

	addr = res_icon_GetAddrAndSize(R_ID_ICON_PLANE_FALL,&w,&h);
	len=w*h;
	nv_read(addr,figure_fighter_5,len);

	addr = res_icon_GetAddrAndSize(R_ID_ICON_PLANE_BUTTLE,&w,&h);
	len=w*h;
	nv_read(addr,figure_fighter_6,len);

	addr = res_icon_GetAddrAndSize(R_ID_ICON_GAME_TIPS_ICON,&w,&h);
	len=w*h;
	nv_read(addr,figure_pass_tips,len);



	gameFighter_buff[0].ptr = NULL;
	gameFighter_buff[GAME_ENEMY1].ptr 			= figure_fighter_1	;
	gameFighter_buff[GAME_ENEMY1+1].ptr 		= figure_fighter_2	;
	gameFighter_buff[GAME_ENEMY1+2].ptr 		= figure_fighter_3	;
	gameFighter_buff[GAME_ENEMY1+3].ptr 			= figure_fighter_4	;
	gameFighter_buff[GAME_ENEMY1+4].ptr 		= figure_fighter_5	;
	gameFighter_buff[GAME_ENEMY1+5].ptr 			= figure_fighter_6	;
	gameFighter_buff[GAME_ENEMY1+6].ptr 			= figure_pass_tips	;
	InitFighterGame();
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameCloseWin
* Description    : nesGameCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int gFighterCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	game_over_in=0;
	hal_sysMemFree(gFighter_temp_icons);
	gFighter_temp_icons = NULL;
	SysCtrl.gui_flush_sta = 1;//GUI �ָ�����ˢ��
	app_draw_Service(1);
//	g_liu = 0;
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameWinChildClose
* Description    : nesGameWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int gFighterWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("gFighterWinChildClose\n");

	return 0;
}
/*******************************************************************************
* Function Name  : gFighterKeyMsgOk
* Description    : gFighterKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int gFighterKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		game_pause = !game_pause;//InitFighterGame();
	}

	return 0;
}
/*******************************************************************************
* Function Name  : gFighterKeyMsgUp
* Description    : gFighterKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int gFighterKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
        gFighter_movedir(handle,KEY_EVENT_UP);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : gFighterKeyMsgDown
* Description    : gFighterKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int gFighterKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
        gFighter_movedir(handle,KEY_EVENT_DOWN);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : gFighterKeyMsgMode
* Description    : gFighterKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int gFighterKeyMsgRight(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
        gFighter_movedir(handle,KEY_EVENT_RIGHT);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : gFighterKeyMsgMenu
* Description    : gFighterKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int gFighterKeyMsgLeft(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
        gFighter_movedir(handle,KEY_EVENT_LEFT);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : gFighterKeyMsgMain
* Description    : gFighterKeyMsgMain
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int gFighterKeyMsgMain(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;

	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
    	uiWinDestroy(&handle);
	}
	return 0;
}

static int gFighterMsg100MS(winHandle handle,u32 parameNum,u32* parame)
{
	static u32 cnttemp;
	if(game_over_in==0)
	{	
		if(game_pause)
		{
			return 0;
		}
		cnttemp=0;
	   	update();	
		gFighter_draw_map(game_frame_buff);
		memcpy(ui_draw_ctrl.bufStart,game_frame_buff,game_frame_w*game_frame_h);

		app_draw_Service(1);
//		if(game_over_in){
//			game_over_in=0;
//			uiWinDestroy(&handle);
//		}else{
//			uiWinDrawUpdate();
//		}
	}
	else
	{
		cnttemp++;
		if(cnttemp>9)
		{
			game_over_in=0;
			uiWinDestroy(&handle);
		}

	}
	return 0;
}


ALIGNED(4) msgDealInfor gFighterMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		gFighterOpenWin},
	{SYS_CLOSE_WINDOW,		gFighterCloseWin},
	{SYS_CHILE_COLSE,		gFighterWinChildClose},
	//{KEY_EVENT_POWER,		gFighterKeyMsgPower},

	{KEY_EVENT_OK,			gFighterKeyMsgOk},
	{KEY_EVENT_UP,			gFighterKeyMsgUp},
	{KEY_EVENT_DOWN,		gFighterKeyMsgDown},
	{KEY_EVENT_LEFT,		gFighterKeyMsgLeft},
	{KEY_EVENT_RIGHT,		gFighterKeyMsgRight},
	{KEY_EVENT_POWER,		gFighterKeyMsgMain},//返回主界�?
	{SYS_EVENT_100MS,	    gFighterMsg100MS},
	{EVENT_MAX,NULL},
};

WINDOW(gFighterWindow,gFighterMsgDeal,gFighterWin)


