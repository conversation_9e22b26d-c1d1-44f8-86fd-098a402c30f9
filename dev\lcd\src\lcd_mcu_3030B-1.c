/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_MCU_3030B

#define CMD(x)    LCD_CMD_MCU_CMD8(x)
#define DAT(x)    LCD_CMD_MCU_DAT8(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)



LCD_INIT_TAB_BEGIN()
#if 0
	CMD(0x11), //Sleep Out
	DLY(120),  //Delay 120ms
	
	CMD(0xFD),	
	DAT(0x06),	
	DAT(0x08), 
	
	CMD(0x61),	
	DAT(0x07), 
	DAT(0x04), 
	
	CMD(0x73),	
	DAT(0x70), 
	
	CMD(0xFD),	
	DAT(0xFA),	
	DAT(0xFC), 
	
	CMD(0xFD),	
	DAT(0x06),	
	DAT(0x08), 
	
	CMD(0x61),	
	DAT(0x07), 
	DAT(0x04), 
	
	CMD(0x73),	
	DAT(0x70), 
	
	CMD(0xFD),	
	DAT(0xFA),	
	DAT(0xFC), 





	CMD(0xfd),//private_access
	DAT(0x06),
	DAT(0x08),
	//osc 11.2K 45.87M
	//CMD(0x60),//
	//DAT(0x03),//osc_user_adj[3:0] 77.6
	//DAT(0x01),//
	//DAT(0x01),//01
	CMD(0x61),//
	DAT(0x07),//
	DAT(0x04),//
	//bias
	CMD(0x62),//bias setting
	DAT(0x00),//01
	//DAT(0x04),//04 44
	//DAT(0x40),//44 65 40
	DAT(0x00),//04	44
	DAT(0x46),//44	65	40
	//VSP
	CMD(0x65),//Pump1=4.7MHz //PUMP1 VSP
	DAT(0x08),//D6-5:pump1_clk[1:0] clamp 28 2b
	DAT(0x10),//6.26
	DAT(0x21),

	//VSN
	CMD(0x66), //pump=2 AVCL
	DAT(0x08), //clamp 08 0b 09
	DAT(0x10), //10
	DAT(0x21),
	//add source_neg_time
	CMD(0x67),//pump_sel
	DAT(0x21),//21 20
	DAT(0x40),
	//gamma vap/van
	CMD(0x68),//gamma vap/van
	DAT(0x9F),//78-90-af 9f
	DAT(0x30),//
	DAT(0x2a),//
	DAT(0x21),//
	CMD(0xb1),//frame rate
	DAT(0x0f),//0x0f fr_h[5:0]
	DAT(0x02),//0x02 fr_v[4:0]
	DAT(0x03),//0x04 fr_div[2:0]
	CMD(0xB4),
	//DAT(0x02), //2dot
	DAT(0x01), //1dot
	////porch
	CMD(0xB5),
	DAT(0x05),//0x02 vfp[6:0]
	DAT(0x05),//0x02 vbp[6:0]
	DAT(0x0a),//0x0A hfp[6:0]
	DAT(0x14),//0x14 hbp[6:0]
	CMD(0xB6), //display function
	DAT(0x44), //rev sm
	DAT(0x00), //gs norblack
	DAT(0x9f),
	DAT(0x00),
	DAT(0x02),
	//source
	CMD(0xE6),
	DAT(0x00),
	DAT(0xff),//SC_EN_START[7:0] f0
	CMD(0xE7),
	DAT(0x01),//CS_START[3:0] 01
	DAT(0x04),//scdt_inv_sel cs_vp_en
	DAT(0x03),//CS1_WIDTH[7:0] 12
	DAT(0x03),//CS2_WIDTH[7:0] 12
	DAT(0x00),//PREC_START[7:0] 06
	DAT(0x12),//PREC_WIDTH[7:0] 12
	CMD(0xE8), //source
	DAT(0x00), //VCMP_OUT_EN 81-vcmp/vref_output pad
	DAT(0x70), //chopper_sel[6:4]

	DAT(0x00), //gchopper_sel[6:4]
	////gate
	CMD(0xEc),
	DAT(0x43),//(0x43),//47
	////gamme sel
	CMD(0xdf),//
	DAT(0x11),//gofc_gamma_en_sel=1
	////gamma_test1 A1#_wangly
	CMD(0xe0), //gmama set 2.2
	DAT(0x04), //PKP0[4:0]
	DAT(0x05), //PKP1[4:0]
	DAT(0x0c), //PKP2[4:0]
	DAT(0x0f), //PKP3[4:0]
	DAT(0x0e), //PKP4[4:0]
	DAT(0x10), //PKP5[4:0]
	DAT(0x0f), //PKP6[4:0]
	DAT(0x16), //PKP6[4:0]
	CMD(0xe3),
	DAT(0x16), //PKN0[4:0]
	DAT(0x10), //PKN1[4:0]
	DAT(0x10), //PKN2[4:0]
	DAT(0x0d), //PKN3[4:0]
	DAT(0x0e), //PKN4[4:0]
	DAT(0x09), //PKN5[4:0]
	DAT(0x04), //PKN6[4:0]
	DAT(0x05), //PKN6[4:0]
	CMD(0xe1),
	DAT(0x2f), //PRP0[6:0]
	DAT(0x76), //PRP1[6:0]
	CMD(0xe4),
	DAT(0x75), //PRN0[6:0]
	DAT(0x2f), //PRN1[6:0]
	CMD(0xe2),
	DAT(0x26), //VRP0[5:0]
	DAT(0x1f), //VRP1[5:0]
	DAT(0x23), //VRP2[5:0]
	DAT(0x36), //VRP3[5:0]
	DAT(0x38), //VRP4[5:0]
	DAT(0x3f), //VRP5[5:0]
	CMD(0xe5),
	DAT(0x3f), //VRN0[5:0]
	DAT(0x3a), //VRN1[5:0]
	DAT(0x36), //VRN2[5:0]
	DAT(0x20), //VRN3[5:0]
	DAT(0x27), //VRN4[5:0]
	DAT(0x26), //VRN5[5:0]
	CMD(0xF6),
	DAT(0x01),
	DAT(0x30),
	// CMD(0xF1),
	// DAT(0x01),//te
	// DAT(0x01),
	// DAT(0x02),

	CMD(0xfd),
	DAT(0xfa),
	DAT(0xfc),
	CMD(0x3a),
	DAT(0x55),//SH 0x66
	
	CMD(0x35),
	DAT(0x00),
	
	CMD(0x36),//bgr_[3]
	DAT(0x00),//c0
	CMD(0x11), // exit sleep
	DLY(100),
	//Delay(200),
	CMD(0x29), // display on
	DLY(10),
	//Delay(10),
	CMD(0x2c), 
#elif 1 //6代线
CMD(0xfd),//private_access
DAT(0x06),
DAT(0x08),

CMD(0x61),  
DAT(0x07), 
DAT(0x07), 
CMD(0x73),  
DAT(0x70), 
CMD(0x73),  
DAT(0x00), //07

CMD(0x61),//
DAT(0x07),//
DAT(0x07),//
//bias
CMD(0x62),//bias setting
DAT(0x00),//01
DAT(0x44),//04 44
DAT(0x40),//44 65 40
//VSP
CMD(0x65),//Pump1=4.7MHz //PUMP1 VSP
DAT(0x08),//D6-5:pump1_clk[1:0] clamp 28 2b
DAT(0x10),//6.26
DAT(0x21),

//VSN
CMD(0x66), //pump=2 AVCL
DAT(0x08), //clamp 08 0b 09
DAT(0x10), //10
DAT(0x21),
//add source_neg_time
CMD(0x67),//pump_sel
DAT(0x21),//21 20
DAT(0x40),
//gamma vap/ van
CMD(0x68),//gamma vap/van
DAT(0x60),//78-90-af 9f
DAT(0x47),//
DAT(0x2A),//2A
DAT(0x1B),//21
CMD(0xb1),//frame rate
DAT(0x0f),//0x0f fr_h[5:0]
DAT(0x02),//0x02 fr_v[4:0]
//DAT(0x00),//0x04 fr_div[2:0]  00
DAT(0x02),//0x04 fr_div[2:0]  00

CMD(0xB4),
DAT(0x01), //01  00
////porch
CMD(0xB5),
DAT(0x02),//0x02 vfp[6:0]
DAT(0x02),//0x02 vbp[6:0]
DAT(0x0a),//0x0A hfp[6:0]
DAT(0x14),//0x14 hbp[6:0]
CMD(0xB6), //display function
DAT(0x44), //rev sm
DAT(0x00), //gs norblack
DAT(0x9f),
DAT(0x00),
DAT(0x02),
//source
CMD(0xE6),
DAT(0x00),
DAT(0xff),//SC_EN_START[7:0] f0
CMD(0xE7),
DAT(0x01),//CS_START[3:0] 01
DAT(0x04),//scdt_inv_sel cs_vp_en
DAT(0x03),//CS1_WIDTH[7:0] 12
DAT(0x03),//CS2_WIDTH[7:0] 12
DAT(0x00),//PREC_START[7:0] 06
DAT(0x12),//PREC_WIDTH[7:0] 12
CMD(0xE8), //source
DAT(0x00), //VCMP_OUT_EN 81-vcmp/vref_output pad
DAT(0x70), //chopper_sel[6:4]
DAT(0x00), //gchopper_sel[6:4]
////gate
CMD(0xEc),
DAT(0x52),//47
////gamme sel
CMD(0xdf),//
DAT(0x11),//gofc_gamma_en_sel=1
////gamma_test1 A1#_wangly

CMD(0xe0), 
DAT(0x08),//pkp0[4:0] V60       
DAT(0x05),//pkp1[4:0] V56       
DAT(0x0c),//pkp2[4:0] V45       
DAT(0x0f),//pkp3[4:0] V37//      
DAT(0x0F),//pkp4[4:0] V29    
DAT(0x0F),//pkp5[4:0] V21      
DAT(0x0E),//pkp6[4:0] V7        
DAT(0x1A),//pkp7[4:0] V3      
CMD(0xe3),                       
DAT(0x12),//pkn0[4:0] V3       
DAT(0x10),//pkn1[4:0] V7        
DAT(0x11),//pkn2[4:0] V21      
DAT(0x0C),//pkn3[4:0] V29
DAT(0x10),//pkn4[4:0] V37//       
DAT(0x09),//pkn5[4:0] V45       
DAT(0x05),//pkn6[4:0] V56       
DAT(0x08),//pkn7[4:0] V60       
CMD(0xe1),                       
DAT(0x2E),//prp0[6:0] V51      
DAT(0x72),//prp1[6:0] V15      
CMD(0xe4),                       
DAT(0x6B),//prn0[6:0] V15      
DAT(0x2E),//prn1[6:0] V51       
CMD(0xe2),                       
DAT(0x2A),//vrp0[5:0] V63       
DAT(0x1F),//vrp1[5:0] V62      
DAT(0x23),//vrp2[5:0] V61      
DAT(0x31),//vrp3[5:0] V2        
DAT(0x33),//vrp4[5:0] V1        
DAT(0x36),//vrp5[5:0] V0        
CMD(0xe5),                       
DAT(0x36),//vrn0[5:0] V0        
DAT(0x33),//vrn1[5:0] V1        
DAT(0x32),//vrn2[5:0] V2       
DAT(0x24),//vrn3[5:0] V61      
DAT(0x2B),//vrn4[5:0] V62     
DAT(0x2A),//vrn5[5:0] V63       

CMD(0xF6),
DAT(0x01),
DAT(0x30),
CMD(0xF1),
DAT(0x01),//te
DAT(0xAA),//01
DAT(0xAB),//02

CMD(0xfd),
DAT(0xfa),
DAT(0xfc),
CMD(0x3a),
DAT(0x55),//SH 0x66

CMD(0x35),
DAT(0x00),

CMD(0x36),//bgr_[3]
DAT(0x00),//00
CMD(0x11), // exit sleep
DLY(200),
CMD(0x29), // display on
DLY(20),
CMD(0x2c), 			

#else
CMD(0x11), //Sleep Out
DLY(120),  //Delay 120ms

CMD(0xFD),  
DAT(0x06),  
DAT(0x08), 

CMD(0x61),  
DAT(0x07), 
DAT(0x04), 

CMD(0x73),  
DAT(0x70), 

CMD(0xFD),  
DAT(0xFA),  
DAT(0xFC), 

CMD(0xFD),  
DAT(0x06),  
DAT(0x08), 

CMD(0x61),  
DAT(0x07), 
DAT(0x04), 

CMD(0x73),  
DAT(0x70), 

CMD(0xFD),  
DAT(0xFA),  
DAT(0xFC), 

CMD(0xfd),//private_access
DAT(0x06),	
DAT(0x08),

CMD(0x61),//	
DAT(0x07),//
DAT(0x04),//
//bias
CMD(0x62),//bias setting	
DAT(0x00),//01
DAT(0x00),//04	44
DAT(0x46),//44	65	40

//VSP
CMD(0x65),//Pump1=4.7MHz //PUMP1 VSP
DAT(0x08),//D6-5:pump1_clk[1:0] clamp  28 2b
DAT(0x10),//6.26	
DAT(0x21),

//VSN
CMD(0x66),	//pump=2 AVCL		  
DAT(0x08), //clamp  08	 0b  09
DAT(0x10),	//10
DAT(0x21),

//add source_neg_time
CMD(0x67),//pump_sel
DAT(0x21),//21	20
DAT(0x40),

//gamma vap/van
CMD(0x68),//gamma vap/van
DAT(0x9F),//78-90-af	 9f
DAT(0x30),//	
DAT(0x2a),// 
DAT(0x21),//

CMD(0xb1),//frame rate	
DAT(0x0f),//0x0f fr_h[5:0]
DAT(0x02),//0x02 fr_v[4:0]
DAT(0x03),//0x04 fr_div[2:0]

CMD(0xB4),	
DAT(0x01),	//1dot 

////porch
CMD(0xB5),	
DAT(0x05),//0x02 vfp[6:0] 
DAT(0x05),//0x02 vbp[6:0] 
DAT(0x0a),//0x0A hfp[6:0] 
DAT(0x14),//0x14 hbp[6:0]

CMD(0xB6),		 //display function 																									
DAT(0x44),   //rev  sm																								   
DAT(0x01),	//gs  norblack
DAT(0x9f),																										
DAT(0x00),
DAT(0x02),	
//source
CMD(0xE6),	
DAT(0x00),	
DAT(0xff),//SC_EN_START[7:0]	f0	

CMD(0xE7),	
DAT(0x01),//CS_START[3:0]  01 	
DAT(0x04),//scdt_inv_sel cs_vp_en 
DAT(0x03),//CS1_WIDTH[7:0]	12
DAT(0x03),//CS2_WIDTH[7:0]	12
DAT(0x00),//PREC_START[7:0]  06	
DAT(0x12),//PREC_WIDTH[7:0]  12


CMD(0xE8),	//source
DAT(0x00), //VCMP_OUT_EN	 81-vcmp/vref_output pad
DAT(0x50),	//chopper_sel[6:4]
DAT(0x00), //gchopper_sel[6:4]

////gate 
CMD(0xEc),	
DAT(0x52),//47

////gamme sel						
CMD(0xdf),//	
DAT(0x11),//gofc_gamma_en_sel=1

////gamma_test1 A1#_wangly
CMD(0xe0), //gmama set 2.2
DAT(0x04), //PKP0[4:0] 
DAT(0x05), //PKP1[4:0]	
DAT(0x0c), //PKP2[4:0]		
DAT(0x0f), //PKP3[4:0]		 
DAT(0x0e), //PKP4[4:0]   
DAT(0x10), //PKP5[4:0]	 
DAT(0x0f), //PKP6[4:0]	   
DAT(0x16), //PKP6[4:0]   
CMD(0xe3),
DAT(0x16), //PKN0[4:0]   
DAT(0x10), //PKN1[4:0]   
DAT(0x10), //PKN2[4:0]	 
DAT(0x0d), //PKN3[4:0]   
DAT(0x0e), //PKN4[4:0]		 
DAT(0x09), //PKN5[4:0]			
DAT(0x04), //PKN6[4:0]	
DAT(0x05), //PKN6[4:0]			 
CMD(0xe1),
DAT(0x2f), //PRP0[6:0]	
DAT(0x76), //PRP1[6:0]	 
CMD(0xe4),												 
DAT(0x75), //PRN0[6:0]	   
DAT(0x2f), //PRN1[6:0]  
CMD(0xe2),
DAT(0x26), //VRP0[5:0] 
DAT(0x1f), //VRP1[5:0]	 
DAT(0x23), //VRP2[5:0]  
DAT(0x36), //VRP3[5:0]	  
DAT(0x38), //VRP4[5:0]		 
DAT(0x3f), //VRP5[5:0]		
CMD(0xe5),
DAT(0x3f), //VRN0[5:0]	 
DAT(0x3a), //VRN1[5:0]	
DAT(0x36), //VRN2[5:0]		
DAT(0x20), //VRN3[5:0]  
DAT(0x27), //VRN4[5:0]	 
DAT(0x26), //VRN5[5:0]	

CMD(0xF6),
DAT(0x01),
DAT(0x30),//epf[1:0]
DAT(0x00),
DAT(0x00),//SPI2L: 40

CMD(0xF1),	
DAT(0x01),//te
DAT(0x01),
DAT(0x02),

CMD(0xfd),	
DAT(0xfa),	
DAT(0xfc),

CMD(0x3a),	
DAT(0x05),//SH 0x66

//	CMD(0x35),	
//	DAT(0x00),
					
CMD(0x36),//bgr_[3]
DAT(0x00),//c0
																											 
CMD(0x11), // exit sleep																								  
//DLY(200), 			   //ms
																										   
CMD(0x29), // display on		  
//DLY(10), 			   //ms
CMD(0x2c), 

#endif
LCD_INIT_TAB_END()

LCD_UNINIT_TAB_BEGIN()  
    CMD(0x28),
    DLY(10),
LCD_UNINIT_TA_ENDB()

LCD_DESC_BEGIN()
    .name 			= "MCU_3030B",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_90,//LCD_DISPLAY_ROTATE_270,
    .te_mode 		= LCD_MCU_TE_ENABLE,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div 		= LCD_PCLK_DIV(320*240*2*60),
    .clk_per_pixel 	= 2,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .data_mode = LCD_DATA_MODE0_8BIT_RGB565,

    .screen_w 		= 240,
    .screen_h 		= 320,

    .video_w  		= 320,
    .video_h 	 	= 240,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast   	= LCD_CONTRAST_100,

    .brightness 	= 0,//0xff,

    .saturation 	= LCD_SATURATION_100,

    .contra_index 	= 4,

    .gamma_index 	= {6, 7, 6},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
    UNINIT_TAB_INIT
LCD_DESC_END()



#endif


