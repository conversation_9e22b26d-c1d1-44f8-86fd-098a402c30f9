/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../inc/hal.h"
extern void lens_add_lcd(u8 *y_buf, u8* uv_buf, u32 size);
/*******************************************************************************
* Function Name  : hal_lcdSetCsiCrop
* Description    : set csi LDMA crop
* Input          : u16 crop_sx,u16 crop_ex,u16 crop_sy,u16 crop_ey
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetCsiCrop(u16 crop_sx,u16 crop_sy,u16 crop_ex,u16 crop_ey)
{
    lcd_show_ctrl.crop_sx = crop_sx;
    lcd_show_ctrl.crop_sy = crop_sy;
    lcd_show_ctrl.crop_ex = crop_ex;
    lcd_show_ctrl.crop_ey = crop_ey;
    lcd_show_ctrl.lcd_scaler_process = 1;
    hal_lcdSetWINAB(LCDWIN_A,-1,-1,-1,-1,-1,-1);
    hal_lcdSetWINAB(LCDWIN_B,-1,-1,-1,-1,-1,-1);
}
/*******************************************************************************
* Function Name  : hal_lcdVideoSetRotate
* Description    : hardware layer ,set lcd video rotate degree
* Input          : u8 rotate : rotate
    LCD_DISPLAY_ROTATE_NONE = 0x00,
    LCD_DISPLAY_ROTATE_0    = 0x00,
    LCD_DISPLAY_ROTATE_90   = 0x01,
    LCD_DISPLAY_ROTATE_180  = 0x02,
    LCD_DISPLAY_ROTATE_270  = 0x03,

    LCD_DISPLAY_MIRROR_NONE = 0x00,
    LCD_DISPLAY_V_MIRROR    = 0x10,
    LCD_DISPLAY_H_MIRROR    = 0x30,
* Output         : None
* Return         :
*******************************************************************************/
s32 hal_lcdVideoSetRotate(u8 rotate)
{
    u8 target = lcd_show_ctrl.p_lcddev->scan_mode + rotate;
    u8 rotate_mode;
    u8 mirror_mode = LCD_DISPLAY_ROTATE_0;
    switch(target & (LCD_DISPLAY_ROTATE_MASK|LCD_DISPLAY_MIRROR_MASK))
    {
        case (LCD_DISPLAY_V_MIRROR + LCD_DISPLAY_H_MIRROR) : target += LCD_DISPLAY_ROTATE_180; break;
        case (LCD_DISPLAY_V_MIRROR + LCD_DISPLAY_V_MIRROR) :
        case (LCD_DISPLAY_H_MIRROR + LCD_DISPLAY_H_MIRROR) : target += LCD_DISPLAY_ROTATE_0;   break;
        default: mirror_mode = target & LCD_DISPLAY_MIRROR_MASK;
    }
    rotate_mode = (target & LCD_DISPLAY_ROTATE_MASK);
    if((rotate_mode >= LCD_DISPLAY_ROTATE_180 ) && (mirror_mode != LCD_DISPLAY_ROTATE_0))
    {
        rotate_mode -= LCD_DISPLAY_ROTATE_180;
        if(mirror_mode == LCD_DISPLAY_V_MIRROR)
        {
            mirror_mode = LCD_DISPLAY_H_MIRROR;
        }else
        {
            mirror_mode = LCD_DISPLAY_V_MIRROR;
        }
    }
    deg_Printf("[LCD SET ROTATE] rotate:%x, mirror:%x\n",rotate_mode, mirror_mode );
    if( mirror_mode != LCD_DISPLAY_MIRROR_NONE && rotate_mode != LCD_DISPLAY_ROTATE_0 &&  rotate_mode!= LCD_DISPLAY_ROTATE_90)
    {
        return -1;
    }
    lcd_show_ctrl.video_scan_mode = rotate_mode + mirror_mode;
    if(lcd_show_ctrl.video_need_rotate != (lcd_show_ctrl.video_scan_mode & LCD_DISPLAY_ROTATE_EN))
    {
        lcd_show_ctrl.video_need_rotate = (lcd_show_ctrl.video_scan_mode & LCD_DISPLAY_ROTATE_EN);
        lcd_show_ctrl.video_w     = lcd_show_ctrl.p_lcddev->video_h;
        lcd_show_ctrl.video_h     = lcd_show_ctrl.p_lcddev->video_w;
        lcd_show_ctrl.video_scaler_w     = lcd_show_ctrl.p_lcddev->video_scaler_h;
        lcd_show_ctrl.video_scaler_h     = lcd_show_ctrl.p_lcddev->video_scaler_w;
        //lcd_show_ctrl.ui_w     = lcd_show_ctrl.p_lcddev->ui_h;
        //lcd_show_ctrl.ui_h     = lcd_show_ctrl.p_lcddev->ui_w;
    }
    deg_Printf("[LCD VIDEO ROTATE]%x\n", lcd_show_ctrl.video_scan_mode);
	return 0;
}
/*******************************************************************************
* Function Name  : hal_lcdUiSetRotate
* Description    : hardware layer ,set lcd ui rotate degree
* Input          : u8 rotate : rotate
    LCD_DISPLAY_ROTATE_NONE = 0x00,
    LCD_DISPLAY_ROTATE_0    = 0x00,
    LCD_DISPLAY_ROTATE_90   = 0x01,
    LCD_DISPLAY_ROTATE_180  = 0x02,
    LCD_DISPLAY_ROTATE_270  = 0x03,
* Output         : None
* Return         :
*******************************************************************************/
s32 hal_lcdUiSetRotate(u8 rotate)
{
    u8 src_mode = lcd_show_ctrl.p_lcddev->scan_mode & LCD_DISPLAY_ROTATE_MASK;
    rotate = rotate & LCD_DISPLAY_ROTATE_MASK;
    u8 target_mode = (src_mode + rotate) & LCD_DISPLAY_ROTATE_MASK;

    lcd_show_ctrl.ui_scan_mode = target_mode;
    if(lcd_show_ctrl.ui_need_rotate != (lcd_show_ctrl.ui_scan_mode & LCD_DISPLAY_ROTATE_EN))
    {
        lcd_show_ctrl.ui_w     = lcd_show_ctrl.p_lcddev->ui_h;
        lcd_show_ctrl.ui_h     = lcd_show_ctrl.p_lcddev->ui_w;
        lcd_show_ctrl.ui_x     = lcd_show_ctrl.p_lcddev->ui_y;
        lcd_show_ctrl.ui_y     = lcd_show_ctrl.p_lcddev->ui_x;
    }
    lcd_show_ctrl.ui_need_rotate = (lcd_show_ctrl.ui_scan_mode & LCD_DISPLAY_ROTATE_EN);
    deg_Printf("[LCD UI][%x,%x,%x,%x]\n", lcd_show_ctrl.ui_w,lcd_show_ctrl.ui_h,lcd_show_ctrl.ui_x,lcd_show_ctrl.ui_y);
    deg_Printf("[LCD UI ROTATE]%x\n", lcd_show_ctrl.ui_scan_mode);
	return 0;
}
/*******************************************************************************
* Function Name  : hal_lcdRatioResCal
* Description    : calculate resolution of ratio
* Input          : u8 ratio_w,u8 ratio_h
                   u16 *width : width
                   u16 *height : height
* Output         : none
* Return         :0
*******************************************************************************/
static void hal_lcdRatioResCal(u8 ratio_w,u8 ratio_h,u16 *width,u16 *height)
{
    int ratw,rath;
 	u16 tar_width, tar_height;
    if(width == NULL || height == NULL)
		return ;

	ratw = *width/ratio_w;
	rath = *height/ratio_h;
	if(ratw == rath)
		return;

	tar_height = ratw*ratio_h; //120*3 = 360
	if(tar_height > *height)
	{
		tar_width = rath*ratio_w; //91*4 = 364
		if(tar_width < *width)
			*width = tar_width;
	}
	else
		*height = tar_height;
    return;
}

/*******************************************************************************
* Function Name  : hal_lcdSetRatio
* Description    : hal_lcdSetRatio
* Input          : u16 ratio : LCD_RATIO_MAKE(w, h)
* Output         : None
* Return         :
*******************************************************************************/
void hal_lcdSetRatio(u16 ratio)
{
    HAL_CRITICAL_INIT();
    HAL_CRITICAL_ENTER();
    u8 ratio_w = LCD_RATIO_GET_W(ratio);
    u8 ratio_h = LCD_RATIO_GET_H(ratio);
    lcd_show_ctrl.ratio_w = lcd_show_ctrl.video_w;
    lcd_show_ctrl.ratio_h = lcd_show_ctrl.video_h;
    lcd_show_ctrl.ratio_dest_w = lcd_show_ctrl.video_scaler_w;
    lcd_show_ctrl.ratio_dest_h = lcd_show_ctrl.video_scaler_h;
    lcd_show_ctrl.ratio_x  = lcd_show_ctrl.video_x;
    lcd_show_ctrl.ratio_y  = lcd_show_ctrl.video_y;
    if(ratio_w && ratio_h )
    {
        hal_lcdRatioResCal(ratio_w,ratio_h, &lcd_show_ctrl.ratio_dest_w, &lcd_show_ctrl.ratio_dest_h);
        if(lcd_show_ctrl.ratio_dest_w < lcd_show_ctrl.ratio_w )
        {
           lcd_show_ctrl.ratio_w =  lcd_show_ctrl.ratio_dest_w;
        }
        if(lcd_show_ctrl.ratio_dest_h < lcd_show_ctrl.ratio_h )
        {
           lcd_show_ctrl.ratio_h =  lcd_show_ctrl.ratio_dest_h;
        }
        lcd_show_ctrl.ratio_x +=  (lcd_show_ctrl.video_scaler_w - lcd_show_ctrl.ratio_dest_w)/2;
        lcd_show_ctrl.ratio_y +=  (lcd_show_ctrl.video_scaler_h - lcd_show_ctrl.ratio_dest_h)/2;
    }
    deg_Printf("[LCD ratio][%d:%d] pos[%d,%d], res[%d,%d], dest[%d,%d]\n",
    ratio_w, ratio_h, lcd_show_ctrl.ratio_x, lcd_show_ctrl.ratio_y,
    lcd_show_ctrl.ratio_w, lcd_show_ctrl.ratio_h, lcd_show_ctrl.ratio_dest_w, lcd_show_ctrl.ratio_dest_h);
    HAL_CRITICAL_EXIT();
}
/*******************************************************************************
* Function Name  : hal_lcdVideoNeedRotateGet
* Description    : hardware layer ,get lcd video need rotate or not
* Input          : u8 rotate : rotate degree
* Output         : None
* Return         :
*******************************************************************************/
bool hal_lcdVideoNeedRotateGet(void)
{
    return (bool) lcd_show_ctrl.video_need_rotate;
}
/*******************************************************************************
* Function Name  : hal_lcdUiNeedRotateGet
* Description    : hardware layer, get lcd ui need rotate or not
* Input          : u8 rotate : rotate degree
* Output         : None
* Return         :
*******************************************************************************/
bool hal_lcdUiNeedRotateGet(void)
{
    return (bool) lcd_show_ctrl.ui_need_rotate;
}
/*******************************************************************************
* Function Name  : hal_lcdSetBufYUV
* Description    : memset buffer color,but U must equ V
* Input          : buffer: lcd buffer pointer
                   y:
                   u: must u = v
                   v: must u = v
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetBufYUV(lcdshow_frame_t * buffer,u8 y,u8 u,u8 v)
{
    if(buffer)
    {
        u32 y_size  = buffer->ratio_h * buffer->stride;
        u32 uv_size = y_size / 2;
        memset(buffer->y_addr,y,y_size);
        hx330x_sysDcacheWback((u32)buffer->y_addr,y_size);
        memset(buffer->uv_addr,u,uv_size);
        hx330x_sysDcacheWback((u32)buffer->uv_addr,uv_size);
    }
}
/*******************************************************************************
* Function Name  : hal_lcdSetBufYUV
* Description    : memset buffer color,but U must equ V
* Input          : buffer: lcd buffer pointer
                   y:
                   u: must u = v
                   v: must u = v
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetBufYUV_2(u8 * buffer,u32 buf_size,u8 y,u8 uv)
{
    if(buffer)
    {
		u32 y_size = buf_size*2/3;
        memset(buffer,y,y_size);
		memset(buffer+y_size,uv,y_size/2);
        hx330x_sysDcacheWback((u32)buffer,buf_size);
    }
}
/*******************************************************************************
* Function Name  : hal_lcdSetWINAB
* Description    : set lcd video channles
* Input          : u8 src:video channle source,enum {LCDWIN_B,LCDWIN_A}
*                  u8 layer:video channle layer,enum {LCDWIN_TOP_LAYER,LCDWIN_BOT_LAYER}
*                  u16 x:if x == -1,means don't change this parameter
*                  u16 y:
*                  u16 w:
*                  u16 h:
*                  u8 win_en:channle enable,enum {WINAB_EN,WINAB_DIS},if win_en == -1,means don't change
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetWINAB(u8 src,u8 layer,
                  u16 x,u16 y,u16 w,u16 h,
                  u8 win_en)
{

    lcd_win_t * p_lcd_vch = lcd_show_ctrl.lcdwins[src]->bak;
    //deg_Printf("[%d:%d] pos[%d,%d], res[%d,%d]\n", src, layer, x, y,w, h);
    if(p_lcd_vch)
    {
        p_lcd_vch->config_enable = 0;
        if(x != 0xffff)    p_lcd_vch->x = x & ~7;
        if(y != 0xffff)    p_lcd_vch->y = y & ~1;
        if(w != 0xffff)    p_lcd_vch->w = w;
        if(h != 0xffff)    p_lcd_vch->h = h;
        if(layer != 0xff)  p_lcd_vch->layer = layer;
        if(win_en != 0xff) p_lcd_vch->status = win_en;
        p_lcd_vch->config_enable = 1;
    }


}
/*******************************************************************************
* Function Name  : hal_lcdWinEnablePreSet
* Description    : prepare set lcd win enable/disbale,
*                  take effect when next csi frame done
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdWinEnablePreSet(u8 enable)
{
    lcd_show_ctrl.winAB_EN = enable;
}
/*******************************************************************************
* Function Name  : hal_lcdSetWinEnable
* Description    : set lcd win enable/disbale,take effect immediately
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetWinEnable(u8 enable)
{
    lcd_show_ctrl.winAB_EN = enable;
    hx330x_lcdWinABEnable(enable);
}
/*******************************************************************************
* Function Name  : lcd_struct_get
* Description    : lcd_struct_get
* Input          :
* Output         : lcddev_t * p_lcd_struct
* Return         : none
*******************************************************************************/
lcddev_t * lcd_struct_get(void)
{
	return lcd_show_ctrl.p_lcddev;
}
/*******************************************************************************
* Function Name  : hal_lcdLCMPowerOff
* Description    : lcd module power off sequence
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdLCMPowerOff(void)
{
    if(lcd_show_ctrl.p_lcddev && lcd_show_ctrl.p_lcddev->uninit_table)
    {
        if(lcd_show_ctrl.p_lcddev->lcd_bus_type == LCD_BUS_MCU)
        {
            hx330x_lcdEnable(0);
            hx330x_lcdTeMode(0, 0);
            hx330x_lcdEnable(1);
        }
        lcd_initTab_config(lcd_show_ctrl.p_lcddev->uninit_table);
    }
}

/*******************************************************************************
* Function Name  : hal_lcd_getJpgIdleBuf
* Description    : hal_lcd_getJpgIdleBuf
* Input          : none
* Output         : None
* Return         : bool: true: no kic lcd dma, false: kick lcd dma
*******************************************************************************/
static void hal_lcd_win_dec_kick(void)
{
    if(lcd_show_ctrl.jpg_dcd_buf && lcd_show_ctrl.jpg_dcd_len)
    {
        lcd_win_t * p_WIN_top = lcd_show_ctrl.lcdwins[LCDWIN_TOP_LAYER];
        //deg_Printf("B");
        if(lcd_show_ctrl.winAB_process_step != WINAB_PROCESS_STEP_END)
        {
            return;
        }
        //debgchar('4');
        //deg_Printf("F");
        lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_0_DECWIN;
        lcd_show_ctrl.lcd_dma_sta = 1;
        if(lcd_show_ctrl.magic_win.config_en)
        {
            magic_mirror_lcd_add((u8*)lcd_show_ctrl.win_using->y_addr);    
        }else if(lcd_show_ctrl.len_win_en)
        {
            lens_add_lcd((u8*)lcd_show_ctrl.win_using->y_addr, (u8*)lcd_show_ctrl.win_using->uv_addr, lcd_show_ctrl.win_using->buf_size);
        }else
        {
            kid_frame_lcd_add((u8*)lcd_show_ctrl.win_using->y_addr);  
        } 
        if(lcd_show_ctrl.win_pip_en)
        {
            //if(lcd_show_ctrl.magic_win.config_en)
            {
                hx330x_lcdWinABConfig(p_WIN_top->src,
                                p_WIN_top->x,p_WIN_top->y,
                                p_WIN_top->w,p_WIN_top->h,
                                lcd_show_ctrl.win_pip_en);
                hx330x_lcdVideoSetScaleLine(p_WIN_top->y + p_WIN_top->h - 1,1);
            }
        } 
        hx330x_mjpB_reset();
        hx330x_mjpB_DecodeODma1Cfg(0, 0, 0);
        

        lcd_win_t * p_WINB = lcd_show_ctrl.lcdwins[LCDWIN_B];
        u8 * p_y = &lcd_show_ctrl.win_using->y_addr[p_WINB->x +
                       (p_WINB->y * lcd_show_ctrl.win_using->stride)];
        u8 * p_uv= &lcd_show_ctrl.win_using->uv_addr[p_WINB->x +
                       (p_WINB->y / 2 * lcd_show_ctrl.win_using->stride)];
        hal_mjpDecodeOneFrame_Fast(lcd_show_ctrl.jpg_dcd_buf,lcd_show_ctrl.jpg_dcd_buf + lcd_show_ctrl.jpg_dcd_len,
                                        p_y,p_uv,
                                        p_WINB->w,p_WINB->h,
                                        lcd_show_ctrl.win_using->stride);
    }else
    {
        //deg_Printf("k");
        //debgchar('5');
        lcd_show_ctrl.lcd_dma_sta = 0;
        lcd_show_ctrl.win_using->win_sta |= LB_READY1;
    }
}
/*******************************************************************************
* Function Name  : hal_lcdwin_buf_cfg
* Description    : hardware layer ,switch frame when csi frame end
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_lcdwin_buf_cfg(u8 * buf, u32 len)
{
    lcd_show_ctrl.jpg_dcd_buf = buf;
    lcd_show_ctrl.jpg_dcd_len = len + 64;
    //deg_Printf("hal_lcdwin_buf_cfg:%x,%d\n", buf, len);
}
/*******************************************************************************
* Function Name  : hal_lcdwin_framesta_get
* Description    : hardware layer ,switch frame when csi frame end
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
u16 hal_lcdwin_framesta_get(void)
{
    return lcd_show_ctrl.jpg_frame_sta;
}
/*******************************************************************************
* Function Name  : hal_lcdwin_framesta_reset
* Description    : hardware layer ,switch frame when csi frame end
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_lcdwin_framesta_reset(int reset)
{
    if(reset == 1)
    {
        //deg_Printf("2");
        lcd_show_ctrl.jpg_frame_sta = WINAB_JFRAME_STA_START;
    }  
    else
    {
        //deg_Printf("1");
        lcd_show_ctrl.jpg_frame_sta = WINAB_JFRAME_STA_END;
    }
        
}
/*******************************************************************************
* Function Name  : hal_lcdMJPB_Decode_Done
* Description    : hardware layer ,lcd process for mjpB decode done
* Input          : int flag: mjpB decode flag
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcd_decwin_done(int flag)
{
    u8 kick_lcddma = 1;
    if(!lcd_show_ctrl.win_using) return;

    //deg_Printf("[%d]\n",lcd_show_ctrl.winAB_process_step);

    //debgchar('6');
	if(flag & (BIT(1)|BIT(8)|BIT(5)|BIT(12)|BIT(13)))//error,不管处于哪个step，都要结束，同时释放usensor_frame
    {
        deg_Printf("err:%x\n", flag);
        goto LCD_DECWIN_FAIL;
	}else if(flag & (BIT(2)|BIT(6)))
    {
        //if(!(flag & BIT(1)))
        //{
        //    deg_Printf("[%d]\n",lcd_show_ctrl.winAB_process_step);
        //    deg_Printf("err:%x\n", flag);
        //    goto LCD_DECWIN_FAIL;
        //}else  //decode win done
        {
            if(flag & BIT(6))
            {
                //deg_Printf("C");
                if(lcd_show_ctrl.winAB_process_step == WINAB_PROCESS_STEP_0_DECWIN)
                {
                    //lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_END;
                    lcd_show_ctrl.win_using->win_sta |= LB_READY1;

                        
                }
                goto LCD_DECWIN_OK;
            }else
            {
                //deg_Printf("E");
                goto LCD_DECWIN_FAIL;
                
            }
            

        }    
    }else
    {
        //deg_Printf("[%d]\n",lcd_show_ctrl.winAB_process_step);
        deg_Printf("err:%x\n", flag);      
    }
LCD_DECWIN_FAIL:
    //kick_lcddma = 1;
    hx330x_lcdWinReset(WIN_SRC_B);

    //deg_Printf("[%d]\n",lcd_show_ctrl.winAB_process_step);
    //deg_Printf("err:%x\n", flag);
    
LCD_DECWIN_OK:
    
    //deg_Printf("1[%d]\n",lcd_show_ctrl.winAB_process_step);
    if(/*lcd_show_ctrl.magic_win.config_en && */lcd_show_ctrl.win_pip_en)
    {
        hx330x_lcdWinABEnable(0);
    }else
    {
        //lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_END;
    }
        
    if(kick_lcddma && lcd_show_ctrl.lcd_dma_sta)
    {
        lcd_show_ctrl.lcd_dma_sta = 0;
        //deg_Printf("D");
        hal_CSI_lcdFrameEndCallback(); 
        //hx330x_csiLCDDmaKick();
        
        
        //hx330x_csiLCDDmaKick();
           
    }
    lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_END;

}
/*******************************************************************************
* Function Name  : hal_CSI_lcdFrameEndCallback
* Description    : hardware layer ,switch frame when csi frame end
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_CSI_lcdFrameEndCallback(void)
{
    lcdshow_frame_t * ready = lcd_show_ctrl.win_using;
    lcdshow_frame_t * next = NULL;
    int magic_sta;

    if(!ready)
    {
        //never run here
        deg_Printf("[LCD Err] csi using null\n");
        return;
    }
    
    //debgchar('-');
    //debgchar('1');
    hal_lcdWinUpdata();

    lcd_win_t * p_WINA = lcd_show_ctrl.lcdwins[LCDWIN_A];
    lcd_win_t * p_WINB = lcd_show_ctrl.lcdwins[LCDWIN_B];
    lcd_win_t * p_WIN_top = lcd_show_ctrl.lcdwins[LCDWIN_TOP_LAYER];

    u8 not_skip = 1;
    
    lcd_show_ctrl.lcd_dma_sta = 0;
    
    //config lcdwinAB,必须在csi没有DMA，且JPGB也没有DMA的情况下修改lcdwinAB配置
    //if(p_WIN_top->config_enable && (p_WIN_top->status == WINAB_EN))
    //{
    //    hx330x_csiLCDDmaEnable(0);
    //    
    //    hx330x_lcdWinABConfig(p_WIN_top->src,
    //                     p_WIN_top->x,p_WIN_top->y,
    //                     p_WIN_top->w,p_WIN_top->h,
    //                     lcd_show_ctrl.winAB_EN);
    //    hx330x_lcdVideoSetScaleLine(p_WIN_top->y + p_WIN_top->h - 1,1);
    //    if(lcd_show_ctrl.jpg_frame_sta == WINAB_JFRAME_STA_START)
    //    {
    //        lcd_show_ctrl.jpg_frame_sta = WINAB_JFRAME_STA_SHOW;
    //   //     //deg_Printf("A");
    //    }
    //        
    //    //deg_Printf("[WIN%d]%x, %x, %x,%x,%x\n",p_WIN_top->src, XSFR_LCDPIP_CON,XSFR_LCDPIP_POS,XSFR_LCDPIP_BSIZE, XSFR_LCDC_SHOW_CTRL );
    //    //deg_Printf("pip:%d, [%d,%d],[%d,%d]%d\n",p_WIN_top->src,
    //    //                p_WIN_top->x,p_WIN_top->y,
    //    //                p_WIN_top->w,p_WIN_top->h, lcd_show_ctrl.winAB_EN);
    //    //deg_Printf("JPG:[%d,%d][%d,%d,%d]\n",p_WINB->x, p_WINB->y,
    //    //p_WINB->w,p_WINB->h,lcd_show_ctrl.win_using->stride);
    //    ///hx330x_csiLCDDmaEnable(1);
    //} 

    //2: use magic crop , 1: use updata magic crop  , 0: use origin, -1: invalid
    magic_sta = magic_mirror_lcd_updata(&lcd_show_ctrl.magic_win);
    if((p_WINA->config_enable && magic_sta < 0) || (magic_sta == 0))
    {
        
        not_skip = 0;//skip this frame
        lcd_show_ctrl.magic_win.config_en = 0;
    }else if((magic_sta == 1) || (magic_sta > 1 && lcd_show_ctrl.magic_win.config_en == 0) || p_WINA->config_enable)
    {
        not_skip = 0;//skip this frame
        lcd_show_ctrl.magic_win.config_en = 1;
    }
    lcd_show_ctrl.len_win_en = lens_add_lcd_check();

    if(p_WIN_top->config_enable && (p_WIN_top->status == WINAB_EN))
    {
        
        if(lcd_show_ctrl.jpg_frame_sta == WINAB_JFRAME_STA_START)
        {
            lcd_show_ctrl.jpg_frame_sta = WINAB_JFRAME_STA_SHOW;
            //deg_Printf("A");
        }
        if(lcd_show_ctrl.magic_win.config_en || lcd_show_ctrl.len_win_en)
        {
            lcd_show_ctrl.win_pip_en = lcd_show_ctrl.winAB_EN;
        }else
        {

            hx330x_lcdWinABConfig(p_WIN_top->src,
                            p_WIN_top->x,p_WIN_top->y,
                            p_WIN_top->w,p_WIN_top->h,
                            lcd_show_ctrl.winAB_EN);
            hx330x_lcdVideoSetScaleLine(p_WIN_top->y + p_WIN_top->h - 1,1);    
             lcd_show_ctrl.win_pip_en = 0;   
        }          
        
        //deg_Printf("[%d]",lcd_show_ctrl.win_pip_en);
    }
    if(p_WIN_top->config_enable && lcd_show_ctrl.winAB_EN == 0)
    {
        hx330x_lcdWinABEnable(0);
    }
    //config linescaler
    //deg_Printf("%d",magic_sta);
    if(not_skip == 0)
    {
        if(lcd_show_ctrl.magic_win.config_en == 0)
        {
            hx330x_csiLCDScaler(p_WINA->w,p_WINA->h,
                                lcd_show_ctrl.crop_sx,lcd_show_ctrl.crop_sy,lcd_show_ctrl.crop_ex,lcd_show_ctrl.crop_ey,
                                ready->stride);
            //deg_Printf("ORIGN:[%d, %d], %d,%d,%d,%d\n",p_WINA->w,p_WINA->h,
            //                    lcd_show_ctrl.crop_sx,lcd_show_ctrl.crop_sy,lcd_show_ctrl.crop_ex,lcd_show_ctrl.crop_ey);
        }else
        {
            hx330x_csiLCDScaler(lcd_show_ctrl.magic_win.dst_w, lcd_show_ctrl.magic_win.dst_h,
                            lcd_show_ctrl.magic_win.src_crop_sx,lcd_show_ctrl.magic_win.src_crop_sy,
                            lcd_show_ctrl.magic_win.src_crop_ex,lcd_show_ctrl.magic_win.src_crop_ey,
							ready->stride);
            //deg_Printf("MAGIC:[%d, %d], %d,%d,%d,%d\n", lcd_show_ctrl.magic_win.dst_w, lcd_show_ctrl.magic_win.dst_h,
            //                   lcd_show_ctrl.magic_win.src_crop_sx,lcd_show_ctrl.magic_win.src_crop_sy,
            //                    lcd_show_ctrl.magic_win.src_crop_ex,lcd_show_ctrl.magic_win.src_crop_ey);   
        }
    }

    ready->win_sta |= LB_READY0;
    u8  buf_ready = LB_READY0;
    if (p_WINB->status == WINAB_EN)
    {
        buf_ready |= LB_READY1;
        
    }
    if(hardware_setup.lcd_first_drop)
    {
        hardware_setup.lcd_first_drop--;
        not_skip = 0;
    }

    
    if(((ready->win_sta & buf_ready) == buf_ready) && not_skip)
    {
        next = hal_lcdVideoIdleFrameMalloc();
        if(next)
        {
             //config next frame
			u16 csi_w,csi_h;
			hal_SensorResolutionGet(&csi_w,&csi_h);
            lcd_show_ctrl.ratio_w = hx330x_min(csi_w,lcd_show_ctrl.ratio_w);
		    lcd_show_ctrl.ratio_h = hx330x_min(csi_h,lcd_show_ctrl.ratio_h);
            //deg_Printf("next:pos[%d,%d],src[%d,%d],dst[%d,%d]\n",lcd_show_ctrl.ratio_x, lcd_show_ctrl.ratio_y,
            //                       lcd_show_ctrl.ratio_w, lcd_show_ctrl.ratio_h,
            //                       lcd_show_ctrl.ratio_dest_w, lcd_show_ctrl.ratio_dest_h);
            hal_lcdVideoFrameFlush(next,
                                   lcd_show_ctrl.ratio_x, lcd_show_ctrl.ratio_y,
                                   lcd_show_ctrl.ratio_w, lcd_show_ctrl.ratio_h,
                                   lcd_show_ctrl.ratio_dest_w, lcd_show_ctrl.ratio_dest_h);
            if(lcd_show_ctrl.magic_win.config_en)
            {
                u32 csi_y_ofs  = lcd_show_ctrl.magic_win.dst_pos_x + (lcd_show_ctrl.magic_win.dst_pos_y * next->stride);
                u32 csi_uv_ofs = lcd_show_ctrl.magic_win.dst_pos_x + (lcd_show_ctrl.magic_win.dst_pos_y / 2 * next->stride);
                hx330x_csiLCDFrameSet((u32)&next->y_addr[csi_y_ofs],
                                    (u32)&next->uv_addr[csi_uv_ofs]);
            }else
            {
                u32 csi_y_ofs  = p_WINA->x + (p_WINA->y * next->stride);
                u32 csi_uv_ofs = p_WINA->x + (p_WINA->y / 2 * next->stride);

                hx330x_csiLCDFrameSet((u32)&next->y_addr[csi_y_ofs],
                                    (u32)&next->uv_addr[csi_uv_ofs]);
            }
            //u32 lens_en = lens_add_lcd_check();
            //deg_Printf("+");
            //debgchar('2');
            if(!(buf_ready & LB_READY1))
            {
                //hx330x_csiLCDDmaKick();    
                //lcd_show_ctrl.lcd_dma_sta = 1;
                if(lcd_show_ctrl.magic_win.config_en)
                {
                    magic_mirror_lcd_add((u8*)ready->y_addr);    
                }else if(lcd_show_ctrl.len_win_en)
                {
                    lens_add_lcd((u8*)ready->y_addr, (u8*)ready->uv_addr, ready->buf_size);
                }else
                {
                    kid_frame_lcd_add((u8*)ready->y_addr);        
                }     
            }
            lcd_show_ctrl.lcd_fcnt++;
            if(hx330x_mjpA_EncodeLcdKick_Func_Check())
            {
                hx330x_mjpA_EncodeLcdKick_Func_call((u32)ready->y_addr, (u32)ready->uv_addr);
            }
            
            hal_lcdVideoSetFrame(ready);     

            if(lcd_show_ctrl.jpg_frame_sta == WINAB_JFRAME_STA_SHOW)
            {
                //deg_Printf("0");
                lcd_show_ctrl.jpg_frame_sta = WINAB_JFRAME_STA_END;
            }                       
            //updata using
            lcd_show_ctrl.win_using = next;
        }
    }

    if(!next)
    {
        //debgchar('3');
        //not_skip = 0;
        if(lcd_show_ctrl.magic_win.config_en)
        {
            u32 csi_y_ofs  = lcd_show_ctrl.magic_win.dst_pos_x + (lcd_show_ctrl.magic_win.dst_pos_y * ready->stride);
            u32 csi_uv_ofs = lcd_show_ctrl.magic_win.dst_pos_x + (lcd_show_ctrl.magic_win.dst_pos_y / 2 * ready->stride);
            hx330x_csiLCDFrameSet((u32)&ready->y_addr[csi_y_ofs],
                                    (u32)&ready->uv_addr[csi_uv_ofs]);
        }
        else{
            u32 csi_y_ofs  = p_WINA->x + (p_WINA->y * ready->stride);
            u32 csi_uv_ofs = p_WINA->x + (p_WINA->y / 2 * ready->stride);
            hx330x_csiLCDFrameSet((u32)&ready->y_addr[csi_y_ofs],
                                (u32)&ready->uv_addr[csi_uv_ofs]);
        }

        ready->win_sta = LB_IDLE;
    }
    if(not_skip && (p_WINB->status == WINAB_EN) &&
            (hal_BackRecDecodeStatusCheck()) &&
            hx330x_csiLCDScalerDoneCheck())
    {
        hal_lcd_win_dec_kick();
    }
    



    if(lcd_show_ctrl.lcd_dma_sta == 0)
    {
        //debgchar('7');
        hx330x_csiLCDDmaKick();
        //deg_Printf("3");
    }
        
    p_WINA->config_enable =
    p_WINB->config_enable = 0;

    if(not_skip && next)
    {
        lcd_show_ctrl.lcd_scaler_process = 0;
    }

}
/*******************************************************************************
* Function Name  : hal_CSI_lcdFrameEndOnceCallback
* Description    : hardware layer ,csi to lcd dma done callback, only fill buf but no show
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_CSI_lcdFrameEndOnceCallback(void)
{
    lcdshow_frame_t * ready = lcd_show_ctrl.win_using;
    if(!ready)
    {
        //never run here
        deg_Printf("[LCD Err] csi using null\n");
        return;
    }
    if(hardware_setup.lcd_first_drop)
    {
        hardware_setup.lcd_first_drop--;
        hx330x_csiLCDFrameSet((u32)ready->y_addr,(u32)ready->uv_addr);
        hx330x_csiLCDDmaKick();
        return;
    }

    if(hx330x_mjpA_EncodeLcdKick_Func_Check())
    {
        hx330x_mjpA_EncodeLcdKick_Func_call((u32)ready->y_addr, (u32)ready->uv_addr);
    }
    hx330x_csiLCDDmaEnable(0);
    hal_dispframeFree(lcd_show_ctrl.win_using);
    lcd_show_ctrl.win_using = NULL;  
    hx330x_csiISRRegiser(CSI_IRQ_LCD_FRAME_END,NULL);

}
/*******************************************************************************
* Function Name  : hal_lcd_fcnt_mnt
* Description    : hal layer .mjpA encode frame debg
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
u32 hal_lcd_fcnt_mnt(void)
{
    if(lcd_show_ctrl.win_using)
    {
        u32 temp = lcd_show_ctrl.lcd_fcnt;
        deg_Printf("lcd_fcnt:%d\n",temp);
        lcd_show_ctrl.lcd_fcnt = 0;
        return temp;
    }else
    {
        return 0;
    }

}