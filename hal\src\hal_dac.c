/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../inc/hal.h"



ALIGNED(4) static const u32 halSampleRateTable[]=
{
	48000,44100,32000,0,24000,22050,16000,0,12000,11025,8000
};
static u8 halVolume;
static void (*halDacCallBack)(int flag);
/*******************************************************************************
* Function Name  : hal_dacCallback
* Description    : hal layer .dac callback
* Input          : none
* Output         : None
* Return         : none 
*******************************************************************************/
SDRAM_TEXT_SECTION
static void hal_dacCallback(u8 flag)
{
	
	if(halDacCallBack)
		halDacCallBack(flag);
	else
	{
		if(flag == 4)
		   hx330x_dacStop();
	}
}
/*******************************************************************************
* Function Name  : hal_dacInit
* Description    : hal layer .initial dac
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_dacInit(void)
{
#if CURRENT_CHIP == FPGA	
	hx330x_dacInit(0);
#else
	hx330x_dacInit(1);
#endif
	hx330x_dacEnable(1);
	hx330x_dacVolumeSet(HAL_VOLUME_MAKE(0));

	halVolume = 0;

	halDacCallBack = NULL;
	//hx330x_dacTypeCfg(0); //CLASS D		//声音加大，但电流消耗大
	hx330x_dacTypeCfg(1); //CLASS AB
	hx330x_dacISRRegister(hal_dacCallback);

}
/*******************************************************************************
* Function Name  : hal_dacPlayStart
* Description    : hal layer .start dac play
* Input          : u32 sampleRate : sample rate
				   u32 buffer : buffer
				   u32 size : size
* Output         : None
* Return         : bool : true - success; false - fail
*******************************************************************************/
bool hal_dacPlayInit(u32 sampleRate)
{
	int i;

    if(sampleRate==0)
		return false;
	for(i=0;i<sizeof(halSampleRateTable)/sizeof(halSampleRateTable[0]);i++)
	{
		if(sampleRate == halSampleRateTable[i])
			break;
	}
	if(i >= sizeof(halSampleRateTable)/sizeof(halSampleRateTable[0]))
	{		
		return false;
	}
	hx330x_dacSampleRateSet(i);
//    deg_Printf("halVolume = %d,%x\n",halVolume,HAL_VOLUME_MAKE(halVolume));
	hx330x_dacVolumeSet(HAL_VOLUME_MAKE(halVolume));
	return true;
}
/*******************************************************************************
* Function Name  : hal_dacPlayStart
* Description    : hal layer .start dac play
* Input          : u32 sampleRate : sample rate
				   u32 buffer : buffer
				   u32 size : size
* Output         : None
* Return         : bool : true - success; false - fail
*******************************************************************************/
bool hal_dacPlayStart(u32 sampleRate,u32 buffer,u32 size)
{
	if(hal_dacPlayInit(sampleRate) == false)
		return false;
   
	hx330x_dacStart(buffer,size);

	return true;
}
/*******************************************************************************
* Function Name  : hal_dacPlayStop
* Description    : hal layer .stop dac play
* Input          : none
* Output         : None
* Return         : none 
*******************************************************************************/
void hal_dacPlayStop(void)
{
	hx330x_dacVolumeSet(HAL_VOLUME_MAKE(0));
	hx330x_dacStop();
}
/*******************************************************************************
* Function Name  : hal_dacSetVolume
* Description    : hal layer .set dac volume
* Input          : u8 volume : dac volume
* Output         : None
* Return         : none 
*******************************************************************************/
void hal_dacSetVolume(u8 volume)
{
	if(halVolume==volume)
		return;
	if(volume>100)
		volume = 100;
	halVolume = volume;

	hx330x_dacVolumeSet(HAL_VOLUME_MAKE(halVolume));
}
/*******************************************************************************
* Function Name  : hal_dacSetBuffer
* Description    : hal layer .set dac buffer
* Input          : u32 buffer : buffer addr
				   u32 size : size
* Output         : None
* Return         : none
*******************************************************************************/
/*void hal_dacSetBuffer(u32 buffer,u32 size)
{
	hx330x_dacBufferSet(buffer,size);
}*/
/*******************************************************************************
* Function Name  : hal_dacCallBackRegister
* Description    : hal layer .dac callback register
* Input          : void (*callback)(int flag) : callback
* Output         : None
* Return         : none
*******************************************************************************/
void hal_dacCallBackRegister(void (*callback)(int flag))
{
	halDacCallBack = callback;
}
/*******************************************************************************
* Function Name  : hal_dacHPSet
* Description    : hal layer .dac hp vdd output
* Input          :  u8 en : enable.1->enable,0-disable
                       u32 level : vdd level.SEE HP_VDD_E
* Output         : None
* Return         : none
*******************************************************************************/
/*void hal_dacHPSet(u8 en,u32 level)
{
	hx330x_dacHPSet(en,level);
}*/


