/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HAL_ISP_H
    #define HAL_ISP_H
/*******************************************************************************
* Function Name  : hal_isp_filter_cfg
* Description    : hal_isp_filter_cfg
* Input          : filter_type: SENSOR_FILTER_TYPE
* Output         : none
* Return         : NONE
*******************************************************************************/
void hal_isp_filter_cfg(u8 filter_type);
/*******************************************************************************
* Function Name  : hal_isp_color_filter_init
* Description    : hal_isp_color_filter_init
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void hal_isp_color_filter_init(void);
/*******************************************************************************
* Function Name  : hal_SensorRegister
* Description    : sensor api register
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void hal_isp_init(void);
/*******************************************************************************
* Function Name  : hal_SensorRegister
* Description    : sensor api register
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void hal_SensorRegister(SENSOR_API_T *pSensor_api);
/*******************************************************************************
* Function Name  : hal_SensorRegister
* Description    : sensor api register
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
SENSOR_API_T* hal_SensorApiGet(void);
/*******************************************************************************
* Function Name  : hal_SensorResolutionGet
* Description    : hal layer .get csi resolution
* Input          : u16 *width : width
                      u16 *height : height
* Output         : None
* Return         : int 0 : success
                      int -1: fail
*******************************************************************************/
int hal_SensorResolutionGet(u16 *width,u16 *height);
/*******************************************************************************
* Function Name  : hal_sensor_fps_adpt
* Description    : hal_sensor_fps_adpt
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_sensor_fps_adpt(u32 frequecy_mode,u32 frame_rate);
/*******************************************************************************
* Function Name  : hal_isp_process
* Description    : hal layer .isp auto adjust process
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_isp_process(void);
/*******************************************************************************
* Function Name  : hal_sensor_awb_scene_set
* Description    : awb scene mode set
* Input          : u32 awb_scene_mod: 0 - auto mode, 1~5 : en mode
* Output         : none                                            
* Return         : none
*******************************************************************************/
void hal_sensor_awb_scene_set(u32 awb_scene_mod);

/*******************************************************************************
* Function Name  : hal_sensor_EV_set
* Description    : sensor EV setting
* Input          : u32 ev_mode: cur level, base VDE bright_oft(0~255)
				   u32 level_max: (level_max + 1)/2 为原始亮度，1~ (level_max + 1)/2：降低亮度，(level_max + 1)/2 ~level_max：增加亮度
				   u32 level_range：每个level亮度范围
* Output         : none
* Return         : none
*******************************************************************************/
void hal_sensor_EV_set(u32 ev_mode, u32 level_max, u32 level_range);
/*******************************************************************************
* Function Name  : hal_isp_br_get
* Description    : get light level from sensor
* Input          : none
* Output         : none                                            
* Return         : level value(0~255)  0: max dark  255: max light
*******************************************************************************/
u8 hal_isp_br_get(void);
/*******************************************************************************
* Function Name  : hal_ispService
* Description    : hal_ispService
* Input          : none
* Output         : none                                            
* Return         : NONE
*******************************************************************************/
void hal_ispService(void);
/*******************************************************************************
* Function Name  : hal_SensorRotate
* Description    : hal_SensorRotate
* Input          : rotate       : for fp_rotate(rotate)
                   colrarray    : see CSI_POLARITY_E
                                : 0xff = skip
* Output         : none                                            
* Return         : NONE
*******************************************************************************/
void hal_SensorRotate(u32 rotate,u32 colrarray);

#endif
