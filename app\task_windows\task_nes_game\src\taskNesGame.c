/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

ALIGNED(4) NESGAME_OP_T task_nesGame_op;

/*******************************************************************************
* Function Name  : taskNesGameIsStart
* Description    : taskNesGameIsStart
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
bool taskNesGameIsStart(void)
{
	if(task_nesGame_op.nesGame_state == NES_GAME_START)
	{
		return true;
	}else
	{
		return false;
	}
}
/*******************************************************************************
* Function Name  : taskNesGameOpenList
* Description    : taskNesGameOpenList
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
void taskNesGameOpenList(void)
{
	deg_Printf("SysCtrl.nes_list:%d\n",SysCtrl.nes_list);
	if(SysCtrl.nes_list < 0)
	{
		SysCtrl.nes_list		= filelist_api_nodecreate(FILEDIR_NES, FILELIST_TYPE_NES, -1);
		filelist_api_scan(SysCtrl.nes_list);
		SysCtrl.file_index		= 0;
	}
	SysCtrl.file_cnt = task_nesGame_op.openListTotal = filelist_api_CountGet(SysCtrl.nes_list);
	if(SysCtrl.file_index >= SysCtrl.file_cnt)
		SysCtrl.file_index = 0;
}
/*******************************************************************************
* Function Name  : taskNesGameCloseList
* Description    : taskNesGameCloseList
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
void taskNesGameCloseList(void)
{
	filelist_api_nodedestory(SysCtrl.nes_list);
	SysCtrl.nes_list		= -1;
}
/*******************************************************************************
* Function Name  : taskNesGameOpenList
* Description    : taskNesGameOpenList
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
void taskNesGameStart(void)
{
	int fd;
	u32 type;
	if(task_nesGame_op.nesGame_step == NES_GAME_INNER)
	{
		if(task_nesGame_op.nesGame_inner_index >= task_nesGame_op.nesGame_inner_indexmax)
		{
			deg_Printf("[NES] INNER index err:%d >= %d\n", task_nesGame_op.nesGame_inner_index, task_nesGame_op.nesGame_inner_indexmax);
			return;
		}
		fd = nes_game_inner_list[task_nesGame_op.nesGame_inner_index].fd;
		type = GAME_SRC_NVFS;

	}else
	{
		char* name = filelist_GetFileFullNameByIndex(SysCtrl.nes_list,(int)SysCtrl.file_index, NULL);
		fd = (int)fs_open(name,FA_READ);	
		if(fd < 0)
		{
			deg_Printf("[NES]open file:%s fail\n", name);
			return;
		}
		deg_Printf("[NES]open file:%s\n", name);
		type = GAME_SRC_FS;

	}
	//nes_dac_volume_cfg(50);
	res_music_end();
	nes_dac_volume_cfg(task_com_curVolume_get());
	//nes_dac_volume_cfg(40);
	if(nes_start(fd,type) >= 0)
	{
		
		//task_nesGame_op.nesGame_state = NES_GAME_START;
		//nes_palette_init();
		uiOpenWindow(&nesGameSubWindow, 0, 0);
	}	
	
	
}
/*******************************************************************************
* Function Name  : taskRecordWinChangeProcess
* Description    : taskRecordWinChangeProcess function.
* Input          : 
* Output         : none                                            
* Return         : int fd : file handle
*******************************************************************************/
static void taskNesGameWinChangeProcess(u8 enter)
{
	if(SysCtrl.winChangeEnable == 0)
		return;
	if(enter)
	{
		taskMainWinInit(0,MEDIA_SRC_NVFS, R_ID_IMAGE_GAME_1_U, 0,MAIN_TO_SUB_VOR_UP);
		if(taskWinChangeProcess() < 0)
		{
			taskNesGameInnerCurWinShow();
		}
	}else
	{
		taskMainWinInit(0,MEDIA_SRC_NVFS, task_nesGame_op.curId, 0,SUB_TO_MAIN_VOR_DOWN);
	}


}
/*******************************************************************************
* Function Name  : app_taskPlayAudioOpen
* Description    : APP LAYER: app_taskPlayAudioOpen
* Input          : u32 arg
* Output         : none
* Return         : none
*******************************************************************************/
static void app_taskNesGameOpen(u32 arg)
{
	//hal_sysMemPrint();
	task_com_usb_dev_out(1);
	dusb_api_Uninit();
	app_lcdCsiVideoShowStop();
	hal_csiEnable(0);
#if  FUN_KID_FRAME_EN
	app_kid_frame_ctrl(KID_FRAME_DISTROY, 0);
#endif
#if  DEV_SENSOR_MAGIC_EN
	app_sensor_magic_ctrl(SENSOR_MAGIC_CHANGE_NONE, 0);
#endif
#if DEV_SENSOR_FILTER_EN
	app_sensor_filter_ctrl(SENSOR_FILTER_CHANGE_NONE);
#endif

#if DEV_SENSOR_LENS_EN
	app_sensor_lens_ctrl(SENSOR_LENS_CHANGE_NONE, 0);
#endif
	task_nesGame_op.nesGame_state = NES_GAME_DISABLE;
	task_nesGame_op.nesGame_step  = NES_GAME_INNER;
	task_nesGame_op.curIdShowUp	  = 1;
	task_nesGame_op.nesGame_inner_index = 0;
	taskNesGameWinChangeProcess(1);
	//hal_lcdSetBufYUV(hal_lcdVideoShowFrameGet(),0,0x80,0x80);

	task_nesGame_op.music_delay   = XOSTimeGet();


	if(nes_api_init() >= 0)
	{
		task_nesGame_op.nesGame_state = NES_GAME_INIT;
		//if(task_nesGame_op.nesGame_step == NES_GAME_INNER)
			uiOpenWindow(&nesGameInnerWindow, 0, 0);
	}else
	{
		SysCtrl.winChangeEnable = 1;
		app_taskStart(TASK_MAIN,0);
	}
	//if(task_nesGame_op.nesGame_step == NES_GAME_CARD)	
	//	uiOpenWindow(&nesGameWindow, 0, 0);
	
}
/*******************************************************************************
* Function Name  : app_taskPlayAudioOpen
* Description    : APP LAYER: app_taskPlayAudioOpen
* Input          : u32 arg
* Output         : none
* Return         : none
*******************************************************************************/
static void app_taskNesGameClose(u32 arg)
{
	if(task_nesGame_op.nesGame_state != NES_GAME_DISABLE)
	{
		nes_api_uinit();
		hal_lcdUiSetBufferWaitDone(UI_LAYER0);
		app_lcdNesExitUiShowInit();
	}
		
	task_nesGame_op.nesGame_state = NES_GAME_DISABLE;
	taskNesGameCloseList();
	task_com_usb_dev_out(0);

	taskNesGameWinChangeProcess(0);
	//hal_sysMemPrint();
	
}
/*******************************************************************************
* Function Name  : app_taskPlayAudioOpen
* Description    : APP LAYER: app_taskPlayAudioOpen
* Input          : u32 arg
* Output         : none
* Return         : none
*******************************************************************************/
static void app_taskNesGameService(u32 arg)
{
	
	//{
	//	if(task_nesGame_op.nesGame_state == NES_GAME_START)
	//	{
	//		//nes_palette_init();
	//		//inner games service
	//		nes_games_service();
	//		task_nesGame_op.nesGame_state = NES_GAME_STOP;
	//		task_nesGame_op.nesGame_step  = NES_GAME_CARD;
	//		app_lcdNesExitUiShowInit();
//
	//		uiOpenWindow(&nesGameWindow, 0, 0);
//
	//	}
//
	//}
	//deg_Printf("1\n");
	//if(task_nesGame_op.nesGame_step == NES_GAME_CARD)
	{
		if(task_nesGame_op.nesGame_state == NES_GAME_START)
		{

			//deg_Printf("nes service\n");
			app_draw_Service(0);
			//注意，NES使用了调色板 编号128~191号颜色
			//nes_palette_init();
			nes_service();
			
			task_com_auto_poweroff(1);
			task_com_sreen_check(SREEN_CHECK_AUTOOFF); // system check,no event
			//deg_Printf("nes service end\n");
			task_nesGame_op.nesGame_state = NES_GAME_STOP;
			//hal_lcdUiKickWait((lcdshow_frame_t *)ui_draw_ctrl.drawframe);
			//app_lcdNesExitUiShowInit();
			winHandle cur_win = uiWinGetCurrent();
			uiWinDestroy(&cur_win);
			hx330x_lcdShowWaitDone();

			
			//XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_POWER,KEY_PRESSED));
			//app_msgDealByType(KEY_EVENT_POWER,uiWinGetCurrent(),0,NULL);
		}else
		{
			if(task_nesGame_op.nesGame_step == NES_GAME_INNER)
			{
				if(audioPlaybackGetStatus() == MEDIA_STAT_STOP) //循环播放背景音
				{
					if(task_nesGame_op.music_delay == 0)
					{
						task_nesGame_op.music_delay = XOSTimeGet();
					}else if((task_nesGame_op.music_delay + 100) < XOSTimeGet())
					{
						task_nesGame_op.music_delay = 0;
						res_music_end();
						res_music_start(R_ID_MUSIC_GAMES_MUSIC,0,task_com_curVolume_get());

					}	
				}
			}
		}
	}

}

ALIGNED(4) sysTask_T taskNesGame =
{
	"NES Game",
	0,
	app_taskNesGameOpen,
	app_taskNesGameClose,
	app_taskNesGameService,
};



