/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	PLAYMP3MAIN_MODE_ID = 0,
	PLAYMP3MAIN_SD_ID,
	PLAYMP3MAIN_SDVOL_ID,
	PLAYMP3MAIN_VOLUME_ID,
	PLAYMP3MAIN_VOLUME_VALUE_ID,
	PLAYMP3MAIN_INDEX_ID,
	PLAYMP3MAIN_BATERRY_ID,


	PLAYMP3MAIN_NAME_ID,
	PLAYMP3MAIN_TIME_ID,
	PLA<PERSON>MP3MAIN_TOTAL_TIME_ID,
	PLAYMP3MAIN_BAR_ID,

	PLAYMP3MAIN_FREQ_ID1,
	PLAYMP3MAIN_FREQ_ID2,
	PLAYMP3MAIN_FREQ_ID3,
	PLAYMP3MAIN_FREQ_ID4,
	PLAYMP3MAIN_FREQ_ID5,
	PLAYMP3MAIN_FREQ_ID6,
	PLAYMP3MAIN_LRC_ID,
	PLAYMP3MAIN_STAT_ID,

};

UNUSED ALIGNED(4) const widgetCreateInfor playMp3MainWin[] =
{
	createFrameWin( 								Rx(0),   Ry(0),   Rw(320), Rh(240), R_ID_PALETTE_Transparent,WIN_ABS_POS),
	createImageIcon(PLAYMP3MAIN_MODE_ID,      		Rx(4),   Ry(0),   Rw(32),  Rh(32),  R_ID_ICON_MTMUSIC, 		ALIGNMENT_LEFT),
	createImageIcon(PLAYMP3MAIN_SD_ID,        		Rx(40),  Ry(0),   Rw(40),  Rh(32),  R_ID_ICON_MTSDCNORMAL,	ALIGNMENT_CENTER),
	createStringIcon(PLAYMP3MAIN_SDVOL_ID,			Rx(40),  Ry(5),   Rw(36),  Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_GREEN2,	DEFAULT_FONT),
	createImageIcon(PLAYMP3MAIN_VOLUME_ID,    		Rx(84),  Ry(0),   Rw(32),  Rh(32),  R_ID_ICON_MENUVOLUME,	ALIGNMENT_RIGHT),
	createStringIcon(PLAYMP3MAIN_VOLUME_VALUE_ID,	Rx(120), Ry(4),   Rw(32),  Rh(24),	RAM_ID_MAKE(" "),	    ALIGNMENT_LEFT, 	R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(PLAYMP3MAIN_INDEX_ID,			Rx(160), Ry(8),   Rw(120), Rh(24),	RAM_ID_MAKE(" "),	    ALIGNMENT_CENTER, 	R_ID_PALETTE_Yellow,DEFAULT_FONT),
	createImageIcon(PLAYMP3MAIN_BATERRY_ID,   		Rx(284), Ry(0),   Rw(32),  Rh(32), 	R_ID_ICON_MTBATTERY3,	ALIGNMENT_RIGHT),

	createStringIcon(PLAYMP3MAIN_TIME_ID,			Rx(5),  Ry(210), Rw(120), Rh(30),	RAM_ID_MAKE(" "),	    ALIGNMENT_LEFT, 	R_ID_PALETTE_Red,  DEFAULT_FONT),
	createStringIcon(PLAYMP3MAIN_TOTAL_TIME_ID,		Rx(195), Ry(210), Rw(120), Rh(30),	RAM_ID_MAKE(" "),	    ALIGNMENT_RIGHT, 	R_ID_PALETTE_White,DEFAULT_FONT),
	createProgressBar(PLAYMP3MAIN_BAR_ID,     		Rx(55),  Ry(225), Rw(210), Rh(5),	R_ID_PALETTE_DarkGray,	R_ID_PALETTE_Yellow, ALIGNMENT_LEFT),

	createStringIcon(PLAYMP3MAIN_NAME_ID,			Rx(120), Ry(40),  Rw(200), Rh(40),	RAM_ID_MAKE(" "),	    ALIGNMENT_CENTER, 	R_ID_PALETTE_Yellow,DEFAULT_FONT),
	createStringEx(PLAYMP3MAIN_LRC_ID,				Rx(120), Ry(80),  Rw(200), Rh(120), RAM_ID_MAKE(" "),  		ALIGNMENT_CENTER,R_ID_PALETTE_White, DEFAULT_FONT, ALIGNMENT_CENTER,R_ID_PALETTE_Yellow, DEFAULT_FONT,16),
	createImageIcon(PLAYMP3MAIN_STAT_ID,     		Rx(120), Ry(80),  Rw(80),  Rh(80),  R_ID_ICON_MTPLAY1,      ALIGNMENT_CENTER),
	createImageIcon(PLAYMP3MAIN_FREQ_ID1,   		Rx(24),  Ry(100), Rw(12), Rh(100),  R_ID_ICON_MUSICBAR1,	ALIGNMENT_CENTER),
	createImageIcon(PLAYMP3MAIN_FREQ_ID2,   		Rx(40),  Ry(100), Rw(12), Rh(100),  R_ID_ICON_MUSICBAR1,	ALIGNMENT_CENTER),
	createImageIcon(PLAYMP3MAIN_FREQ_ID3,   		Rx(56),  Ry(100), Rw(12), Rh(100),  R_ID_ICON_MUSICBAR1,	ALIGNMENT_CENTER),
	createImageIcon(PLAYMP3MAIN_FREQ_ID4,   		Rx(72),  Ry(100), Rw(12), Rh(100),  R_ID_ICON_MUSICBAR1,	ALIGNMENT_CENTER),
	createImageIcon(PLAYMP3MAIN_FREQ_ID5,   		Rx(88),  Ry(100), Rw(12), Rh(100),  R_ID_ICON_MUSICBAR1,	ALIGNMENT_CENTER),
	createImageIcon(PLAYMP3MAIN_FREQ_ID6,   		Rx(104), Ry(100), Rw(12), Rh(100),  R_ID_ICON_MUSICBAR1,	ALIGNMENT_CENTER),

	widgetEnd(),
};

/*******************************************************************************
* Function Name  : playMp3MainSDShow
* Description    : playMp3MainSDShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playMp3MainSDShow(winHandle handle)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
	{
		uiWinSetResid(winItem(handle,PLAYMP3MAIN_SD_ID),R_ID_ICON_MTSDCNORMAL);
		uiWinSetVisible(winItem(handle,PLAYMP3MAIN_SDVOL_ID),1);
		uiWinSetResid(winItem(handle,PLAYMP3MAIN_SDVOL_ID),RAM_ID_MAKE(task_com_sdcCap_str()));
		
	}
	else{
		uiWinSetResid(winItem(handle,PLAYMP3MAIN_SD_ID),R_ID_ICON_MTSDCNULL);
		uiWinSetVisible(winItem(handle,PLAYMP3MAIN_SDVOL_ID),0);
	}

}
/*******************************************************************************
* Function Name  : playMp3MainVolumeShow
* Description    : playMp3MainVolumeShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playMp3MainVolumeShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,PLAYMP3MAIN_VOLUME_VALUE_ID),RAM_ID_MAKE(task_com_curVolume_strid()));

}
/*******************************************************************************
* Function Name  : playMp3MainFileNameShow
* Description    : playMp3MainFileNameShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playMp3MainFileNameShow(winHandle handle, u32 visable)
{
	//uiWinSetVisible(winItem(handle,PLAYMP3MAIN_NAME_ID),visable);
	uiWinSetVisible(winItem(handle,PLAYMP3MAIN_INDEX_ID),visable);
	if(visable)
	{
		uiWinSetResid(winItem(handle,PLAYMP3MAIN_INDEX_ID),RAM_ID_MAKE(task_com_fileIndex_str()));
		//if(SysCtrl.file_cnt > 0)
		//{
		//	uiWinSetResid(winItem(handle,PLAYMP3MAIN_NAME_ID),RAM_ID_MAKE(task_playMp3_op.playfilename));
		//}else
		//{
		//	visable = 0;
		//}
	}



}
/*******************************************************************************
* Function Name  : playMp3MainBaterryShow
* Description    : playMp3MainBaterryShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playMp3MainBaterryShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,PLAYMP3MAIN_BATERRY_ID),task_com_battery_res_get());
	//uiWinSetVisible(winItem(handle,PLAYMP3MAIN_BATERRY_ID),1);
}
/*******************************************************************************
* Function Name  : playMp3MainTimeShow
* Description    : playMp3MainTimeShow
* Input          : winHandle handle,u32 playTime,u32 totalTime
* Output         : none
* Return         : none
*******************************************************************************/
static void playMp3MainTimeShow(winHandle handle, u32 visable)
{
	if(visable && !(SysCtrl.file_type & FILELIST_TYPE_MP3) )
	{
		visable = 0;
	}
	uiWinSetVisible(winItem(handle,PLAYMP3MAIN_TIME_ID),  visable);
	uiWinSetVisible(winItem(handle,PLAYMP3MAIN_TOTAL_TIME_ID),visable);
	uiWinSetVisible(winItem(handle,PLAYMP3MAIN_BAR_ID),   visable);
	if(visable)
	{	
		uiWinSetResid(winItem(handle,PLAYMP3MAIN_TIME_ID),RAM_ID_MAKE(task_com_play_time_str(0)));
		uiWinSetResid(winItem(handle,PLAYMP3MAIN_TOTAL_TIME_ID),RAM_ID_MAKE(task_com_play_time_str(1)));
		uiWinSetProgressRate(winItem(handle,PLAYMP3MAIN_BAR_ID), (SysCtrl.play_cur_time *100) / SysCtrl.play_total_time);
		
	}
	//deg_Printf("vis:%x %d\n",SysCtrl.file_type, visable);

}

/*******************************************************************************
* Function Name  : playMp3MainStatShow
* Description    : playMp3MainStatShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playMp3MainStatShow(winHandle handle, u32 visable)
{
	uiWinSetVisible(winItem(handle,PLAYMP3MAIN_STAT_ID),   visable);
}
/*******************************************************************************
* Function Name  : playMp3MainFreqShow
* Description    : playMp3MainFreqShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
ALIGNED(4) resID mp3FreqTab[] = {
	R_ID_ICON_MUSICBAR1,
	R_ID_ICON_MUSICBAR2,
	R_ID_ICON_MUSICBAR3,
	R_ID_ICON_MUSICBAR4,
	R_ID_ICON_MUSICBAR5,
	R_ID_ICON_MUSICBAR6,
	R_ID_ICON_MUSICBAR7,
	R_ID_ICON_MUSICBAR8,
	R_ID_ICON_MUSICBAR9,
	R_ID_ICON_MUSICBAR10,
	R_ID_ICON_MUSICBAR11,
	R_ID_ICON_MUSICBAR12,
	R_ID_ICON_MUSICBAR13,
	R_ID_ICON_MUSICBAR14,
	R_ID_ICON_MUSICBAR15,
	R_ID_ICON_MUSICBAR16,
	R_ID_ICON_MUSICBAR17,
	R_ID_ICON_MUSICBAR18,
	R_ID_ICON_MUSICBAR19,
	R_ID_ICON_MUSICBAR20,
	R_ID_ICON_MUSICBAR21,
	R_ID_ICON_MUSICBAR22,
	R_ID_ICON_MUSICBAR23,
	R_ID_ICON_MUSICBAR24,
	R_ID_ICON_MUSICBAR25
};

ALIGNED(4) u32 mp3FreqIndexTab[][6] =
{
	{0, 2,  3,  5,   4,  1},
	{1, 4,  6,  10,  8,  3},
	{2, 6,  9,  15,  12, 3},
	{0, 16, 20, 24,  8,  4},
	{1, 17, 21, 23,  7,  3},
	{2, 15, 19, 22,  6,  7},
	{4, 18, 17, 10,  18, 10},
	{5, 14, 21, 18,  16,  8},
	{16,20, 24, 8,   4,  12},
	{17,21, 23, 7,   3,  11},
	{15,19, 22, 6,   7,  13},
};
ALIGNED(4) u32 mp3FreqIDTab[] =
{
	PLAYMP3MAIN_FREQ_ID1,
	PLAYMP3MAIN_FREQ_ID2,
	PLAYMP3MAIN_FREQ_ID3,
	PLAYMP3MAIN_FREQ_ID4,
	PLAYMP3MAIN_FREQ_ID5,
	PLAYMP3MAIN_FREQ_ID6
};
static void playMp3MainFreqShow(winHandle handle, u32 visable)
{
	u32 i, j;
	u32 volmax = task_com_Volume_MaxIndex() - 1;
	if(visable)
	{
		for(i = 0; i < 6; i++)
		{
			j = mp3FreqIndexTab[task_playMp3_op.freqShowIndex][i] * SysCtrl.curVolume /volmax;
			uiWinSetResid(winItem(handle,mp3FreqIDTab[i]),mp3FreqTab[j]);
		}
		task_playMp3_op.freqShowIndex++;
		if(task_playMp3_op.freqShowIndex >= ARRAY_NUM(mp3FreqIndexTab))
		{
			task_playMp3_op.freqShowIndex = 0;
		}
	}
	uiWinSetVisible(winItem(handle,PLAYMP3MAIN_FREQ_ID1),   visable);
	uiWinSetVisible(winItem(handle,PLAYMP3MAIN_FREQ_ID2),   visable);
	uiWinSetVisible(winItem(handle,PLAYMP3MAIN_FREQ_ID3),   visable);
	uiWinSetVisible(winItem(handle,PLAYMP3MAIN_FREQ_ID4),   visable);
	uiWinSetVisible(winItem(handle,PLAYMP3MAIN_FREQ_ID5),   visable);
	uiWinSetVisible(winItem(handle,PLAYMP3MAIN_FREQ_ID6),   visable);
}
