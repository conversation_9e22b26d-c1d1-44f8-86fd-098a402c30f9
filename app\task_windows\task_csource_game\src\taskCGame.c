/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

ALIGNED(4) CGAME_OP_T task_cGame_op = {
	.cGame_state = C_GAME_STOP,
	.music_delay = 0,
	.openListTotal = 0,		
	.cGame_step = NES_GAME_INNER,
	.cGame_inner_index = 0,
	.cGame_inner_indexmax = 1,
	.curIdShowUp = 0,
	.curId =R_ID_IMAGE_SNAKE1,//R_ID_IMAGE_PUSHBOX1,
};
extern u16 game_frame_w;
extern u16 game_frame_h;
static int volumegame = 0;	
/*******************************************************************************
* Function Name  : taskCGameWinChangeProcess
* Description    : taskCGameWinChangeProcess function.
* Input          : 
* Output         : none                                            
* Return         : int fd : file handle
*******************************************************************************/
static void taskCGameWinChangeProcess(u8 enter)
{
	if(enter)
	{
		taskMainWinInit(0,MEDIA_SRC_NVFS, task_cGame_op.curId, 0,MAIN_TO_SUB_VOR_UP);
		if(taskWinChangeProcess() < 0)
		{
			taskCGameCurWinShow();
		}
	}else
	{
		taskMainWinInit(0,MEDIA_SRC_NVFS, task_cGame_op.curId, 0,SUB_TO_MAIN_VOR_DOWN);
	}
}


/*******************************************************************************
* Function Name  : app_taskCGameOpen
* Description    : APP LAYER: app_taskCGameOpen
* Input          : u32 arg
* Output         : none
* Return         : none
*******************************************************************************/

u8 *game_frame_buff = NULL;

static void app_taskCGameOpen(u32 arg)
{
	task_com_usb_dev_out(1);
	dusb_api_Uninit();
	app_lcdCsiVideoShowStop();
	hal_csiEnable(0);
#if  FUN_KID_FRAME_EN
	app_kid_frame_ctrl(KID_FRAME_DISTROY, 0);
#endif
#if  DEV_SENSOR_MAGIC_EN
	app_sensor_magic_ctrl(SENSOR_MAGIC_CHANGE_NONE, 0);
#endif
#if DEV_SENSOR_FILTER_EN
	app_sensor_filter_ctrl(SENSOR_FILTER_CHANGE_NONE);
#endif
	
#if DEV_SENSOR_LENS_EN
	app_sensor_lens_ctrl(SENSOR_LENS_CHANGE_NONE, 0);
#endif

	task_cGame_op.curId = 0;
	task_cGame_op.cGame_inner_index = 0;
	task_cGame_op.curId = R_ID_IMAGE_SNAKE1;
	
	//if(task_cGame_op.curId == 0)
	//	task_cGame_op.curId = R_ID_IMAGE_PUSHBOX1;
    taskCGameWinChangeProcess(1);
	uiOpenWindow(&cGameWindow, 0, 0);
    hal_lcdGetVideoResolution(&game_frame_w,&game_frame_h);
	game_frame_buff = hal_sysMemMalloc(game_frame_w*game_frame_h);
}
/*******************************************************************************
* Function Name  : app_taskCGameClose
* Description    : APP LAYER: app_taskCGameClose
* Input          : u32 arg
* Output         : none
* Return         : none
*******************************************************************************/
static void app_taskCGameClose(u32 arg)
{
  //  app_kid_frame_exit(0);
  	hal_sysMemFree(game_frame_buff);
 	game_frame_buff = NULL;
	task_com_usb_dev_out(0);
    taskCGameWinChangeProcess(0);
}
/*******************************************************************************
* Function Name  : app_taskPlayAudioOpen
* Description    : APP LAYER: app_taskPlayAudioOpen
* Input          : u32 arg
* Output         : none
* Return         : none
*******************************************************************************/
static void app_taskCGameService(u32 arg)
{
    if(task_cGame_op.cGame_state == C_GAME_START)
    {
        task_com_auto_poweroff(1);
        task_com_sreen_check(SREEN_CHECK_AUTOOFF); // system check,no event
        hal_wdtClear();
	XOSTimeDly(10);
        //deg_Printf("nes service end\n");
    }else
    {
        if(audioPlaybackGetStatus() == MEDIA_STAT_STOP) //循环播放背景音
        {
            if(task_cGame_op.music_delay == 0)
            {
                task_cGame_op.music_delay = XOSTimeGet();
            }else if((task_cGame_op.music_delay + 100) < XOSTimeGet())
            {
                task_cGame_op.music_delay = 0;
                res_music_end();
                volumegame = task_com_curVolume_get();
				// if (volumegame > 42)
				// {
				// 	volumegame = 42;
				// }

				res_music_start(R_ID_MUSIC_GAMES_MUSIC,0,volumegame);

            }
        }

    }
}

ALIGNED(4) sysTask_T taskCGame =
{
	"C_Game",
	0,
	app_taskCGameOpen,
	app_taskCGameClose,
	app_taskCGameService,
};



