/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	FORMAT_RECT_ID = 0,
	FORMAT_TIPS_ID,
	FORMAT_SELECT_ID
};

UNUSED ALIGNED(4) const widgetCreateInfor formatWin[] =
{
	createFrameWin(						Rx(58),	Ry(58), Rw(204),Rh(124),SMENU_FRAME_COLOR,WIN_ABS_POS),
	createRect(FORMAT_RECT_ID,          Rx(2),	<PERSON>y(2),  <PERSON>w(200),<PERSON>h(120),SMENU_UNSELECT_BG_COLOR),
	createStringIcon(FORMAT_TIPS_ID,	Rx(2),	Ry(2), 	Rw(200),Rh(40),R_ID_STR_FMT_FORMAT,ALIGNMENT_CENTER, SMENU_UNSELECT_FN_COLOR,DEFAULT_FONT),
	createItemManage(FORMAT_SELECT_ID,	Rx(10),	Ry(42), Rw(184),Rh(80),	SMENU_UNSELECT_BG_COLOR),
	widgetEnd(),
};

