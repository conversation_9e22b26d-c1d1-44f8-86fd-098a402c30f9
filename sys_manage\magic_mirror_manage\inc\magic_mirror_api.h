/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef MAGIC_MIRROR_API_H
#define MAGIC_MIRROR_API_H

#define MAGIC_MIRROR_FUNC_SUPPORT		1 //change this should updata lib file		
typedef enum
{
	MAGIC_MIRROR_NONE = 0,
    MAGIC_MIRROR_2TH_H,	//HEIGHT 方向二分
    MAGIC_MIRROR_2TH_H_MIRROR, //HEIGHT 方向二分镜像
    MAGIC_MIRROR_2TH_W,	//WIDTH 方向二分
    MAGIC_MIRROR_4TH,	//四分屏
    MAGIC_MIRROR_9TH,	//九分屏
    MAGIC_MIRROR_CRYSTAL, //菱形分屏
    MAGIC_MIRROR_MAX
}MAGIC_MIRROR_TYPE;
typedef enum
{
	MAGIC_MIRROR_CH_LCD,
    MAGIC_MIRROR_CH_CSI,
}MAGIC_MIRROR_CH;

typedef enum
{
	MAGIC_CH_NONE 		 = 0,
	MAGIC_CH_LCD		 = (1 << 0),    //LCD MCP EN
	MAGIC_CH_CSI		 = (1 << 1),	//CSI MCP EN
    MAGIC_CH_USENSOR	 = (1 << 2),	//USENSOR MCP EN
	MAGIC_ADD_CH_EN		 = (1 << 3),	//MCP EN
    MAGIC_LCD_BAK_UPDATA = (1 << 4),	//MCP BAK UPDATA
	MAGIC_CSI_BAK_UPDATA = (1 << 5),	//MCP BAK UPDATA

    MAGIC_LCD_CROP_CFG   = (1 << 6),	//CFG CROP
	MAGIC_CSI_CROP_CFG   = (1 << 7),	//CFG CROP

    MAGIC_LCD_CROP_RESET   = (1 << 8),	//CFG CROP
	MAGIC_CSI_CROP_RESET   = (1 << 9),	//CFG CROP
}MAGIC_MIRROR_STAT;

typedef struct MAGIC_MCP_TABLE_S
{
	u32 src_ofs;
    u32 dst_ofs;
	u32 cpy_len;
}MAGIC_MCP_TABLE_T;
typedef struct MAGIC_SRC_WIN_S
{
	u32 config_en;
	u32 cpy_index;

	u16 dst_w;
	u16 dst_h;
	u16 dst_pos_x;
	u16 dst_pos_y;

	u16 src_crop_sx;
	u16 src_crop_sy;
	u16 src_crop_ex;
	u16 src_crop_ey;

}MAGIC_SRC_WIN_T;
typedef struct MAGIC_MIRROR_CH_S
{
	u32 ch;  //0:lcd, 1: csi
	u16 src_width;
	u16 src_height;
	u16 src_stride;
	u16 dst_width;
	u16 dst_height;
	u16 dst_stride;
	MAGIC_SRC_WIN_T   crop_win;
	MAGIC_SRC_WIN_T   crop_win_bak;
	MAGIC_MCP_TABLE_T * mcp_tab;
    MAGIC_MCP_TABLE_T * mcp_tab_bak;
}MAGIC_MIRROR_CH_T;
typedef struct MAGIC_MIRROR_OP_S
{
	u32 magic_ch_en;
	u32 magic_type;
	MAGIC_MIRROR_CH_T	lcd_ch;
	MAGIC_MIRROR_CH_T	csi_ch;
}MAGIC_MIRROR_OP_T;

/*******************************************************************************
* Function Name  : magic_mirror_init
* Description    : magic_mirror_init
* Input          :
* Output         : none
* Return         : int 0;
*******************************************************************************/
void magic_mirror_init(void);
/*******************************************************************************
* Function Name  : magic_mirror_lcd_add
* Description    : magic_mirror_lcd_add
* Input          :
* Output         : none
* Return         : int 0;
*******************************************************************************/
void magic_mirror_lcd_add(u8 *yuv_buf);
/*******************************************************************************
* Function Name  : magic_mirror_csi_add
* Description    : magic_mirror_csi_add
* Input          :
* Output         : none
* Return         : int 0;
*******************************************************************************/
void magic_mirror_csi_add(u8 *yuv_buf);
/*******************************************************************************
* Function Name  : magic_mirror_lcd_updata
* Description    : magic_mirror_lcd_updata
* Input          :
* Output         : none
* Return         : 2: use magic crop , 1: use updata magic crop  , 0: use origin, -1: invalid
*******************************************************************************/
int magic_mirror_lcd_updata(MAGIC_SRC_WIN_T * win);
/*******************************************************************************
* Function Name  : magic_mirror_csi_updata
* Description    : magic_mirror_csi_updata
* Input          :
* Output         : none
* Return         : 2: use magic crop , 1: use updata magic crop  , 0: use origin, -1: invalid
*******************************************************************************/
int magic_mirror_csi_updata(MAGIC_SRC_WIN_T * win);
/*******************************************************************************
* Function Name  : kid_frame_create
* Description    : kid_frame_create:should  reset pip mode
* Input          :
* Output         : none
* Return         : -1 : MAGIC_MIRROR_NONE 0: other
*******************************************************************************/
int magic_mirror_create(u32 magic_type, u8 lcd_en, u8 csi_en);

#endif
