/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/

#ifndef HAL_LCD_H
     #define HAL_LCD_H
#include "hal_lcdMem.h"

#include "../../sys_manage/magic_mirror_manage/inc/magic_mirror_api.h"


enum LCDWIN_ID_e {
    LCDWIN_B,
    LCDWIN_A,
    LCDWIN_TOP_LAYER,
    LCDWIN_BOT_LAYER,
};

enum lcdwin_status_e {
    WINAB_DIS = 0,
    WINAB_EN,
};

enum buffer_win_sta_e {
    LB_IDLE   = (0<<0),
    LB_READY0 = (1<<2),
    LB_READY1 = (1<<3),
};

enum WINAB_PROCESS_STEP_E {
    WINAB_PROCESS_STEP_END = 0,
    WINAB_PROCESS_STEP_0_DECWIN,
    WINAB_PROCESS_STEP_0_DECSRC,
    WINAB_PROCESS_STEP_1_DECWIN,
    WINAB_PROCESS_STEP_1_DECWINTOTAL,

};
enum WINAB_JFRAME_STA_E {
    WINAB_JFRAME_STA_END = 0,
    WINAB_JFRAME_STA_SHOW,
    WINAB_JFRAME_STA_START,

};
#define LCD_RATIO_MAKE(w, h)         (((w)<<8 )| (h))
#define LCD_RATIO_GET_W(ratio)       (((ratio)>>8)&0xff)
#define LCD_RATIO_GET_H(ratio)       ((ratio)&0xff)
//lcd video-layer input channel
typedef struct _lcd_win_s {
    u8 status;            //通道使能
	u8 config_enable;	  //1:使能配置
    u8 src;               //数据来源
    u8 layer;
	u16 x,y; //video起始坐标作为原点
	u16 w,h;
    struct _lcd_win_s * bak;
} lcd_win_t;

typedef struct _lcd_show_ctrl_s
{
    u8  winAB_EN;
    u8  video_need_rotate;
    u8  ui_need_rotate;
    u8  video_scan_mode;
    u8  ui_scan_mode;
    u8  lcd_scaler_process;

    u8  winAB_process_step;
    //u8  jpg_frame_sta;
    u8  lcd_dma_sta;
    //u8  win_reset_en;
    u16 jpg_frame_sta;
    u8 *jpg_dcd_buf;
    u32 jpg_dcd_len;
    u32 win_pip_en;
    u32 lcd_fcnt;
    lcd_win_t * lcdwins[4];
    lcdshow_frame_t * display_preset;
    lcdshow_frame_t * displaying;
    lcdshow_frame_t * win_using;

    lcddev_t *  p_lcddev;
	// for LDMA crop
	u16 crop_sx,crop_ex,crop_sy,crop_ey;
    u16 video_x, video_y;
    u16 video_w, video_h;
    u16 video_scaler_w, video_scaler_h;
    u16 ui_x,  ui_y;
    u16 ui_w,  ui_h;
    u16 ratio_x, ratio_y;
    u16 ratio_w,ratio_h;
    u16 ratio_dest_w, ratio_dest_h;
    u32 len_win_en;

    MAGIC_SRC_WIN_T magic_win;
} _lcd_show_ctrl_t;

extern  _lcd_show_ctrl_t lcd_show_ctrl;

/*******************************************************************************
* Function Name  : hal_lcdCsiShowStart
* Description    : hardware layer ,lcd preview csi frame strat
* Input          : u16 width : sensor frame width
				   u16 height: sensor frame height
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdCsiShowStart(void);
/*******************************************************************************
* Function Name  : hal_lcdCsiFrameKickOnce
* Description    : hardware layer ,kick csi to lcd frame dma, not show lcd
* Output         : None
* Return         : None
*******************************************************************************/
int hal_lcdCsiFrameKickOnce(void);
/*******************************************************************************
* Function Name  : hal_lcdCsiShowStop
* Description    : hardware layer ,lcd preview csi frame stop
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdCsiShowStop(void);



/*******************************************************************************
* Function Name  : hal_lcdVideoShowFrameGet
* Description    : hardware layer ,get lcd displaying buffer
* Input          : none
* Output         : None
* Return         : lcdshow_frame_t *p_lcd_buffer : buffer frame addr
*******************************************************************************/
lcdshow_frame_t * hal_lcdVideoShowFrameGet(void);
/*******************************************************************************
* Function Name  : hal_lcdVideoIdleFrameMalloc
* Description    : hardware layer ,get lcd idle buffer,得到的buffer将用于csi output/mjpeg output
* Input          : none
* Output         : None
* Return         : lcdshow_frame_t *p_lcd_buffer : buffer frame addr
*******************************************************************************/
lcdshow_frame_t * hal_lcdVideoIdleFrameMalloc(void);
/*******************************************************************************
* Function Name  : hal_lcdVideoSetFrame
* Description    : hardware layer ,frame to lcd show
* Input          : lcdshow_frame_t *p_lcd_buffer : buffer frame addr
* Output         : None
* Return         : none
*******************************************************************************/
void hal_lcdVideoSetFrame(lcdshow_frame_t *p_lcd_buffer);

/*******************************************************************************
* Function Name  : hal_lcdVideoSetFrameWait
* Description    : hal_lcdVideoSetFrameWait
* Input          : lcdshow_frame_t *p_lcd_buffer : buffer frame addr
* Output         : None
* Return         : none
*******************************************************************************/
void hal_lcdVideoSetFrameWait(lcdshow_frame_t *p_lcd_buffer);

/*******************************************************************************
* Function Name  : hal_lcdGetSreenResolution
* Description    : hardware layer ,get lcd resolution,after rotate
* Input          : u16 *width : width
				   u16 *height : height
* Output         : None
* Return         : int 0: success
                          <0: fail
*******************************************************************************/
s32 hal_lcdGetSreenResolution(u16 *width,u16 *height);
/*******************************************************************************
* Function Name  : hal_lcdGetUiResolution
* Description    : hardware layer ,get lcd ui showing resolution
* Input          : u16 *width : width
                   u16 *height : height
* Output         : None
* Return         : int 0: success
                          <0: fail
*******************************************************************************/
s32 hal_lcdGetUiResolution(u16 *width,u16 *height);
/*******************************************************************************
* Function Name  : hal_lcdVideoFrameReset
* Description    : hal_lcdVideoFrameReset
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdVideoFrameReset(u16 x, u16 y, u16 w, u16 h, u16 dest_w, u16 dest_h);
/*******************************************************************************
* Function Name  : hal_lcdGetUiPosition
* Description    : hardware layer ,get lcd ui showing position
* Input          : u16 *x : y
                  u16 *x : y
* Output         : None
* Return         : int 0: success
                          <0: fail
*******************************************************************************/
s32 hal_lcdGetUiPosition(u16 *x,u16 *y);
/*******************************************************************************
* Function Name  : hal_lcdVideoScanModeGet
* Description    : hardware layer ,get lcd video showing rotate degree
* Input          : none
* Output         : None
* Return         : int :    
    LCD_DISPLAY_ROTATE_NONE = 0x00,
    LCD_DISPLAY_ROTATE_0    = 0x00,
    LCD_DISPLAY_ROTATE_90   = 0x01,
    LCD_DISPLAY_ROTATE_180  = 0x02,
    LCD_DISPLAY_ROTATE_270  = 0x03,

    LCD_DISPLAY_MIRROR_NONE = 0x00,
    LCD_DISPLAY_V_MIRROR    = 0x10,
    LCD_DISPLAY_H_MIRROR    = 0x30,
                          <0: fail
*******************************************************************************/
s32 hal_lcdVideoScanModeGet(void);
/*******************************************************************************
* Function Name  : hal_lcdUiScanModeGet
* Description    : hardware layer ,get lcd ui showing rotate degree
* Input          : none
* Output         : None
* Return         : int :    
    LCD_DISPLAY_ROTATE_NONE = 0x00,
    LCD_DISPLAY_ROTATE_0    = 0x00,
    LCD_DISPLAY_ROTATE_90   = 0x01,
    LCD_DISPLAY_ROTATE_180  = 0x02,
    LCD_DISPLAY_ROTATE_270  = 0x03,

                          <0: fail
*******************************************************************************/
s32 hal_lcdUiScanModeGet(void);
/*******************************************************************************
* Function Name  : hal_lcdGetVideoRatioResolution
* Description    : hardware layer ,get lcd showing ratio resolution
* Input          : u16 *width : width
				   u16 *height : height
* Output         : None
* Return         : int 0: success
                          <0: fail
*******************************************************************************/
s32 hal_lcdGetVideoRatioResolution(u16 *width,u16 *height);
/*******************************************************************************
* Function Name  : hal_lcdGetVideoRatioDestResolution
* Description    : hardware layer ,get lcd showing ratio resolution
* Input          : u16 *width : width
				   u16 *height : height
* Output         : None
* Return         : int 0: success
                          <0: fail
*******************************************************************************/
s32 hal_lcdGetVideoRatioDestResolution(u16 *width,u16 *height);
/*******************************************************************************
* Function Name  : hal_lcdGetVideoRatioPos
* Description    : hardware layer ,get lcd showing position
* Input          : u16 *width : width
				   u16 *height : height
* Output         : None
* Return         : int 0: success
                          <0: fail
*******************************************************************************/
s32 hal_lcdGetVideoRatioPos(u16 *x,u16 *y);
/*******************************************************************************
* Function Name  : hal_lcdGetVideoResolution
* Description    : hardware layer ,get lcd showing resolution
* Input          : u16 *width : width
				   u16 *height : height
* Output         : None
* Return         : int 0: success
                          <0: fail
*******************************************************************************/
s32 hal_lcdGetVideoResolution(u16 *width,u16 *height);
/*******************************************************************************
* Function Name  : hal_lcdGetVideoPos
* Description    : hardware layer ,get lcd showing position
* Input          : u16 *width : width
				   u16 *height : height
* Output         : None
* Return         : int 0: success
                          <0: fail
*******************************************************************************/
s32 hal_lcdGetVideoPos(u16 *x,u16 *y);


/*******************************************************************************
* Function Name  : hal_lcdMJPB_Decode_Done
* Description    : hardware layer ,lcd process for mjpB decode done
* Input          : int flag: mjpB decode flag
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcd_decwin_done(int flag);
/*******************************************************************************
* Function Name  : hal_lcdMJPB_Decode_Done
* Description    : hardware layer ,lcd process for mjpB decode done
* Input          : int flag: mjpB decode flag
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcd_encwin_done(void);
/*******************************************************************************
* Function Name  : hal_lcdRegister
* Description    : hardware layer ,lcd device register
* Input          : lcddev_t *lcd_op : lcd device node
* Output         : None
* Return         : int 0: success
*******************************************************************************/
s32 hal_lcdRegister(lcddev_t *lcd_op);
/*******************************************************************************
* Function Name  : hal_lcdBrightnessGet
* Description    : hardware layer 
* Input          : none
* Output         : None
* Return         : int :
*******************************************************************************/
s8 hal_lcdBrightnessGet(void);
/*******************************************************************************
* Function Name  : hal_lcdFrameEndCallBackRegister
* Description    : register callback for lcd frame end
* Input          : void (*callback)(void) : call back
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdFrameEndCallBackRegister(void (*callback)(void));



/*******************************************************************************
* Function Name  : hal_lcdSetCsiCrop
* Description    : set csi LDMA crop
* Input          : u16 crop_sx,u16 crop_ex,u16 crop_sy,u16 crop_ey
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetCsiCrop(u16 crop_sx,u16 crop_sy,u16 crop_ex,u16 crop_ey);
/*******************************************************************************
* Function Name  : hal_lcdVideoSetRotate
* Description    : hardware layer ,set lcd rotate degree
* Input          : u8 rotate :
    LCD_DISPLAY_ROTATE_NONE = 0x00,
    LCD_DISPLAY_ROTATE_0    = 0x00,
    LCD_DISPLAY_ROTATE_90   = 0x01,
    LCD_DISPLAY_ROTATE_180  = 0x02,
    LCD_DISPLAY_ROTATE_270  = 0x03,

    LCD_DISPLAY_MIRROR_NONE = 0x00,
    LCD_DISPLAY_V_MIRROR    = 0x10,
    LCD_DISPLAY_H_MIRROR    = 0x30,
* Output         : None
* Return         :
*******************************************************************************/
s32 hal_lcdVideoSetRotate(u8 rotate);
/*******************************************************************************
* Function Name  : hal_lcdUiSetRotate
* Description    : hardware layer ,set lcd ui rotate degree
* Input          : u8 rotate : rotate
    LCD_DISPLAY_ROTATE_NONE = 0x00,
    LCD_DISPLAY_ROTATE_0    = 0x00,
    LCD_DISPLAY_ROTATE_90   = 0x01,
    LCD_DISPLAY_ROTATE_180  = 0x02,
    LCD_DISPLAY_ROTATE_270  = 0x03,
* Output         : None
* Return         :
*******************************************************************************/
s32 hal_lcdUiSetRotate(u8 rotate);
/*******************************************************************************
* Function Name  : hal_lcdSetRatio
* Description    : hardware layer ,set lcd ratio
* Input          : u8 rotate : LCD_RATIO_MAKE(w, h)
* Output         : None
* Return         :
*******************************************************************************/
void hal_lcdSetRatio(u16 ratio);
/*******************************************************************************
* Function Name  : hal_lcdVideoNeedRotateGet
* Description    : hardware layer ,get lcd video need rotate or not
* Input          : u8 rotate : rotate degree
* Output         : None
* Return         :
*******************************************************************************/
bool hal_lcdVideoNeedRotateGet(void);
/*******************************************************************************
* Function Name  : hal_lcdUiNeedRotateGet
* Description    : hardware layer, get lcd ui need rotate or not
* Input          : u8 rotate : rotate degree
* Output         : None
* Return         :
*******************************************************************************/
bool hal_lcdUiNeedRotateGet(void);
/*******************************************************************************
* Function Name  : hal_lcdSetBufYUV
* Description    : memset buffer color,but U must equ V
* Input          : buffer: lcd buffer pointer
                   y:
                   u: must u = v
                   v: must u = v
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetBufYUV(lcdshow_frame_t * buffer,u8 y,u8 u,u8 v);
/*******************************************************************************
* Function Name  : hal_lcdSetBufYUV
* Description    : memset buffer color,but U must equ V
* Input          : buffer: lcd buffer pointer
                   y:
                   u: must u = v
                   v: must u = v
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetBufYUV_2(u8 * buffer,u32 buf_size,u8 y,u8 uv);
/*******************************************************************************
* Function Name  : hal_lcdSetWINAB
* Description    : set lcd video channles
* Input          : u8 src:video channle source,enum {LCDWIN_B,LCDWIN_A}
*                  u8 layer:video channle layer,enum {LCDWIN_TOP_LAYER,LCDWIN_BOT_LAYER}
*                  u16 x:if x == -1,means don't change this parameter
*                  u16 y:
*                  u16 w:
*                  u16 h:
*                  u8 win_en:channle enable,enum {WINAB_EN,WINAB_DIS},if win_en == -1,means don't change
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetWINAB(u8 src,u8 layer,
                  u16 x,u16 y,u16 w,u16 h,
                  u8 win_en);
/*******************************************************************************
* Function Name  : hal_lcdWinEnablePreSet
* Description    : prepare set lcd win enable/disbale,
*                  take effect when next csi frame done
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdWinEnablePreSet(u8 enable);
/*******************************************************************************
* Function Name  : hal_lcdSetWinEnable
* Description    : set lcd win enable/disbale,take effect immediately
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetWinEnable(u8 enable);
/*******************************************************************************
* Function Name  : lcd_struct_get
* Description    : lcd_struct_get
* Input          :
* Output         : lcddev_t * p_lcd_struct
* Return         : none
*******************************************************************************/
lcddev_t * lcd_struct_get(void);
/*******************************************************************************
* Function Name  : hal_lcdLCMPowerOff
* Description    : lcd module power off sequence
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdLCMPowerOff(void);
/*******************************************************************************
* Function Name  : hal_lcdParaLoad
* Description    : load lcddev_t from resource
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
void hal_lcdParaLoad(lcddev_t * dev);
/*******************************************************************************
* Function Name  : hal_lcdPQToolGetInfo
* Description    :
* Input          : u32 * buffer : write info to the buffer
                   u32 size : buffer size
* Output         : none
* Return         : none
*******************************************************************************/
void hal_lcdPQToolGetInfo(u32 * buffer, u32 size);
/*******************************************************************************
* Function Name  : hal_lcdWinUpdata
* Description    : set lcd video channles
* Input          : None
* Output         : None
* Return         : int 0: success
                    <0: fail
*******************************************************************************/
int hal_lcdWinUpdata(void);
/*******************************************************************************
* Function Name  : hal_lcdWinUpdataCheckDone
* Description    : 
* Input          : None
* Output         : None
* Return         : int 0: done
                    <0: busy
*******************************************************************************/
int hal_lcdWinUpdataCheckDone(void);
/*******************************************************************************
* Function Name  : hal_lcdwin_buf_cfg
* Description    : hardware layer ,switch frame when csi frame end
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_lcdwin_buf_cfg(u8 * buf, u32 len);
/*******************************************************************************
* Function Name  : hal_lcdwin_framesta_get
* Description    : hardware layer ,switch frame when csi frame end
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
u16 hal_lcdwin_framesta_get(void);
/*******************************************************************************
* Function Name  : hal_lcdwin_framesta_reset
* Description    : hardware layer ,switch frame when csi frame end
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_lcdwin_framesta_reset(int reset);
/*******************************************************************************
* Function Name  : hal_CSI_lcdFrameEndCallback
* Description    : hardware layer ,switch frame when csi frame end
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/

void hal_CSI_lcdFrameEndCallback(void);
/*******************************************************************************
* Function Name  : hal_CSI_lcdFrameEndOnceCallback
* Description    : hardware layer ,csi to lcd dma done callback, only fill buf but no show
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_CSI_lcdFrameEndOnceCallback(void);

/*******************************************************************************
* Function Name  : hal_lcd_fcnt_mnt
* Description    : hal layer .mjpA encode frame debg
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
u32 hal_lcd_fcnt_mnt(void);
#endif
