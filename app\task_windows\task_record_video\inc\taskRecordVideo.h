/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef  __TASK_RECORD_VIDEO_H
#define  __TASK_RECORD_VIDEO_H

#define VIDEO_STDAVI_TMP_FILE	"VIDEO.tmp"
#define VIDEOA_STDAVI_TMP_FILE	"VIDEOA.tmp"
#define VIDEOB_STDAVI_TMP_FILE	"VIDEOB.tmp"

typedef enum{
	RECORD_MODE_NORMAL = 0,
	RECORD_MODE_SLOW,
	RECORD_MODE_DELAY,
}RECORD_MODE_E;
typedef enum{
    RECORD_NONE_MODE = 0,
    RECORD_KIDFRAME_MODE,
    RECORD_FILTER_MODE,
}RECORD_ADD_MODE;
typedef struct{
	u32 recordMode; //
    //u32 recordDelayTimeSec;    // sec 
    //u32 recordDelayTimeCur;    // sec 
    //u32 recodeDelayKick;
	u32 winState;
	u32 jpgsize;
	u8* jpgbuf;
	u32 wintype; //
	int step;
	u32 record_add_mode;
	u8  upkeystate;
	u8  downKeyState;
}taskRecordVideoOp;

EXTERN_WINDOW(recordVideoWindow);
extern sysTask_T taskRecordVideo;
extern taskRecordVideoOp recordVideoOp;
/*******************************************************************************
* Function Name  : app_taskRecordVideo_start
* Description    : app_taskRecordVideo_start function.
* Input          : 
* Output         : none                                            
* Return         : int fd : file handle
*******************************************************************************/
int app_taskRecordVideo_start(void);
/*******************************************************************************
* Function Name  : app_taskRecordVideo_stop
* Description    : app_taskRecordVideo_stop function.
* Input          : 
* Output         : none                                            
* Return         : int: >=0 stop success
*******************************************************************************/
int app_taskRecordVideo_stop(void);
/*******************************************************************************
* Function Name  : app_taskRecordVideo_start
* Description    : app_taskRecordVideo_start function.
* Input          : 
* Output         : none                                            
* Return         : int fd : file handle
*******************************************************************************/
void app_taskRecordVideo_caltime(void);
/*******************************************************************************
* Function Name  : app_taskRecordVideo_Capture
* Description    : app_taskRecordVideo_Capture function.
* Input          : 
* Output         : none                                            
* Return         : int fd : file handle
*******************************************************************************/
void app_taskRecordVideo_Capture(u32 cmd);

/*******************************************************************************
* Function Name  : taskRecordVideoModeSwitch
* Description    : taskRecordVideoModeSwitch function.
* Input          : 
* Output         : none                                            
* Return         : int fd : file handle
*******************************************************************************/
void taskRecordVideoModeSwitch(void);

/*******************************************************************************
* Function Name  : taskRecordVideoWinShowKick
* Description    : take a photo by user config
* Input          : none
* Output         : none                                            
* Return         : int <0 fail
*******************************************************************************/
int taskRecordVideoWinShowKick(void);
/*******************************************************************************
* Function Name  : taskRecordVideoWinShowProcess
* Description    : take a photo by user config
* Input          : none
* Output         : none                                            
* Return         : int <0 fail
*******************************************************************************/
int taskRecordVideoWinShowProcess(void);

#endif
