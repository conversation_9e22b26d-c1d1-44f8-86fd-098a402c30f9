/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskMainWin.c"
extern u8 flash_success;

#if Five_menus==0&&Doraemon==0

	ALIGNED(4) const MAIN_MENU_TAB mainMenuTab[] = {
		{R_ID_IMAGE_MAIN_PHOTO, 		TASK_RECORD_PHOTO},
		{R_ID_IMAGE_MAIN_VIDEO,  		TASK_RECORD_VIDEO},
		{R_ID_IMAGE_MAIN_PLAY, 			TASK_PLAY_VIDEO},
		{R_ID_IMAGE_MAIN_SETTINGS,		TASK_SETTING},
	#if No_games
		{R_ID_IMAGE_MAIN_GAME,			TASK_VERSION},
	#else
		{R_ID_IMAGE_MAIN_GAME,			TASK_C_GAME},
	#endif
		{R_ID_IMAGE_MAIN_MP3,			TASK_PLAY_MP3}
	};
#elif Five_menus==0&&Doraemon==1






#else
	ALIGNED(4) const MAIN_MENU_TAB mainMenuTab[] = {
		{R_ID_IMAGE_MAIN_PHOTO, 		TASK_RECORD_PHOTO},
		{R_ID_IMAGE_MAIN_VIDEO,  		TASK_RECORD_VIDEO},
		{R_ID_IMAGE_MAIN_PLAY, 			TASK_PLAY_VIDEO},
		{R_ID_IMAGE_MAIN_SETTINGS,		TASK_SETTING},
		//{R_ID_IMAGE_MAIN_GAME,			TASK_VERSION},
		{R_ID_IMAGE_MAIN_MP3,			TASK_PLAY_MP3}
	};

#endif
#define MAIN_ID_MAX			(sizeof(mainMenuTab)/sizeof(mainMenuTab[0]))


/*******************************************************************************
* Function Name  : taskMainService
* Description    : taskMainService function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
void taskMainCurIdCfg(uint32 taskId)
{
	u32 i;
	for(i = 0; i < MAIN_ID_MAX; i++)
	{
		if(taskId == mainMenuTab[i].taskId)
		{
			mainTaskOp.curId = i;
			break;
		}
	}		
}

/*******************************************************************************
* Function Name  : taskMainWinShow
* Description    : taskMainWinShow
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
void taskMainWinShow(void)
{
	resID id = mainTaskOp.curIdShowBig ? mainMenuTab[mainTaskOp.curId].resId :R_ID_IMAGE_MAIN_BACKGROUND ;
	res_image_show(id, 0);

}
/*******************************************************************************
* Function Name  : mainKeyMsgOk
* Description    : mainKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		app_taskStart(mainMenuTab[mainTaskOp.curId%MAIN_ID_MAX].taskId,0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : mainKeyMsgUp
* Description    : mainKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		mainTaskOp.curIdShowBig = 0;
		taskMainWinShow();
#if Five_menus==0
		if(mainTaskOp.curId >= MAIN_ID_MAX/2)
		{
			mainTaskOp.curId -= MAIN_ID_MAX/2;
		}else
		{
			mainTaskOp.curId += MAIN_ID_MAX/2;
		}
#else
		if (mainTaskOp.curId == 0)
		{
			mainTaskOp.curId = 3;
		}else if (mainTaskOp.curId == 1)
		{
			mainTaskOp.curId = 4;
		}else if (mainTaskOp.curId == 2)
		{
			mainTaskOp.curId = 4;
		}else if (mainTaskOp.curId == 3)
		{
			mainTaskOp.curId = 0;
		}else if (mainTaskOp.curId == 4)
		{
			mainTaskOp.curId = 1;
		}
#endif
		mainTaskOp.curIdShowBig = 1;
		taskMainWinShow();
		mainTaskOp.winShowToggleCurtime = XOSTimeGet();
	}
	return 0;
}
/*******************************************************************************
* Function Name  : mainKeyMsgDown
* Description    : mainKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		mainTaskOp.curIdShowBig = 0;
		taskMainWinShow();
#if Five_menus==0
		if(mainTaskOp.curId >= MAIN_ID_MAX/2)
		{
			mainTaskOp.curId -= MAIN_ID_MAX/2;
		}else
		{
			mainTaskOp.curId += MAIN_ID_MAX/2;
		}
#else
		if (mainTaskOp.curId == 3)
		{
			mainTaskOp.curId = 0;
		}else if (mainTaskOp.curId == 4)
		{
			mainTaskOp.curId = 1;
		}else if (mainTaskOp.curId == 0)
		{
			mainTaskOp.curId = 3;
		}else if (mainTaskOp.curId == 1)
		{
			mainTaskOp.curId = 4;
		}
		else if (mainTaskOp.curId == 2)
		{
			mainTaskOp.curId = 4;
		}
#endif
		mainTaskOp.curIdShowBig = 1;
		taskMainWinShow();
		mainTaskOp.winShowToggleCurtime = XOSTimeGet();
	}
	return 0;
}
/*******************************************************************************
* Function Name  : mainKeyMsgLeft
* Description    : mainKeyMsgLeft
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainKeyMsgLeft(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		mainTaskOp.curIdShowBig = 0;
		taskMainWinShow();	
		if(mainTaskOp.curId <= 0)
			mainTaskOp.curId = MAIN_ID_MAX -1;
		else
			mainTaskOp.curId--;
		mainTaskOp.curIdShowBig = 1;
		taskMainWinShow();
		mainTaskOp.winShowToggleCurtime = XOSTimeGet();
	}
	return 0;
}
/*******************************************************************************
* Function Name  : mainKeyMsgRight
* Description    : mainKeyMsgRight
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainKeyMsgRight(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		mainTaskOp.curIdShowBig = 0;
		taskMainWinShow();
		mainTaskOp.curId++;
		if(mainTaskOp.curId >=  MAIN_ID_MAX)
			mainTaskOp.curId = 0;
		mainTaskOp.curIdShowBig = 1;
		taskMainWinShow();
		mainTaskOp.winShowToggleCurtime = XOSTimeGet();
	}
	return 0;
}

/*******************************************************************************
* Function Name  : mainSysMsgUSBDEV
* Description    : mainSysMsgUSBDEV
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainSysMsgUSBDEV(winHandle handle,uint32 parameNum,uint32* parame)
{
	//if(SysCtrl.dev_dusb_stat == USBDEV_STAT_PC)
	//	app_taskStart(TASK_USB_DEVICE,0);
#if CHARGE_OUT_OF_USER 
//	app_taskStart(TASK_BAT_CHARGE,1);

#endif

	return 0;
}
/*******************************************************************************
* Function Name  : mainSysMsg1sec
* Description    : mainSysMsg1sec
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainSysMsgTimeUpdata(winHandle handle,uint32 parameNum,uint32* parame)
{
	mainTaskOp.curIdShowBig ^= 1;
	taskMainWinShow();
	return 0;
}

/*******************************************************************************
* Function Name  : mainOpenWin
* Description    : mainOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	//task_com_sound_wait_end();
	//res_music_end();
	deg_Printf("[WIN]mainOpenWin\n");
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL&&!flash_success){//wxn-�Ǽ���flash��ʾ
		task_com_tips_show(TIPS_ERROR);
		app_taskStart(TASK_POWER_OFF,0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : mainCloseWin
* Description    : mainCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]mainCloseWin\n");
	
	return 0;
}
/*******************************************************************************
* Function Name  : mainWinChildClose
* Description    : mainWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	mainTaskOp.curIdShowBig = 1;
	taskMainWinShow();
	mainTaskOp.winShowToggleCurtime = XOSTimeGet();
	deg_Printf("[WIN]mainWinChildClose\n");
	return 0;
}


ALIGNED(4) msgDealInfor mainMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	mainOpenWin},
	{SYS_CLOSE_WINDOW,	mainCloseWin},
	{SYS_CHILE_COLSE,	mainWinChildClose},
	{KEY_EVENT_OK,		mainKeyMsgOk},
	{KEY_EVENT_UP,		mainKeyMsgUp},
	{KEY_EVENT_DOWN,	mainKeyMsgDown},
	{KEY_EVENT_LEFT,	mainKeyMsgLeft},
	{KEY_EVENT_RIGHT,	mainKeyMsgRight},
	{SYS_EVENT_USBDEV,	mainSysMsgUSBDEV},
	{SYS_EVENT_TIME_UPDATE,mainSysMsgTimeUpdata},

	{EVENT_MAX,NULL},
};

WINDOW(mainWindow,mainMsgDeal,mainWin)
