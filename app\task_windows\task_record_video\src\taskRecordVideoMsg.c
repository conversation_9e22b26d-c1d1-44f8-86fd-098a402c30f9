/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskRecordVideoWin.c"

#define KEY_DONW_SREEN_SAVE		1
/*******************************************************************************
* Function Name  : videoKeyMsgOk
* Description    : videoKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(videoRecordGetStatus() == MEDIA_STAT_START)
		{
			app_taskRecordVideo_stop();
			taskRecordVideoWinShowKick();
			taskRecordVideoWinShowProcess();
		}
		else{
			app_taskRecordVideo_start();
		}

	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgUp
* Description    : videoKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];

	if(keyState == KEY_RELEASE && recordVideoOp.upkeystate  == KEY_PRESSED)
	{
		if(videoRecordGetStatus() == MEDIA_STAT_STOP)
		{
		#if FUN_KID_FRAME_EN || DEV_SENSOR_FILTER_EN
			//deg_Printf("ENTER [MODE:%d, %d,%d]\n",recordVideoOp.record_add_mode,SysCtrl.sensor_filter_index, SysCtrl.kid_frame_index);
			if(recordVideoOp.record_add_mode == RECORD_NONE_MODE)
			{
				recordVideoOp.record_add_mode = RECORD_FILTER_MODE;
			}
			if(recordVideoOp.record_add_mode == RECORD_FILTER_MODE)
			{
			#if DEV_SENSOR_FILTER_EN
				app_sensor_filter_ctrl(SENSOR_FILTER_CHANGE_PREV);
			#endif
				if(SysCtrl.sensor_filter_index < 0)
					recordVideoOp.record_add_mode = RECORD_KIDFRAME_MODE;
			}
			if(recordVideoOp.record_add_mode == RECORD_KIDFRAME_MODE)
			{
			#if FUN_KID_FRAME_EN
			//	app_kid_frame_ctrl(KID_FRAME_SHOWPREV, (MJPEG_VIDEO_ENC_TYPE == MJPEG_ENC_SRC_LCD) ? 0 : 1);
			#endif
				if(SysCtrl.kid_frame_index < 0)
				{
				#if FUN_KID_FRAME_EN
					app_kid_frame_ctrl(KID_FRAME_DISTROY, 0);
				#endif
					recordVideoOp.record_add_mode = RECORD_NONE_MODE;
				}
			}
			//deg_Printf("EXIT [MODE:%d, %d,%d]\n",recordVideoOp.record_add_mode,SysCtrl.sensor_filter_index, SysCtrl.kid_frame_index);

		#endif
		}
	}else if(keyState == KEY_RELEASE && recordVideoOp.upkeystate  == KEY_CONTINUE)
	{
		{
			task_com_keysound_play();
			task_com_sound_wait_end();
			recordvideoLineShow(handle, 2); //yellow
			videoScalerBarShow(handle, 0);
			app_draw_Service(1);
			hal_sysDelayMS(10);

			recordvideoLineShow(handle, 0); //tranfer
			app_draw_Service(1);
			
		}

	}else if(keyState == KEY_CONTINUE)
	{
		{
		#if LCDSHOW_CSI_SCALE
			while(hal_lcdWinUpdataCheckDone()) hal_wdtClear;
			app_lcdVideoShowScaler_cfg(1);
			videoScalerShow(handle);
			videoScalerBarShow(handle, 1);
			if(recordVideoOp.upkeystate  == KEY_PRESSED)
			{
				recordvideoLineShow(handle, 1); //RED
				app_draw_Service(1);
			}
		#endif
		}
	}
	recordVideoOp.upkeystate = keyState;
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgDown
* Description    : videoKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	deg_Printf("[video MSG]keyState=%d,recordVideoOp.downKeyState=%d\r\n",keyState,recordVideoOp.downKeyState);
	if(keyState == KEY_RELEASE && recordVideoOp.downKeyState  == KEY_PRESSED)
	{
	task_com_tips_show(TIPS_SCREEN_PRO);
	hal_sysDelayMS(200);
	#if 1
		task_com_pwmMinus();
		task_com_sreen_check(SREEN_SET_OFF);
		
	#else
		if(videoRecordGetStatus() == MEDIA_STAT_STOP)
		{
		#if FUN_KID_FRAME_EN || DEV_SENSOR_FILTER_EN
			//deg_Printf("ENTER [MODE:%d, %d,%d]\n",recordVideoOp.record_add_mode,SysCtrl.sensor_filter_index, SysCtrl.kid_frame_index);
			if(recordVideoOp.record_add_mode == RECORD_NONE_MODE)
			{
				recordVideoOp.record_add_mode = RECORD_KIDFRAME_MODE;
			}
			if(recordVideoOp.record_add_mode == RECORD_KIDFRAME_MODE)
			{
			#if FUN_KID_FRAME_EN
				app_kid_frame_ctrl(KID_FRAME_SHOWNEXT,(MJPEG_VIDEO_ENC_TYPE == MJPEG_ENC_SRC_LCD) ? 0 : 1);
			#endif
				if(SysCtrl.kid_frame_index < 0)
				{
				#if FUN_KID_FRAME_EN
					app_kid_frame_ctrl(KID_FRAME_DISTROY,0);
				#endif
					recordVideoOp.record_add_mode = RECORD_FILTER_MODE;
				}
			}
			if(recordVideoOp.record_add_mode == RECORD_FILTER_MODE)
			{
			#if DEV_SENSOR_FILTER_EN
				app_sensor_filter_ctrl(SENSOR_FILTER_CHANGE_NEXT);
			#endif
				if(SysCtrl.sensor_filter_index < 0)
					recordVideoOp.record_add_mode = RECORD_NONE_MODE;
			}
			//deg_Printf("EXIT [MODE:%d, %d,%d]\n",recordVideoOp.record_add_mode,SysCtrl.sensor_filter_index, SysCtrl.kid_frame_index);
		#endif
		}
	#endif
	}else if(keyState == KEY_RELEASE && recordVideoOp.downKeyState  == KEY_CONTINUE)
	{
		{
			task_com_keysound_play();
			task_com_sound_wait_end();
			recordvideoLineShow(handle, 2); //yellow
			videoScalerBarShow(handle, 0);
			app_draw_Service(1);
			hal_sysDelayMS(10);

			recordvideoLineShow(handle, 0); //tranfer
			app_draw_Service(1);
			
		}

	}else if(keyState == KEY_CONTINUE)
	{
		{
		#if LCDSHOW_CSI_SCALE
			while(hal_lcdWinUpdataCheckDone()) hal_wdtClear;
			app_lcdVideoShowScaler_cfg(-1);
			videoScalerShow(handle);
			videoScalerBarShow(handle, 1);
			if(recordVideoOp.downKeyState  == KEY_PRESSED)
			{
				recordvideoLineShow(handle, 1); //RED
				app_draw_Service(1);
			}
		#endif
		}		

	}
	recordVideoOp.downKeyState = keyState;
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgLeft
* Description    : videoKeyMsgLeft
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgLeft(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(videoRecordGetStatus() == MEDIA_STAT_STOP)
		{
			taskRecordVideoModeSwitch();
			videoRecordModeShow(handle);
			videoRemainTimeShow(handle);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgRight
* Description    : videoKeyMsgRight
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgRight(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(videoRecordGetStatus() == MEDIA_STAT_STOP)
		{
			app_Cmos_Sensor_Switch();
			recordVideoOp.record_add_mode = RECORD_NONE_MODE;
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgPower
* Description    : videoKeyMsgPower
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(videoRecordGetStatus() != MEDIA_STAT_STOP)
		{
			app_taskRecordVideo_stop();
			taskRecordVideoWinShowKick();
			taskRecordVideoWinShowProcess();
		}
		SysCtrl.winChangeEnable = 1;
		app_taskStart(TASK_MAIN,0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsgSD
* Description    : videoSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL && videoRecordGetStatus() == MEDIA_STAT_START) // sdc out when recording
	{
		app_taskRecordVideo_stop();
	}
	videoRemainTimeShow(handle);
	videoSDShow(handle);

	task_com_tips_show(TIPS_TYPE_SD);
	if(task_com_get_aging()){
		videoTESTONShow(handle);
		task_com_set_aging(0);
	XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsgUSB
* Description    : videoSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{
	videoBaterryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsgBattery
* Description    : videoSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
		videoBaterryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsgMD
* Description    : videoSysMsgMD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
//static int videoSysMsgMD(winHandle handle,u32 parameNum,u32* parame)
//{
//	if(SysCtrl.rec_md_time == 0 && videoRecordGetStatus() != MEDIA_STAT_START)
//	{
//		videoRecordCmdSet(CMD_COM_MDTIME, FUN_MOTION_DEC_TIME*1000);
//		app_taskRecordVideo_start();
//		SysCtrl.rec_md_time	= 1;//XOSTimeGet();
//		deg_Printf("md start\n");
//	}
//
//	return 0;
//}
/*******************************************************************************
* Function Name  : videoSysMsgRecord
* Description    : videoSysMsgRecord
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsgRecord(winHandle handle,u32 parameNum,u32* parame)
{
	u32 type = MSG_RECORD_MAX;
	if(parameNum == 1)
		type = parame[0];
	if(type == MSG_RECORD_MAX)
	{
		return 0;
	}
	else if(type == MSG_RECORD_START)
	{
		SysCtrl.rec_show_time = 0;
		//SysCtrl.dev_stat_gsensorlock = 0;
		videoRecTimeShow(handle);
		videoRecPointShow(handle, 1);
	}else if(type == MSG_RECORD_STOP || type == MSG_RECORD_ERROR)
	{
		SysCtrl.dev_stat_gsensorlock = 0;
		videoRemainTimeShow(handle);
		videoRecPointShow(handle, 0);

	}else if(type == MSG_RECORD_RESTART)
	{
		if(videoRecordGetStatus() != MEDIA_STAT_START)
			app_taskRecordVideo_start();
	}else if(type == MSG_RECORD_LOCK)
	{
		SysCtrl.dev_stat_gsensorlock = 1;
	}
	if(type == MSG_RECORD_START)
		app_draw_Service(1);
	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsg1S
* Description    : videoSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
	static u32 flag = 0;
		if(task_com_get_aging()){
			videoTESTONShow(handle);
			task_com_set_aging(0);
			XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
		}	


	videoSysTimeShow(handle);
	//if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
	//{
	//	if(uiWinIsVisible(winItem(handle,VIDEO_BATERRY_ID)))
	//		uiWinSetVisible(winItem(handle,VIDEO_BATERRY_ID),0);
	//	else
	//	{
    //        uiWinSetVisible(winItem(handle,VIDEO_BATERRY_ID),1);
	//		uiWinSetResid(winItem(handle,VIDEO_BATERRY_ID),R_ID_ICON_MTBATTERYCHARGE);
	//	}
	//}
	//if(user_config_get(CONFIG_ID_MOTIONDECTION) == R_ID_STR_COM_ON)
	//{
	//	if(SysCtrl.rec_md_time && XOSTimeGet()- SysCtrl.rec_md_time >= FUN_MOTION_DEC_TIME*1000)
	//	{
	//		app_taskRecordVideo_stop();
	//		SysCtrl.rec_md_time = 0;
	//	}
	//}


	if(videoRecordGetStatus() == MEDIA_STAT_START)
	{
		//task_com_sreen_check(SREEN_RESET_AUTOOFF);
		task_com_auto_poweroff(1);
		if(flag&1)
			dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 1);
		else
			dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 0);
		flag ^= 1;
	}
	//app_draw_Service(1);
	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsgTimeUpdate
* Description    : videoSysMsgTimeUpdate
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsgTimeUpdate(winHandle handle,u32 parameNum,u32* parame)
{
	//deg_Printf("videoSysMsgTimeUpdate\n");
	if(videoRecordGetStatus() == MEDIA_STAT_START)
	{
		videoRecPointShow(handle, 1);
		videoRecTimeShow(handle);
	}else
	{
		
		videoRecPointShow(handle, 0);
		videoRemainTimeShow(handle);
		
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoOpenWin
* Description    : videoOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]videoOpenWin\n");
	videoSDShow(handle);
	videoResolutionShow(handle);
	videoRemainTimeShow(handle);
	videoRecordModeShow(handle);
	videoSysTimeShow(handle);
	videoBaterryShow(handle);
	videoScalerShow(handle);
	videoScalerBarShow(handle, 0);
	videoRecPointShow(handle, 0);
	recordvideoLineShow(handle, 0); //tranfer

	return 0;
}
/*******************************************************************************
* Function Name  : videoCloseWin
* Description    : videoCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]videoCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : videoWinChildClose
* Description    : videoWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]videoWinChildClose\n");
	if(videoRecordGetStatus() != MEDIA_STAT_START)
		videoRemainTimeShow(handle);
	else
	{
		videoRecTimeShow(handle);
	}
	videoSDShow(handle);
	videoResolutionShow(handle);
	videoRecordModeShow(handle);
	videoSysTimeShow(handle);
	videoBaterryShow(handle);
	videoScalerShow(handle);
	videoScalerBarShow(handle, 0);
	videoRecPointShow(handle, 0);
	recordvideoLineShow(handle, 0); //tranfer
	return 0;
}
/*******************************************************************************
* Function Name  : videoWinChildOpen
* Description    : videoWinChildOpen
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoWinChildOpen(winHandle handle,uint32 parameNum,uint32* parame)
{
	deg_Printf("[WIN]videoWinChildOpen\n");
	return 0;
}


ALIGNED(4) msgDealInfor recordVideoMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		videoOpenWin},
	{SYS_CLOSE_WINDOW,		videoCloseWin},
	{SYS_CHILE_COLSE,		videoWinChildClose},
	{SYS_CHILE_OPEN,		videoWinChildOpen},
	{KEY_EVENT_OK,			videoKeyMsgOk},
	{KEY_EVENT_UP,			videoKeyMsgUp},
	{KEY_EVENT_DOWN,		videoKeyMsgDown},
	{KEY_EVENT_LEFT,		videoKeyMsgLeft},
	{KEY_EVENT_RIGHT,		videoKeyMsgRight},
	{KEY_EVENT_POWER,		videoKeyMsgPower},
	{SYS_EVENT_SDC,			videoSysMsgSD},
	{SYS_EVENT_USBDEV,		videoSysMsgUSB},
	{SYS_EVENT_BAT,			videoSysMsgBattery},
	//{SYS_EVENT_MD,			videoSysMsgMD},
	{SYS_EVENT_RECORD,		videoSysMsgRecord},
	{SYS_EVENT_1S,			videoSysMsg1S},
	{SYS_EVENT_TIME_UPDATE, videoSysMsgTimeUpdate},
	{EVENT_MAX,				NULL},
};

WINDOW(recordVideoWindow,recordVideoMsgDeal,recordVideoWin)


