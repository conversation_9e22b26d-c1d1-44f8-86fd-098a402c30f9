/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskGamePushBoxWin.c"
#define BOXNUM 8
#define GAMELE 6
static INT8U curx,cury;
INT8U g_liu=0; //推�?�子关卡

static INT8U game_continue;
 static INT8U game_Level;
static u8* pushbox_temp_icons = NULL;
static u8 passnum = 0;
extern INT8U game_over_in;

extern u8 *game_frame_buff;

extern u16 game_frame_w;
extern u16 game_frame_h;


INT8U get_gui_flush_sta()
{
	return SysCtrl.gui_flush_sta;

}

#if 1//defined(  ICON_SAVE_DEBUG)
	//---------------------------------game--------------------------------------
	INT8U* figure_up;  // man up //24*24
	INT8U* figure_down;// man down //24*24
	INT8U* figure_left;// man left//24*24
	INT8U* figure_right;// man right //24*24
	INT8U* figure_bg_wall;
	INT8U* figure_box;
	INT8U* figure_target_box;
	INT8U* figure_target_Point;
	INT8U* figure_flood;
	INT8U* figure_bg;
    INT8U* figure_pass_tips;

#endif

void InitPushboxGame(void)
{
	INT16U tft_w,tft_h;
	game_over_in=0;
   	 hal_lcdGetVideoResolution(&tft_w,&tft_h);
	//g_liu= 1;//ap_state_config_pushbox_get();
	deg_Printf("InitPushboxGame1111\n");
	memset(game_frame_buff,R_ID_PALETTE_Black,tft_w*tft_h);
	deg_Printf("InitPushboxGame2222,game_frame_buff[0]=%x,game_frame_buff[1]=%x\n",game_frame_buff[0],game_frame_buff[1]);
	pushbox_draw_map(game_frame_buff);
	memcpy(ui_draw_ctrl.bufStart,game_frame_buff,game_frame_w*game_frame_h);

    uiWinDrawUpdate();
	//g_draw_str(game_frame_buff,0,0,"str display test",R_ID_PALETTE_Green,R_ID_PALETTE_Red);
	//g_draw_char(game_frame_buff,0,0,'w',R_ID_PALETTE_Green,R_ID_PALETTE_Red);
	//g_draw_str_in_block(game_frame_buff,"str display test",10,20,R_ID_PALETTE_Yellow,R_ID_PALETTE_Blue,RES_FONT_NUM0,R_ID_PALETTE_Green,0);
	//g_draw_str_default_style_in_screen_center(game_frame_buff,"str display test",RES_FONT_NUM1);
	//uiWinDrawUpdate();
}

void pass(u8 * buf_show,u32 line)
{
	INT8U i,j;
	game_continue=1;
    deg_Printf("pass---->%d\r\n",line);

	for(i=0;i<BOXNUM;i++){
		if(game_continue==0)
			break;
		for(j=0;j<BOXNUM;j++){
			if(level[g_liu][j][i]==3||level[g_liu][j][i]==7){
				if(level_temp[i][j]==7){
					game_continue=1;
				}else{
					game_continue=0;
					break;
				}
			}
		}
	}
	if(game_continue==1){
		if(g_liu < 9){
			g_liu += 1;
			game_Level=0;
			//show_game_state(0);
		}else{
			g_liu=0;
			game_Level=1;
			//show_game_state(1);
		}
        game_over_in=1;
	}
}

static void game_over(winHandle handle,INT8U* display_buf)
{
	ENTER();
	//INT32U	buf_show= setting_frame_show->base + setting_frame_show->offset;
	//uiWinSetResid(winItem(handle,PUSHBOX_TIPS_STR_TIP),"game over");
	//g_draw_str_default_style_in_screen_center(display_buf,"game over",RES_FONT_NUM1);
	//game_draw_str( display_buf,STR_60HZ);
	//game_over_in=1;
    game_over_in=3;
    OSDFrameDrawIcon(game_frame_buff,game_buff[GAME_PASS_TIPS].ptr,50,80,220,80);
    g_draw_resstr_default_style_in_screen_center(game_frame_buff,R_ID_STR_GAME_OVER,RES_FONT_NUM0);
    //uiWinDrawUpdate();
	// uiWinDestroy(&handle);
	//msgQSend(ApQ, MSG_APQ_MODE, NULL, 0, MSG_PRI_NORMAL);
	//pushbox_draw_map(display_buf);
}

void image_darwbg(INT8U *buf_show,INT8U* icon_stream,INT8U cow,INT8U col,INT16U w,INT16U h)
{
#if 1
	DISPLAY_ICONSHOW	icon;
	INT16U tft_w = game_frame_w;
	INT16U tft_h = game_frame_h;
	INT16U x,y;
	x =w*cow+w+10;
	y= h*col;

	if((x>=tft_w)||(y>=tft_h)){
		return;
	}

	// if(g_liu==0){
	// 	x+=w;
    // }else if(g_liu<7){
	// 	x+=w*3;
	// }
	#if 0
	icon.transparent=0x87c1;
	icon.icon_w=w;
	icon.icon_h=h;
	icon.pos_x=x;
	icon.pos_y=y;
	ap_setting_special_icon_draw(buf_show, icon_stream, &icon);
	//OSQPost(DisplayTaskQ, (void *) (MSG_DISPLAY_TASK_MJPEG_DRAW | (INT32U)display_frame));
	#else
	OSDFrameDrawIcon(buf_show,icon_stream,x,y,w,h);
	#endif
#else
	OSDFrameDrawIcon(buf_show,icon_stream,cow,col,w,h);
#endif
}

void image_draw_picture(INT8U *buf_show,INT8U* icon_stream,INT8U cow,INT8U col,INT16U w,INT16U h)
{
#if 1
	INT16U x,y;
	INT16U tft_w = game_frame_w;
	INT16U tft_h = game_frame_h;

    x =w*cow+w+10;
    y= h*col;
    if((x>=tft_w)||(y>=tft_h)){
        return;
    }
	// if(g_liu==0){
	// 	x+=w;
	// }else if(g_liu<7){
	// 	x+=w*3;
	// }
	//deg_Printf("x:%d  y:%d\r\n",x,y);
	#if 0
	DISPLAY_ICONSHOW	icon;
	icon.transparent=0x87c1;
	icon.icon_w=w;
	icon.icon_h=h;
	icon.pos_x=x;
	icon.pos_y=y;
	ap_display_icon_draw(buf_show, icon_stream, &icon);
	//ap_setting_icon_draw(buf_show,icon_stream, &icon, SETTING_ICON_NORMAL_DRAW);
	#else
	OSDFrameDrawIcon(buf_show,icon_stream,x,y,w,h);
	#endif
#else
	OSDFrameDrawIcon(buf_show,icon_stream,cow,col,w,h);
#endif
}

void pushbox_draw_map(INT8U * buf_show)
{
#if 1
    INT8U i,j;
    INT16U tft_w = game_frame_w;
    INT16U tft_h = game_frame_h;
    curx=0;
    cury=0;
    deg_Printf("pushbox_draw_map\r\n");
	//0 空地 1 ??2背景 3 �?�???4 箱子 5 ??
/*
	deg_Printf("level:%d\r\n",level[0][0][0]);
	deg_Printf("level:%d\r\n",level[0][0][1]);
	deg_Printf("level:%d\r\n",level[0][0][2]);
	deg_Printf("level:%d\r\n",level[0][0][3]);
	deg_Printf("level:%d\r\n",level[0][0][4]);
	deg_Printf("level:%d\r\n",level[0][0][5]);
	deg_Printf("level:%d\r\n",level[0][0][6]);
	deg_Printf("level:%d\r\n",level[0][0][7]);
	deg_Printf("level:%d\r\n",level[0][0][8]);
	deg_Printf("level:%d\r\n",level[0][0][9]);
	deg_Printf("\r\n------------------------------\r\n");

	deg_Printf("level:%d\r\n",level[0][0][0]);
	deg_Printf("level:%d\r\n",level[0][1][0]);
	deg_Printf("level:%d\r\n",level[0][2][0]);
	deg_Printf("level:%d\r\n",level[0][3][0]);
	deg_Printf("level:%d\r\n",level[0][4][0]);
	deg_Printf("level:%d\r\n",level[0][5][0]);
	deg_Printf("level:%d\r\n",level[0][6][0]);
	*/


	for(i=0;i<BOXNUM;i++){
		for(j=0;j<BOXNUM;j++){

        //	if(level_temp[i][j]==0)
        //	continue;
		level_temp[i][j]=level[g_liu][j][i];
        //deg_Printf("level:%d\r\n",level[g_liu][j][i]);
        // XOSTimeDly(1);
        //if(level_temp[i][j]==4)
        //image_draw_picture(buf_show,figure_target_Point,i,j,16,16);
        //if(level_temp[i][j]==5)
        //image_draw_picture(buf_show,figure_target_box,i,j,32,32);
        //image_darwbg(buf_show,figure_bg,90,180,i,j);

#if 1
           // g_Draw_Fill(buf_show,30,0,30+40*4,176,R_ID_PALETTE_Transparent);//R_ID_PALETTE_Transparent R_ID_PALETTE_DoderBlue
			switch(level_temp[i][j]){
				case 0:
					//ap_display_clear_screen((INT32U)buf_show);
                    image_draw_picture(buf_show,game_buff[GAME_MAN_BG].ptr/*figure_bg_wall*/,i,j,game_buff[GAME_MAN_BG].w,game_buff[GAME_MAN_BG].h);
				break;
				case 1:		/*wall-1*/
		            image_draw_picture(buf_show,game_buff[GAME_MAN_WALL].ptr/*figure_bg_wall*/,i,j,game_buff[GAME_MAN_WALL].w,game_buff[GAME_MAN_WALL].h);
		            //image_display(i*16,16+j*16,(u8 *)gImage_1);
				break;
				case 2:		/*background-2*/
					image_darwbg(buf_show,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,i,j,game_buff[GAME_MAN_BG].w,game_buff[GAME_MAN_BG].h);
					break;
				case 3:		/*target point-3*/
					image_draw_picture(buf_show,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,i,j,game_buff[GAME_TARGET_POINT].w,game_buff[GAME_TARGET_POINT].h);
		            //image_display(i*16,16+j*16,(u8 *)gImage_3);
				break;
				case 4:		/*box-4*/
		            image_draw_picture(buf_show,game_buff[GAME_BOX].ptr/*figure_box*/,i,j,game_buff[GAME_BOX].w,game_buff[GAME_BOX].h);
					break;
				case 5:		/*man-5*/
					curx=i;
					cury=j;
					image_draw_picture(buf_show,game_buff[GAME_MAN_DOWN].ptr/*figure_down*/,i,j,game_buff[GAME_MAN_DOWN].w,game_buff[GAME_MAN_DOWN].h);
		            //image_display(i*16,16+j*16,(u8 *)gImage_5);
				break;
				case 7:		/*succeed box*/
					image_draw_picture(buf_show,game_buff[GAME_TARGET_BOX].ptr/*figure_target_box*/,i,j,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].h);
				break;
			}
#endif
		}
	}
	
	#endif
}

void MoveLeft_pushbox(winHandle handle,INT8U* display_buf)
{
    INT16U x,y;
    //0 空地 1 ??2背景 3 �?�???4 箱子 5 ??7 成功箱子
    /*关卡8*8点阵 人物1 砖头2 箱子3 �?�?4 成功5*/
    if(level_temp[curx-1][cury]==2||level_temp[curx-1][cury]==3){
        if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
          level_temp[curx][cury]=3;
            image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
          //LCD_sprintf_PIC(56+curx*16,93+cury*16,4,BLACK,rgb(20,106,222),1);
        }else{
            level_temp[curx][cury]=2;

            image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            //  LCD_sprintf_PIC(56+curx*16,93+cury*16,0,rgb(20,106,222),rgb(20,106,222),1);
            //	filename=OpenPhotosdir("Push",playcount1);
            //	if(filename !=1)
            //	{
            //	write_cmd_data(0x0003,0x1090);
            //	LCD_FilledRectangle(56+curx*16,93+cury*16,16,16,rgb(20,106,222));
            //	BmpDecode(56+curx*16,93+cury*16,filename);
            //	}
        }
        curx=curx-1;
        level_temp[curx][cury]=5;
        image_draw_picture(display_buf,game_buff[GAME_MAN_LEFT].ptr/*figure_left*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
        //  LCD_sprintf_PIC(56+curx*16,93+cury*16,1,BLUE,rgb(20,106,222),1);
    }else if(level_temp[curx-1][cury]==4){
        if(level_temp[curx-2][cury]==2){
            if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
              level_temp[curx][cury]=3;
              image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
              //LCD_sprintf_PIC(56+curx*16,93+cury*16,4,BLACK,rgb(20,106,222),1);
            }else{
                level_temp[curx][cury]=2;
                image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                // LCD_sprintf_PIC(56+curx*16,93+cury*16,0,rgb(20,106,222),rgb(20,106,222),1);
                // filename=OpenPhotosdir("Push",playcount1);
                // if(filename !=1)
                // {
                    // write_cmd_data(0x0003,0x1090);
                    // LCD_FilledRectangle(56+curx*16,93+cury*16,16,16,rgb(20,106,222));
                    // BmpDecode(56+curx*16,93+cury*16,filename);
                // }
            }
            curx=curx-1;
            level_temp[curx][cury]=5;
            image_draw_picture(display_buf,game_buff[GAME_MAN_LEFT].ptr/*figure_left*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            // LCD_sprintf_PIC(56+curx*16,93+cury*16,1,BLUE,rgb(20,106,222),1);
            level_temp[curx-1][cury]=4;
            x=curx-1;
            y=cury;

            // if(level_temp[x-1][y]==1 &&(level_temp[x][y-1]==1||level_temp[x][y+1]==1))
            // {
            //     game_over(handle,display_buf);
            // }
            image_draw_picture(display_buf,game_buff[GAME_BOX].ptr/*figure_box*/,x,y,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            //LCD_sprintf_PIC(56+(curx-1)*16,93+cury*16,3,BLACK,rgb(20,106,222),1);
        }else if(level_temp[curx-2][cury]==3){
            if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
              level_temp[curx][cury]=3;
               image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
             // LCD_sprintf_PIC(56+curx*16,93+cury*16,4,BLACK,rgb(20,106,222),1);
            }else{
                level_temp[curx][cury]=2;
                image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                // LCD_sprintf_PIC(56+curx*16,93+cury*16,0,rgb(20,106,222),rgb(20,106,222),1);
                // filename=OpenPhotosdir("Push",playcount1);
                // if(filename !=1)
                // {
                // write_cmd_data(0x0003,0x1090);
                // LCD_FilledRectangle(56+curx*16,93+cury*16,16,16,rgb(20,106,222));
                // BmpDecode(56+curx*16,93+cury*16,filename);
                // }
            }
            curx=curx-1;
            level_temp[curx][cury]=5;
            image_draw_picture(display_buf,game_buff[GAME_MAN_LEFT].ptr/*figure_left*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            //  LCD_sprintf_PIC(56+curx*16,93+cury*16,1,BLUE,rgb(20,106,222),1);
            level_temp[curx-1][cury]=7;
            x=curx-1;
            y=cury;
            image_draw_picture(display_buf,game_buff[GAME_TARGET_BOX].ptr/*figure_target_box*/,x,y,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
			res_music_start(R_ID_MUSIC_BOOM,0,task_com_curVolume_get());

            //LCD_sprintf_PIC(56+(curx-1)*16,93+cury*16,5,BLACK,rgb(20,106,222),1);
            pass(display_buf,__LINE__);
        }
    }else if(level_temp[curx-1][cury]==7){
      if(level_temp[curx-2][cury]==2){
          if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
              level_temp[curx][cury]=3;
              image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            //  LCD_sprintf_PIC(56+curx*16,93+cury*16,4,BLACK,rgb(20,106,222),1);
          }else{
                level_temp[curx][cury]=2;
                image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                // LCD_sprintf_PIC(56+curx*16,93+cury*16,0,rgb(20,106,222),rgb(20,106,222),1);
                // filename=OpenPhotosdir("Push",playcount1);
                // if(filename !=1)
                // {
                //  write_cmd_data(0x0003,0x1090);
                //  LCD_FilledRectangle(56+curx*16,93+cury*16,16,16,rgb(20,106,222));
                //  BmpDecode(56+curx*16,93+cury*16,filename);
                // }
            }
            curx=curx-1;
          level_temp[curx][cury]=5;
            image_draw_picture(display_buf,game_buff[GAME_MAN_LEFT].ptr/*figure_left*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
        //  LCD_sprintf_PIC(56+curx*16,93+cury*16,1,BLUE,rgb(20,106,222),1);
          level_temp[curx-1][cury]=4;
         x=curx-1;
         y=cury;
         image_draw_picture(display_buf,game_buff[GAME_BOX].ptr/*figure_box*/,x,y,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
         // LCD_sprintf_PIC(56+(curx-1)*16,93+cury*16,3,BLACK,rgb(20,106,222),1);

                if(level_temp[x-1][y]==1 &&(level_temp[x][y-1]==1||level_temp[x][y+1]==1))
            {


            //if(level_temp[x][y-1]==1)
                // {

                //     game_over(handle,display_buf);
                // }
            }

      }else if(level_temp[curx-2][cury]==3){
          if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
              level_temp[curx][cury]=3;
                image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            //  LCD_sprintf_PIC(56+curx*16,93+cury*16,4,BLACK,rgb(20,106,222),1);
          }else{
                level_temp[curx][cury]=2;
                image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                // LCD_sprintf_PIC(56+curx*16,93+cury*16,0,rgb(20,106,222),rgb(20,106,222),1);
                // filename=OpenPhotosdir("Push",playcount1);
                // if(filename !=1)
                // {
                // write_cmd_data(0x0003,0x1090);
                // LCD_FilledRectangle(56+curx*16,93+cury*16,16,16,rgb(20,106,222));
                // BmpDecode(56+curx*16,93+cury*16,filename);
                // }
            }
          curx=curx-1;
          level_temp[curx][cury]=5;
            image_draw_picture(display_buf,game_buff[GAME_MAN_LEFT].ptr/*figure_left*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
          //LCD_sprintf_PIC(56+curx*16,93+cury*16,1,BLUE,rgb(20,106,222),1);
          level_temp[curx-1][cury]=7;
           x=curx-1;
           y=cury;
           image_draw_picture(display_buf,game_buff[GAME_TARGET_BOX].ptr/*figure_target_box*/,x,y,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
		   res_music_start(R_ID_MUSIC_BOOM,0,task_com_curVolume_get());

		   // LCD_sprintf_PIC(56+(curx-1)*16,93+cury*16,5,BLACK,rgb(20,106,222),1);
          pass(display_buf,__LINE__);
      }
      pass(display_buf,__LINE__);
    }
    //  OSQPost(DisplayTaskQ, (void *) (MSG_DISPLAY_TASK_MJPEG_DRAW | (INT32U)display_frame));
}



void MoveRight_pushbox(winHandle handle,INT8U* display_buf)
{

    INT16U x,y;

    //0 空地 1 ??2背景 3 �?�???4 箱子 5 ??7 成功箱子

    /*关卡8*8点阵 人物1 砖头2 箱子3 �?�?4 成功5*/
    if(level_temp[curx+1][cury]==2||level_temp[curx+1][cury]==3){
        if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
            level_temp[curx][cury]=3;
            image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            // LCD_sprintf_PIC(56+curx*16,93+cury*16,4,BLACK,rgb(20,106,222),1);
        }else{
            level_temp[curx][cury]=2;
            image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            //	LCD_sprintf_PIC(56+curx*16,93+cury*16,0,rgb(20,106,222),rgb(20,106,222),1);
            //filename=OpenPhotosdir("Push",playcount1);
            //if(filename !=1)
            //{
            //write_cmd_data(0x0003,0x1090);
            //LCD_FilledRectangle(56+curx*16,93+cury*16,16,16,rgb(20,106,222));
            //BmpDecode(56+curx*16,93+cury*16,filename);
            //}
        }
        curx=curx+1;
        level_temp[curx][cury]=5;
        image_draw_picture(display_buf,game_buff[GAME_MAN_RIGHT].ptr/*figure_right*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
    }else if(level_temp[curx+1][cury]==4){
        if(level_temp[curx+2][cury]==2){
            if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
                level_temp[curx][cury]=3;
                image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                // LCD_sprintf_PIC(56+curx*16,93+cury*16,4,BLACK,rgb(20,106,222),1);
            }else{
                level_temp[curx][cury]=2;
                image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                //LCD_sprintf_PIC(56+curx*16,93+cury*16,0,rgb(20,106,222),rgb(20,106,222),1);
                //filename=OpenPhotosdir("Push",playcount1);
                //if(filename !=1)
                //{
                //write_cmd_data(0x0003,0x1090);
                //LCD_FilledRectangle(56+curx*16,93+cury*16,16,16,rgb(20,106,222));
                //BmpDecode(56+curx*16,93+cury*16,filename);
                //}
            }
            curx=curx+1;
            level_temp[curx][cury]=5;
            image_draw_picture(display_buf,game_buff[GAME_MAN_RIGHT].ptr/*figure_right*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            // LCD_sprintf_PIC(56+curx*16,93+cury*16,1,BLUE,rgb(20,106,222),1);
            level_temp[curx+1][cury]=4;
            x=curx+1;
            y=cury;
            image_draw_picture(display_buf,game_buff[GAME_BOX].ptr/*figure_box*/,x,y,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);

            // if(level_temp[x+1][y]==1 &&(level_temp[x][y-1]==1||level_temp[x][y+1]==1))
            // {
            //     game_over(handle,display_buf);
            // }
            //  LCD_sprintf_PIC(56+(curx+1)*16,93+cury*16,3,BLACK,rgb(20,106,222),1);
        }else if(level_temp[curx+2][cury]==3){
            if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
                level_temp[curx][cury]=3;
                image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                //LCD_sprintf_PIC(56+curx*16,93+cury*16,4,BLACK,rgb(20,106,222),1);
            }else{
                level_temp[curx][cury]=2;
                image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                //LCD_sprintf_PIC(56+curx*16,93+cury*16,0,rgb(20,106,222),rgb(20,106,222),1);
                //filename=OpenPhotosdir("Push",playcount1);
                //if(filename !=1)
                //{
                //write_cmd_data(0x0003,0x1090);
                //LCD_FilledRectangle(56+curx*16,93+cury*16,16,16,rgb(20,106,222));
                //BmpDecode(56+curx*16,93+cury*16,filename);
                //}
            }
            curx=curx+1;
            level_temp[curx][cury]=5;
            image_draw_picture(display_buf,game_buff[GAME_MAN_RIGHT].ptr/*figure_right*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            // LCD_sprintf_PIC(56+curx*16,93+cury*16,1,BLUE,rgb(20,106,222),1);
            level_temp[curx+1][cury]=7;
            x=curx+1;
            y=cury;
            image_draw_picture(display_buf,game_buff[GAME_TARGET_BOX].ptr/*figure_target_box*/,x,y,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
			res_music_start(R_ID_MUSIC_BOOM,0,task_com_curVolume_get());

			//  LCD_sprintf_PIC(56+(curx+1)*16,93+cury*16,5,BLACK,rgb(20,106,222),1);
            pass(display_buf,__LINE__);
        }
    }else if(level_temp[curx+1][cury]==7){
        if(level_temp[curx+2][cury]==2){
            if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
                level_temp[curx][cury]=3;
                image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                // LCD_sprintf_PIC(56+curx*16,93+cury*16,4,BLACK,rgb(20,106,222),1);
            }else{
                level_temp[curx][cury]=2;
                image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                //LCD_sprintf_PIC(56+curx*16,93+cury*16,0,rgb(20,106,222),rgb(20,106,222),1);
                //filename=OpenPhotosdir("Push",playcount1);
                //if(filename !=1)
                //{
                //write_cmd_data(0x0003,0x1090);
                //LCD_FilledRectangle(56+curx*16,93+cury*16,16,16,rgb(20,106,222));
                //BmpDecode(56+curx*16,93+cury*16,filename);
                //}
            }
            curx=curx+1;
            level_temp[curx][cury]=5;
            image_draw_picture(display_buf,game_buff[GAME_MAN_RIGHT].ptr/*figure_right*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            // LCD_sprintf_PIC(56+curx*16,93+cury*16,1,BLUE,rgb(20,106,222),1);
            level_temp[curx+1][cury]=4;
            x=curx+1;
            y=cury;
            image_draw_picture(display_buf,game_buff[GAME_BOX].ptr/*figure_box*/,x,y,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            // LCD_sprintf_PIC(56+(curx+1)*16,93+cury*16,3,BLACK,rgb(20,106,222),1);

            // if(level_temp[x-1][y]==1 &&(level_temp[x][y-1]==1||level_temp[x][y+1]==1)){
            //     game_over(handle,display_buf);
            // }
        }else if(level_temp[curx+2][cury]==3){
            if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
                level_temp[curx][cury]=3;
                image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                // LCD_sprintf_PIC(56+curx*16,93+cury*16,4,BLACK,rgb(20,106,222),1);
            }else{
                level_temp[curx][cury]=2;

                image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                //LCD_sprintf_PIC(56+curx*16,93+cury*16,0,rgb(20,106,222),rgb(20,106,222),1);
                //filename=OpenPhotosdir("Push",playcount1);
                //if(filename !=1)
                //{
                //write_cmd_data(0x0003,0x1090);
                //LCD_FilledRectangle(56+curx*16,93+cury*16,16,16,rgb(20,106,222));
                //BmpDecode(56+curx*16,93+cury*16,filename);
                //}
            }
            curx=curx+1;
            level_temp[curx][cury]=5;
            image_draw_picture(display_buf,game_buff[GAME_MAN_RIGHT].ptr/*figure_right*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            // LCD_sprintf_PIC(56+curx*16,93+cury*16,1,BLUE,rgb(20,106,222),1);
            level_temp[curx+1][cury]=7;
            x=curx+1;
            y=cury;
            image_draw_picture(display_buf,game_buff[GAME_TARGET_BOX].ptr/*figure_target_box*/,x,y,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
			res_music_start(R_ID_MUSIC_BOOM,0,task_com_curVolume_get());

			// LCD_sprintf_PIC(56+(curx+1)*16,93+cury*16,5,BLACK,rgb(20,106,222),1);
            pass(display_buf,__LINE__);
        }
        pass(display_buf,__LINE__);
    }
    //  OSQPost(DisplayTaskQ, (void *) (MSG_DISPLAY_TASK_MJPEG_DRAW | (INT32U)display_frame));
}


void MoveUp_pushbox(winHandle handle,u8* display_buf)
{
	INT16U x=0,y=0;
//0 空地 1 ??2背景 3 �?�???4 箱子 5 ??7 成功箱子

	deg_Printf("level_temp[curx][cury-1]:%d\r\n",level_temp[curx][cury-1]);

    if(level_temp[curx][cury-1]==2||level_temp[curx][cury-1]==3){
        if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
            level_temp[curx][cury]=3;
            image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
        }else{
            level_temp[curx][cury]=2;
            image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);

        }
        cury=cury-1;
        level_temp[curx][cury]=5;
        image_draw_picture(display_buf,game_buff[GAME_MAN_UP].ptr/*figure_up*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
    }else if(level_temp[curx][cury-1]==4){
        //deg_Printf("[curx:%d][cury:%d]\r\n",curx,cury);
        //deg_Printf("level_temp[curx][cury-2]:%d\r\n",level_temp[curx][cury-2]);
        if(level_temp[curx][cury-2]==2){
        //deg_Printf("level[g_liu][curx][cury]:%d\r\n",level[g_liu][curx][cury]);
            if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
                level_temp[curx][cury]=3;
                image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                //LCD_sprintf_PIC(56+curx*16,93+cury*16,4,BLACK,rgb(20,106,222),1);
            }else{
                level_temp[curx][cury]=2;
                image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                //LCD_sprintf_PIC(56+curx*16,93+cury*16,0,rgb(20,106,222),rgb(20,106,222),1);
                //filename=OpenPhotosdir("Push",playcount1);
                //if(filename !=1)
                //{
                //write_cmd_data(0x0003,0x1090);
                //LCD_FilledRectangle(56+curx*16,93+cury*16,16,16,rgb(20,106,222));
                //BmpDecode(56+curx*16,93+cury*16,filename);
                //}
            }
            cury=cury-1;
            level_temp[curx][cury]=5;
            image_draw_picture(display_buf,game_buff[GAME_MAN_UP].ptr/*figure_up*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            //LCD_sprintf_PIC(56+curx*16,93+cury*16,1,BLUE,rgb(20,106,222),1);
            level_temp[curx][cury-1]=4;
            x=curx;
            y=cury-1;
            image_draw_picture(display_buf,game_buff[GAME_BOX].ptr/*figure_box*/,x,y,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            //	deg_Printf(":%d\r\n",level[g_liu][x-1][y]);
            //	deg_Printf(":%d\r\n",level[g_liu][x][y-1]);
            //	deg_Printf(":%d\r\n",level[g_liu][x][y+1]);
            // if(level_temp[x][y-1]==1 &&(level_temp[x-1][y]==1||level_temp[x+1][y]==1)){
            //     game_over(handle,display_buf);
            // }
            //	LCD_sprintf_PIC(56+curx*16,93+(cury-1)*16,3,BLACK,rgb(20,106,222),1);
        }else if(level_temp[curx][cury-2]==3){
            if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
                level_temp[curx][cury]=3;
                image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                //LCD_sprintf_PIC(56+curx*16,93+cury*16,4,BLACK,rgb(20,106,222),1);
            }else{
                level_temp[curx][cury]=2;
                image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);

                //  LCD_sprintf_PIC(56+curx*16,93+cury*16,0,rgb(20,106,222),rgb(20,106,222),1);
                //	filename=OpenPhotosdir("Push",playcount1);
                //	if(filename !=1)
                //	{
                //	write_cmd_data(0x0003,0x1090);
                //	LCD_FilledRectangle(56+curx*16,93+cury*16,16,16,rgb(20,106,222));
                //	BmpDecode(56+curx*16,93+cury*16,filename);
                //	}
			}
            cury=cury-1;
            level_temp[curx][cury]=5;

            image_draw_picture(display_buf,game_buff[GAME_MAN_UP].ptr/*figure_up*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            //LCD_sprintf_PIC(56+curx*16,93+cury*16,1,BLUE,rgb(20,106,222),1);
            level_temp[curx][cury-1]=7;
            x=curx;
            y=cury-1;
            image_draw_picture(display_buf,game_buff[GAME_TARGET_BOX].ptr/*figure_target_box*/,x,y,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
			res_music_start(R_ID_MUSIC_BOOM,0,task_com_curVolume_get());

			//LCD_sprintf_PIC(56+curx*16,93+(cury-1)*16,5,BLACK,rgb(20,106,222),1);
            pass(display_buf,__LINE__);
        }else if (level_temp[curx][cury-2]==3){
            if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
                level_temp[curx][cury]=3;

                image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                //LCD_sprintf_PIC(56+curx*16,93+cury*16,4,BLACK,rgb(20,106,222),1);
            }else{
                level_temp[curx][cury]=2;
                image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);

            }
            cury=cury-1;
            level_temp[curx][cury]=5;

            image_draw_picture(display_buf,game_buff[GAME_MAN_UP].ptr/*figure_up*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            level_temp[curx][cury-1]=7;
             x=curx;
            y=cury-1;
            image_draw_picture(display_buf,game_buff[GAME_TARGET_BOX].ptr/*figure_target_box*/,x,y,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
			res_music_start(R_ID_MUSIC_BOOM,0,task_com_curVolume_get());

			pass(display_buf,__LINE__);
        }
	}else if(level_temp[curx][cury-1]==7){
        if(level_temp[curx][cury-2]==2){
            if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
                level_temp[curx][cury]=3;

                image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            }else{
                level_temp[curx][cury]=2;
                image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);

            }
            cury=cury-1;
            level_temp[curx][cury]=5;

            image_draw_picture(display_buf,game_buff[GAME_MAN_UP].ptr/*figure_up*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            level_temp[curx][cury-1]=4;
            x=curx;
            y=cury-1;
            image_draw_picture(display_buf,game_buff[GAME_BOX].ptr/*figure_box*/,x,y,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);

            // if(level_temp[x][y-1]==1 &&(level_temp[x-1][y]==1||level_temp[x+1][y]==1)){
            //     game_over(handle,display_buf);
            // }
            //LCD_sprintf_PIC(56+curx*16,93+(cury-1)*16,3,BLACK,rgb(20,106,222),1);
        }else if(level_temp[curx][cury-2]==3){
            if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
                level_temp[curx][cury]=3;

                image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                //LCD_sprintf_PIC(56+curx*16,93+cury*16,4,BLACK,rgb(20,106,222),1);
            }else{
                level_temp[curx][cury]=2;
                image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);

            }
            cury=cury-1;
            level_temp[curx][cury]=5;

            image_draw_picture(display_buf,game_buff[GAME_MAN_UP].ptr/*figure_up*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            level_temp[curx][cury-1]=7;
             x=curx;
            y=cury-1;
            image_draw_picture(display_buf,game_buff[GAME_TARGET_BOX].ptr/*figure_target_box*/,x,y,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
			res_music_start(R_ID_MUSIC_BOOM,0,task_com_curVolume_get());

			pass(display_buf,__LINE__);
        }
		pass(display_buf,__LINE__);
	}
	return;
}

void MoveDown_pushbox(winHandle handle,u8* display_buf)
{
	INT16U x,y;
    //0 空地 1 ??2背景 3 �?�???4 箱子 5 ??7 成功箱子

    /*关卡8*8点阵 人物1 砖头2 箱子3 �?�?4 成功5*/
    // deg_Printf("level_temp[curx][cury+1]:%d\r\n",level_temp[curx][cury+1]);
    // deg_Printf("level_temp[curx][cury+21]:%d\r\n",level_temp[curx][cury+2]);
    // deg_Printf("level_temp[curx+1][cury]:%d\r\n",level_temp[curx+1][cury]);
    // deg_Printf("[curx:%d][cury:%d]\r\n",curx,cury);
    if(level_temp[curx][cury+1]==2||level_temp[curx][cury+1]==3){
        if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
            level_temp[curx][cury]=3;
            image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            //LCD_sprintf_PIC(56+curx*16,93+cury*16,4,BLACK,rgb(20,106,222),1);
        }else{
            level_temp[curx][cury]=2;

            image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            //  LCD_sprintf_PIC(56+curx*16,93+cury*16,0,rgb(20,106,222),rgb(20,106,222),1);
            //	filename=OpenPhotosdir("Push",playcount1);
            //	if(filename !=1)
            //	{
            //	    write_cmd_data(0x0003,0x1090);
            //	    LCD_FilledRectangle(56+curx*16,93+cury*16,16,16,rgb(20,106,222));
            //	    BmpDecode(56+curx*16,93+cury*16,filename);
            //	}
        }
        cury=cury+1;
        level_temp[curx][cury]=5;
        deg_Printf("[curx:%d][cury:%d]\r\n",curx,cury);
        image_draw_picture(display_buf,game_buff[GAME_MAN_DOWN].ptr/*figure_down*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
    }else if(level_temp[curx][cury+1]==4){//  箱子
        if(level_temp[curx][cury+2]==2){
            if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
                level_temp[curx][cury]=3;
                image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            }else{
                level_temp[curx][cury]=2;
                image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            }
            cury=cury+1;
            level_temp[curx][cury]=5;

            image_draw_picture(display_buf,game_buff[GAME_MAN_DOWN].ptr/*figure_down*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            level_temp[curx][cury+1]=4;
            x=curx;
            y=cury+1;
            image_draw_picture(display_buf,game_buff[GAME_BOX].ptr/*figure_box*/,x,y,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);

            // if(level_temp[x][y+1]==1 &&(level_temp[x-1][y]==1||level_temp[x+1][y]==1)){
                //if(level_temp[x][y-1]==1)
            //     game_over(handle,display_buf);
            // }
        }else if(level_temp[curx][cury+2]==3){//  �?�???
            if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
                level_temp[curx][cury]=3;
                image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            }else{
                level_temp[curx][cury]=2;
                image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            }
            cury=cury+1;
            level_temp[curx][cury]=5;

            image_draw_picture(display_buf,game_buff[GAME_MAN_DOWN].ptr/*figure_down*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            level_temp[curx][cury+1]=7;
            x=curx;
            y=cury+1;
            image_draw_picture(display_buf,game_buff[GAME_TARGET_BOX].ptr/*figure_target_box*/,x,y,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
			res_music_start(R_ID_MUSIC_BOOM,0,task_com_curVolume_get());

			pass(display_buf,__LINE__);
        }
	}else if(level_temp[curx][cury+1]==7){
        if(level_temp[curx][cury+2]==2){
            if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
                level_temp[curx][cury]=3;

                image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
                // LCD_sprintf_PIC(56+curx*16,93+cury*16,4,BLACK,rgb(20,106,222),1);
            }else{
                level_temp[curx][cury]=2;

                image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            }
            cury=cury+1;
            level_temp[curx][cury]=5;

            image_draw_picture(display_buf,game_buff[GAME_MAN_DOWN].ptr/*figure_down*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            level_temp[curx][cury+1]=4;
            x=curx;
            y=cury+1;
            image_draw_picture(display_buf,game_buff[GAME_BOX].ptr/*figure_box*/,x,y,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);

            // if(level_temp[x][y+1]==1 &&(level_temp[x-1][y]==1||level_temp[x+1][y]==1)){
            //     game_over(handle,display_buf);
            // }
        }else if(level_temp[curx][cury+2]==3){
            if(level[g_liu][cury][curx]==3||level[g_liu][cury][curx]==7){
                level_temp[curx][cury]=3;
                image_draw_picture(display_buf,game_buff[GAME_TARGET_POINT].ptr/*figure_target_Point*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            }else{
                level_temp[curx][cury]=2;
                image_darwbg(display_buf,game_buff[GAME_MAN_BG].ptr/*figure_bg*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
            }
            cury=cury+1;
            level_temp[curx][cury]=5;
            image_draw_picture(display_buf,game_buff[GAME_MAN_DOWN].ptr/*figure_down*/,curx,cury,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);

            level_temp[curx][cury+1]=7;
            x=curx;
            y=cury+1;
            image_draw_picture(display_buf,game_buff[GAME_TARGET_BOX].ptr/*figure_target_box*/,x,y,game_buff[GAME_TARGET_BOX].w,game_buff[GAME_TARGET_BOX].w);
			res_music_start(R_ID_MUSIC_BOOM,0,task_com_curVolume_get());

			pass(display_buf,__LINE__);
        }
        pass(display_buf,__LINE__);
    }
}

#if 0
void game_draw_str(INT16U* display_buf,INT16U str_idx)
{
	STRING_INFO 			str = { 0 };
	DISPLAY_ICONSHOW		icon;
	t_STRING_TABLE_STRUCT	str_res;
	INT16U tft_w = game_frame_w;
	INT16U tft_h = game_frame_h;


    str.font_type = 0;
	str.str_idx = str_idx;//???????
	str.buff_w = tft_w;
	str.buff_h = tft_h;
	str.language = ap_state_config_language_get() - 1;
	ap_state_resource_string_resolution_get(&str, &str_res);
	str.pos_x = (tft_w - str_res.string_width) >> 1;
	str.pos_y = (tft_h - str_res.string_height) >> 1;
	str.font_color = R_ID_PALETTE_Red;
/*
	icon.icon_w = 216;
	icon.icon_h = 144;
	icon.pos_x = (tft_w - icon.icon_w) / 2;
	icon.pos_y = (tft_h - icon.icon_h) / 2;
*/
	icon.transparent = R_ID_PALETTE_Transparent;

	str.pos_x +=1;
	str.pos_y +=1;
	icon.icon_w = str_res.string_width+12;
	icon.icon_h = str_res.string_height+12;
	icon.pos_x = (tft_w-icon.icon_w)>>1;
	icon.pos_y = (tft_h-icon.icon_h)>>1;
	ap_setting_rect_draw(display_buf, 0x2595,0xffe0,1, &icon);
	ap_state_resource_string_draw( display_buf, &str);
}
#endif
void pushbox_movedir(winHandle handle,INT32U type)
{

   if(type==KEY_EVENT_UP){
	   MoveUp_pushbox(handle,game_frame_buff);
   	}else if(type==KEY_EVENT_DOWN){
		MoveDown_pushbox(handle,game_frame_buff);
  	}else if(type==KEY_EVENT_LEFT){
		MoveLeft_pushbox(handle,game_frame_buff);
	}else if(type==KEY_EVENT_RIGHT){
	     MoveRight_pushbox(handle,game_frame_buff);
	}
	//deg_Printf("[curx:%d][cury:%d]\r\n",curx,cury);

	if(game_continue==1){
		if (game_Level==0){
			//uiWinSetResid(winItem(handle,PUSHBOX_TIPS_STR_TIP),"game over");
			//g_draw_str_default_style_in_screen_center(game_frame_buff,"game over!",RES_FONT_NUM1);
			//uiWinDrawUpdate();
			//XOSTimeDly(1000);
            XOSTimeDly(10);
            OSDFrameDrawIcon(game_frame_buff,game_buff[GAME_PASS_TIPS].ptr,50,80,220,80);
            g_draw_resstr_default_style_in_screen_center(game_frame_buff,R_ID_STR_GAME_PASS,RES_FONT_NUM0);
			memcpy(ui_draw_ctrl.bufStart,game_frame_buff,game_frame_w*game_frame_h);
            uiWinDrawUpdate();
            XOSTimeDly(1000);
			deg_Printf("game_continue---->\n");
  			//game_draw_str( game_frame_buff,STR_50HZ);
		}else{
			//g_draw_str_default_style_in_screen_center(game_frame_buff,"pass!",RES_FONT_NUM1);
            // g_draw_str_default_style_in_screen_center(game_frame_buff,"game over!",RES_FONT_NUM1);
            // uiWinDrawUpdate();
			deg_Printf("game pass,next---->\n");
		    //game_draw_str( game_frame_buff,STR_NTSC);
		}

        game_continue = 0;
        passnum += 1;
        if(passnum == 8)
        {
			passnum = 0;
            game_over_in = 3;

        }
	//SysCtrl.gui_flush_sta = 1;//GUI �ָ�����ˢ��
	//uiWinDestroy(&handle);
	//deg_Printf("game over and exit-->\n");
        //show_game_state();
        memset(game_frame_buff,R_ID_PALETTE_Black,game_frame_w*game_frame_h);
        pushbox_draw_map(game_frame_buff);
		memcpy(ui_draw_ctrl.bufStart,game_frame_buff,game_frame_w*game_frame_h);
	    uiWinDrawUpdate();
    }else{
    	//MARK();
    	deg_Printf("flush---->\n");
		memcpy(ui_draw_ctrl.bufStart,game_frame_buff,game_frame_w*game_frame_h);
	    uiWinDrawUpdate();
	}
    
    if(game_over_in==1){
         game_over_in=0;

        // if(game_Level!=0){
        //         g_draw_str_default_style_in_screen_center(game_frame_buff,"game over!",RES_FONT_NUM1);
        //         uiWinDrawUpdate();
        // }
    
        // XOSTimeDly(1000);
        // deg_Printf("game over ---->\n");
        //SysCtrl.gui_flush_sta = 1;//GUI �ָ�����ˢ��
        // g_draw_str_default_style_in_screen_center(game_frame_buff,"PASS!",RES_FONT_NUM1);
        // uiWinDrawUpdate();
        //XOSTimeDly(1000);
        deg_Printf("game over ---->\n");
    }


}

//-------------------------pusbbox----------------------

/*******************************************************************************
* Function Name  : pushBoxOpenWin
* Description    : pushBoxOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int pushBoxOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	u16 w,h;
	deg_Printf("pushBoxOpenWin\n");

	pushbox_temp_icons = hal_sysMemMalloc(30*30*7+220*80);
	if(NULL == pushbox_temp_icons){
		deg_Printf("mem malloc foro icon  fail--->\n");
		return -1;
	}
	//uiWinSetVisible(winItem(handle,PUSHBOX_TIPS_RECT_ID1),0);
	//uiWinSetVisible(winItem(handle,PUSHBOX_TIPS_RECT_ID2),0);
	//uiWinSetVisible(winItem(handle,PUSHBOX_TIPS_STR_TIP),0);

	figure_up 			= pushbox_temp_icons+30*30*3;  //小人 //30*30
	figure_down 		= pushbox_temp_icons+30*30*3; //小人 //30*30
	figure_left 		= pushbox_temp_icons+30*30*3; //小人//30*30
	figure_right 		= pushbox_temp_icons+30*30*3; //小人 //30*30
	figure_bg_wall 		= pushbox_temp_icons+30*30*5;
	figure_box 			= pushbox_temp_icons+30*30*1;
	figure_target_box	= pushbox_temp_icons+30*30*0;
	figure_target_Point	= pushbox_temp_icons+30*30*4;
	figure_flood		= pushbox_temp_icons+30*30*2;
	figure_bg			= pushbox_temp_icons+30*30*6;
    figure_pass_tips	= pushbox_temp_icons+30*30*7;//180*60*6;

	u32 addr;
	addr = res_icon_GetAddrAndSize(R_ID_ICON_PUSHBOX_UNREACH,&w,&h);
	nv_read(addr,figure_box,w*h);

	addr = res_icon_GetAddrAndSize(R_ID_ICON_PUSHBOX_REACH,&w,&h);
	nv_read(addr,figure_target_box,w*h);

	addr = res_icon_GetAddrAndSize(R_ID_ICON_PUSHBOX_FLOOD,&w,&h);
	nv_read(addr,figure_flood,w*h);

	addr = res_icon_GetAddrAndSize(R_ID_ICON_PUSHBOX_MAN,&w,&h);
	nv_read(addr,figure_up,w*h);

	addr = res_icon_GetAddrAndSize(R_ID_ICON_PUSHBOX_POINT,&w,&h);
	nv_read(addr,figure_target_Point,w*h);

	addr = res_icon_GetAddrAndSize(R_ID_ICON_PUSHBOX_WALL,&w,&h);
	nv_read(addr,figure_bg_wall,w*h);

	addr = res_icon_GetAddrAndSize(R_ID_ICON_PUSHBOX_FLOOD,&w,&h);
	nv_read(addr,figure_bg,w*h);

	addr = res_icon_GetAddrAndSize(R_ID_ICON_GAME_TIPS_ICON,&w,&h);
	nv_read(addr,figure_pass_tips,w*h);
	figure_right = figure_left = figure_down = figure_up;

	game_buff[0].ptr = NULL;
	game_buff[GAME_MAN_UP].ptr 			= figure_up;
	game_buff[GAME_MAN_DOWN].ptr 		= figure_down;
	game_buff[GAME_MAN_LEFT].ptr 		= figure_left;
	game_buff[GAME_MAN_RIGHT].ptr 		= figure_right;
	game_buff[GAME_MAN_BG].ptr 			= figure_bg;
	game_buff[GAME_MAN_WALL].ptr 		= figure_bg_wall;
	game_buff[GAME_BOX].ptr 			= figure_box;
	game_buff[GAME_TARGET_BOX].ptr 		= figure_target_box;
	game_buff[GAME_TARGET_POINT].ptr 	= figure_target_Point;
    game_buff[GAME_PASS_TIPS].ptr 	= figure_pass_tips;

	SysCtrl.gui_flush_sta = 0;

	InitPushboxGame();
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameCloseWin
* Description    : nesGameCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int pushBoxCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	game_over_in=0;
    passnum = 0;
	hal_sysMemFree(pushbox_temp_icons);
	pushbox_temp_icons = NULL;
	SysCtrl.gui_flush_sta = 1;//GUI
	app_draw_Service(1);
	g_liu = 0;
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameWinChildClose
* Description    : nesGameWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int pushBoxWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("pushBoxWinChildClose\n");

	return 0;
}
/*******************************************************************************
* Function Name  : pushBoxKeyMsgOk
* Description    : pushBoxKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int pushBoxKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
		InitPushboxGame();
	}

	return 0;
}
/*******************************************************************************
* Function Name  : pushBoxKeyMsgUp
* Description    : pushBoxKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int pushBoxKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
        pushbox_movedir(handle,KEY_EVENT_UP);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : pushBoxKeyMsgDown
* Description    : pushBoxKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int pushBoxKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
        pushbox_movedir(handle,KEY_EVENT_DOWN);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : pushBoxKeyMsgMode
* Description    : pushBoxKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int pushBoxKeyMsgRight(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
        pushbox_movedir(handle,KEY_EVENT_RIGHT);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : pushBoxKeyMsgMenu
* Description    : pushBoxKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int pushBoxKeyMsgLeft(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
        pushbox_movedir(handle,KEY_EVENT_LEFT);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : pushBoxKeyMsgMain
* Description    : pushBoxKeyMsgMain
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int pushBoxKeyMsgMain(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;

	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
        passnum = 0;
    	uiWinDestroy(&handle);
	}
	return 0;
}
static int pushBoxSysMsg500MS(winHandle handle,u32 parameNum,u32* parame)
{
    static u32 cnttemp;
	if(game_over_in == 3){			
		cnttemp++;
		if(cnttemp>3)
		{
			game_over_in=0;
			uiWinDestroy(&handle);
		}
		else
		{
			//g_draw_resstr_default_style_in_screen_center(game_frame_buff,R_ID_STR_GAME_OVER,RES_FONT_NUM0);
			memcpy(ui_draw_ctrl.bufStart,game_frame_buff,game_frame_w*game_frame_h);
			uiWinDrawUpdate();
		}
			
		
	}else{
		cnttemp=0;
		memcpy(ui_draw_ctrl.bufStart,game_frame_buff,game_frame_w*game_frame_h);
		uiWinDrawUpdate();
	}
    return 0;
}


ALIGNED(4) msgDealInfor pushBoxMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		pushBoxOpenWin},
	{SYS_CLOSE_WINDOW,		pushBoxCloseWin},
	{SYS_CHILE_COLSE,		pushBoxWinChildClose},
	//{KEY_EVENT_POWER,		pushBoxKeyMsgPower},

	{KEY_EVENT_OK,			pushBoxKeyMsgOk},
	{KEY_EVENT_UP,			pushBoxKeyMsgUp},
	{KEY_EVENT_DOWN,		pushBoxKeyMsgDown},
	{KEY_EVENT_LEFT,		pushBoxKeyMsgLeft},
	{KEY_EVENT_RIGHT,		pushBoxKeyMsgRight},
	{KEY_EVENT_POWER,		pushBoxKeyMsgMain},
    {SYS_EVENT_500MS,	    pushBoxSysMsg500MS},
	{EVENT_MAX,NULL},
};

WINDOW(pushBoxWindow,pushBoxMsgDeal,pushBoxWin)


