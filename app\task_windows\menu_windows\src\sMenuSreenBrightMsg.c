/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuSreenBrightWin.c"

typedef struct{
	u32 strid;
	u32 value;
}SreenBrigntCtl;
ALIGNED(4) const SreenBrigntCtl sreenBrightTab[] = {
	{R_ID_STR_COM_VALUE_1, 1},
	{R_ID_STR_COM_VALUE_2, 2},
	{R_ID_STR_COM_VALUE_3, 3},
	{R_ID_STR_COM_VALUE_4, 4},
	{R_ID_STR_COM_VALUE_5, 5},
	{R_ID_STR_COM_VALUE_6, 6},
	{R_ID_STR_COM_VALUE_7, 7},
	{R_ID_STR_COM_VALUE_8, 8}
};
ALIGNED(4) u32 sreenBright_config;
/*******************************************************************************
* Function Name  : getSreenBrightResInfor
* Description    : getSreenBrightResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : none
*******************************************************************************/
static u32 getSreenBrightResInfor(u32 item,u32* image,u32* str)
{
	if(item < ARRAY_NUM(sreenBrightTab))
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = sreenBrightTab[item].strid;
	}else
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = INVALID_RES_ID;	
	}
	deg_Printf("[WIN]f11111111111111111\n");
	return 0;
}
/*******************************************************************************
* Function Name  : sreenBrightKeyMsgOk
* Description    : sreenBrightKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int sreenBrightKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;


	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,SREEN_B_SELECT_ID));
		if(item < ARRAY_NUM(sreenBrightTab))
		{
			sreenBright_config = sreenBrightTab[item].strid;
			user_config_set(CONFIG_ID_SREEN_BRIGHT, sreenBrightTab[item].strid);
			user_config_cfgSys(CONFIG_ID_SREEN_BRIGHT);
			user_config_save();	
		}	
			deg_Printf("[WIN]2222222222222\n");
		uiWinDestroy(&handle);			
	}
	return 0;
}
/*******************************************************************************
* Function Name  : sreenBrightKeyMsgUp
* Description    : sreenBrightKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int sreenBrightKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,SREEN_B_SELECT_ID));
		item = uiItemManageGetCurrentItem(winItem(handle,SREEN_B_SELECT_ID));
		if(item < ARRAY_NUM(sreenBrightTab))
		{
			task_com_lcd_brightness_cfg(sreenBrightTab[item].value, ARRAY_NUM(sreenBrightTab));
		}
		deg_Printf("[WIN]33333333333\n");
	}
	return 0;
}
/*******************************************************************************
* Function Name  : sreenBrightKeyMsgDown
* Description    : sreenBrightKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int sreenBrightKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	if(parameNum == 1)
		keyState = parame[0];
		
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManageNextItem(winItem(handle,SREEN_B_SELECT_ID));
		item = uiItemManageGetCurrentItem(winItem(handle,SREEN_B_SELECT_ID));
		if(item < ARRAY_NUM(sreenBrightTab))
		{
			task_com_lcd_brightness_cfg(sreenBrightTab[item].value, ARRAY_NUM(sreenBrightTab));
		}
	}
	deg_Printf("[WIN]444444444444\n");
	return 0;
}
/*******************************************************************************
* Function Name  : sreenBrightKeyMsgPower
* Description    : sreenBrightKeyMsgPower
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int sreenBrightKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : sreenBrightOpenWin
* Description    : sreenBrightOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int sreenBrightOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	 
	deg_Printf("[WIN]formatOpenWin\n");
	//uiItemManageSetRowSum(winItem(handle,FORMAT_SELECT_ID),1,Rh(40));
	//uiItemManageSetColumnSumWithGap(winItem(handle,FORMAT_SELECT_ID),0,2,Rw(100), Rw(0));
	//uiItemManageCreateItem(		winItem(handle,FORMAT_SELECT_ID),uiItemCreateMenuOption,getformatResInfor,2);
	
	uiItemManageSetItemHeight(winItem(handle,SREEN_B_SELECT_ID),Rh(35));
	uiItemManageCreateItem(		winItem(handle,SREEN_B_SELECT_ID),uiItemCreateMenuOption,getSreenBrightResInfor,ARRAY_NUM(sreenBrightTab));
#if 0
	uiItemManageSetCharInfor(	winItem(handle,FORMAT_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_White);
	uiItemManageSetSelectColor(	winItem(handle,FORMAT_SELECT_ID),R_ID_PALETTE_DoderBlue);
	uiItemManageSetUnselectColor(winItem(handle,FORMAT_SELECT_ID),R_ID_PALETTE_Gray);
#else
	uiItemManageSetSelectColorEx(winItem(handle,SREEN_B_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER, SMENU_SELECT_FN_COLOR, SMENU_SELECT_BG_COLOR);
	uiItemManageSetUnselectColorEx(winItem(handle,SREEN_B_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER, SMENU_UNSELECT_FN_COLOR, SMENU_UNSELECT_BG_COLOR);
#endif

	sreenBright_config  = user_config_get(CONFIG_ID_SREEN_BRIGHT);
	u32 itemNum = 0;
	while(itemNum < ARRAY_NUM(sreenBrightTab))
	{
		if(sreenBrightTab[itemNum].strid == sreenBright_config)
			break;
		itemNum++;
	}
	if(itemNum >= ARRAY_NUM(sreenBrightTab))
		itemNum = 0;
	uiItemManageSetCurItem(		winItem(handle,SREEN_B_SELECT_ID),itemNum);
	
	return 0;
}
/*******************************************************************************
* Function Name  : sreenBrightCloseWin
* Description    : sreenBrightCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int sreenBrightCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	task_com_lcd_brightness_cfg(user_configValue2Int(CONFIG_ID_SREEN_BRIGHT), ARRAY_NUM(sreenBrightTab));
	deg_Printf("[WIN]sreenBrightCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : sreenBrightWinChildClose
* Description    : sreenBrightWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int sreenBrightWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]sreenBrightWinChildClose\n");
	uiWinDestroy(&handle);
	return 0;
}


ALIGNED(4) msgDealInfor sreenBrightMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	sreenBrightOpenWin},
	{SYS_CLOSE_WINDOW,	sreenBrightCloseWin},
	{SYS_CHILE_COLSE,	sreenBrightWinChildClose},
	{KEY_EVENT_OK,		sreenBrightKeyMsgOk},
	{KEY_EVENT_UP,		sreenBrightKeyMsgUp},
	{KEY_EVENT_DOWN,	sreenBrightKeyMsgDown},
	{KEY_EVENT_LEFT,	sreenBrightKeyMsgPower},
	//{KEY_EVENT_RIGHT,	sreenBrightKeyMsgDown},
	{KEY_EVENT_POWER,	sreenBrightKeyMsgPower},
	{EVENT_MAX,NULL},
};

WINDOW(sreenBrightWindow,sreenBrightMsgDeal,sreenBrightWin)


