/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../app_common/inc/app_api.h"


ALIGNED(4) static SYS_TASK_OP_T sysTaskOp;
/*******************************************************************************
* Function Name  : app_taskRegister
* Description    : app_taskRegister
* Input          : taskID id,sysTask* task
* Output         : none
* Return         : none
*******************************************************************************/
static void app_taskRegister(taskID id,sysTask_T* task)
{
	if(id < TASK_MAX)
	{
		if(sysTaskOp.taskArray[id])
			deg_Printf("waring: task[%s]already registered\n",sysTaskOp.taskArray[id]->name);
		sysTaskOp.taskArray[id] = task;
		deg_Printf("task[%s] register\n",sysTaskOp.taskArray[id]->name);
	}
	else
	{
		deg_Printf("task id[%d] too large,max id:%d\n",id,TASK_MAX);
	}
}
/*******************************************************************************
* Function Name  : app_taskInit
* Description    : app_taskInit
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void app_taskInit(void)
{
	memset(&sysTaskOp,0, sizeof(sysTaskOp));
#if  FUN_BATTERY_CHARGE_SHOW
	app_taskRegister(TASK_BAT_CHARGE,	&taskBatCharge);
#endif
	app_taskRegister(TASK_POWER_OFF,	&taskPowerOff);
	app_taskRegister(TASK_MAIN,			&taskMain);

	app_taskRegister(TASK_RECORD_VIDEO,	&taskRecordVideo);
	app_taskRegister(TASK_RECORD_PHOTO,	&taskRecordPhoto);
	app_taskRegister(TASK_PLAY_VIDEO,	&taskPlayVideo);
#if (FUN_NES_GAME_EN == 1)
	app_taskRegister(TASK_NES_GAME,		&taskNesGame);
#endif

	app_taskRegister(TASK_C_GAME,		&taskCGame);
	app_taskRegister(TASK_VERSION,  	&taskVersion);
#if (FUN_MP3_PLAY_EN == 1)
	app_taskRegister(TASK_PLAY_MP3,		&taskPlayMp3);
#endif
	app_taskRegister(TASK_SETTING,		&taskSetting);
	app_taskRegister(TASK_USB_DEVICE,	&taskUSBDevice);
	app_taskRegister(TASK_SD_UPDATE,	&taskSDUpdate);

#if (FUN_AUDIO_RECORD_EN == 1)
	app_taskRegister(TASK_RECORD_AUDIO,	&taskRecordAudio);
	app_taskRegister(TASK_PLAY_AUDIO,	&taskPlayAudio);
#endif

	sysTaskOp.curTaskId 	= TASK_MAX;
	sysTaskOp.nextTaskId	= TASK_MAX;
	sysTaskOp.preTaskId		= TASK_MAX;
	task_com_para_init();
	sysMsgFuncRegister((msgDealInfor*)sysComMsgDeal);

}
/*******************************************************************************
* Function Name  : app_taskCurId
* Description    : app_taskCurId
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
taskID app_taskCurId(void)
{
	return sysTaskOp.curTaskId;
}
/*******************************************************************************
* Function Name  : app_taskStart
* Description    : app_taskStart
* Input          : taskID id,uint32 arg
* Output         : none
* Return         : none
*******************************************************************************/
void app_taskStart(u32 id,u32 arg)
{
	if(id < TASK_MAX)
	{
		if(sysTaskOp.nextTaskId == TASK_POWER_OFF)
		{
			deg_Printf("task [%s] has highest priority,task [%d] was ignored\n",sysTaskOp.taskArray[TASK_POWER_OFF]->name,id);
			return;
		}
		if(sysTaskOp.taskArray[id])
		{
			sysTaskOp.taskArray[id]->arg 	= arg;
			sysTaskOp.preTaskId 			= sysTaskOp.curTaskId;
			sysTaskOp.nextTaskId			= id;
			deg_Printf("will start task [%s]\n",sysTaskOp.taskArray[id]->name);
		}
		else
		{
			if(id == TASK_USB_UPDATE)
			{
				sysTaskOp.nextTaskId = id;
				deg_Printf("start usb update\n");
			}
			else
			{
				deg_Printf("start task [%d] not registered\n",id);
			}
		}
	}
}
/*******************************************************************************
* Function Name  : app_taskService
* Description    : app_taskService
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void app_taskService(void)
{
	while(1)
	{
		if(sysTaskOp.nextTaskId == TASK_USB_UPDATE)
			return;
		if(sysTaskOp.curTask)
		{
			deg_Printf("task [%s] closed\n",sysTaskOp.curTask->name);
			if(sysTaskOp.curTask->taskClose)
				sysTaskOp.curTask->taskClose(sysTaskOp.curTask->arg);
			uiWinDestroyDeskTopChildWin();
		}
		//uiWinHeapMemInfo();
		sysTaskOp.curTaskId 	= sysTaskOp.nextTaskId;
		sysTaskOp.nextTaskId	= TASK_MAX;
		sysTaskOp.curTask		= sysTaskOp.taskArray[sysTaskOp.curTaskId];
		if(sysTaskOp.curTask==NULL)
		{
			deg_Printf("task [%d] not registered,start [%s] task\n",sysTaskOp.curTaskId,sysTaskOp.taskArray[TASK_POWER_OFF]->name);
			sysTaskOp.curTaskId	= TASK_POWER_OFF;
			sysTaskOp.curTask   = sysTaskOp.taskArray[sysTaskOp.curTaskId];
		}
		deg_Printf("task [%s] open\n",sysTaskOp.curTask->name);
		if(sysTaskOp.curTask->taskOpen)
			sysTaskOp.curTask->taskOpen(sysTaskOp.curTask->arg);
		if(sysTaskOp.curTaskId != TASK_POWER_OFF)
		{
			app_draw_Service(1);
		}
		while(1)
		{
			if(sysTaskOp.nextTaskId < TASK_MAX)
				break;
			app_systemService();
			if(sysTaskOp.curTask->taskService)
				sysTaskOp.curTask->taskService(sysTaskOp.curTask->arg);
		}
	}
}


