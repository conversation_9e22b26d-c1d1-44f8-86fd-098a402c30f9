/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	PLAYAUDIO_MODE_ID   = 0,
	PLAYAUDIO_SD_ID,
	PLAYAUDIO_SDVOL_ID,
	PLAYAUDIO_VOLUME_ID,
	PLAYAUDIO_VOLUME_VALUE_ID,
	PLAYAUDIO_BATERRY_ID,
	PLAYAUDIO_SELECT_ID,
};

UNUSED ALIGNED(4) const widgetCreateInfor playAudioWin[] =
{
	createFrameWin( 							Rx(0),   Ry(0),   Rw(320), Rh(240), R_ID_PALETTE_Transparent,WIN_ABS_POS),
	createImageIcon(PLAYAUDIO_MODE_ID,      	Rx(0),   Ry(0),   Rw(40),  Rh(40),  R_ID_ICON_MTMUSIC, 		ALIGNMENT_LEFT),
	createImageIcon(PLAYAUDIO_SD_ID,        	Rx(40),  Ry(0),   Rw(40),  Rh(40),  R_ID_ICON_MTSDCNORMAL,	ALIGNMENT_CENTER),
	createStringIcon(PLAYAUDIO_SDVOL_ID,		Rx(40),  Ry(0),   Rw(40),  Rh(40),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_GREEN2,	DEFAULT_FONT),
	createImageIcon(PLAYAUDIO_VOLUME_ID,    	Rx(80),  Ry(0),   Rw(40),  Rh(40),  R_ID_ICON_MENUVOLUME,	ALIGNMENT_RIGHT),
	createStringIcon(PLAYAUDIO_VOLUME_VALUE_ID,	Rx(120), Ry(0),   Rw(40),  Rh(40),	RAM_ID_MAKE(" "),	    ALIGNMENT_LEFT, 	R_ID_PALETTE_White,DEFAULT_FONT),
	createImageIcon(PLAYAUDIO_BATERRY_ID,   	Rx(280), Ry(0),   Rw(40),  Rh(40), 	R_ID_ICON_MTBATTERY3,	ALIGNMENT_RIGHT),
	createItemManage(PLAYAUDIO_SELECT_ID,		Rx(0),   Ry(40),  Rw(320), Rh(200), R_ID_PALETTE_DimGray),
	
	widgetEnd(),

};
/*******************************************************************************
* Function Name  : playAudioVolumeShow
* Description    : playAudioVolumeShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playAudioVolumeShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,PLAYAUDIO_VOLUME_VALUE_ID),RAM_ID_MAKE(task_com_curVolume_strid()));
}

/*******************************************************************************
* Function Name  : playAudioSDShow
* Description    : playAudioSDShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playAudioSDShow(winHandle handle)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
	{
		uiWinSetResid(winItem(handle,PLAYAUDIO_SD_ID),R_ID_ICON_MTSDCNORMAL);
		uiWinSetVisible(winItem(handle,PLAYAUDIO_SDVOL_ID),1);
		uiWinSetResid(winItem(handle,PLAYAUDIO_SDVOL_ID),RAM_ID_MAKE(task_com_sdcCap_str()));
		
	}	
	else{
		uiWinSetResid(winItem(handle,PLAYAUDIO_SD_ID),R_ID_ICON_MTSDCNULL);
		uiWinSetVisible(winItem(handle,PLAYAUDIO_SDVOL_ID),0);
	}	
}
/*******************************************************************************
* Function Name  : playAudioBaterryShow
* Description    : playAudioBaterryShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playAudioBaterryShow(winHandle handle)
{
	uiWinSetVisible(winItem(handle,PLAYAUDIO_BATERRY_ID),1);
	uiWinSetResid(winItem(handle,PLAYAUDIO_BATERRY_ID),task_com_battery_res_get());
	
	
}
