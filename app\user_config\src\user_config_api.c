/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../app_common/inc/app_api.h"

static ALIGNED(4) SYSTEM_FLAG_T SysFlag;
static ALIGNED(4) u32 user_config_reset = 0;

/*******************************************************************************
* Function Name  : configReadFromSD and configWriteToSD
* Description    : user config read from SD and write to SD
* Input          : u32 buff
* 	               u32 len
* Output         : None
* Return         : none
*******************************************************************************/
#if NVFS_STROAGE == NV_STROAGE_SDC

#define USER_CONFIG_FILE		"user_config.bin"
static void configReadFromSD(u32 buff,u32 len)
{
	int fd;
    //UINT size;
    fd = fs_open((const char *)USER_CONFIG_FILE,FA_READ);
	if(fd<0)
	    fd = fs_open((const char *)USER_CONFIG_FILE,FA_READ|FA_CREATE_ALWAYS);
	fs_read((int)fd,(void *)buff,len);
    fs_close((int)fd);
}
static void configWriteToSD(INT32U buff,INT32U len)
{
	int fd;
    //UINT size;
    fd = fs_open((const char *)USER_CONFIG_FILE,FA_READ|FA_WRITE);
	fs_seek((int)fd,0,0);
	fs_write((int)fd,(void *)buff,len);
	fs_close((int)fd);
}
#endif
/*******************************************************************************
* Function Name  : configReadFromSPI and configWriteToSPI
* Description    : user config read from FLASH and write to FLASH
* Input          : u32 buff
* 	               u32 len
* Output         : None
* Return         : none
*******************************************************************************/
#if ((NVFS_STROAGE == NV_STROAGE_SPI) || (NVFS_STROAGE == NV_STROAGE_SDRAM))
static u32 configSPIStartAddress = 0;
static void configReadFromSPI(u32 buff,u32 len)
{
	hal_spiFlashRead(configSPIStartAddress, buff, len);
}
static void configWriteToSPI(u32 buff,u32 len)
{
	u32 i = 0;
	while(len)
	{
		hal_spiFlashEraseSector(configSPIStartAddress + i * SF_SECTOR_SIZE, 1);
		hal_spiFlashWrite(configSPIStartAddress + i * SF_SECTOR_SIZE, buff + i * SF_SECTOR_SIZE, (len > SF_SECTOR_SIZE)?SF_SECTOR_SIZE:len, 1);
		if(len > SF_SECTOR_SIZE)
			len -= SF_SECTOR_SIZE;
		else
			len = 0;
		i++;
	}
}
#endif
/*******************************************************************************
* Function Name  : user_config_read and user_config_read
* Description    : user config read  and write
* Input          : u32 buff
* 	               u32 len
* Output         : None
* Return         : none
*******************************************************************************/
static void user_config_read(u32 buff,u32 len)
{
#if NVFS_STROAGE == NV_STROAGE_SDC
	configReadFromSD(buff,len);
#else
	configReadFromSPI(buff,len);
#endif
}
static void user_config_write(u32 buff,u32 len)
{
#if NVFS_STROAGE == NV_STROAGE_SDC
	configWriteToSD(buff,len);
#elif NVFS_STROAGE == NV_STROAGE_SPI
	configWriteToSPI(buff,len);
#endif
}
/*******************************************************************************
* Function Name  : user_config_checksum_cal
* Description    : set configure value
* Input          : u32 configId : configure id
*                  u32 value   : configure value
* Output         : None
* Return         : none
*******************************************************************************/
static u32 user_config_checksum_cal(void)
{
	u32 CheckSum=0,i;
	for(i = 0;i < sizeof(SysFlag.flag)/sizeof(SysFlag.flag[0]);i++)
	{
		CheckSum += SysFlag.flag[i];
	}
	if(CheckSum == 0)
		CheckSum = 0xaa55aa55;
	return CheckSum;
}
/*******************************************************************************
* Function Name  : user_config_set
* Description    : set configure value
* Input          : u32 configId : configure id
*                  u32 value    : configure value
* Output         : None
* Return         : none
*******************************************************************************/
void user_config_set(u32 configId,u32 value)
{
	if(configId < sizeof(SysFlag.flag)/sizeof(SysFlag.flag[0]))
    	SysFlag.flag[configId]=value;
}

/*******************************************************************************
* Function Name  : userCofigGetValue
* Description    : get configure value in configure table
* Input          : u8_t configId : configure id
* Output         : None
* Return         : u32_t : configure value
*******************************************************************************/
u32 user_config_get(u32 configId)
{
    if(configId < sizeof(SysFlag.flag)/sizeof(SysFlag.flag[0]))
		return SysFlag.flag[configId];
    return 0;
}
/*******************************************************************************
* Function Name  : user_config_save
* Description    : save user configure value to spi flash
* Input          : none
* Output         : None
* Return         : s32_t
*                    0 : always
*******************************************************************************/
void user_config_save(void)
{
  	SysFlag.CheckSum = user_config_checksum_cal();
	deg_Printf("write config check sum:0x%x\n",SysFlag.CheckSum);
	user_config_write((INT32U)(&SysFlag),sizeof(SYSTEM_FLAG_T));

}
/*******************************************************************************
* Function Name  : userConfigReset
* Description    : reset or default user configure value
* Input          : none
* Output         : None
* Return         : s32_t
*                    0 : always
*******************************************************************************/
void userConfig_Reset(void)
{
	USER_CFG_TAB_T * tab = (USER_CFG_TAB_T *)user_cfg_tab;
    deg_Printf("user config : reset\n");
	while(tab->cfg_id < CONFIG_ID_MAX)
	{
		user_config_set(tab->cfg_id, tab->cfg_value);
		tab++;
	}

	user_config_save();

}
/*******************************************************************************
* Function Name  : userConfig_Init
* Description    : initial user configure value
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
void userConfig_Init(void)
{
    INT32U CheckSum;
	configSPIStartAddress = nv_configAddr();
	user_config_read((u32)(&SysFlag),sizeof(SYSTEM_FLAG_T));
	CheckSum 	= user_config_checksum_cal();

	deg_Printf("user config checksum: 0x%x,0x%x\n",CheckSum,SysFlag.CheckSum);
	if((CheckSum != SysFlag.CheckSum) || (0 == CheckSum))
	{
		deg_Printf("user config checksum fail, reset config.0x%x\n",SysFlag.CheckSum);
		memset(&SysFlag,0,sizeof(SYSTEM_FLAG_T));
		userConfig_Reset();
		user_config_reset = 1;
	}
}
/*******************************************************************************
* Function Name  : userConfig_Init
* Description    : initial user configure value
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
u32 userConfigInitial(void)
{
	return user_config_reset;
}
/*******************************************************************************
* Function Name  : user_configValue2Int
* Description    : change user config value to int value
* Input          : none
* Output         : None
* Return         : u32 value
*******************************************************************************/
u32 user_configValue2Int(u32 config_id)
{
	u32 value = 0;
	switch(user_config_get(config_id))
	{
		// lanuage
		case R_ID_STR_LAN_CZECH: 		value = LAN_CZECH;		break;
		case R_ID_STR_LAN_DUTCH: 		value = LAN_DUTCH;		break;
		case R_ID_STR_LAN_FRECH: 		value = LAN_FRENCH;		break;
		case R_ID_STR_LAN_GERMAN: 		value = LAN_GERMAN;		break;
	//	case R_ID_STR_LAN_HEBREW: 		value = LAN_HEBREW;		break;
		case R_ID_STR_LAN_ITALIAN: 		value = LAN_ITALIAN;	break;

		case R_ID_STR_LAN_JAPANESE: 	value = LAN_JAPANESE;	break;
#if JAPANESE_HIRAGANA
		case R_ID_STR_LAN_JAPANESE_HIRAGANA: 	value = LAN_JAPANESE_HIRAGANA;	break;
#endif
		case R_ID_STR_LAN_KOERA: 		value = LAN_KOERA;		break;

		case R_ID_STR_LAN_POLISH: 		value = LAN_POLISH;		break;
		case R_ID_STR_LAN_PORTUGUESE: 	value = LAN_PORTUGUESE;	break;
		case R_ID_STR_LAN_RUSSIAN: 		value = LAN_RUSSIAN;	break;
		case R_ID_STR_LAN_SCHINESE: 	value = LAN_SCHINESE;	break;
		case R_ID_STR_LAN_SPANISH: 		value = LAN_SPANISH;	break;
		case R_ID_STR_LAN_TAI: 			value = LAN_TAI;		break;
		case R_ID_STR_LAN_TCHINESE: 	value = LAN_TCHINESE;	break;
		case R_ID_STR_LAN_TURKEY: 		value = LAN_TURKEY;		break;
		//case R_ID_STR_LAN_UKRAINIAN: 	value = LAN_UKRAINIAN;	break;
        case R_ID_STR_LAN_ENGLISH: 		value = LAN_ENGLISH;	break;
		case R_ID_STR_LAN_ARABIA: 		value = LAN_ARABIA;		break;
        case R_ID_STR_LAN_CROATIA: 		value = LAN_CROATIA;	break;
		case R_ID_STR_LAN_SLOVENIAN: 	value = LAN_SLOVENIAN;	break;
		// common
        case R_ID_STR_COM_ON:
		case R_ID_STR_COM_OK:
		case R_ID_STR_COM_YES:			value = 1;					break;
		case R_ID_STR_COM_OFF:
		case R_ID_STR_COM_CANCEL:
		case R_ID_STR_COM_NO:			value = 0;					break;
		// time
		case R_ID_STR_TIM_2SEC: 		value = 2;					break;
		case R_ID_STR_TIM_5SEC: 		value = 5;					break;
		case R_ID_STR_TIM_10SEC:		value = 10;					break;
		case R_ID_STR_TIM_30SEC: 		value = 30;					break;
		case R_ID_STR_TIM_1MIN: 		value = 1*60;				break;
		case R_ID_STR_TIM_2MIN: 		value = 2*60;				break;
		case R_ID_STR_TIM_3MIN: 		value = 3*60;				break;
		case R_ID_STR_TIM_5MIN: 		value = 5*60;				break;
		case R_ID_STR_TIM_10MIN: 		value = 10*60;				break;
		// resolution
		case R_ID_STR_RES_480FHD: 		value = (720<<16)|480;		break;
        case R_ID_STR_RES_1024P: 		value = (1280<<16)|1024;	break;
		case R_ID_STR_RES_1080P: 		value = (1440<<16)|1080;	break;
		case R_ID_STR_RES_FHD:
		case R_ID_STR_RES_1080FHD: 		value = (1440<<16)|1080;	break;
		case R_ID_STR_RES_HD:
		case R_ID_STR_RES_720P: 		value = (960<<16)|720;		break;
		case R_ID_STR_RES_240P:
		case R_ID_STR_RES_QVGA: 		value = (320<<16)|240;		break;
		case R_ID_STR_RES_480P:
		case R_ID_STR_RES_VGA: 			value = (640<<16)|480;		break; //width align to 32, height align to 16
		case R_ID_STR_RES_1M: 			value = (1280<<16)|960;		break; //width align to 32, height align to 16
		case R_ID_STR_RES_2M: 			value = (1440<<16)|1080;	break; //width align to 32, height align to 16
		case R_ID_STR_RES_3M: 			value = (1920<<16)|1440;	break; //width align to 32, height align to 16
		case R_ID_STR_RES_5M: 			value = (2560<<16)|1920;	break; //width align to 32, height align to 16
		case R_ID_STR_RES_8M: 			value = (3200<<16)|2400;	break; //width align to 32, height align to 16
		case R_ID_STR_RES_10M: 			value = (3520<<16)|2640;	break; //width align to 32, height align to 16
		case R_ID_STR_RES_12M: 			value = (3840<<16)|2880;	break; //width align to 32, height align to 16
		case R_ID_STR_RES_16M: 			value = (4480<<16)|3360;	break; //width = 640*N, height = 480*6
		case R_ID_STR_RES_18M: 			value = (4800<<16)|3600;	break; //width = 640*N, height = 480*7
		case R_ID_STR_RES_20M: 			value = (5120<<16)|3840;	break; //width = 640*N, height = 480*8
		case R_ID_STR_RES_24M: 			value = (5760<<16)|4320;	break; //width = 640*N, height = 480*10
		case R_ID_STR_RES_40M: 			value = (7040<<16)|5280;	break; //width = 640*N, height = 480*12
		case R_ID_STR_RES_48M: 			value = (7680<<16)|5760;	break; //width = 640*N, height = 480*14
		// level
        case R_ID_STR_COM_LOW: 			value = 1;					break;
		case R_ID_STR_COM_MIDDLE: 		value = 2;					break;
		case R_ID_STR_COM_HIGH: 		value = 3;					break;
		// frq
        case R_ID_STR_COM_50HZ: 		value = 0;					break;
		case R_ID_STR_COM_60HZ: 		value = 1;					break;
		// IR
        case R_ID_STR_IR_AUTO: 			value = 2;					break;

		//isp
		// white blance
        case R_ID_STR_ISP_AUTO: 		value = 0;					break;
		case R_ID_STR_ISP_SUNLIGHT: 	value = 1;					break;
		case R_ID_STR_ISP_CLOUDY: 		value = 2;					break;
		case R_ID_STR_ISP_TUNGSTEN: 	value = 3;					break;
		case R_ID_STR_ISP_FLUORESCENT: 	value = 4;					break;
		//  ev
        case R_ID_STR_COM_N2_0: 		value = 1;					break;
		case R_ID_STR_COM_N1_0: 		value = 2;					break;
		case R_ID_STR_COM_P0_0: 		value = 3;					break;
		case R_ID_STR_COM_P1_0: 		value = 4;					break;
		case R_ID_STR_COM_P2_0: 		value = 5;					break;
		//value
		case R_ID_STR_COM_VALUE_0: 		value = 0;					break;
		case R_ID_STR_COM_VALUE_1: 		value = 1;					break;
		case R_ID_STR_COM_VALUE_2: 		value = 2;					break;
		case R_ID_STR_COM_VALUE_3: 		value = 3;					break;
		case R_ID_STR_COM_VALUE_4: 		value = 4;					break;
		case R_ID_STR_COM_VALUE_5: 		value = 5;					break;
		case R_ID_STR_COM_VALUE_6: 		value = 6;					break;
		case R_ID_STR_COM_VALUE_7: 		value = 7;					break;
		case R_ID_STR_COM_VALUE_8: 		value = 8;					break;
		case R_ID_STR_COM_VALUE_9: 		value = 9;					break;
		case R_ID_STR_COM_VALUE_10: 	value = 10;					break;
		case R_ID_STR_COM_VALUE_11: 	value = 11;					break;
		case R_ID_STR_COM_VALUE_12: 	value = 12;					break;
		case R_ID_STR_COM_VALUE_13: 	value = 13;					break;
		case R_ID_STR_COM_VALUE_14: 	value = 14;					break;
		case R_ID_STR_COM_VALUE_15: 	value = 15;					break;

	}

	return value;
}
/*******************************************************************************
* Function Name  : user_config_Language
* Description    : config user language
* Input          : none
* Output         : None
* Return         : u32 value
*******************************************************************************/
extern void winUpdateAllResId(void);
void user_config_Language(void)
{
	static u32 lastSetting;
	res_font_StringTableInit((void *)User_String_Table,R_STR_MAX);// load string table
	res_font_SetLanguage(user_configValue2Int(CONFIG_ID_LANGUAGE));
	if(user_config_get(CONFIG_ID_LANGUAGE)!=lastSetting)
	{
		lastSetting = user_config_get(CONFIG_ID_LANGUAGE);
		uiWinUpdateAllResId();
	}
}
/*******************************************************************************
* Function Name  : user_config_Language
* Description    : config user language
* Input          : none
* Output         : None
* Return         : u32 value
*******************************************************************************/
void user_config_cfgSys(u32 configId)
{
	INT32U value = user_configValue2Int(configId);
    switch(configId)
	{
		case CONFIG_ID_KEYSOUND:
			SysCtrl.dev_stat_keysound = value;
			break;
		case CONFIG_ID_RESOLUTION:
			videoRecordCmdSet(CMD_COM_RESOLUTIONN,value);
			//app_taskRecordVideo_caltime();
			break;
		case CONFIG_ID_PRESLUTION:
			break;
		case CONFIG_ID_MOTIONDECTION:
			if(value)
				hal_mdEnable(1);
			else
				hal_mdEnable(0);
			break;
		case CONFIG_ID_PARKMODE:
			if(SysCtrl.dev_fd_gsensor < 0)
			{
			}
			break;
		case CONFIG_ID_AUDIOREC:
			videoRecordCmdSet(CMD_COM_AUDIOEN,value);
			break;
	    case CONFIG_ID_TIMESTAMP:
			videoRecordCmdSet(CMD_COM_TIMESTRAMP,value);break;
	    case CONFIG_ID_LOOPTIME :
			SysCtrl.rec_looptime = value;
			if(value == 0)
			{
				videoRecordCmdSet(CMD_COM_LOOPREC,0);
				videoRecordCmdSet(CMD_COM_LOOPTIME,10*60);
				SysCtrl.rec_looptime = 10*60;
			}
			else
			{
				videoRecordCmdSet(CMD_COM_LOOPREC,1);
				videoRecordCmdSet(CMD_COM_LOOPTIME,value);
			}
			break;
	    case CONFIG_ID_LANGUAGE:
			user_config_Language();
			break;
		case CONFIG_ID_GSENSOR:
			if(SysCtrl.dev_fd_gsensor >= 0)
				dev_ioctrl(SysCtrl.dev_fd_gsensor,DEV_GSENSOR_LOCK_WRITE,(INT32U)value);
			break;
	    case CONFIG_ID_EV:
			hal_sensor_EV_set(value, 5, 0x10);
			break;
		case CONFIG_ID_WBLANCE:
			hal_sensor_awb_scene_set(value);
			break;
		//case CONFIG_ID_FREQUNCY:
		//	  //hal_sensor_fps_adpt(value,25);
		//	break;
		case CONFIG_ID_IR_LED:
			dev_ioctrl(SysCtrl.dev_fd_ir,DEV_IR_WRITE,value);
			break;
		case CONFIG_ID_SREEN_BRIGHT:
			task_com_lcd_brightness_cfg(value, 8);
			break;
		case CONFIG_ID_SYSTEM_VOLUME:
			SysCtrl.curVolume = value;
			hal_dacSetVolume(task_com_curVolume_get());
			break;
	}
}
/*******************************************************************************
* Function Name  : user_config_cfgSysAll
* Description    : user config change to system ctrl
* Input          : none
* Output         : None
* Return         : u32 value
*******************************************************************************/
void user_config_cfgSysAll(void)
{
	uint32 i;
	for(i=0;i < CONFIG_ID_MAX;i++)
		user_config_cfgSys(i);
}







