/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	TERIS_MODE_ID=0,
	TERIS_BG1_ID,
	TERIS_BG2_ID,
	TERIS_BG3_ID,
	TERIS_BG4_ID,
	TERIS_TIPS_ID,
	TERIS_GRADE_ID,
	TERIS_LEVEL_ID,
	TERIS_LEVEL_NUM_ID,
    TERIS_NEXT_STR_ID,
    TERIS_NEXT_ICO_ID,
	TERIS_BATERRY_ID,

	TERIS_MAX_ID
};

UNUSED ALIGNED(4) const widgetCreateInfor terisWin[] =
{
	createFrameWin(                     Rx(0),  Ry(0),   Rw(220), Rh(176),  R_ID_PALETTE_Gray,WIN_ABS_POS),
	createRect(TERIS_BG1_ID,             Rx(0),  Ry(0),   Rw(128), Rh(176),  R_ID_PALETTE_LightGreen),
	createRect(TERIS_BG2_ID,             Rx(8),  Ry(5),   Rw(112), Rh(171),  R_ID_PALETTE_Yellow),

	createRect(TERIS_BG3_ID,             Rx(150),  Ry(0),   Rw(70), Rh(176),  R_ID_PALETTE_LightGreen),
	createRect(TERIS_BG4_ID,             Rx(155),  Ry(5),   Rw(60), Rh(166),  R_ID_PALETTE_Yellow),
	

	createStringIcon(TERIS_NEXT_STR_ID, Rx(150),Ry(10),   Rw(60),  Rh(24),	RAM_ID_MAKE("NEXT"),	ALIGNMENT_CENTER, 	R_ID_PALETTE_Green,	DEFAULT_FONT),
	//createImageIcon(TERIS_NEXT_ICO_ID,  Rx(220),  Ry(130),   Rw(32),  Rh(32),   R_ID_ICON_MTRECORD, 	ALIGNMENT_CENTER),

	createStringIcon(TERIS_TIPS_ID,  	Rx(150),Ry(80),  Rw(60),  Rh(32),	RAM_ID_MAKE("Score"),	ALIGNMENT_CENTER, 	R_ID_PALETTE_Green,	DEFAULT_FONT),
	createStringIcon(TERIS_GRADE_ID,  	Rx(150),Ry(112),  Rw(60),  Rh(32),	RAM_ID_MAKE("000000"),		ALIGNMENT_CENTER, 	R_ID_PALETTE_Green,	DEFAULT_FONT),

	//createStringIcon(TERIS_LEVEL_ID, Rx(220),Ry(170),   Rw(60),  Rh(32),	RAM_ID_MAKE("LEVEL"),	ALIGNMENT_CENTER, 	R_ID_PALETTE_Green,	DEFAULT_FONT),
	//createStringIcon(TERIS_LEVEL_NUM_ID, Rx(220),Ry(210),   Rw(60),  Rh(32),	RAM_ID_MAKE("000"),	ALIGNMENT_CENTER, 	R_ID_PALETTE_Green,	DEFAULT_FONT),
	widgetEnd(),
};
