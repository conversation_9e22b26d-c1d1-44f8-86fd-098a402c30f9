/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskNesGameWin.c"

/*******************************************************************************
* Function Name  : getPlayAudioResInfor
* Description    : audioKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static u32 getNesGameResInfor(u32 item,u32* image,u32* str)
{
	if(image)
		*image 	= INVALID_RES_ID;
	if(str)
		*str	= (u32) filelist_GetFileNameByIndex(SysCtrl.nes_list, (int)item);
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameOpenWin
* Description    : nesGameOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	task_nesGame_op.curId = INVALID_RES_ID;
	//nes_palette_init();
	task_nesGame_op.nesGame_step  = NES_GAME_CARD;
	taskNesGameOpenList();
	uiItemManageSetHeightAvgGap(winItem(handle,NESGAME_SELECT_ID),	Rh(33));
	uiItemManageCreateItem(		winItem(handle,NESGAME_SELECT_ID),	uiItemCreateMenuOption,	getNesGameResInfor, task_nesGame_op.openListTotal);
	uiItemManageSetCharInfor(	winItem(handle,NESGAME_SELECT_ID),	DEFAULT_FONT,	ALIGNMENT_LEFT,		R_ID_PALETTE_White);
	uiItemManageSetSelectColor(	winItem(handle,NESGAME_SELECT_ID),	R_ID_PALETTE_DoderBlue);
	uiItemManageSetUnselectColor(winItem(handle,NESGAME_SELECT_ID),	R_ID_PALETTE_Gray);
	uiItemManageSetCurItem(		winItem(handle,NESGAME_SELECT_ID),	SysCtrl.file_index);
	if(task_nesGame_op.openListTotal == 0)
	{
		app_taskStart(TASK_MAIN,0);
		//uiOpenWindow(&noFileWindow,0, 1, "no game file");
	}	
	else
	{
		nesGameSDShow(handle);
		nesGameBaterryShow(handle);
	}
			
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameCloseWin
* Description    : nesGameCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameWinChildClose
* Description    : nesGameWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("nesGameWinChildClose\n");
	taskNesGameOpenList();
	if(task_nesGame_op.openListTotal == 0)
	{
		SysCtrl.winChangeEnable = 1;
		app_taskStart(TASK_MAIN,0);
		//uiOpenWindow(&noFileWindow, 0, 1, "no game file");
		return 0;
	}
	uiItemManageUpdateRes(winItem(handle,NESGAME_SELECT_ID),task_nesGame_op.openListTotal,SysCtrl.file_index);
	nesGameSDShow(handle);
	nesGameBaterryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameKeyMsgOk
* Description    : nesGameKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;


	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
		task_com_sound_wait_end();
		SysCtrl.file_index = uiItemManageGetCurrentItem(winItem(handle,NESGAME_SELECT_ID));
		taskNesGameStart();
	}
			
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameKeyMsgUp
* Description    : nesGameKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		task_com_keysound_play();
		task_com_sound_wait_end();
		uiItemManagePreItem(winItem(handle,NESGAME_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameKeyMsgDown
* Description    : nesGameKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		task_com_keysound_play();
		task_com_sound_wait_end();
		uiItemManageNextItem(winItem(handle,NESGAME_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameKeyMsgLeft
* Description    : nesGameKeyMsgLeft
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameKeyMsgLeft(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		task_com_keysound_play();
		task_com_sound_wait_end();
		uiItemManagePrePage(winItem(handle,NESGAME_SELECT_ID));
		//uiOpenWindow(&delCurWindow,0,0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameKeyMsgRight
* Description    : nesGameKeyMsgRight
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameKeyMsgRight(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		task_com_keysound_play();
		task_com_sound_wait_end();
		uiItemManageNextPage(winItem(handle,NESGAME_SELECT_ID));
		//uiOpenWindow(&delAllWindow,0,0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameKeyMsgPower
* Description    : nesGameKeyMsgPower
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
		task_com_sound_wait_end();
		SysCtrl.winChangeEnable = 1;
		app_taskStart(TASK_MAIN,0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameSysMsgSD
* Description    : nesGameSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	if((SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL)) 
	{
		SysCtrl.winChangeEnable = 1;
		app_taskStart(TASK_MAIN,0);
		//uiOpenWindow(&noFileWindow, 0, 1, "no game file");
	}
	else
	{
		taskNesGameOpenList();
		if(task_nesGame_op.openListTotal == 0)
		{
			SysCtrl.winChangeEnable = 1;
			app_taskStart(TASK_MAIN,0);
			//uiOpenWindow(&noFileWindow, 0, 1, "no game file");
			return 0;
		}
		uiItemManageUpdateRes(winItem(handle,NESGAME_SELECT_ID),(u32)task_nesGame_op.openListTotal,SysCtrl.file_index);
		nesGameSDShow(handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameSysMsgUSB
* Description    : nesGameSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{	
	nesGameBaterryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : nesGameSysMsgBattery
* Description    : nesGameSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
		nesGameBaterryShow(handle);	
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainSysMsg1S
* Description    : playMp3MainSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int nesGameSysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
	//if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
	//{
	//	if(uiWinIsVisible(winItem(handle,NESGAME_BATERRY_ID)))
	//		uiWinSetVisible(winItem(handle,NESGAME_BATERRY_ID),0);
	//	else
	//	{
	//		uiWinSetVisible(winItem(handle,NESGAME_BATERRY_ID),1);
	//		uiWinSetResid(winItem(handle,NESGAME_BATERRY_ID),R_ID_ICON_MTBATTERYCHARGE);
	//		
	//	}	
	//}
	return 0;
}



ALIGNED(4) msgDealInfor nesGameMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		nesGameOpenWin},
	{SYS_CLOSE_WINDOW,		nesGameCloseWin},
	{SYS_CHILE_COLSE,		nesGameWinChildClose},
	{KEY_EVENT_OK,			nesGameKeyMsgOk},
	{KEY_EVENT_UP,			nesGameKeyMsgUp},
	{KEY_EVENT_DOWN,		nesGameKeyMsgDown},
	{KEY_EVENT_LEFT,		nesGameKeyMsgLeft},
	{KEY_EVENT_RIGHT,		nesGameKeyMsgRight},
	{KEY_EVENT_POWER,		nesGameKeyMsgPower},
	{SYS_EVENT_SDC,			nesGameSysMsgSD},
	{SYS_EVENT_USBDEV,		nesGameSysMsgUSB},
	{SYS_EVENT_BAT,			nesGameSysMsgBattery},
	{SYS_EVENT_1S,			nesGameSysMsg1S},	
	{EVENT_MAX,NULL},
};

WINDOW(nesGameWindow,nesGameMsgDeal,nesGameWin)


