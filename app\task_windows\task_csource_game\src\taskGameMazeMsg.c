﻿/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        : 迷宫
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskGameMazeWin.c"

extern u8 *game_frame_buff;
extern u16 game_frame_w;
extern u16 game_frame_h;
static u8* maze_temp_icons = NULL;

/********************************迷宫****************************************************/
#define  MAZE_WIDTH       22 // 22 //12*12
#define  MAZE_HEIGHT      15 // 15 //14*14

static INT8U  maze_width;
static INT8U  maze_high;
static INT8U  maze_width_peple;
static INT8U  maze_high_peple;
static INT8U  maze_level =1;
static INT8U  maze_level_flag =0;
static INT8U  game_over_in =0;
static INT8U maze[MAZE_HEIGHT][MAZE_WIDTH];
/********************0 :地板***1:通道*********************/	
static const INT8U maze1[MAZE_HEIGHT][MAZE_WIDTH] = {
	{0,0,1,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},  //  1
	{0,0,1,1,1  ,1,1,0,1,1  ,1,1,1,1,1  ,0,1,1,1,1  ,1,0},	//  2
	{0,0,1,0,0  ,0,1,0,0,0  ,0,0,1,0,1  ,0,1,0,1,0  ,0,0},	//  3
	{0,0,1,1,1  ,0,1,1,1,1  ,1,1,1,0,1  ,0,1,0,1,1  ,1,0},	//  4
	{0,0,1,0,1  ,0,0,0,0,0  ,0,0,1,0,0  ,0,1,0,0,0  ,1,0},	//  5
	{0,0,1,0,1  ,0,1,1,1,1  ,1,1,1,0,1  ,1,1,0,1,1  ,1,0},	//  6
	{0,0,1,0,1  ,0,0,0,0,0  ,0,0,0,0,1  ,0,0,0,1,0  ,0,0},	//  7
	{0,0,1,0,1  ,0,1,1,1,1  ,1,1,1,0,1  ,0,1,0,1,1  ,1,0},	//  8
	{0,0,1,0,1  ,0,1,0,0,0  ,0,0,1,0,1  ,0,1,0,0,0  ,1,0},	//  9
	{0,0,1,0,1  ,1,1,0,1,1  ,1,1,1,0,1  ,0,1,1,1,1  ,1,0},	//  10 
	{0,0,1,0,0  ,0,0,0,1,0  ,0,0,1,0,1  ,0,0,0,0,0  ,0,0},	//  11
	{0,0,1,0,1  ,1,1,0,1,0  ,1,1,1,0,1  ,0,1,1,1,1  ,1,0},	//  12
	{0,0,1,0,1  ,0,1,0,1,0  ,0,0,0,0,1  ,0,0,0,0,0  ,1,0},	//  13
	{0,0,1,1,1  ,0,1,0,1,1  ,1,1,1,1,1  ,1,1,1,1,1  ,1,0},	//  14
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,1  ,0,0},	//  15
};

static const INT8U maze2[MAZE_HEIGHT][MAZE_WIDTH] = {
	{0,0,1,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},  //  1
	{0,0,1,0,1  ,1,1,1,1,1  ,1,1,1,0,1  ,0,1,1,1,1  ,1,0},	//  2
	{0,0,1,0,1  ,0,0,0,1,0  ,0,0,1,0,1  ,0,1,0,0,0  ,1,0},	//  3
	{0,0,1,1,1  ,1,1,0,1,1  ,1,0,1,1,1  ,0,1,1,1,0  ,1,0},	//  4
	{0,0,0,0,0  ,0,0,0,0,0  ,1,0,1,0,0  ,0,0,0,1,0  ,1,0},	//  5
	{0,0,1,1,1  ,1,1,1,1,0  ,1,0,1,0,1  ,1,1,0,1,0  ,1,0},	//  6
	{0,0,1,0,0  ,0,0,0,1,0  ,1,0,0,0,1  ,0,1,0,1,0  ,1,0},	//  7
	{0,0,1,1,1  ,1,1,0,1,0  ,1,1,1,1,1  ,0,1,1,1,0  ,1,0},	//  8
	{0,0,0,0,0  ,0,1,0,1,0  ,0,0,0,0,0  ,0,0,0,0,0  ,1,0},	//  9
	{0,0,1,0,1  ,1,1,0,1,1  ,1,1,1,1,1  ,1,1,1,1,0  ,1,0},	//  10 
	{0,0,1,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,1,0  ,1,0},	//  11
	{0,0,1,0,1  ,1,1,1,1,0  ,1,1,1,1,1  ,1,1,0,1,1  ,1,0},	//  12
	{0,0,1,0,1  ,0,0,0,1,0  ,1,0,1,0,0  ,0,0,0,0,0  ,1,0},	//  13
	{0,0,1,1,1  ,1,1,0,1,1  ,1,0,1,1,1  ,1,1,1,1,1  ,1,0},	//  14
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,1  ,0,0},	//  15
};

static const INT8U maze3[MAZE_HEIGHT][MAZE_WIDTH] = {
	{0,0,1,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},  //  1
	{0,0,1,1,1  ,1,1,0,1,1  ,1,0,1,1,1  ,1,1,1,1,1  ,1,0},	//  2
	{0,0,0,0,1  ,0,1,0,1,0  ,1,0,1,0,0  ,0,0,0,0,0  ,1,0},	//  3
	{0,0,1,1,1  ,0,1,0,1,0  ,1,1,1,1,1  ,0,1,1,1,1  ,1,0},	//  4
	{0,0,1,0,0  ,0,1,0,0,0  ,0,0,0,0,1  ,0,1,0,0,0  ,1,0},	//  5
	{0,0,1,0,1  ,0,1,1,1,1  ,1,0,1,1,1  ,0,1,0,1,1  ,1,0},	//  6
	{0,0,1,0,1  ,0,0,0,0,0  ,1,0,0,0,0  ,0,1,0,0,0  ,1,0},	//  7
	{0,0,1,0,1  ,1,1,1,1,0  ,1,0,1,1,1  ,0,1,1,1,0  ,1,0},	//  8
	{0,0,1,0,1  ,0,0,0,0,0  ,1,0,1,0,1  ,0,0,0,1,0  ,1,0},	//  9
	{0,0,1,0,1  ,1,1,1,1,1  ,1,0,1,0,1  ,1,1,0,1,0  ,1,0},	//  10 
	{0,0,1,0,0  ,0,0,0,1,0  ,0,0,1,0,0  ,0,1,0,1,0  ,1,0},	//  11
	{0,0,1,1,1  ,0,1,0,1,0  ,1,1,1,0,1  ,0,1,1,1,0  ,1,0},	//  12
	{0,0,1,0,1  ,0,1,0,1,0  ,1,0,0,0,1  ,0,0,0,0,0  ,1,0},	//  13
	{0,0,1,0,1  ,1,1,0,1,1  ,1,0,1,1,1  ,1,1,1,1,1  ,1,0},	//  14
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,1  ,0,0},	//  15
};

static const INT8U maze4[MAZE_HEIGHT][MAZE_WIDTH] = {
	{0,0,1,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},  //  1
	{0,0,1,0,1  ,1,1,0,1,1  ,1,1,1,1,1  ,0,1,1,1,1  ,1,0},	//  2
	{0,0,1,0,1  ,0,1,0,1,0  ,0,0,1,0,0  ,0,1,0,1,0  ,0,0},	//  3
	{0,0,1,0,1  ,0,1,0,1,1  ,1,0,1,1,1  ,1,1,0,1,1  ,1,0},	//  4
	{0,0,1,0,1  ,0,1,0,0,0  ,1,0,0,0,0  ,0,0,0,0,0  ,1,0},	//  5
	{0,0,1,0,1  ,0,1,1,1,0  ,1,1,1,1,1  ,0,1,0,1,1  ,1,0},	//  6
	{0,0,1,0,1  ,0,1,0,1,0  ,0,0,0,0,1  ,0,1,0,1,0  ,0,0},	//  7
	{0,0,1,0,1  ,0,1,0,1,1  ,1,1,1,0,1  ,1,1,0,1,1  ,1,0},	//  8
	{0,0,1,0,1  ,0,1,0,0,0  ,1,0,0,0,1  ,0,0,0,0,0  ,1,0},	//  9
	{0,0,1,1,1  ,0,1,0,1,1  ,1,0,1,1,1  ,0,1,1,1,1  ,1,0},	//  10 
	{0,0,1,0,0  ,0,1,0,0,0  ,0,0,1,0,0  ,0,1,0,0,0  ,1,0},	//  11
	{0,0,1,0,1  ,1,1,0,1,1  ,1,1,1,0,1  ,0,1,0,1,1  ,1,0},	//  12
	{0,0,1,0,1  ,0,0,0,1,0  ,0,0,0,0,1  ,0,1,0,0,0  ,0,0},	//  13
	{0,0,1,0,1  ,1,1,1,1,1  ,1,1,1,1,1  ,0,1,1,1,1  ,1,0},	//  14
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,1  ,0,0},	//  15
};

static const INT8U maze5[MAZE_HEIGHT][MAZE_WIDTH] = {
	{0,0,1,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},  //  1
	{0,0,1,1,1  ,1,1,1,1,1  ,1,1,1,1,1  ,1,1,0,1,1  ,1,0},	//  2
	{0,0,1,0,0  ,0,1,0,0,0  ,0,0,0,0,0  ,0,0,0,1,0  ,1,0},	//  3
	{0,0,1,0,1  ,0,1,0,1,1  ,1,0,1,1,1  ,1,1,1,1,0  ,1,0},	//  4
	{0,0,1,0,1  ,0,1,0,1,0  ,1,0,1,0,0  ,0,0,0,1,0  ,1,0},	//  5
	{0,0,1,0,1  ,1,1,0,1,0  ,1,1,1,0,1  ,1,1,0,1,0  ,1,0},	//  6
	{0,0,1,0,0  ,0,0,0,1,0  ,0,0,0,0,1  ,0,1,0,1,0  ,1,0},	//  7
	{0,0,1,0,1  ,1,1,0,1,0  ,1,0,1,1,1  ,0,1,0,1,0  ,1,0},	//  8
	{0,0,1,0,1  ,0,1,0,1,0  ,1,0,1,0,0  ,0,1,0,0,0  ,1,0},	//  9
	{0,0,1,1,1  ,0,1,1,1,1  ,1,0,1,1,1  ,0,1,1,1,1  ,1,0},	//  10 
	{0,0,1,0,0  ,0,0,0,0,0  ,0,0,0,0,1  ,0,0,0,0,0  ,1,0},	//  11
	{0,0,1,0,1  ,1,1,0,1,1  ,1,1,1,1,1  ,0,1,1,1,1  ,1,0},	//  12
	{0,0,1,0,0  ,0,1,0,1,0  ,0,0,0,0,0  ,0,1,0,0,0  ,0,0},	//  13
	{0,0,1,1,1  ,1,1,0,1,1  ,1,0,1,1,1  ,1,1,1,1,1  ,1,0},	//  14
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,1  ,0,0},	//  15
};

static const INT8U maze6[MAZE_HEIGHT][MAZE_WIDTH] = {
	{0,0,1,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},  //  1
	{0,0,1,1,1  ,1,1,0,1,1  ,1,1,1,1,1  ,1,1,1,1,1  ,1,0},	//  2
	{0,0,1,0,1  ,0,0,0,1,0  ,0,0,0,0,0  ,0,0,0,0,0  ,1,0},	//  3
	{0,0,1,0,1  ,1,1,0,1,1  ,1,0,1,0,1  ,1,1,1,1,1  ,1,0},	//  4
	{0,0,0,0,0  ,0,1,0,0,0  ,1,0,1,0,1  ,0,1,0,1,0  ,0,0},	//  5
	{0,0,1,1,1  ,0,1,1,1,0  ,1,0,1,1,1  ,0,1,0,1,0  ,1,0},	//  6
	{0,0,1,0,1  ,0,1,0,1,0  ,1,0,0,0,0  ,0,0,0,0,0  ,1,0},	//  7
	{0,0,1,0,1  ,0,1,0,1,0  ,1,0,1,1,1  ,1,1,1,1,1  ,1,0},	//  8
	{0,0,1,0,1  ,0,1,0,1,0  ,1,0,1,0,0  ,0,0,0,1,0  ,1,0},	//  9
	{0,0,1,0,1  ,1,1,0,1,0  ,1,0,1,0,1  ,1,1,1,1,0  ,1,0},	//  10 
	{0,0,1,0,0  ,0,0,0,1,0  ,1,0,1,0,1  ,0,0,0,0,0  ,1,0},	//  11
	{0,0,1,0,1  ,1,1,1,1,0  ,1,0,1,0,1  ,0,1,1,1,0  ,1,0},	//  12
	{0,0,1,0,0  ,0,0,0,0,0  ,1,0,0,0,1  ,0,1,0,1,0  ,1,0},	//  13
	{0,0,1,1,1  ,1,1,1,1,1  ,1,1,1,1,1  ,0,1,0,1,1  ,1,0},	//  14
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,1  ,0,0},	//  15
};

static const INT8U maze7[MAZE_HEIGHT][MAZE_WIDTH] = {
	{0,0,1,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},  //  1
	{0,0,1,1,1  ,0,1,1,1,1  ,1,1,1,1,1  ,1,1,1,1,1  ,1,0},	//  2
	{0,0,1,0,1  ,0,1,0,0,0  ,1,0,1,0,0  ,0,0,0,0,0  ,1,0},	//  3
	{0,0,1,0,1  ,0,1,1,1,0  ,1,0,1,0,1  ,1,1,0,1,0  ,1,0},	//  4
	{0,0,1,0,1  ,0,0,0,0,0  ,1,0,0,0,1  ,0,1,0,1,0  ,1,0},	//  5
	{0,0,1,0,1  ,1,1,1,1,0  ,1,0,1,1,1  ,0,1,0,1,1  ,1,0},	//  6
	{0,0,1,0,0  ,0,0,0,1,0  ,1,0,1,0,0  ,0,1,0,0,0  ,0,0},	//  7
	{0,0,1,0,1  ,1,1,0,1,0  ,1,1,1,1,1  ,0,1,1,1,1  ,1,0},	//  8
	{0,0,1,0,1  ,0,1,0,1,0  ,0,0,0,0,0  ,0,0,0,0,0  ,1,0},	//  9
	{0,0,1,0,1  ,0,1,1,1,1  ,1,1,1,1,1  ,1,1,0,1,1  ,1,0},	//  10 
	{0,0,1,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,1,0,1,0  ,1,0},	//  11
	{0,0,1,0,1  ,1,1,1,1,0  ,1,1,1,1,1  ,0,1,1,1,0  ,1,0},	//  12
	{0,0,1,0,1  ,0,0,0,1,0  ,1,0,0,0,1  ,0,0,0,0,0  ,1,0},	//  13
	{0,0,1,1,1  ,1,1,0,1,0  ,1,1,1,0,1  ,1,1,1,1,1  ,1,0},	//  14
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,1  ,0,0},	//  15
};

static const INT8U maze8[MAZE_HEIGHT][MAZE_WIDTH] = {
	{0,0,1,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},  //  1
	{0,0,1,1,1  ,0,1,1,1,1  ,1,1,1,1,1  ,1,1,1,1,1  ,1,0},	//  2
	{0,0,1,0,1  ,0,1,0,0,0  ,1,0,0,0,0  ,0,0,0,0,0  ,1,0},	//  3
	{0,0,1,0,1  ,0,1,0,1,0  ,1,1,1,0,1  ,1,1,1,1,0  ,1,0},	//  4
	{0,0,1,0,1  ,0,1,0,1,0  ,0,0,1,0,0  ,0,0,0,1,0  ,1,0},	//  5
	{0,0,1,0,1  ,1,1,0,1,1  ,1,0,1,0,1  ,1,1,0,1,1  ,1,0},	//  6
	{0,0,1,0,0  ,0,0,0,0,0  ,1,0,1,0,1  ,0,1,0,0,0  ,1,0},	//  7
	{0,0,1,0,1  ,1,1,1,1,1  ,1,0,1,1,1  ,0,1,0,1,0  ,1,0},	//  8
	{0,0,1,0,1  ,0,1,0,0,0  ,0,0,0,0,0  ,0,1,0,1,0  ,0,0},	//  9
	{0,0,1,0,1  ,0,1,0,1,1  ,1,0,1,1,1  ,1,1,0,1,1  ,1,0},	//  10 
	{0,0,1,0,1  ,0,1,0,1,0  ,1,0,1,0,0  ,0,0,0,0,0  ,1,0},	//  11
	{0,0,1,0,1  ,0,1,0,1,0  ,1,1,1,0,1  ,1,1,0,1,1  ,1,0},	//  12
	{0,0,1,0,0  ,0,1,0,1,0  ,0,0,0,0,1  ,0,1,0,1,0  ,0,0},	//  13
	{0,0,1,1,1  ,1,1,0,1,1  ,1,1,1,1,1  ,0,1,1,1,1  ,1,0},	//  14
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,1  ,0,0},	//  15
};

static const INT8U maze9[MAZE_HEIGHT][MAZE_WIDTH] = {
	{0,0,1,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},  //  1
	{0,0,1,1,1  ,0,1,1,1,1  ,1,0,1,1,1  ,0,1,1,1,1  ,1,0},	//  2
	{0,0,1,0,0  ,0,1,0,0,0  ,1,0,1,0,1  ,0,1,0,0,0  ,1,0},	//  3
	{0,0,1,1,1  ,0,1,0,1,0  ,1,0,1,0,1  ,1,1,0,1,1  ,1,0},	//  4
	{0,0,1,0,1  ,0,1,0,1,0  ,1,0,1,0,0  ,0,0,0,1,0  ,1,0},	//  5
	{0,0,1,0,1  ,0,1,1,1,0  ,1,1,1,0,1  ,1,1,1,1,0  ,1,0},	//  6
	{0,0,1,0,1  ,0,0,0,1,0  ,0,0,0,0,1  ,0,0,0,0,0  ,1,0},	//  7
	{0,0,1,0,1  ,1,1,1,1,0  ,1,1,1,0,1  ,0,1,1,1,1  ,1,0},	//  8
	{0,0,1,0,0  ,0,0,0,0,0  ,1,0,1,0,1  ,0,1,0,0,0  ,0,0},	//  9
	{0,0,1,1,1  ,0,1,1,1,1  ,1,0,1,1,1  ,0,1,0,1,1  ,1,0},	//  10 
	{0,0,0,0,0  ,0,1,0,0,0  ,0,0,0,0,0  ,0,1,0,1,0  ,1,0},	//  11
	{0,0,1,1,1  ,1,1,0,1,1  ,1,0,1,1,1  ,0,1,1,1,0  ,1,0},	//  12
	{0,0,0,0,0  ,0,0,0,1,0  ,0,0,1,0,1  ,0,0,0,0,0  ,1,0},	//  13
	{0,0,1,1,1  ,1,1,1,1,1  ,1,1,1,0,1  ,1,1,1,1,1  ,1,0},	//  14
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,1  ,0,0},	//  15
};

/*
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},  //  1
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},	//  2
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},	//  3
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},	//  4
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},	//  5
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},	//  6
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},	//  7
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},	//  8
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},	//  9
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},	//  10 
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},	//  11
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},	//  12
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},	//  13
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},	//  14
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},	//  15
*/

INT8U maze_wall[]={  /*/ 16*16     */
 	0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,
 	0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,
 	0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,
 	0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,
 	0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,
 	0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,
 	0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,
 	0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,
 	0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,
 	0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,
 	0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,
 	0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,
 	0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,
 	0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,
 	0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,
 	0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,

};
INT8U maze_pass[]={  /*/ 16*16    ͨ*/
 	0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,
 	0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,
 	0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,
 	0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,
 	0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,
 	0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,
 	0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,
 	0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,
 	0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,
 	0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,
 	0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,
 	0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,
 	0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,
 	0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,
 	0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,
 	0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD
};
INT8U maze_people[]={  /*/ 16*16    */
 	0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC,
 	0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC,
 	0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC,
 	0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC,
 	0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC,
 	0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC,
 	0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC,
 	0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC,
 	0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC,
 	0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC,
 	0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC,
 	0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC,
 	0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC,
 	0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC,
 	0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC,
 	0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC,
};

typedef struct maze_info{
    u8  *game;
    u32 game_size;
    u32 game_level;
}MAZE_INFO;


// static MAZE_INFO hx_maze = {
//     .game = &maze1[MAZE_HEIGHT][MAZE_WIDTH],
//     .game_size = sizeof(maze1),
//     .game_level = 1,
// };
static INT8U hx_maze[MAZE_HEIGHT][MAZE_WIDTH]= {
	{0,0,1,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0},  //  1
	{0,0,1,1,1  ,1,1,0,1,1  ,1,1,1,1,1  ,0,1,1,1,1  ,1,0},	//  2
	{0,0,1,0,0  ,0,1,0,0,0  ,0,0,1,0,1  ,0,1,0,1,0  ,0,0},	//  3
	{0,0,1,1,1  ,0,1,1,1,1  ,1,1,1,0,1  ,0,1,0,1,1  ,1,0},	//  4
	{0,0,1,0,1  ,0,0,0,0,0  ,0,0,1,0,0  ,0,1,0,0,0  ,1,0},	//  5
	{0,0,1,0,1  ,0,1,1,1,1  ,1,1,1,0,1  ,1,1,0,1,1  ,1,0},	//  6
	{0,0,1,0,1  ,0,0,0,0,0  ,0,0,0,0,1  ,0,0,0,1,0  ,0,0},	//  7
	{0,0,1,0,1  ,0,1,1,1,1  ,1,1,1,0,1  ,0,1,0,1,1  ,1,0},	//  8
	{0,0,1,0,1  ,0,1,0,0,0  ,0,0,1,0,1  ,0,1,0,0,0  ,1,0},	//  9
	{0,0,1,0,1  ,1,1,0,1,1  ,1,1,1,0,1  ,0,1,1,1,1  ,1,0},	//  10 
	{0,0,1,0,0  ,0,0,0,1,0  ,0,0,1,0,1  ,0,0,0,0,0  ,0,0},	//  11
	{0,0,1,0,1  ,1,1,0,1,0  ,1,1,1,0,1  ,0,1,1,1,1  ,1,0},	//  12
	{0,0,1,0,1  ,0,1,0,1,0  ,0,0,0,0,1  ,0,0,0,0,0  ,1,0},	//  13
	{0,0,1,1,1  ,0,1,0,1,1  ,1,1,1,1,1  ,1,1,1,1,1  ,1,0},	//  14
	{0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,0  ,0,0,0,0,1  ,0,0},	//  15
};

extern u8 *game_frame_buff;


//- game : 0, game pass; game :1, game over-//
void ap_setting_game_pass_gate_show(winHandle handle,INT8U game)
{
	deg_Printf("--ap_setting_game_pass_gate_show()\r\n");
	
//	uiWinSetVisible(winItem(handle,MAZE_TIPS_RECT_ID1),1);
//	uiWinSetVisible(winItem(handle,MAZE_TIPS_RECT_ID2),1);
//	uiWinSetVisible(winItem(handle,MAZE_TIPS_STR_TIP),1);
	OSDFrameDrawIcon(game_frame_buff,gamemaze_buff[GAME_PASS_TIPS].ptr,50,80,220,80);
	if(game_over_in)
		g_draw_resstr_default_style_in_screen_center(game_frame_buff,R_ID_STR_GAME_OVER,RES_FONT_NUM0);
	else
		g_draw_resstr_default_style_in_screen_center(game_frame_buff,R_ID_STR_GAME_PASS,RES_FONT_NUM0);
	memcpy(ui_draw_ctrl.bufStart,game_frame_buff,game_frame_w*game_frame_h);
	uiWinDrawUpdate();
//	if(game)
//		uiWinSetResid(winItem(handle,MAZE_TIPS_STR_TIP),R_ID_STR_COM_FAILED);
//	else
//		uiWinSetResid(winItem(handle,MAZE_TIPS_STR_TIP),R_ID_STR_COM_SUCCESS);
#if 0
	str.pos_x = (game_frame_w - str_res.string_width) >> 1;
	str.pos_y = (game_frame_h - str_res.string_height) >> 1;
	str.font_color = 0xFFFF;

	cursor_icon.transparent = TRANSPARENT_COLOR;
	cursor_icon.icon_w = 160;
	cursor_icon.icon_h = 64;
	cursor_icon.pos_x = (game_frame_w-160)>>1;
	cursor_icon.pos_y = (game_frame_h-64)>>1;

	ap_game_show_GPZP_file((INT8U*)"GAME_D_BOX.GPZP",(INT16U *)game_bg_buff,&cursor_icon,GAME_ICON_NORMAL_DRAW);
//	ap_setting_icon_draw((INT16U *)game_bg_buff, draw_tips_icon, &cursor_icon, SETTING_ICON_NORMAL_DRAW);

	ap_state_resource_string_draw((INT16U *)game_bg_buff, &str);

//	ap_game_flash_timer_stop();
	ap_game_draw();
#endif	
}

void mazeGameDraw(INT8U tag)
{
	//IMAGE_DECODE_STRUCT img_info;
	//DISPLAY_ICONSHOW cursor_icon;
	//DISPLAY_ICONSHOW icon = {320, 240, R_ID_PALETTE_Transparent, 0, 0};
	//INT32U background_image_ptr;
	INT32U size;
	INT16U i,j;

	INT16U x,y,w,h;
	memcpy(maze,hx_maze,sizeof(hx_maze));
	hal_lcdSetBufYUV(hal_lcdVideoShowFrameGet(),0x80,0x80,0x80);
	memset(game_frame_buff,R_ID_PALETTE_Black,game_frame_w*game_frame_h);

	//OSDFrameDrawIcon(game_frame_buff,0,0,320,240);
	//ap_game_show_GPZP_file((INT8U*)"MAZE.GPZP",(INT16U *)game_bg_buff,&icon,GAME_ICON_NORMAL_DRAW);

	w = 14;
	h  = 14;

	for(i=0;i<17;i++){
	for(j=0;j<22;j++){
		x = j*14+0;
		y = i*14+0;
		OSDFrameDrawIcon(game_frame_buff,maze_wall,x,y,w,h);//maze_pass
	}
	}

	w = 14;
	h  = 14;
	for(i=0;i<15;i++){
		for(j=0;j<22;j++){
			x = j*14;
			y = i*14+14;
			if(maze[i][j] == 1){
				OSDFrameDrawIcon(game_frame_buff,maze_pass,x,y,w,h);
				//OSDFrameDrawIcon(game_frame_buff,maze_wall, x,y, w,h);
				//OSDFrameDrawIcon1(game_frame_buff,maze_pass, x,y, w,h);
			}
		}
	}
	maze_high= 0;
	maze_width = 2;
	maze_high_peple = maze_high;
	maze_width_peple = maze_width;

	w = 14;
	h = 14;
	x = maze_width_peple*14;
	y = maze_high_peple*14+14;
	//ap_setting_icon_draw((INT16U *)game_bg_buff, maze_people, &cursor_icon, GAME_ICON_NORMAL_DRAW);
	OSDFrameDrawIcon(game_frame_buff,maze_people,x,y,w,h);
	for(j=0;j<22;j++){
		x = j*14;
		y = 0;
		OSDFrameDrawIcon(game_frame_buff,maze_pass,x,0,14,14);//maze_pass
	}
	for(j=0;j<22;j++){
		x = j*14;
		y = 226;
		OSDFrameDrawIcon(game_frame_buff,maze_pass,x,y,14,14);//maze_pass
	}		
//	for(j=0;j<22;j++){
//		x = j*14;
//		y = 166;
//		OSDFrameDrawIcon(game_frame_buff,maze_pass,x,166,14,14);//maze_pass
//	}	
	memcpy(ui_draw_ctrl.bufStart,game_frame_buff,game_frame_w*game_frame_h);	
	uiWinDrawUpdate();
}

void mazeMoveActive(winHandle handle,INT32U dir)
{
	//STRING_ASCII_INFO ascii_str;
	//DISPLAY_ICONSHOW cursor_icon;
	INT8U i,j;
	INT16U x,y,w,h;
	static u8 fist_in = 1;
	deg_Printf("dir = %d \n",dir);
	if(maze_level==0)
	 return 0;
	if(fist_in){
		fist_in = 0;
		mazeGameDraw(0);
	}
    memcpy(maze,hx_maze,sizeof(hx_maze));

	w = 14;
	h = 14;
	x = maze_width_peple*14;
	y = maze_high_peple*14+14;
	//ap_setting_icon_draw((INT16U *)game_bg_buff, maze_pass, &cursor_icon, GAME_ICON_NORMAL_DRAW);
	OSDFrameDrawIcon(game_frame_buff,maze_pass,x,y,w,h);
	
	if(dir == KEY_EVENT_DOWN){
		maze_high += 1;
	}else if(dir == KEY_EVENT_UP){
		if(maze_high != 0)
			maze_high -= 1;
	}else if(dir == KEY_EVENT_LEFT){
		if(maze_width != 0)
			maze_width -= 1;
	}else if(dir == KEY_EVENT_RIGHT){
		maze_width += 1;
	}
//	deg_Printf("--maze[ %d][%d]= %d\r\n",maze_high,maze_width,maze[maze_high][maze_width]);
	if(maze[maze_high][maze_width] ==1) 
	{
		maze_width_peple = maze_width;
		maze_high_peple = maze_high;
	}else{
		maze_width = maze_width_peple;
		maze_high = maze_high_peple;
	}

	w = 14;
	h =  14;
	x = maze_width_peple*14;
	y = maze_high_peple*14+14;
	//ap_setting_icon_draw(game_bg_buff, maze_people, &cursor_icon, GAME_ICON_NORMAL_DRAW);
	OSDFrameDrawIcon(game_frame_buff, maze_people,x,y,w,h);

	if((maze_high_peple == (MAZE_HEIGHT-1)) && (maze_width_peple ==(MAZE_WIDTH-3))){//MAZE_WIDTH-3
		deg_Printf("--maze level[%d] pass\r\n",maze_level);
		maze_level += 1;
		switch(maze_level){
			case 1:
				memcpy(hx_maze,maze1,sizeof(maze1));
			break;
			case 2:
				memcpy(hx_maze,maze2,sizeof(maze2));
			break;
			case 3:
				memcpy(hx_maze,maze3,sizeof(maze3));
			break;
			case 4:
				memcpy(hx_maze,maze4,sizeof(maze4));
			break;
			case 5:
				memcpy(hx_maze,maze5,sizeof(maze5));
			break;
			case 6:
				memcpy(hx_maze,maze6,sizeof(maze6));
			break;
			case 7:
				memcpy(hx_maze,maze7,sizeof(maze7));
			break;
			case 8:
				memcpy(hx_maze,maze8,sizeof(maze8));
			break;
			case 9:
				memcpy(hx_maze,maze9,sizeof(maze9));
				//maze_level = 1;
				//game_over_in = 1;
				//ap_setting_game_pass_gate_show(handle,0);
			break;
			case 10:
					maze_level = 1;
					game_over_in = 1;
			break;
				

		}

 		ap_setting_game_pass_gate_show(handle,0);
		XOSTimeDly(10);
//		OSDFrameDrawIcon(game_frame_buff,gamemaze_buff[GAME_PASS_TIPS].ptr,50,80,220,80);
//		g_draw_resstr_default_style_in_screen_center(game_frame_buff,R_ID_STR_GAME_PASS,RES_FONT_NUM0);
//		memcpy(ui_draw_ctrl.bufStart,game_frame_buff,game_frame_w*game_frame_h);	
//		uiWinDrawUpdate();

		XOSTimeDly(1000);
		if(game_over_in == 1)
		{
			game_over_in = 0;
			uiWinDestroy(&handle);
			return;

		}
		maze_level_flag = 1;
		if(maze_level){
			mazeGameDraw(0);
		}
	}
	if(maze_level){
		memcpy(ui_draw_ctrl.bufStart,game_frame_buff,game_frame_w*game_frame_h);
		uiWinDrawUpdate();
	}	
	
}


/*******************************************************************************
* Function Name  : MazeKeyMsgOk
* Description    : MazeKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int MazeKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//deg_Printf("MazeKeyMsgOk\n");
		task_com_keysound_play();
		maze_level = 1;
		mazeGameDraw(0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : MazeKeyMsgUp
* Description    : MazeKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int MazeKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
		//deg_Printf("MazeKeyMsgUp\n");
		mazeMoveActive(handle,KEY_EVENT_UP);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : MazeKeyMsgDown
* Description    : MazeKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int MazeKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
		//deg_Printf("MazeKeyMsgDown\n");
		mazeMoveActive(handle,KEY_EVENT_DOWN);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : MazeKeyMsgLeft
* Description    : MazeKeyMsgLeft
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int MazeKeyMsgLeft(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
		//deg_Printf("MazeKeyMsgLeft\n");
		mazeMoveActive(handle,KEY_EVENT_LEFT);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : MazeKeyMsgRight
* Description    : MazeKeyMsgRight
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int MazeKeyMsgRight(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
		//deg_Printf("MazeKeyMsgRight\n");
		mazeMoveActive(handle,KEY_EVENT_RIGHT);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : MazeKeyMsgMain
* Description    : MazeKeyMsgMain
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int MazeKeyMsgMain(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//deg_Printf("MazeKeyMsgMain\n");
		uiWinDestroy(&handle);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : MazeSysMsg500MS
* Description    : MazeSysMsg500MS
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int MazeSysMsg500MS(winHandle handle,u32 parameNum,u32* parame)
{
	return 0;
}
/*******************************************************************************
* Function Name  : MazeOpenWin
* Description    : MazeOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int MazeOpenWin(winHandle handle,u32 parameNum,u32* parame)
{

	INT8U* figure_pass_tips;
	u16 w,h;
	deg_Printf("MazeOpenWin\n");
	maze_temp_icons = hal_sysMemMalloc(220*120);
	if(NULL == maze_temp_icons){
		deg_Printf("mem malloc foro icon  fail--->\n");
		return -1;
	}
	//uiWinSetVisible(winItem(handle,MAZE_TIPS_RECT_ID1),0);
	//uiWinSetVisible(winItem(handle,MAZE_TIPS_RECT_ID2),0);
	//uiWinSetVisible(winItem(handle,MAZE_TIPS_STR_TIP),0);
	figure_pass_tips	= maze_temp_icons+220*120*0;
    u32 addr,len;
    addr = res_icon_GetAddrAndSize(R_ID_ICON_GAME_TIPS_ICON,&w,&h);
	len=w*h;
    nv_read(addr,figure_pass_tips,w*h);
	gamemaze_buff[0].ptr 	= NULL;
  	gamemaze_buff[GAME_PASS_TIPS].ptr 	= figure_pass_tips	;

	app_draw_Service(1);
	
	SysCtrl.gui_flush_sta = 0;
	//memset(game_frame_buff,1,320*120); //osd test
	mazeGameDraw(1);
	memcpy(ui_draw_ctrl.bufStart,game_frame_buff,game_frame_w*game_frame_h);
	uiWinDrawUpdate();
	return 0;
}
/*******************************************************************************
* Function Name  : MazeCloseWin
* Description    : MazeCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int MazeCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("MazeCloseWin\n");
	hal_sysMemFree(maze_temp_icons);
	maze_temp_icons = NULL;	
	SysCtrl.gui_flush_sta = 1;
	app_draw_Service(1);
	return 0;
}
/*******************************************************************************
* Function Name  : MazeWinChildClose
* Description    : MazeWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int MazeWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	//deg_Printf("MazeWinChildClose\n");
	return 0;
}

ALIGNED(4) msgDealInfor mazeMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		MazeOpenWin},
	{SYS_CLOSE_WINDOW,		MazeCloseWin},
	{SYS_CHILE_COLSE,		MazeWinChildClose},
	{KEY_EVENT_OK,	        MazeKeyMsgOk},
	{KEY_EVENT_UP,	        MazeKeyMsgUp},
	{KEY_EVENT_DOWN,	    MazeKeyMsgDown},
	{KEY_EVENT_LEFT,	    MazeKeyMsgLeft},
	{KEY_EVENT_RIGHT,	    MazeKeyMsgRight},
	{KEY_EVENT_POWER,	    MazeKeyMsgMain},
	{SYS_EVENT_500MS,	    MazeSysMsg500MS},
	{EVENT_MAX,NULL},
};

WINDOW(mazeWindow,mazeMsgDeal,mazeWin)


