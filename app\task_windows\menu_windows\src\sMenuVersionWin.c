/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	VERSION_RECT_ID=0,
	VERSION_TIPS_ID,
	VERSION_LOG1_ID,
	VERSION_LOG2_ID,
};

UNUSED ALIGNED(4) const widgetCreateInfor versionWin[] =
{
	createFrameWin(							Rx(58),	Ry(58), Rw(204),Rh(124),SMENU_FRAME_COLOR,WIN_ABS_POS),
	createRect(VERSION_RECT_ID,         	Rx(2),	<PERSON>y(2),  <PERSON>w(200),Rh(120),SMENU_UNSELECT_BG_COLOR),
	createStringIcon(VERSION_TIPS_ID,		Rx(2),	Ry(12),  Rw(200),Rh(80),VERSION_TIPS,ALIGNMENT_CENTER, SMENU_UNSELECT_FN_COLOR,DEFAULT_FONT),
	createStringIcon(VERSION_LOG1_ID,		Rx(2),	Ry(52), Rw(200),Rh(40),	SysCtrl.version_str,	ALIGNMENT_CENTER, SMENU_UNSELECT_FN_COLOR,DEFAULT_FONT),
	//createStringIcon(VERSION_LOG2_ID,		Rx(2),	Ry(82), Rw(200),Rh(40),	"20220401",	ALIGNMENT_CENTER, SMENU_UNSELECT_FN_COLOR,DEFAULT_FONT),
	widgetEnd(),
};
