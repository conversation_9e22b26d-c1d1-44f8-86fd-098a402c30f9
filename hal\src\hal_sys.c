/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../inc/hal.h"


USB20FIFO_SECTION __USB20_FIFO__ usb20_fifo; 
USB11FIFO_SECTION __USB11_FIFO__ usb11_fifo;  



#define  MEM_NODE_MAX      64
typedef struct MEM_NODE_S
{
	u32 flag;
	u32 addr;
	u32 size;
	struct MEM_NODE_S *prev;
	struct MEM_NODE_S *next;
}MEM_NODE_T;
ALIGNED(4) static MEM_NODE_T memNode[MEM_NODE_MAX];
ALIGNED(4) static MEM_NODE_T *idleMem;
ALIGNED(4) static MEM_NODE_T *busyMem;
ALIGNED(4) static MEM_NODE_T *freeMem;

/*******************************************************************************
* Function Name  : hal_sysMemPrint
* Description    : printf system memory info
* Input          : 
* Output         : None
* Return         :
*******************************************************************************/
static void hal_sysMemNodeRemove(MEM_NODE_T * node)
{
	if(node == NULL)
		return;
	if(node->prev)
	{
		node->prev->next = node->next;
	}
	if(node->next)
		node->next->prev = node->prev;
	//node->next = NULL;
	//node->prev = NULL;
}
/*******************************************************************************
* Function Name  : hal_sysMemPrint
* Description    : printf system memory info
* Input          : 
* Output         : None
* Return         :
*******************************************************************************/
static void hal_sysMemNodeAddNext(MEM_NODE_T * node, MEM_NODE_T * add_node)
{
	if(node == NULL || add_node == NULL)
		return;
	add_node->next = NULL;
	if(node->next)
	{
		node->next->prev = add_node;
		add_node->next   = node->next;
	}
	node->next 		= add_node;
	add_node->prev 	= node;
}
/*******************************************************************************
* Function Name  : hal_sysMemPrint
* Description    : printf system memory info
* Input          : 
* Output         : None
* Return         :
*******************************************************************************/
static void hal_sysMemNodeAddPrev(MEM_NODE_T * node, MEM_NODE_T * add_node)
{
	if(node == NULL || add_node == NULL)
		return;
	add_node->prev = NULL;
	if(node->prev)
	{
		node->prev->next = add_node;
		add_node->prev	 = node->prev;
	}
	node->prev 		= add_node;
	add_node->next 	= node;
}
/*******************************************************************************
* Function Name  : hal_sysMemIdleNodeIn
* Description    : hal_sysMemIdleNodeIn
* Input          : 
* Output         : None
* Return         :
*******************************************************************************/
static void hal_sysMemIdleNodeIn(MEM_NODE_T * node_idle)
{
	if(node_idle == NULL)
		return;
	//idleMem: idleMem始终指向idle链表的起始节点
	MEM_NODE_T *node = idleMem;
	MEM_NODE_T *node_prev = NULL;
	//idle链表按size做升序排列，寻找链表中是否有比node_idle 的size大的node
	while(node) 
	{
		if(node->size > node_idle->size)
			break;
		node_prev = node;
		node = node->next;
	}
	node_idle->prev = NULL;
	node_idle->next = NULL;
	if(idleMem == NULL)
	{
		idleMem = node_idle;
	}
	else{
		if(node)
		{
			hal_sysMemNodeAddPrev(node, node_idle);
			if(node == idleMem)
				idleMem = node_idle;
		}
		else
			hal_sysMemNodeAddNext(node_prev,node_idle);
	}
}
/*******************************************************************************
* Function Name  : hal_sysMemIdleNodeOut
* Description    : hal_sysMemIdleNodeOut
* Input          : 
* Output         : None
* Return         :
*******************************************************************************/
static int hal_sysMemIdleNodeOut(MEM_NODE_T * node_idle)
{
	if(node_idle == NULL || idleMem == NULL)
		return -1;
	node_idle->flag = 0;
	//idleMem: idleMem始终指向idle链表的起始节点
	if(idleMem == node_idle)
		idleMem = idleMem->next;
	hal_sysMemNodeRemove(node_idle);
	return 0;
}
/*******************************************************************************
* Function Name  : hal_sysMemFreeNodeIn
* Description    : hal_sysMemFreeNodeIn
* Input          : 
* Output         : None
* Return         :
*******************************************************************************/
static void hal_sysMemFreeNodeIn(MEM_NODE_T * node_free)
{
	if(node_free == NULL)
		return;
	node_free->flag = 0;
	node_free->addr = 0;
	node_free->size = 0;
	node_free->prev = NULL;
	node_free->next = NULL;
	//新释放的node_free始终放在free链表的起始节点
	if(freeMem)
		hal_sysMemNodeAddPrev(freeMem, node_free);
	freeMem = node_free;

}
/*******************************************************************************
* Function Name  : hal_sysMemFreeNodeOut
* Description    : hal_sysMemFreeNodeOut
* Input          : 
* Output         : None
* Return         :
*******************************************************************************/
static MEM_NODE_T* hal_sysMemFreeNodeOut(void)
{
	MEM_NODE_T* node;
	if(freeMem == NULL)
		return NULL;
	node = freeMem;
	freeMem = freeMem->next;
	hal_sysMemNodeRemove(node);
	return node;
}
/*******************************************************************************
* Function Name  : hal_sysMemBusyNodeIn
* Description    : hal_sysMemBusyNodeIn
* Input          : 
* Output         : None
* Return         :
*******************************************************************************/
static void hal_sysMemBusyNodeIn(MEM_NODE_T * node_busy)
{
	if(node_busy == NULL)
		return;
	node_busy->flag = 1;
	node_busy->prev = NULL;
	node_busy->next = NULL;
	//busyMem指向busy链表最后一个节点，新插入的node始终放在busy链表的末尾
	hal_sysMemNodeAddNext(busyMem, node_busy);
	busyMem	= node_busy;
}
/*******************************************************************************
* Function Name  : hal_sysMemBusyNodeOut
* Description    : hal_sysMemBusyNodeOut
* Input          : 
* Output         : None
* Return         :
*******************************************************************************/
static int hal_sysMemBusyNodeOut(MEM_NODE_T * node_busy)
{
	if(node_busy == NULL || busyMem == NULL)
		return -1;
	node_busy->flag = 0;
	//busyMem指向busy链表最后一个节点
	if(busyMem == node_busy)
		busyMem = busyMem->prev;
	hal_sysMemNodeRemove(node_busy);
	return 0;
}
/*******************************************************************************
* Function Name  : hal_memNodeMalloc
* Description    : hal layer,hal_memNodeMalloc
* Input          : 
* Output         : None
* Return         : 
*******************************************************************************/
static void hal_memNodeinit(u32 startaddr, u32 size)
{
	int i;
	for(i=0;i < MEM_NODE_MAX; i++)
	{
		memNode[i].flag = 0;
		memNode[i].addr = 0;
		memNode[i].size = 0;
		memNode[i].prev = (i == 0) ? NULL:(&memNode[i-1]);
		memNode[i].next = (i == MEM_NODE_MAX - 1)?NULL:(&memNode[i+1]);
	}
	memNode[0].addr = startaddr;
	memNode[0].size = size;
	hal_sysMemIdleNodeIn(&memNode[0]);
	memNode[1].prev = NULL;
	freeMem = &memNode[1];
	busyMem = NULL;
	

}
/*******************************************************************************
* Function Name  : hal_sysMemPrint
* Description    : printf system memory info
* Input          : 
* Output         : None
* Return         :
*******************************************************************************/
void hal_sysMemPrint(void)
{
	MEM_NODE_T *node;
	node = busyMem;	
	while(node)
	{
		deg_Printf("[MEM BUSY]<%d> [0x%x + 0x%x = 0x%x],size:%dkb\n", node->flag,node->addr, node->size, node->addr + node->size, node->size/1024);
		node = node->prev;
	}	
	node = idleMem;	
	while(node)
	{
		deg_Printf("[MEM IDLE]<%d> [0x%x + 0x%x = 0x%x],size:%dkb\n", node->flag,node->addr, node->size, node->addr + node->size,node->size/1024);
		node = node->next;
	}	
	if(freeMem)
		deg_Printf("[MEM FREE]<%d> [0x%x + 0x%x = 0x%x],size:%dkb\n", freeMem->flag,freeMem->addr, freeMem->size, freeMem->addr + freeMem->size,freeMem->size/1024);
	//node = freeMem;	
	//while(node)
	//{
	//	deg_Printf("[MEM FREE]<%x %d> %x,%x,%x,%x\n",node,node->flag,node->addr,node->size, node->prev, node->next);
	//	node = node->next;
	//}	
}
/*******************************************************************************
* Function Name  : hal_sysMemMalloc
* Description    : hal layer,hal_sysMemMalloc
* Input          : 
* Output         : None
* Return         : 
*******************************************************************************/
void *hal_sysMemMalloc(u32 size)
{
	MEM_NODE_T *node_idle = idleMem;
	if(size == 0)
	{
		deg_Printf("[MEM] size = 0\n");
		return (void*)NULL;
	}
	//HAL_CRITICAL_INIT();
	//HAL_CRITICAL_ENTER();
	size = ((size+0x3f)&(~0x3f)); //size¶ÔÆë
	//查询idle链表中是否有匹配size的node
	while(node_idle)
	{
		//deg_Printf("malloc idleMem:[%x,%x]\n", node_start,node_start->next);
		if(node_idle->flag == 0 && node_idle->size >= size)
		{
			break;
		}
		node_idle = node_idle->next;
	}
	if(node_idle == NULL)
	{
		deg_Printf("[MEM MALLOC] fail:%x\n",size);
		hal_sysMemPrint();
		//HAL_CRITICAL_EXIT();
		return NULL;
	}
	//deg_Printf("[MEM TOBUSY NODE]<%d> [0x%x + 0x%x = 0x%x]\n", node_idle->flag,node_idle->addr, node_idle->size, node_idle->addr + node_idle->size);
	//将匹配到的node从idle链表中取出，放入到busy链表
	hal_sysMemIdleNodeOut(node_idle);
	hal_sysMemBusyNodeIn(node_idle);
	if(node_idle->size > size)
	{
		MEM_NODE_T *node_free = hal_sysMemFreeNodeOut();
		if(node_free)
		{
			node_free->addr = node_idle->addr + size;
			node_free->size = node_idle->size - size;
			hal_sysMemIdleNodeIn(node_free);
			node_idle->size = size;
			//deg_Printf("[MEM IDLE0 NODE]<%d> [0x%x + 0x%x = 0x%x]\n", node_free->flag,node_free->addr, node_free->size, node_free->addr + node_free->size);
		}
	}
	//deg_Printf("[MEM MALLOC] [0x%x + 0x%x = 0x%x],size:%dkb \n",node_idle->addr, node_idle->size, node_idle->addr + node_idle->size, node_idle->size/1024);
	//hal_sysMemPrint();
	//HAL_CRITICAL_EXIT();
	return (void*)(node_idle->addr);
}
/*******************************************************************************
* Function Name  : hal_sysMemMallocLast
* Description    : hal layer,hal_sysMemMallocLast
* Input          : 
* Output         : None
* Return         : 
*******************************************************************************/
void *hal_sysMemMallocLast(u32 size)
{
	MEM_NODE_T *node_idle = idleMem;
	if(size == 0)
	{
		deg_Printf("[MEM] size = 0\n");
		return (void*)NULL;
	}
	//HAL_CRITICAL_INIT();
	//HAL_CRITICAL_ENTER();
	size = ((size+0x3f)&(~0x3f)); //size¶ÔÆë
	//查询idle链表中是否有匹配size的node
	while(node_idle)
	{
		hal_wdtClear();
		//deg_Printf("malloc idleMem:[%x,%x]\n", node_start,node_start->next);
		if(node_idle->flag == 0 && node_idle->size >= size)
		{
			break;
		}
		node_idle = node_idle->next;
	}
	if(node_idle == NULL)
	{
		deg_Printf("[MEM MALLOC] fail:%x\n",size);
		hal_sysMemPrint();
		//HAL_CRITICAL_EXIT();
		return NULL;
	}
	//deg_Printf("[MEM TOBUSY NODE]<%d> [0x%x + 0x%x = 0x%x]\n", node_idle->flag,node_idle->addr, node_idle->size, node_idle->addr + node_idle->size);
	if(node_idle->size == size)
	{
		//将匹配到的node从idle链表中取出，放入到busy链表
		hal_sysMemIdleNodeOut(node_idle);
		hal_sysMemBusyNodeIn(node_idle);
	}else //if(node_idle->size > size)
	{	
		MEM_NODE_T *node_free = hal_sysMemFreeNodeOut();
		if(node_free)
		{
			node_free->addr = node_idle->addr + node_idle->size - size;
			node_free->size = size;
			hal_sysMemBusyNodeIn(node_free);
			node_idle->size = node_idle->size - size;
			node_idle = node_free;
			//deg_Printf("[MEM IDLE0 NODE]<%d> [0x%x + 0x%x = 0x%x]\n", node_free->flag,node_free->addr, node_free->size, node_free->addr + node_free->size);
		}else
		{
			//将匹配到的node从idle链表中取出，放入到busy链表
			hal_sysMemIdleNodeOut(node_idle);
			hal_sysMemBusyNodeIn(node_idle);			
		}
			
	}
	//deg_Printf("[MEM MALLOC LAST] [0x%x + 0x%x = 0x%x],size:%dkb \n",node_idle->addr, node_idle->size, node_idle->addr + node_idle->size, node_idle->size/1024);
	//hal_sysMemPrint();
	//HAL_CRITICAL_EXIT();
	return (void*)(node_idle->addr);
}
/*******************************************************************************
* Function Name  : hal_sysMemFree
* Description    : hal layer,hal_sysMemFree
* Input          : 
* Output         : None
* Return         : 
*******************************************************************************/
void hal_sysMemFree(void *mem)
{
	MEM_NODE_T *node_busy = busyMem;
	MEM_NODE_T *node_idle_prev, *node_idle_next,*node_idle;
	if(mem == NULL)
		return;
	//HAL_CRITICAL_INIT();
	//HAL_CRITICAL_ENTER();

	while(node_busy)
	{
		if(node_busy->flag == 1 && node_busy->addr == (u32)mem)
			break;
		node_busy = node_busy->prev;
	}
	if(node_busy == NULL)
	{
		deg_Printf("[MEM FREE] fail:%x\n",(u32)mem);
		hal_sysMemPrint();
		//HAL_CRITICAL_EXIT();
		return;
	}
	//deg_Printf("[MEM TOFREE NODE]<%d> [0x%x + 0x%x = 0x%x],size = %dkb\n", node_busy->flag,node_busy->addr, node_busy->size, node_busy->addr + node_busy->size,node_busy->size/1024);
	
	hal_sysMemBusyNodeOut(node_busy);
	
	
	node_idle 		= idleMem;
	node_idle_prev 	= NULL;
	node_idle_next 	= NULL;
	while(node_idle)
	{
	
		if(node_idle->flag == 0 && ((node_idle->addr + node_idle->size) == node_busy->addr))
		{
			//deg_Printf("[MEM IDLE PRE NODE]<%x %d> %x,%x,%x,%x\n",node_idle,node_idle->flag,node_idle->addr,node_idle->size, node_idle->prev, node_idle->next);
			node_idle_prev = node_idle;
		}

		if((node_idle->flag == 0) && (node_idle->addr == (node_busy->addr + node_busy->size)))
		{
			//deg_Printf("[MEM IDLE NEXT NODE]<%x %d> %x,%x,%x,%x\n",node_idle,node_idle->flag,node_idle->addr,node_idle->size, node_idle->prev, node_idle->next);
			node_idle_next = node_idle;
		}
		node_idle = node_idle->next;
	}

	if(node_idle_prev || node_idle_next)
	{
		if(node_idle_prev && node_idle_next)
		{
			hal_sysMemIdleNodeOut(node_idle_prev);
			hal_sysMemIdleNodeOut(node_idle_next);
			node_idle_prev->size = node_idle_prev->size + node_busy->size + node_idle_next->size;
			hal_sysMemIdleNodeIn(node_idle_prev);
			hal_sysMemFreeNodeIn(node_idle_next);
		}else if(node_idle_prev)
		{
			hal_sysMemIdleNodeOut(node_idle_prev);
			node_idle_prev->size += node_busy->size;
			hal_sysMemIdleNodeIn(node_idle_prev);
		}else{
			hal_sysMemIdleNodeOut(node_idle_next);
			node_idle_next->addr = node_busy->addr;
			node_idle_next->size += node_busy->size;
			hal_sysMemIdleNodeIn(node_idle_next);			
		}
		hal_sysMemFreeNodeIn(node_busy);
	}else 
	{
		hal_sysMemIdleNodeIn(node_busy);
		//deg_Printf("[MEM IDLE NODE]<%d> [0x%x + 0x%x = 0x%x]\n", node_busy->flag,node_busy->addr, node_busy->size, node_busy->addr + node_busy->size);
	}
	//hal_sysMemPrint();
	//HAL_CRITICAL_EXIT();
}

/*******************************************************************************
* Function Name  : hal_sysMemRemain
* Bref               : u32 hal_sysMemRemain(void)
* Description    : hal layer.system memory remain check.this function only for temp using.memory can not be free
* Input          : 
* Output         : None
* Return         : u32 : remain size
*******************************************************************************/
u32 hal_sysMemRemain(void)
{
	MEM_NODE_T * node_idle		= idleMem;
	MEM_NODE_T * node_idle_max  = idleMem;
	//hal_sysMemPrint();
	while(node_idle)
	{
		if(node_idle->size > node_idle_max->size)
			node_idle_max = node_idle;
		node_idle = node_idle->next;
	}
	if(node_idle_max)
	{
		//deg_Printf("[MEM IDLEMAX NODE]<%d> [0x%x + 0x%x = 0x%x]\n", node_idle_max->flag,node_idle_max->addr, node_idle_max->size, node_idle_max->addr + node_idle_max->size);
		return node_idle_max->size;
	}
	else
		return 0;
}

/*******************************************************************************
* Function Name  : hal_sysInit
* Bref           : void hal_sysInit(int ms)
* Description    : hal layer.initial system for platform using.
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
void hal_sysInit(void)
{
	u32 halMemStart,halMemEnd;
	
	hx330x_sysInit((u32 *)&halMemStart,(u32 *)&halMemEnd);
    
	halMemStart = (halMemStart+0x3f)&(~0x3f);
	hal_memNodeinit(halMemStart,halMemEnd-halMemStart);
	//hal layer init
	hal_uartInit();
    hal_rtcInit();	
	
    hal_rtcAlarmSet(0,0);
   
	//hal_spiInit();
	//hal_spiFlashReadID();
#if 0
	hal_dmauartTest();
#endif
#if 0
	
	hal_spi1Init();
	hal_spi1_test();
#endif
	hal_watermarkInit();
	
	hal_mdInit(NULL);
	
    hal_adcInit();
	
	//hal_usbdEnable(1);
	
	hal_auadcInit();
	
	hal_dacInit();

	XOSInit();	

//----------start system work queue for system check & deamon service	
	hal_timerStart(HAL_CFG_OS_TIMER,X_CFG_TICK,XOSTickService);  // 1000 hz 

}




