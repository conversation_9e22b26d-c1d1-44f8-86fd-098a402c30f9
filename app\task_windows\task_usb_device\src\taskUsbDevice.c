/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
ALIGNED(4) u32 usb_update_flag;
/*******************************************************************************
* Function Name  : taskUsbDeviceOpen
* Description    : taskUsbDeviceOpen function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskUsbDeviceOpen(u32 arg)
{
    SysCtrl.dev_stat_power &= ~(POWERON_FLAG_FIRST|POWERON_FLAG_WAIT);
	app_lcdCsiVideoShowStop();
	hal_csiEnable(0);
	usb_update_flag = 0;
	uiOpenWindow(&usbDeviceWindow,0,0);

	res_image_show(R_ID_IMAGE_USB_MODE, 0);
	task_com_sound_wait_end();

	dusb_api_Init(USB_DEVTYPE_COMBINE);

}
/*******************************************************************************
* Function Name  : taskUsbDeviceClose
* Description    : taskUsbDeviceClose function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskUsbDeviceClose(uint32 arg)
{
	dusb_api_Uninit();
}
/*******************************************************************************
* Function Name  : taskUsbDeviceService
* Description    : taskUsbDeviceService function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskUsbDeviceService(uint32 arg)
{
	if(false == dusb_api_Process())
	{
		usb_update_flag = 1;
		deg_Printf("usb update\n");
		//SysCtrl.dev_stat_lcd = 0;
		task_com_sound_wait_end();
		task_com_lcdbk_set(0); 

		//dev_ioctrl(SysCtrl.dev_fd_lcd, DEV_LCD_BK_WRITE, 0); // back light off
		//dusb_api_Uninit();
		app_taskStart(TASK_USB_UPDATE,0);
		//app_taskStart(TASK_POWER_OFF,0);
	}	

}

ALIGNED(4) sysTask_T taskUSBDevice =
{
	"usb device",
	0,
	taskUsbDeviceOpen,
	taskUsbDeviceClose,
	taskUsbDeviceService,
};


