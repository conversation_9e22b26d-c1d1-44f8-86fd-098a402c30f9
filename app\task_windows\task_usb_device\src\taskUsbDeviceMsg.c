/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskUsbDeviceWin.c"
/*******************************************************************************
* Function Name  : usbDeviceKeyMsgOk
* Description    : usbDeviceKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceKeyMsgCom(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{

	}
	return 0;
}
/*******************************************************************************
* Function Name  : usbDeviceKeyMsgOk
* Description    : usbDeviceKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		app_taskStart(TASK_BAT_CHARGE,0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : usbDeviceSysMsgUSBDEV
* Description    : usbDeviceSysMsgUSBDEV
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceSysMsgUSBDEV(winHandle handle,uint32 parameNum,uint32* parame)
{
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
		app_taskStart(TASK_POWER_OFF,0);
	return 0;
}
/*******************************************************************************
* Function Name  : usbDeviceSysMsgUSBDEV
* Description    : usbDeviceSysMsgUSBDEV
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceSysMsg1s(winHandle handle,uint32 parameNum,uint32* parame)
{
	if(usb_update_flag == 0)
	{
		task_com_sreen_check(SREEN_RESET_AUTOOFF);	
	}
	task_com_auto_poweroff(1);
	return 0;
}

/*******************************************************************************
* Function Name  : usbDeviceOpenWin
* Description    : usbDeviceOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]usbDeviceOpenWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : usbDeviceCloseWin
* Description    : usbDeviceCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]usbDeviceCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : usbDeviceWinChildClose
* Description    : usbDeviceWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]usbDeviceWinChildClose\n");
	return 0;
}


ALIGNED(4) msgDealInfor usbDeviceMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	usbDeviceOpenWin},
	{SYS_CLOSE_WINDOW,	usbDeviceCloseWin},
	{SYS_CHILE_COLSE,	usbDeviceWinChildClose},
	{KEY_EVENT_OK,		usbDeviceKeyMsgCom},
	{KEY_EVENT_UP,		usbDeviceKeyMsgCom},
	{KEY_EVENT_DOWN,	usbDeviceKeyMsgCom},
	{KEY_EVENT_LEFT,	usbDeviceKeyMsgCom},
	{KEY_EVENT_RIGHT,	usbDeviceKeyMsgCom},
	{KEY_EVENT_POWER,	usbDeviceKeyMsgPower},
	{KEY_EVENT_POWEROFF,usbDeviceKeyMsgPower},
	{SYS_EVENT_USBDEV,	usbDeviceSysMsgUSBDEV},
	{SYS_EVENT_1S,		usbDeviceSysMsg1s},
	{EVENT_MAX,NULL},
};

WINDOW(usbDeviceWindow,usbDeviceMsgDeal,usbDeviceWin)