/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuDelWin.c"

/*******************************************************************************
* Function Name  : getdelResInfor
* Description    : getdelResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : none
*******************************************************************************/
static u32 getdelResInfor(u32 item,u32* image,u32* str)
{
	if(image)
		*image = INVALID_RES_ID;	
	if(item == 0)
	{
		if(str)
			*str   = R_ID_STR_SET_DELETECUR;
	}
	else if(item==1)
	{
		if(str)
			*str   = R_ID_STR_SET_DELETEALL;
	}else
	{
		if(str)
			*str   = R_ID_STR_COM_CANCEL;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delAllKeyMsgOk
* Description    : delAllKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	int i,cnt;
	int file_type;
	char *name;
	INT32S list;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,DEL_SELECT_ID));
		if(item == 2)
		{
			uiWinDestroy(&handle);
		}else
		{
			/*if(app_taskCurId()  == TASK_PLAY_VIDEO)
			{
				if(SysCtrl.spi_jpg_list >= 0)
				{
					list = SysCtrl.spi_jpg_list;
				}else{
					list = SysCtrl.avi_list;
				}
				
			}else if(app_taskCurId()  == TASK_PLAY_AUDIO)
			{
				list = SysCtrl.wav_list;
			}else if(app_taskCurId()  == TASK_NES_GAME)
			{
				list = SysCtrl.nes_list;
			}else
			{
				uiWinDestroy(&handle);
				return 0;
			}
			cnt = filelist_api_CountGet(list);
			if(cnt <= 0)
			{
				task_com_tips_show(TIPS_NO_FILE);
				return -1;
			}*/
			if(item == 1)
			{
				task_com_keysound_play();
				task_com_sound_wait_end();
				uiOpenWindow(&delAllWindow,0,0);
				/*task_com_tips_show(TIPS_COM_WAITING);
				deg_Printf("del:cnt:%d\n",cnt);
				for(i = 0;i < cnt;i++)
				{
					hal_wdtClear();
					name = filelist_GetFileFullNameByIndex(list, i, &file_type);
					if(name && !(file_type & FILELIST_FLAG_LOK))
					{
						//deg_Printf("delete : %s.",name);
						if(file_type & FILELIST_TYPE_SPI)
						{
							if(nv_jpgfile_delete(filelist_GetFileIndexByIndex(list,i)) == NV_OK)
							{
								//deg_Printf("->ok\n");
							}	
							else
							{
								//deg_Printf("->fail\n");		
							}
											
						}else 
						{	
							if(f_unlink(name)==FR_OK)
							{
								//deg_Printf("->ok\n");
							}		
							else
							{
								//deg_Printf("->fail\n");	
							}
								
						}
					}
				}
				if(cnt > 0)
					task_com_tips_show(TIPS_COM_SUCCESS);
				else
					task_com_tips_show(TIPS_NO_FILE);
				if(list != SysCtrl.spi_jpg_list)
					task_com_sdc_freesize_check();
				filelist_listDelAll(list);
				SysCtrl.file_cnt   = filelist_api_CountGet(list);
				SysCtrl.file_index = SysCtrl.file_cnt - 1;*/
			}else
			{
			
				task_com_keysound_play();
				task_com_sound_wait_end();
				uiOpenWindow(&delCurWindow,0,0);
			#if 0
				if(filelist_fnameChecklockByIndex(list,SysCtrl.file_index) <= 0) // > 0: lock, 0: AVI and unlock, <0: lock invalid
				{
					name = filelist_GetFileFullNameByIndex(list, SysCtrl.file_index, &file_type);
					deg_Printf("delete : %s.",name);
					if(file_type & FILELIST_TYPE_SPI)
					{
						if(nv_jpgfile_delete(filelist_GetFileIndexByIndex(list,SysCtrl.file_index)) == NV_OK)
						{
							deg_Printf("->ok\n");
							filelist_delFileByIndex(list,SysCtrl.file_index);
							SysCtrl.file_cnt   = filelist_api_CountGet(list);
							SysCtrl.file_index = SysCtrl.file_cnt - 1;
							task_com_tips_show(TIPS_COM_SUCCESS);
						}else
						{
							deg_Printf("->fail\n");
							task_com_tips_show(TIPS_COM_FAIL);
						}
					}else
					{
						if(f_unlink(name)==FR_OK)
						{
							deg_Printf("->ok\n");
							filelist_delFileByIndex(list,SysCtrl.file_index);
							SysCtrl.file_cnt   = filelist_api_CountGet(list);
							SysCtrl.file_index = SysCtrl.file_cnt - 1;

							task_com_sdc_freesize_check();
							task_com_tips_show(TIPS_COM_SUCCESS);
						}
						else
						{
							deg_Printf("->fail\n");
							task_com_tips_show(TIPS_COM_FAIL);
						}
					}
				}
				#endif
			}
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delKeyMsgUp
* Description    : delKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,DEL_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delKeyMsgDown
* Description    : delKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManageNextItem(winItem(handle,DEL_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delKeyMsgPower
* Description    : delKeyMsgPower
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delOpenWin
* Description    : delOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]delOpenWin\n");
	//uiItemManageSetRowSum(winItem(handle,DELALL_SELECT_ID),1,Rh(40));
	//uiItemManageSetColumnSumWithGap(winItem(handle,DELALL_SELECT_ID),0,2,Rw(100), Rw(0));
	//uiItemManageCreateItem(		winItem(handle,DELALL_SELECT_ID),uiItemCreateMenuOption,getdelAllResInfor,2);

	uiItemManageSetItemHeight(winItem(handle,DEL_SELECT_ID),Rh(35));
	uiItemManageCreateItem(		winItem(handle,DEL_SELECT_ID),uiItemCreateMenuOption,getdelResInfor,3);
#if 0
	uiItemManageSetCharInfor(	winItem(handle,DEL_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_White);
	uiItemManageSetSelectColor(	winItem(handle,DEL_SELECT_ID),R_ID_PALETTE_DoderBlue);
	uiItemManageSetUnselectColor(winItem(handle,DEL_SELECT_ID),R_ID_PALETTE_Gray);
#else
	uiItemManageSetSelectColorEx(winItem(handle,DEL_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER, SMENU_SELECT_FN_COLOR, SMENU_SELECT_BG_COLOR);
	uiItemManageSetUnselectColorEx(winItem(handle,DEL_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER, SMENU_UNSELECT_FN_COLOR, SMENU_UNSELECT_BG_COLOR);
#endif


	uiItemManageSetCurItem(		winItem(handle,DEL_SELECT_ID),0);
	
	return 0;
}
/*******************************************************************************
* Function Name  : delCloseWin
* Description    : delCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]delCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : delWinChildClose
* Description    : delWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]delWinChildClose\n");
	uiWinDestroy(&handle);
	return 0;
}


ALIGNED(4) msgDealInfor delMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	delOpenWin},
	{SYS_CLOSE_WINDOW,	delCloseWin},
	{SYS_CHILE_COLSE,	delWinChildClose},
	{KEY_EVENT_OK,		delKeyMsgOk},
	{KEY_EVENT_UP,		delKeyMsgUp},
	{KEY_EVENT_DOWN,	delKeyMsgDown},
	{KEY_EVENT_LEFT,	delKeyMsgPower},
	//{KEY_EVENT_RIGHT,	delKeyMsgDown},
	{KEY_EVENT_POWER,	delKeyMsgPower},
	{EVENT_MAX,NULL},
};

WINDOW(delWindow,delMsgDeal,delWin)


