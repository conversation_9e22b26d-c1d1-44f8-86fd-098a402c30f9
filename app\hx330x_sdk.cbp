<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<CodeBlocks_project_file>
	<FileVersion major="1" minor="6" />
	<Project>
		<Option title="HongAo-gaoqing" />
		<Option pch_mode="2" />
		<Option compiler="hx330x-gcc" />
		<Build>
			<Target title="Debug">
				<Option output="bin/Debug/hx330x_sdk" prefix_auto="1" extension_auto="1" />
				<Option object_output="obj/Debug/" />
				<Option type="1" />
				<Option compiler="hx330x-gcc" />
				<Compiler>
					<Add option="-Os" />
				</Compiler>
				<Linker>
					<Add option="-T../mcu/boot/hx330x.ld" />
					<Add option='-Map=&quot;$(TARGET_OUTPUT_DIR)$(PROJECT_NAME).map&quot;' />
					<Add library="../lib/libboot.a" />
					<Add library="../lib/libmcu.a" />
					<Add library="../lib/libisp.a" />
					<Add library="../lib/libjpg.a" />
					<Add library="../lib/liblcd.a" />
					<Add library="../lib/libmultimedia.a" />
					<Add library="../lib/libkidframe.a" />
					<Add library="../lib/libnes.a" />
					<Add library="../lib/libmagicmirror.a" />
					<Add library="../lib/liblens.a" />
					<Add library="c" />
					<Add library="gcc" />
					<Add directory="../mcu/boot" />
				</Linker>
				<ExtraCommands>
					<Add after="script\build_bin.bat $(TARGET_OUTPUT_FILE)" />
					<Mode after="always" />
				</ExtraCommands>
			</Target>
		</Build>
		<Compiler>
			<Add option="-Wall" />
		</Compiler>
		<Unit filename="../dev/battery/inc/battery_api.h" />
		<Unit filename="../dev/battery/src/battery_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/dev_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/dev_api.h" />
		<Unit filename="../dev/dev_typedef.h" />
		<Unit filename="../dev/fs/inc/diskio.h" />
		<Unit filename="../dev/fs/inc/ff.h" />
		<Unit filename="../dev/fs/inc/ffconf.h" />
		<Unit filename="../dev/fs/inc/fs_api.h" />
		<Unit filename="../dev/fs/inc/fs_typedef.h" />
		<Unit filename="../dev/fs/src/diskio.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/fs/src/ff.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/fs/src/ffunicode.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/fs/src/fs_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/gsensor/inc/gsensor_api.h" />
		<Unit filename="../dev/gsensor/src/gsensor_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/gsensor/src/gsensor_da380.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/gsensor/src/gsensor_gma301.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/gsensor/src/gsensor_sc7a30e.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/ir/inc/ir_api.h" />
		<Unit filename="../dev/ir/src/ir_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/key/inc/key_api.h" />
		<Unit filename="../dev/key/src/key_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/inc/lcd_api.h" />
		<Unit filename="../dev/lcd/inc/lcd_typedef.h" />
		<Unit filename="../dev/lcd/src/lcd_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_3030B.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_SPFD5420.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_hx8352b.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_hx8352c.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_ili9225G.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_ili9328.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_ili9335.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_ili9486_T35-H43-86.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_jd9851.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_lgdp4532.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_r61509v.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_st7789.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_st7789P3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_st7789v.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_rgb_ili8961.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_rgb_ili9342c.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_rgb_ota5182.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_rgb_st7282.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_spi_ili9341.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/led/inc/led_api.h" />
		<Unit filename="../dev/led/src/led_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/nvfs/inc/nvfs_api.h" />
		<Unit filename="../dev/nvfs/src/nvfs_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/nvfs/src/nvfs_jpg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sd/inc/sd_api.h" />
		<Unit filename="../dev/sd/inc/sd_typedef.h" />
		<Unit filename="../dev/sd/src/sd_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/inc/sensor_api.h" />
		<Unit filename="../dev/sensor/inc/sensor_typedef.h" />
		<Unit filename="../dev/sensor/src/sensor_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_BF3016.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_FPX1002.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_GC1004.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_GC1034.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_GC1054.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_GC1064.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_H42.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_H62.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_H65.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_H7640.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_NT99141.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_OV9710.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_OV9732.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_SC1045.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_SC1243.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_SC1345.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_SP1409.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_SP140A.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_BF2013.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_BF20a6.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_BF3703.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_BF3a03.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_GC0307.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_GC0308.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_GC0309.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_GC0328.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_GC0329.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_HM1055.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_IT03A1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_NT99142.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_OV7670.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_OV7675.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_OV7725.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_OV7736.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_SC030IOT.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_SIV100B.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_SIV120B.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_SIV121DS.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_mipi_720P_GC1054.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_mipi_720P_H62.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_mipi_720P_OV9714.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_mipi_VGA_BF20A1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_mipi_VGA_GC030A.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_mipi_VGA_GC0339.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_mipi_VGA_GC033A.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_mipi_VGA_SP0A09.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_tab.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/dusb/inc/dusb_api.h" />
		<Unit filename="../dev/usb/dusb/inc/dusb_enum.h" />
		<Unit filename="../dev/usb/dusb/inc/dusb_msc.h" />
		<Unit filename="../dev/usb/dusb/inc/dusb_tool_api.h" />
		<Unit filename="../dev/usb/dusb/inc/dusb_uac.h" />
		<Unit filename="../dev/usb/dusb/inc/dusb_uvc.h" />
		<Unit filename="../dev/usb/dusb/src/dusb_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/dusb/src/dusb_enum.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/dusb/src/dusb_msc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/dusb/src/dusb_tool_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/dusb/src/dusb_uac.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/dusb/src/dusb_uvc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/husb/inc/husb_api.h" />
		<Unit filename="../dev/usb/husb/inc/husb_enum.h" />
		<Unit filename="../dev/usb/husb/inc/husb_tpbulk.h" />
		<Unit filename="../dev/usb/husb/inc/husb_usensor.h" />
		<Unit filename="../dev/usb/husb/inc/husb_uvc.h" />
		<Unit filename="../hal/inc/hal.h" />
		<Unit filename="../hal/inc/hal_adc.h" />
		<Unit filename="../hal/inc/hal_auadc.h" />
		<Unit filename="../hal/inc/hal_cfg.h" />
		<Unit filename="../hal/inc/hal_csi.h" />
		<Unit filename="../hal/inc/hal_dac.h" />
		<Unit filename="../hal/inc/hal_eeprom.h" />
		<Unit filename="../hal/inc/hal_gpio.h" />
		<Unit filename="../hal/inc/hal_iic.h" />
		<Unit filename="../hal/inc/hal_int.h" />
		<Unit filename="../hal/inc/hal_isp.h" />
		<Unit filename="../hal/inc/hal_lcd.h" />
		<Unit filename="../hal/inc/hal_lcdframe.h" />
		<Unit filename="../hal/inc/hal_md.h" />
		<Unit filename="../hal/inc/hal_mjpDecode.h" />
		<Unit filename="../hal/inc/hal_mjpEncode.h" />
		<Unit filename="../hal/inc/hal_osd.h" />
		<Unit filename="../hal/inc/hal_osdcmp.h" />
		<Unit filename="../hal/inc/hal_pmu.h" />
		<Unit filename="../hal/inc/hal_rotate.h" />
		<Unit filename="../hal/inc/hal_rtc.h" />
		<Unit filename="../hal/inc/hal_spi.h" />
		<Unit filename="../hal/inc/hal_stream.h" />
		<Unit filename="../hal/inc/hal_sys.h" />
		<Unit filename="../hal/inc/hal_timer.h" />
		<Unit filename="../hal/inc/hal_uart.h" />
		<Unit filename="../hal/inc/hal_watermark.h" />
		<Unit filename="../hal/inc/hal_wdt.h" />
		<Unit filename="../hal/src/hal_adc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_auadc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_csi.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_dac.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_dmauart.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_eeprom.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_gpio.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_iic.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_int.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_lcdshow.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_md.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_mjpAEncode.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_mjpDecode.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_rtc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_spi.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_spi1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_stream.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_sys.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_timer.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_uart.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_watermark.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_wdt.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../mcu/boot/hx330x.ld" />
		<Unit filename="../mcu/boot/spi_boot_cfg.S" />
		<Unit filename="../mcu/boot/spi_boot_cfg.h" />
		<Unit filename="../mcu/inc/hx330x.h" />
		<Unit filename="../mcu/inc/hx330x_adc.h" />
		<Unit filename="../mcu/inc/hx330x_auadc.h" />
		<Unit filename="../mcu/inc/hx330x_cfg.h" />
		<Unit filename="../mcu/inc/hx330x_csi.h" />
		<Unit filename="../mcu/inc/hx330x_dac.h" />
		<Unit filename="../mcu/inc/hx330x_dma.h" />
		<Unit filename="../mcu/inc/hx330x_emi.h" />
		<Unit filename="../mcu/inc/hx330x_gpio.h" />
		<Unit filename="../mcu/inc/hx330x_iic.h" />
		<Unit filename="../mcu/inc/hx330x_int.h" />
		<Unit filename="../mcu/inc/hx330x_isp.h" />
		<Unit filename="../mcu/inc/hx330x_jpg.h" />
		<Unit filename="../mcu/inc/hx330x_lcd.h" />
		<Unit filename="../mcu/inc/hx330x_md.h" />
		<Unit filename="../mcu/inc/hx330x_mipi.h" />
		<Unit filename="../mcu/inc/hx330x_misc.h" />
		<Unit filename="../mcu/inc/hx330x_osd.h" />
		<Unit filename="../mcu/inc/hx330x_osdcmp.h" />
		<Unit filename="../mcu/inc/hx330x_pip.h" />
		<Unit filename="../mcu/inc/hx330x_pmu.h" />
		<Unit filename="../mcu/inc/hx330x_rotate.h" />
		<Unit filename="../mcu/inc/hx330x_rtc.h" />
		<Unit filename="../mcu/inc/hx330x_sd.h" />
		<Unit filename="../mcu/inc/hx330x_spi.h" />
		<Unit filename="../mcu/inc/hx330x_spr_defs.h" />
		<Unit filename="../mcu/inc/hx330x_sys.h" />
		<Unit filename="../mcu/inc/hx330x_timer.h" />
		<Unit filename="../mcu/inc/hx330x_tminf.h" />
		<Unit filename="../mcu/inc/hx330x_uart.h" />
		<Unit filename="../mcu/inc/hx330x_usb.h" />
		<Unit filename="../mcu/inc/hx330x_wdt.h" />
		<Unit filename="../mcu/inc/typedef.h" />
		<Unit filename="../mcu/xos/xcfg.h" />
		<Unit filename="../mcu/xos/xdef.h" />
		<Unit filename="../mcu/xos/xmbox.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../mcu/xos/xmbox.h" />
		<Unit filename="../mcu/xos/xmsgq.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../mcu/xos/xmsgq.h" />
		<Unit filename="../mcu/xos/xos.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../mcu/xos/xos.h" />
		<Unit filename="../mcu/xos/xwork.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../mcu/xos/xwork.h" />
		<Unit filename="../multimedia/Library/AVI/inc/avi_api.h" />
		<Unit filename="../multimedia/Library/AVI/inc/avi_typedef.h" />
		<Unit filename="../multimedia/Library/JPG/inc/jpg_api.h" />
		<Unit filename="../multimedia/Library/JPG/inc/jpg_typedef.h" />
		<Unit filename="../multimedia/Library/JPG/src/jpg_enc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../multimedia/Library/WAV/inc/wav_api.h" />
		<Unit filename="../multimedia/Library/WAV/inc/wav_typedef.h" />
		<Unit filename="../multimedia/Library/api_multimedia.h" />
		<Unit filename="../multimedia/Library/mp3/mp3_api.h" />
		<Unit filename="../multimedia/Library/mp3/mp3_bsio.h" />
		<Unit filename="../multimedia/Library/mp3/mp3_cpu_fixed.h" />
		<Unit filename="../multimedia/Library/mp3/mp3_dac.h" />
		<Unit filename="../multimedia/Library/mp3/mp3_dec.h" />
		<Unit filename="../multimedia/Library/mp3/mp3_frame.h" />
		<Unit filename="../multimedia/Library/mp3/mp3_huffman.h" />
		<Unit filename="../multimedia/Library/mp3/mp3_layer12.h" />
		<Unit filename="../multimedia/Library/mp3/mp3_layer3.h" />
		<Unit filename="../multimedia/Library/mp3/mp3_lrc.h" />
		<Unit filename="../multimedia/Library/mp3/mp3_synth.h" />
		<Unit filename="../multimedia/audio/audio_playback.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../multimedia/audio/audio_playback.h" />
		<Unit filename="../multimedia/audio/audio_record.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../multimedia/audio/audio_record.h" />
		<Unit filename="../multimedia/image/image_decode.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../multimedia/image/image_decode.h" />
		<Unit filename="../multimedia/image/image_encode.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../multimedia/image/image_encode.h" />
		<Unit filename="../multimedia/multimedia_api.h" />
		<Unit filename="../multimedia/video/video_playback.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../multimedia/video/video_playback.h" />
		<Unit filename="../multimedia/video/video_record.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../multimedia/video/video_record.h" />
		<Unit filename="../sys_manage/filelist_manage/inc/file_list_api.h" />
		<Unit filename="../sys_manage/filelist_manage/inc/file_list_typedef.h" />
		<Unit filename="../sys_manage/filelist_manage/src/file_list_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/filelist_manage/src/file_list_manage.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/kid_frame_manage/inc/kid_frame_api.h" />
		<Unit filename="../sys_manage/lens_manage/inc/lens_api.h" />
		<Unit filename="../sys_manage/nes_manage/inc/nes_api.h" />
		<Unit filename="../sys_manage/nes_manage/inc/nes_dac.h" />
		<Unit filename="../sys_manage/nes_manage/inc/nes_hardware.h" />
		<Unit filename="../sys_manage/nes_manage/inc/nes_k6502.h" />
		<Unit filename="../sys_manage/nes_manage/inc/nes_mapper.h" />
		<Unit filename="../sys_manage/nes_manage/inc/nes_pApu.h" />
		<Unit filename="../sys_manage/nes_manage/inc/nes_typedef.h" />
		<Unit filename="../sys_manage/nes_manage/src/nes_games.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_ascii/inc/res_ascii_api.h" />
		<Unit filename="../sys_manage/res_manage/res_ascii/src/ascii_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_ascii/src/ascii_num0_tab.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_ascii/src/ascii_num1_tab.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_ascii/src/ascii_num2_tab.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_ascii/src/ascii_num3_tab.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_ascii/src/ascii_num4_tab.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_ascii/src/gb2312_tab.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_font/inc/res_font_api.h" />
		<Unit filename="../sys_manage/res_manage/res_font/src/res_font_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_icon/inc/res_icon_api.h" />
		<Unit filename="../sys_manage/res_manage/res_icon/src/res_icon_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_image/inc/res_image_api.h" />
		<Unit filename="../sys_manage/res_manage/res_image/src/res_image_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_manage_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_manage_api.h" />
		<Unit filename="../sys_manage/res_manage/res_music/inc/res_music_api.h" />
		<Unit filename="../sys_manage/res_manage/res_music/src/res_music_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_music/src/res_music_tab.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/sys_manage_api.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinApi.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinButton.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinDialog.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinDraw.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinFrame.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinImageIcon.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinItemManage.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinItemMenu.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinItemMenuEx.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinItemMenuOption.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinLine.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinManage.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinMemManage.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinProgressBar.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinRect.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinStringEx.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinStringIcon.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinTips.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinWidget.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinWidgetManage.h" />
		<Unit filename="../sys_manage/ui_manage/src/uiWinButton.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinCycle.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinDialog.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinDraw.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinFrame.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinImageIcon.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinItemManage.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinItemMenu.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinItemMenuEx.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinItemMenuEx2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinItemMenuOption.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinLine.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinManage.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinMemManage.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinProgressBar.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinProgressBarVer.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinRect.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinStringEx.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinStringIcon.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinTips.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinWidget.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinWidgetManage.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="app_common/inc/app_api.h" />
		<Unit filename="app_common/inc/app_typedef.h" />
		<Unit filename="app_common/src/app_init.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="app_common/src/app_lcdshow.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="app_common/src/main.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="resource/res_file - 新游戏/L100调色板.pal" />
		<Unit filename="resource/res_file - 新游戏/LOGO/STELS/power_off.jpg" />
		<Unit filename="resource/res_file - 新游戏/LOGO/STELS/power_on.jpg" />
		<Unit filename="resource/res_file - 新游戏/LOGO/kitty/power_off.jpg" />
		<Unit filename="resource/res_file - 新游戏/LOGO/kitty/power_on.jpg" />
		<Unit filename="resource/res_file - 新游戏/LOGO/中性/power_off.jpg" />
		<Unit filename="resource/res_file - 新游戏/LOGO/中性/power_on.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/20481.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/20482.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/3660.wav" />
		<Unit filename="resource/res_file - 新游戏/files/bat_main.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/bat_sub.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/boom.wav" />
		<Unit filename="resource/res_file - 新游戏/files/fly1.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/fly2.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/focus.wav" />
		<Unit filename="resource/res_file - 新游戏/files/games_music.wav" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame0.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame1.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame10.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame11.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame12.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame13.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame14.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame15.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame16.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame17.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame18.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame19.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame2.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame20.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame21.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame22.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame23.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame24.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame25.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame26.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame27.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame3.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame4.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame5.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame6.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame7.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame8.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/icon_frame9.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/key_sound.wav" />
		<Unit filename="resource/res_file - 新游戏/files/main_background.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/main_game.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/main_mp3.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/main_photo.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/main_play.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/main_settings.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/main_video.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/maze1.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/maze2.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/music_play.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/power_off.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/power_off.wav" />
		<Unit filename="resource/res_file - 新游戏/files/power_on.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/power_on.wav" />
		<Unit filename="resource/res_file - 新游戏/files/pushBox1.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/pushBox2.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/setting_background.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/snake1.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/snake2.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/take_photo.wav" />
		<Unit filename="resource/res_file - 新游戏/files/usb_mode.jpg" />
		<Unit filename="resource/res_file - 新游戏/files/version.bin" />
		<Unit filename="resource/res_file - 新游戏/icons/MTBattery0.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTBattery1.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTBattery2.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTBattery3.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTBattery4.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTBatteryCharge.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTLeft.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTMore.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTMusic.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTPhoto.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTPlay.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTPlay1.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTRecMode.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTRecord.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTRecording.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTRight.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTSDCNormal.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTSDCNull.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTSetting.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTVideo1080P.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTVideo720P.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MTVideoVGA.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MenuClock.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MenuVolume.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar1.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar10.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar11.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar12.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar13.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar14.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar15.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar16.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar17.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar18.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar19.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar2.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar20.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar21.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar22.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar23.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar24.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar25.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar3.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar4.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar5.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar6.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar7.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar8.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicBar9.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicModeContinue.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicModeCycle.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicModeOrder.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/MusicModeRand.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/PIC1024.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/PIC128.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/PIC16.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/PIC2.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/PIC2048.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/PIC256.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/PIC32.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/PIC4.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/PIC512.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/PIC64.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/PIC8.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/Shoots.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/game_tips_icon.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/plane_buttle.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/plane_diji1.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/plane_diji2.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/plane_diji3.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/plane_fall.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/plane_fight.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/pushbox_flood.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/pushbox_man.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/pushbox_point.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/pushbox_reach.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/pushbox_unreach.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/pushbox_wall.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/tcs_body.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/tcs_food.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/tcs_head_d.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/tcs_head_l.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/tcs_head_r.bmp" />
		<Unit filename="resource/res_file - 新游戏/icons/tcs_head_u.bmp" />
		<Unit filename="resource/res_file - 新游戏/languages/多国语言_1 - 日语.xlsx" />
		<Unit filename="resource/res_file - 新游戏/languages/多国语言_1 - 没改希伯来语前的.xlsx" />
		<Unit filename="resource/res_file - 新游戏/languages/多国语言_1-平假名.xlsx" />
		<Unit filename="resource/res_file - 新游戏/languages/多国语言_1.xlsx" />
		<Unit filename="resource/res_file - 新游戏/uptate_time.exe" />
		<Unit filename="resource/res_file - 新游戏/圣诞.rar" />
		<Unit filename="resource/res_file - 新游戏/圣诞/75x75图标/拍照.bmp" />
		<Unit filename="resource/res_file - 新游戏/圣诞/75x75图标/摄像.bmp" />
		<Unit filename="resource/res_file - 新游戏/圣诞/75x75图标/游戏.bmp" />
		<Unit filename="resource/res_file - 新游戏/圣诞/75x75图标/照片.bmp" />
		<Unit filename="resource/res_file - 新游戏/圣诞/75x75图标/背景.bmp" />
		<Unit filename="resource/res_file - 新游戏/圣诞/75x75图标/设置.bmp" />
		<Unit filename="resource/res_file - 新游戏/圣诞/75x75图标/音乐.bmp" />
		<Unit filename="resource/res_file - 新游戏/圣诞/95x95图标/拍照.bmp" />
		<Unit filename="resource/res_file - 新游戏/圣诞/95x95图标/摄像.bmp" />
		<Unit filename="resource/res_file - 新游戏/圣诞/95x95图标/游戏.bmp" />
		<Unit filename="resource/res_file - 新游戏/圣诞/95x95图标/照片.bmp" />
		<Unit filename="resource/res_file - 新游戏/圣诞/95x95图标/设置.bmp" />
		<Unit filename="resource/res_file - 新游戏/圣诞/95x95图标/音乐.bmp" />
		<Unit filename="resource/res_file - 新游戏/圣诞/bye.jpg" />
		<Unit filename="resource/res_file - 新游戏/圣诞/welcome.jpg" />
		<Unit filename="resource/res_file - 新游戏/圣诞/图标界面/图标界面-01.jpg" />
		<Unit filename="resource/res_file - 新游戏/圣诞/图标界面/图标界面-02.jpg" />
		<Unit filename="resource/res_file - 新游戏/圣诞/图标界面/图标界面-03.jpg" />
		<Unit filename="resource/res_file - 新游戏/圣诞/图标界面/图标界面-04.jpg" />
		<Unit filename="resource/res_file - 新游戏/圣诞/图标界面/图标界面-05.jpg" />
		<Unit filename="resource/res_file - 新游戏/圣诞/图标界面/图标界面-06.jpg" />
		<Unit filename="resource/res_file - 新游戏/圣诞/图标界面/图标界面-07.jpg" />
		<Unit filename="resource/res_file - 新游戏/圣诞/背景.jpg" />
		<Unit filename="resource/res_file - 新游戏/新年.rar" />
		<Unit filename="resource/res_file - 新游戏/新年/75x75图标/拍照.bmp" />
		<Unit filename="resource/res_file - 新游戏/新年/75x75图标/摄像.bmp" />
		<Unit filename="resource/res_file - 新游戏/新年/75x75图标/游戏.bmp" />
		<Unit filename="resource/res_file - 新游戏/新年/75x75图标/照片.bmp" />
		<Unit filename="resource/res_file - 新游戏/新年/75x75图标/背景.bmp" />
		<Unit filename="resource/res_file - 新游戏/新年/75x75图标/设置.bmp" />
		<Unit filename="resource/res_file - 新游戏/新年/75x75图标/音乐.bmp" />
		<Unit filename="resource/res_file - 新游戏/新年/95x95图标/录像.bmp" />
		<Unit filename="resource/res_file - 新游戏/新年/95x95图标/拍照.bmp" />
		<Unit filename="resource/res_file - 新游戏/新年/95x95图标/游戏.bmp" />
		<Unit filename="resource/res_file - 新游戏/新年/95x95图标/照片.bmp" />
		<Unit filename="resource/res_file - 新游戏/新年/95x95图标/设置.bmp" />
		<Unit filename="resource/res_file - 新游戏/新年/95x95图标/音乐.bmp" />
		<Unit filename="resource/res_file - 新游戏/新年/bye.jpg" />
		<Unit filename="resource/res_file - 新游戏/新年/welcome.jpg" />
		<Unit filename="resource/res_file - 新游戏/新年/背景.jpg" />
		<Unit filename="resource/res_file - 新游戏/新年/轮播图/新年-0原.jpg" />
		<Unit filename="resource/res_file - 新游戏/新年/轮播图/新年-1拍照.jpg" />
		<Unit filename="resource/res_file - 新游戏/新年/轮播图/新年-2录像.jpg" />
		<Unit filename="resource/res_file - 新游戏/新年/轮播图/新年-3图片.jpg" />
		<Unit filename="resource/res_file - 新游戏/新年/轮播图/新年-4设置.jpg" />
		<Unit filename="resource/res_file - 新游戏/新年/轮播图/新年-5游戏.jpg" />
		<Unit filename="resource/res_file - 新游戏/新年/轮播图/新年-6音乐.jpg" />
		<Unit filename="resource/user_res.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="resource/user_res.h" />
		<Unit filename="task_windows/menu_windows/inc/menu_api.h" />
		<Unit filename="task_windows/menu_windows/inc/menu_typedef.h" />
		<Unit filename="task_windows/menu_windows/src/mMenuSetting.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/mMenuSettingWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDateTimeMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDateTimeWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDefaultMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDefaultWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDelAllMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDelAllWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDelCurMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDelCurWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDelMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDelWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuFormatMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuFormatWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuItemMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuItemWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuLockCurMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuLockCurWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuOptionMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuOptionWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuSreenBrightMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuSreenBrightWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuSysVolumeMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuSysVolumeWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuUnlockAllMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuUnlockAllWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuUnlockCurMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuUnlockCurWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuVersionMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuVersionWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowNoFileMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowNoFileWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowSelfTestMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowSelfTestWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowTipsMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowTipsWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/msg_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/msg_api.h" />
		<Unit filename="task_windows/task_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_api.h" />
		<Unit filename="task_windows/task_bat_charge/inc/taskBatCharge.h" />
		<Unit filename="task_windows/task_bat_charge/src/taskBatCharge.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_bat_charge/src/taskBatChargeMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_bat_charge/src/taskBatChargeWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_common/inc/task_common.h" />
		<Unit filename="task_windows/task_common/src/sys_common_msg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_common/src/task_common.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_common/src/task_common_msg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_csource_game/inc/taskCGame.h" />
		<Unit filename="task_windows/task_csource_game/src/taskCGame.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_csource_game/src/taskCGameMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_csource_game/src/taskCGameWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_csource_game/src/taskGame2048Msg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_csource_game/src/taskGame2048Win.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_csource_game/src/taskGameCommon.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_csource_game/src/taskGameFighterMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_csource_game/src/taskGameFighterWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_csource_game/src/taskGameMazeMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_csource_game/src/taskGameMazeWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_csource_game/src/taskGamePushBoxMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_csource_game/src/taskGamePushBoxWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_csource_game/src/taskGameSnakeMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_csource_game/src/taskGameSnakeWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_main/inc/taskMain.h" />
		<Unit filename="task_windows/task_main/src/taskMain.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_main/src/taskMainMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_main/src/taskMainWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_nes_game/inc/taskNesGame.h" />
		<Unit filename="task_windows/task_play_audio/inc/taskPlayAudio.h" />
		<Unit filename="task_windows/task_play_audio/src/taskPlayAudio.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_play_audio/src/taskPlayAudioMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_play_audio/src/taskPlayAudioWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_play_mp3/inc/taskPlayMp3.h" />
		<Unit filename="task_windows/task_play_mp3/src/taskPlayMp3.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_play_mp3/src/taskPlayMp3MainMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_play_mp3/src/taskPlayMp3MainWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_play_video/inc/taskPlayVideo.h" />
		<Unit filename="task_windows/task_play_video/src/taskPlayVideo.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_play_video/src/taskPlayVideoMainMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_play_video/src/taskPlayVideoMainWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_play_video/src/taskPlayVideoSlideMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_play_video/src/taskPlayVideoSlideWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_play_video/src/taskPlayVideoThumbnallMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_play_video/src/taskPlayVideoThumbnallWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_poweroff/inc/taskPoweroff.h" />
		<Unit filename="task_windows/task_poweroff/src/taskPowerOff.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_record_audio/inc/taskRecordAudio.h" />
		<Unit filename="task_windows/task_record_audio/src/taskRecordAudio.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_record_audio/src/taskRecordAudioMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_record_audio/src/taskRecordAudioWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_record_photo/inc/taskRecordPhoto.h" />
		<Unit filename="task_windows/task_record_photo/src/taskRecordPhoto.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_record_photo/src/taskRecordPhotoMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_record_photo/src/taskRecordPhotoWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_record_video/inc/taskRecordVideo.h" />
		<Unit filename="task_windows/task_record_video/src/taskRecordVideo.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_record_video/src/taskRecordVideoMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_record_video/src/taskRecordVideoWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_sd_update/inc/taskSdUpdate.h" />
		<Unit filename="task_windows/task_sd_update/src/taskSdUpdate.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_sd_update/src/taskSdUpdateWin.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_setting/inc/taskSetting.h" />
		<Unit filename="task_windows/task_setting/src/taskSetting.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_usb_device/inc/taskUsbDevice.h" />
		<Unit filename="task_windows/task_usb_device/src/taskUsbDevice.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_usb_device/src/taskUsbDeviceMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_usb_device/src/taskUsbDeviceWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_version/inc/taskVersion.h" />
		<Unit filename="task_windows/task_version/src/taskVersion.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_version/src/taskVersionMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_version/src/taskVersionWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/windows_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/windows_api.h" />
		<Unit filename="user_config/inc/user_config_api.h" />
		<Unit filename="user_config/inc/user_config_typedef.h" />
		<Unit filename="user_config/inc/user_hardware_cfg_fgpa.h" />
		<Unit filename="user_config/inc/user_hardware_cfg_hx3302B_8405_V1.h" />
		<Unit filename="user_config/inc/user_hardware_cfg_hx3302B_X200.h" />
		<Unit filename="user_config/inc/user_hardware_cfg_hx3302B_demo.h" />
		<Unit filename="user_config/src/mbedtls_md5.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="user_config/src/user_config_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="user_config/src/user_config_tab.c">
			<Option compilerVar="CC" />
		</Unit>
		<Extensions>
			<lib_finder disable_auto="1" />
		</Extensions>
	</Project>
</CodeBlocks_project_file>
