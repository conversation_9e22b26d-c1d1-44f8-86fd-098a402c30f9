/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef NES_MAPPER_H
#define NES_MAPPER_H

/* The address of 8Kbytes unit of the ROM */
#define ROMPAGE(a)     		&nes_ROM[ (a) * 0x2000 ]
/* From behind the ROM, the address of 8kbytes unit */
#define ROMLASTPAGE(a) 		&nes_ROM[ nes_header.byRomSize * 0x4000 - ( (a) + 1 ) * 0x2000 ]
/* The address of 1Kbytes unit of the VROM */
#define VROMPAGE(a)    		&nes_VROM[ (a) * 0x400 ]
/* The address of 1Kbytes unit of the CRAM */
#define CRAMPAGE(a)   		&nes_PPURAM[ 0x0000 + ((a)&0x1F) * 0x400 ]
/* The address of 1Kbytes unit of the VRAM */
#define VRAMPAGE(a)    		&nes_PPURAM[ 0x2000 + (a) * 0x400 ]
/* Translate the pointer to ChrBuf into the address of Pattern Table */ 
#define PATTBL(a)      		( ( (a) - nes_ChrBuf ) >> 2 )

/* Initialize Mapper */
extern void (*nes_mapper_Init)(void);
/* Write to Mapper */
extern void (*nes_mapper_WriteMapper)( WORD wAddr, BYTE byData );
/* Write to SRAM */
extern void (*nes_mapper_WriteSram)( WORD wAddr, BYTE byData );
/* Write to Apu */
extern void (*nes_mapper_WriteApu)( WORD wAddr, BYTE byData );
/* Read from Apu */
extern BYTE (*nes_mapper_ReadApu)( WORD wAddr );
/* Callback at VSync */
extern void (*nes_mapper_VSync)(void);
/* Callback at HSync */
extern void (*nes_mapper_HSync)(void);
/* Callback at PPU read/write */
extern void (*nes_mapper_RwPPU)( WORD wAddr );
/* Callback at Rendering Screen 1:BG, 0:Sprite */
extern void (*nes_mapper_RenderScreen)( BYTE byMode );

typedef struct NESMAPPER_TAB_S{
	void (*Init)(void);
	u32  addr;
	u32  len;
}NESMAPPER_TAB_T;

/*******************************************************************************
* Function Name  : Mapper_Init
* Description    : Mapper_Init 
* Input          : 
* Output         : none                                            
* Return         : 
*******************************************************************************/
//NES_TEXT_SECTION
int Mapper_Init( void );
/*******************************************************************************
* Function Name  : Mapper_Init
* Description    : Mapper_Init 
* Input          : 
* Output         : none                                            
* Return         : 
*******************************************************************************/
void Mapper_Uinit( void );
#endif
