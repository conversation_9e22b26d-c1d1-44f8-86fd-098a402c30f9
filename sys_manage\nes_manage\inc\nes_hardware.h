/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef NES_HARDWARE_H
#define NES_HARDWARE_H

#define NES_DEBUG_EN		0
typedef struct NESHEAD_S
{
	BYTE byID[ 4 ]; //字符串“NES^Z”用来识别.NES文件 
	BYTE byRomSize; //16kB ROM的数目 		
	BYTE byVRomSize; //8kB VROM的数目	
	BYTE byInfo1;    //D0：1＝垂直镜像，0＝水平镜像
					 // D1：1＝有电池记忆，SRAM地址$6000-$7FFF
					 // D2：1＝在$7000-$71FF有一个512字节的trainer 
					 // D3：1＝4屏幕VRAM布局 
					 //  D4－D7：ROM Mapper的??	  
	BYTE byInfo2;    // D0－D3：保留，必须是0（准备作为副Mapper号^_^）
	                 // D4－D7：ROM Mapper的高4位 	
	BYTE byReserve[ 8 ]; // 保留，必须是0 		
	//OM段升序排列，如果存在trainer，它的512字节摆在ROM段之前 
	//VROM段, 升序排列 
}NESHEAD_T;
// Value and Flag Data
typedef struct VALUE_TABLE_S
{
	BYTE byValue;
	BYTE byFlag;
}VALUE_TABLE_T;


extern BYTE 			nes_RAM[ NES_RAM_SIZE ];	/* RAM 8K*/
extern BYTE 			nes_SRAM[ NES_SRAM_SIZE ];	/* SRAM 8K*/
extern BYTE 			nes_PPURAM[ NES_PPURAM_SIZE ];	/* PPU RAM 16K*/
extern BYTE 			nes_SPRRAM[ NES_SPRRAM_SIZE ];	/* Sprite RAM 256*/
extern BYTE 			nes_PalTable[ 32 ];
extern BYTE 			nes_g_byTestTable[ 256 ];	/*A table for the test*/
extern VALUE_TABLE_T	nes_g_ASLTable[ 256 ];		/*A table for ASL*/
extern VALUE_TABLE_T 	nes_g_LSRTable[ 256 ];		/*A table for LSR*/
extern VALUE_TABLE_T 	nes_g_ROLTable[ 2 ][ 256 ];	/*A table for ROL*/
extern VALUE_TABLE_T 	nes_g_RORTable[ 2 ][ 256 ];	/*A table for ROR*/	
extern NES_DAC_OP_T*   nes_dacOp;
extern BYTE* 			nes_ChrBuf;	//BYTE 		ChrBuf[ NES_CHR_BUF_SIZE ];/* Character Buffer 32K*/
extern BYTE* 			nes_SRAMBANK;
extern BYTE* 			nes_DRAM;
extern BYTE* 			nes_ROM;		/* ROM BANK ( 8Kb * 4 ) */
extern BYTE* 			nes_VROM;
extern BYTE* 			nes_ROMBANK0;
extern BYTE* 			nes_ROMBANK1;
extern BYTE* 			nes_ROMBANK2;
extern BYTE* 			nes_ROMBANK3;
extern BYTE* 			nes_ROMBANK[4];	
extern BYTE* 			nes_PPUBANK[ 16 ];	/* PPU BANK ( 1Kb * 16 ) */
extern BYTE* 			nes_pbyPrevBank[ 8 ];
extern BYTE 			nes_APU_Reg[ 24 ]; /* APU Register */
extern BYTE 			nes_PPU_R0;		/* PPU Register */
extern BYTE 			nes_PPU_R1;
extern BYTE 			nes_PPU_R2;
extern BYTE 			nes_PPU_R3;
extern BYTE 			nes_PPU_R7;
extern BYTE 			nes_ROM_Mirroring;	/* Mirroring 0:Horizontal 1:Vertical */
extern BYTE 			nes_ROM_SRAM;		/* It has SRAM */
extern BYTE 			nes_ROM_Trainer;	/* It has Trainer */
extern BYTE 			nes_ROM_FourScr;	/* Four screen VRAM  */
extern BYTE 			nes_FrameIRQ_Enable;/* Frame IRQ ( 0: Disabled, 1: Enabled )*/
extern BYTE 			nes_ChrBufUpdate;	/* Update flag for ChrBuf */
extern BYTE 			nes_APU_Mute;		/* APU Mute ( 0:OFF, 1:ON ) */
extern BYTE 			nes_byVramWriteEnable;	/* VRAM Write Enable ( 0: Disable, 1: Enable ) */
extern BYTE 			nes_PPU_Latch_Flag;	/* PPU Address and Scroll Latch Flag*/
extern BYTE 			nes_PPU_UpDown_Clip;/* Up and Down Clipping Flag ( 0: non-clip, 1: clip ) */	 
extern BYTE 			nes_MapperNo;		/* Mapper Number */
extern BYTE 			nes_PPU_Scr_V;		/* Vertical scroll value */
extern BYTE 			nes_PPU_Scr_V_Next;
extern BYTE 			nes_PPU_Scr_V_Byte;
extern BYTE 			nes_PPU_Scr_V_Byte_Next;
extern BYTE 			nes_PPU_Scr_V_Bit;
extern BYTE 			nes_PPU_Scr_V_Bit_Next;
extern BYTE 			nes_PPU_Scr_H;		/* Horizontal scroll value */
extern BYTE 			nes_PPU_Scr_H_Next;
extern BYTE 			nes_PPU_Scr_H_Byte;
extern BYTE 			nes_PPU_Scr_H_Byte_Next;
extern BYTE 			nes_PPU_Scr_H_Bit;
extern BYTE 			nes_PPU_Scr_H_Bit_Next;
extern BYTE 			nes_PPU_Increment;			/* The increase value of the PPU Address */
extern BYTE 			nes_PPU_SP_Height;			/* Sprite Height */
extern BYTE 			nes_PPU_NameTableBank;		/* Name Table Bank */
extern BYTE 			nes_reserve1;	
extern WORD 			nes_PC_MASK;				/*PC MASK*/
extern WORD 			nes_PPU_Addr;				/* PPU Address */
extern WORD 			nes_PPU_Temp;				/* PPU Address */
extern WORD 			nes_PPU_Scanline;			/* Current Scanline */
extern BYTE 			nes_PPU_ScanTable[ 263 + 1];	/* Scanline Table */
extern BYTE 			nes_pSprBuf[ NES_DISP_WIDTH + 7 + 1]; 
extern BYTE* 			nes_PPU_BG_Base;			/* BG Base Address */
extern BYTE* 			nes_PPU_SP_Base;			/* Sprite Base Address */
extern WORD	 		    nes_SpriteJustHit;			/* Sprite #0 Scanline Hit Position */
extern WORD  			nes_FrameWidth;				/******Display and Others resouces ************/ 
extern WORD  			nes_FrameHeight;
extern WORD  			nes_FrameX;				
extern WORD  			nes_FrameY;
extern WORD 			nes_FrameStep;
extern BYTE 			nes_FrameSkip;
extern BYTE 			nes_FrameCnt;
extern BYTE*  			nes_WorkFrame;
extern DWORD 			nes_PAD1_Latch;				/* Pad data */
extern DWORD 			nes_PAD2_Latch;
extern DWORD 			nes_PAD_System;
extern BYTE 			nes_PAD1_Bit;
extern BYTE 			nes_PAD2_Bit;	
extern WORD 			nes_PC;						/*K6502 CPU*/
extern WORD 			nes_g_wPassedClocks;		/*The number of the clocks that it passed*/
extern BYTE 			nes_SP; 
extern BYTE 			nes_F;
extern BYTE 			nes_A;
extern BYTE 			nes_X;
extern BYTE 			nes_Y;
extern BYTE 			nes_IRQ_State;				/*The state of the IRQ pin*/
extern BYTE 			nes_IRQ_Wiring;				/*Wiring of the IRQ pin*/
extern BYTE 			nes_NMI_State;				/*The state of the NMI pin*/
extern BYTE 			nes_NMI_Wiring;				/*Wiring of the NMI pin*/
extern BYTE 			nes_reserve;		
extern NESHEAD_T		nes_header;
extern void*			nes_exram;
extern DWORD			nes_curTime;
extern DWORD			nes_lastTime;
extern DWORD			nes_printTime;
extern DWORD			nes_cnt;
extern DWORD			pad_cnt;
extern DWORD			pad_last_event;
extern lcdshow_frame_t *nes_workbuff;

extern u32 				NesPalette_tab[];
/*******************************************************************************
* Function Name  : nes_setup_mirror
* Description    : Set up a Mirroring of Name Table
* Input          : int nType          (Read)
 *      Mirroring Type
 *        0 : Horizontal
 *        1 : Vertical
 *        2 : One Screen 0x2400
 *        3 : One Screen 0x2000
 *        4 : Four Screen
 *        5 : Special for Mapper #233
 *
* Output         : none                                            
* Return         : 
*******************************************************************************/
//NES_TEXT_SECTION
void nes_setup_mirror( int nType );
/*******************************************************************************
* Function Name  : nes_setup_chr
* Description    : Develop character data
* Input          : none
* Output         : none                                            
* Return         : 
*******************************************************************************/
//NES_TEXT_SECTION
void nes_setup_chr(void);
/*******************************************************************************
* Function Name  : nes_set_ppu_banks
* Description    : Develop character data
* Input          : none
* Output         : none                                            
* Return         : 
*******************************************************************************/
//NES_TEXT_SECTION
void nes_set_ppu_banks(void);
/*******************************************************************************
* Function Name  : nes_set_ppu_banks_by8
* Description    : Develop character data
* Input          : none
* Output         : none                                            
* Return         : 
*******************************************************************************/
//NES_TEXT_SECTION
void nes_set_ppu_banks_by8(DWORD addr);
/*******************************************************************************
* Function Name  : nes_set_cpu_banks_by4
* Description    : nes_set_cpu_banks_by4
* Input          : none
* Output         : none                                            
* Return         : 
*******************************************************************************/
//NES_TEXT_SECTION
void nes_set_cpu_banks_by4(DWORD addr);
/*******************************************************************************
* Function Name  : nes_set_cpu_banks_by4
* Description    : nes_set_cpu_banks_by4
* Input          : none
* Output         : none                                            
* Return         : 
*******************************************************************************/
NES_TEXT_SECTION
void nes_set_cpu_banks_by2(DWORD addr);
/*******************************************************************************
* Function Name  : nes_set_cpu_banks_by4
* Description    : nes_set_cpu_banks_by4
* Input          : none
* Output         : none                                            
* Return         : 
*******************************************************************************/
void nes_set_cpu_banks_0(void);
/*******************************************************************************
* Function Name  : nes_set_cpu_banks_by4
* Description    : nes_set_cpu_banks_by4
* Input          : none
* Output         : none                                            
* Return         : 
*******************************************************************************/
//NES_TEXT_SECTION
void nes_set_cpu_banks_1(void);
/*******************************************************************************
* Function Name  : nes_set_cpu_banks_by4
* Description    : nes_set_cpu_banks_by4
* Input          : none
* Output         : none                                            
* Return         : 
*******************************************************************************/
//NES_TEXT_SECTION
void nes_set_cpu_banks_2(void);

/*******************************************************************************
* Function Name  : nes_Cycle
* Description    : The loop of emulation 
* Input          : none
* Output         : none                                            
* Return         :  0 : Normally
 *   				-1 : Exit an emulation
*******************************************************************************/
//NES_TEXT_SECTION
void nes_Cycle(void);
/*******************************************************************************
* Function Name  : nes_Reset
* Description    : Initialize Resources, PPU and Mapper.Reset CPU.
* Input          : 
* Output         : none                                            
* Return         : 0 : Normally
 *    			  -1 : Non support mapper
*******************************************************************************/
//NES_TEXT_SECTION
int nes_Reset(void);
#endif
