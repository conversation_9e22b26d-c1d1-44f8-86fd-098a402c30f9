/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"


/*******************************************************************************
* Function Name  : res_ascii_get
* Description    : ascii table get
* Input          : char c : char value to draw
* 				   unsigned char *width: font width
*                  unsigned char *heigth:font height
*                  unsigned char font: font num
* Output         : None
* Return         : None
*******************************************************************************/
const u8 *res_ascii_get(u8 c,u16 *width,u16 *heigth,u8 font)
{
	const u8 *table;
	u8 index;
	if(c<32 || c == 34 || c>126)
		return NULL;
	if(c<34)
		index = c-32;
	else
		index = c-33;
	font&=0x0f;

	switch(font)
	{
		case RES_FONT_NUM0: table = ascii_num0_table[index]; break;
		case RES_FONT_NUM1: table = ascii_num1_table[index]; break;
		case RES_FONT_NUM2:	table = ascii_num2_table[index]; break;
		case RES_FONT_NUM3:	table = ascii_num3_table[index]; break;
		case RES_FONT_NUM4:	table = ascii_num4_table[index]; break;
		default: return NULL;
	}
	if(width)
		*width = table[0];
	if(heigth)
		*heigth = table[1];
		
	return &table[2];    
}
/*******************************************************************************
* Function Name  : res_ascii_get
* Description    : ascii table get
* Input          : char c : char value to draw
* 				   unsigned char *width: font width
*                  unsigned char *height:font height
*                  unsigned char font: font num
* Output         : None
* Return         : None
*******************************************************************************/
const u8 *res_gb2312_get(u16 s,u16 *width,u16 *height)
{
#if GB2312_EN
	const u8 * table;
	u32 index;
	u8 c1, c2;
	c1 = (s>>8)&0xff; //����+0xA0
	c2 = (s) &0xff;  //λ��+0xA0
	
	if((c1<0xA1)||(c2<0xA1)||(c1>0xFE)||(c2>0xFE))
		return  NULL;
	if(width)
		*width = gb2312_wh[0];
	if(height)
		*height = gb2312_wh[1];
	index = ((c1-0xA0-1)*94+(c2-0xA0-1))*gb2312_wh[2];
	table = &gb2312_table[index];
	
		
	return table;
#else
	return NULL;
#endif
}
/*******************************************************************************
* Function Name  : res_getAsciiCharSize
* Description    : get ascii char size        
* Input          : char c : char value
				   unsigned short *width : width
				   unsigned short *heigth:height
* Output        : 
* Return         : int -1 : fail
                            0 :success  
*******************************************************************************/
int res_getAsciiCharSize(u8 c,u16 *width,u16 *heigth,u8 font)
{
	if(res_ascii_get(c,width,heigth,font) == NULL)
		return -1;
	return 0;
}
/*******************************************************************************
* Function Name  : res_getGB2312Size
* Description    : res_getGB2312Size       
* Input          : u16 s : 
				   unsigned short *width : width
				   unsigned short *heigth:height
* Output        : 
* Return         : int -1 : fail
                            0 :success  
*******************************************************************************/
int res_getGB2312Size(u16 s,u16 *width,u16 *heigth)
{
	if(res_gb2312_get(s,width,heigth) == NULL)
		return -1;
	return 0;
}
/*******************************************************************************
* Function Name  : res_getAsciiStringSize
* Description    : get ascii string size        
* Input          : char c : string
				   u16 *width : width
				   u16 *heigth:height
* Output        : 
* Return         : int -1 : fail
                            0 :success  
*******************************************************************************/
int res_getAsciiStringSize(u8 *str,u16 *width,u16 *heigth,u8 font)
{
    u16 w,h;
    u16 tw,th;
	u32 num = 0;
	tw = 0;
	th = 0;

	while(*str)
	{
		if(str[0] == 0x0D && str[1] == 0x0A)
		{
			break;
		}
		if(*str > 0xA0)
		{
			if(res_getGB2312Size((str[0] << 8)|str[1], &w,&h) >= 0)
			{
				tw += w;
				if(h > th)
					th = h;
			}
			str += 2;
			num++;
		}else if(res_getAsciiCharSize(*str++,&w,&h,font)>=0)
		{
			tw += w;
			if(h > th)
				th = h;
		}
		num++;
	}

	if(width)
		*width = tw;
	if(heigth)
		*heigth = th;
	return num;
}