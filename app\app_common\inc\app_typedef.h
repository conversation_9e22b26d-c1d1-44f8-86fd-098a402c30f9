/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  APP_TYPEDEF_H
    #define  APP_TYPEDEF_H

#define LCDSHOW_WIN_CSI_BOT		 (0 << 4) //SENSOR画面不变
#define LCDSHOW_WIN_CSI_TOP 	 (1 << 4) //SENSOR画面逐渐放大或缩小

#define LCDSHOW_WIN_SCALER_DOWN	 (0 << 5) //小窗口画面逐渐缩小
#define LCDSHOW_WIN_SCALER_UP 	 (1 << 5) //小窗口画面逐渐放大
typedef enum
{
    LCDSHOW_NOT_SUB_WIN 	= 0,
	//SENSOR画面不变，小窗口画面逐渐放大或缩小
	LCDSHOW_WIN_LEFTTOP 	= (1 << 0)|LCDSHOW_WIN_CSI_BOT|LCDSHOW_WIN_SCALER_DOWN,
    LCDSHOW_WIN_RIGHTTOP	= (2 << 0)|LCDSHOW_WIN_CSI_BOT|LCDSHOW_WIN_SCALER_DOWN,
	LCDSHOW_WIN_LEFTDOWN	= (3 << 0)|LCDSHOW_WIN_CSI_BOT|LCDSHOW_WIN_SCALER_DOWN,
	LCDSHOW_WIN_RIGHTDONW	= (4 << 0)|LCDSHOW_WIN_CSI_BOT|LCDSHOW_WIN_SCALER_DOWN,
	LCDSHOW_WIN_LEFT		= (5 << 0)|LCDSHOW_WIN_CSI_BOT|LCDSHOW_WIN_SCALER_DOWN,
	LCDSHOW_WIN_RIGHT		= (6 << 0)|LCDSHOW_WIN_CSI_BOT|LCDSHOW_WIN_SCALER_DOWN,
	LCDSHOW_WIN_TOP			= (7 << 0)|LCDSHOW_WIN_CSI_BOT|LCDSHOW_WIN_SCALER_DOWN,
	LCDSHOW_WIN_DOWN		= (8 << 0)|LCDSHOW_WIN_CSI_BOT|LCDSHOW_WIN_SCALER_DOWN,
	LCDSHOW_WIN_CENTER		= (9 << 0)|LCDSHOW_WIN_CSI_BOT|LCDSHOW_WIN_SCALER_DOWN,
	//SENSOR画面逐渐放大或缩小
	LCDSHOW_WIN_CSI_CENTER_UP= (1 << 0)|LCDSHOW_WIN_CSI_TOP|LCDSHOW_WIN_SCALER_UP,
	LCDSHOW_WIN_CSI_CENTER_DOWN= (2 << 0)|LCDSHOW_WIN_CSI_TOP|LCDSHOW_WIN_SCALER_UP,
	LCDSHOW_WIN_CSI_RIGHTTOP_UP= (3 << 0)|LCDSHOW_WIN_CSI_TOP|LCDSHOW_WIN_SCALER_UP,
	LCDSHOW_WIN_MAX			= (15 << 0)|LCDSHOW_WIN_CSI_TOP|LCDSHOW_WIN_SCALER_UP,
	LCDSHOW_WIN_DISABLE		= 0xff,
	
}LCDSHOW_WINAB_MODE;
typedef enum
{
    KID_FRAME_DISTROY = 0,
    KID_FRAME_CREATE,
    KID_FRAME_SHOWON,
    KID_FRAME_SHOWOFF,
	KID_FRAME_SHOWNEXT,
	KID_FRAME_SHOWPREV,
	
}KID_FRAME_STAT;
typedef enum
{
    SENSOR_FILTER_CHANGE_NONE = 0,
    SENSOR_FILTER_CHANGE_NEXT,
	SENSOR_FILTER_CHANGE_PREV,	
}SENSOR_FILTER_STAT;
typedef enum
{
    SENSOR_MAGIC_CHANGE_NONE = 0,
    SENSOR_MAGIC_CHANGE_NEXT,
	SENSOR_MAGIC_CHANGE_PREV,	
}SENSOR_MAGIC_STAT;
typedef enum
{
    SENSOR_LENS_CHANGE_NONE = 0,
    SENSOR_LENS_CHANGE_NEXT,
	SENSOR_LENS_CHANGE_PREV,	
}SENSOR_LENS_STAT;
typedef struct LCDSHOW_CTRL_S
{
	INT16U video_layer_en;
	int	   win_step;
	INT16U ui_layer_en;
	

}LCDSHOW_CTRL_T;



typedef struct System_Ctrl_S
{
	//dev handler fd
	INT8S dev_fd_battery;  		// dev fd for battery check
	INT8S dev_fd_gsensor; 		// dev fd for gsensor check
	INT8S dev_fd_ir;			// dev fd for ir ctrl
	INT8S dev_fd_key;      		// dev fd for key check
		
	INT8S dev_fd_lcd;      		// dev fd for lcd ctrl	
	INT8S dev_fd_led;      		// dev fd for led ctrl
	INT8S dev_fd_sensor;		// dev fd for sensor
	INT8S dev_fd_sdc;   		// dev fd for sd card check
	
	INT8S dev_fd_dusb;      	// dev fd for usb dev check
	//dev stat 
	INT8U dev_stat_power;		// power on flag.  key/dcin/gsensor/RTC/...
	INT8U dev_stat_battery; 	// battery state;   
	INT8U dev_stat_gsensorlock; // g sensor active flag

	INT8U dev_stat_ir;			//ir flag: 0: ir close , 1: ir auto 
	INT8U dev_stat_keysound;	//keysound flag:0: keysound off, 1: keysound play
	INT8U dev_stat_lcd;			//0: lcd sreen off, 1: lcd sreen on
	INT8U dev_stat_led;			//0: led off, 1: led on

	INT8U dev_stat_sensor;
	INT8U dev_stat_sdc;			// sdcard stat.no sdc,sdc unstable,sdc error,sdc full,sdc normal
	INT8U dev_dusb_stat;        // usb dev stat. no usb,dcin,usb-pc
	INT8U dev_dusb_out;			//software disable usb20 func
	
	XMsgQ 	*sysQ;
	XWork_T *recordIncrease1S;
	INT32U powerOnTime;			// sys power on time: sec
	INT32U rec_remain_time;	    // rec remain time:sec
	INT32U rec_looptime;		// rec loop time: sec	
	INT32U rec_show_time;       // rec cur show time: sec
	INT32U rec_md_time;			// motion rec time
	
	INT32U play_total_time;		//play total time:msec
	INT32U play_cur_time;		//play cur time: msec
	INT32U play_last_time;		//play last time: msec
	
	INT32U sdc_freesize;  		//sdc free size KB
	INT32U fs_clustsize; 		// fs cluster size
	INT32S file_cnt;  		// file index for playback
	INT32S file_index;  		// file index for playback
	int    file_type;
	INT32U curVolume;
	
	
    INT32S avi_list;
	//INT32S avia_list;
	//INT32S avib_list;
	INT32S jpg_list;  // avi & jpg file list handle
	INT32S wav_list;        // wav file list
	INT32S mp3_list;     // mp3 list
	INT32S nes_list;     // nes list
	INT32S spi_jpg_list;
	//INT32S mp3dir_list;     // mp3 dir list
	//INT32S mp3file_list;     // mp3 file list
	
	INT32U	file_premalloc;
	FILELIST_NAME_T new_fname;
	FILELIST_NAME_T old_fname;
	FILELIST_NAME_T jpg_fname;
	char   file_fullname[FILE_PATH_LEN+FILE_NAME_LEN+1];
	char   version_str[32];	
	
	INT32S kid_frame_index; // for kid frame display (-1: NO FRAME, 0: FRAME0 ....) 
	INT32S kid_frameSum;	
	INT32U kid_frame_stat;	//KID_FRAME_STAT

	INT32S sensor_filter_index;
	INT32S sensor_magic_index;
	INT32S sensor_lens_index;
	INT32U lcd_scaler_level;
	INT32U lcd_scaler_level_max;
	INT16U lcd_scaler_w_step;
	INT16U lcd_scaler_h_step;
	INT32U winChangeEnable;
	INT32U lcd_scaler_X;

	INT8U	gui_flush_sta;
	INT32U UserlastTime;
	INT8U havedPwon;
}System_Ctrl_T;

















#endif

