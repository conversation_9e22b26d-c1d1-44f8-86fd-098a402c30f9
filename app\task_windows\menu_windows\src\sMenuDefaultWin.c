/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	DEFAULT_RECT_ID =0,
	DEFAULT_TIPS_ID,
	DEFAULT_SELECT_ID
};
UNUSED ALIGNED(4) const widgetCreateInfor defaultWin[] =
{
	createFrameWin(						 	Rx(58),	Ry(58), Rw(204),Rh(124),SMENU_FRAME_COLOR,WIN_ABS_POS),
	createRect(DEFAULT_RECT_ID,          	Rx(2),	<PERSON>y(2),  <PERSON>w(200),<PERSON>h(120),SMENU_UNSELECT_BG_COLOR),
	createStringIcon(DEFAULT_TIPS_ID,		Rx(2),	Ry(2), 	Rw(200),Rh(40), R_ID_STR_FMT_RESET, ALIGNMENT_CENTER, SMENU_UNSELECT_FN_COLOR,DEFAULT_FONT),
	createItemManage(DEFAULT_SELECT_ID,		Rx(10),	Ry(42),	Rw(184),Rh(80),	SMENU_UNSELECT_BG_COLOR),

	widgetEnd(),
};



