/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#include "../inc/hal.h"
typedef struct{
	u16  led_show_en;
	u16  led_show_sta;
	u32  led_show_interval;
}SPIUPDATA_LED_OP;
ALIGNED(4) SPIUPDATA_LED_OP spi_updata_led = {0};
/*******************************************************************************
* Function Name  : hal_spiUpdata_led_show_init
* Description	 : hal_spiUpdata_led_show_init
* Input 		 : interval: ms
* Output		 : None
* Return		 : none
*******************************************************************************/
SDRAM_TEXT_SECTION
void hal_spiUpdata_led_show(u8 state)
{
	if(spi_updata_led.led_show_en)
	{
		if(state == 0) //off
		{
			spi_updata_led.led_show_sta = 0;
			hx330x_timerTickStop();
		}else if(state == 1) //ON
		{
			spi_updata_led.led_show_sta = 1;
			hx330x_timerTickStart();
		}else 
		{
			if(hx330x_timerTickCount() < spi_updata_led.led_show_interval)
			{
				return;
			}else
			{
				spi_updata_led.led_show_sta ^= 1;
				hx330x_timerTickStop();
				hx330x_timerTickStart();
			}
		}
		if(hardware_setup.led_valid == 0)
		{
			hal_gpioWrite(hardware_setup.led_ch, hardware_setup.led_pin,spi_updata_led.led_show_sta ? GPIO_LOW:GPIO_HIGH);
		}else
		{
			hal_gpioWrite(hardware_setup.led_ch, hardware_setup.led_pin,spi_updata_led.led_show_sta ? GPIO_HIGH:GPIO_LOW);
		}
		
	}
		
}
/*******************************************************************************
* Function Name  : hal_spiUpdata_led_show_init
* Description	 : hal_spiUpdata_led_show_init
* Input 		 : interval: ms
* Output		 : None
* Return		 : none
*******************************************************************************/
SDRAM_TEXT_SECTION 
void hal_spiUpdata_led_show_init(u32 interval)
{
	if(hardware_setup.boot_uart_en == 0 && hardware_setup.led_en)
	{
		spi_updata_led.led_show_interval = (hardware_setup.sys_clk/1000) * interval;
		spi_updata_led.led_show_en		 = 1;
		hal_gpioInit(hardware_setup.led_ch, hardware_setup.led_pin, GPIO_OUTPUT,GPIO_PULL_UP);
		hal_spiUpdata_led_show(1);	
	}

	
}
/*******************************************************************************
* Function Name  : hal_spiInit
* Description	 : spi initial for manual mode
* Input 		 : none
* Output		 : None
* Return		 : none
*******************************************************************************/
SDRAM_TEXT_SECTION 
void hal_spiUpdata_led_show_uinit(void)
{
	if(hardware_setup.boot_uart_en == 0 && hardware_setup.led_en)
	{
		hal_spiUpdata_led_show(0);
		spi_updata_led.led_show_en		 = 0;
	}
	
}
/*******************************************************************************
* Function Name  : hal_spiInit
* Description	 : spi initial for manual mode
* Input 		 : none
* Output		 : None
* Return		 : none
*******************************************************************************/
SDRAM_TEXT_SECTION
void hal_spiManualInit(void)
{
    hx330x_spi0ManualInit(24000000,HAL_CFG_SPI_BUS_MODE);   
}
/*******************************************************************************
* Function Name  : hal_spiAutoModeInit
* Description	 : spi initial for auto mode
* Input 		 : none
* Output		 : None
* Return		 : none
*******************************************************************************/
SDRAM_TEXT_SECTION
void hal_spiAutoModeInit(void)
{
    hx330x_spi0AutoModeInit(HAL_CFG_SPI_BAUD_RATE,HAL_CFG_SPI_BUS_MODE, HAL_CFG_SPI_READ_MODE);   
}
/*******************************************************************************
* Function Name  : hal_spiModeSwitch
* Description	 : switch spi mode between auto mode and manual mode, spi write should switch to manual mode
* Input 		 : bool swc
* Output		 : None
* Return		 : s32 : >=0 success 
*******************************************************************************/
SDRAM_TEXT_SECTION 
void hal_spiModeSwitch(u8 auto_mode, u8 ie_critial)
{
	if(auto_mode == 0)
	{
		//exit automode
		if(ie_critial)
		{
			__LGIE_DIS__();
			__HGIE_DIS__();
		}
		hal_spi0ExitAutoMode();
		hal_spiManualInit();
	}
	else
	{
		hal_spiAutoModeInit();
		if(ie_critial)
		{
			__LGIE_EN__();
			__HGIE_EN__();
		}
	}
}
/*******************************************************************************
* Function Name  : hal_spiSendByte
* Description	 : spi send one byte data
* Input 		 : u8 : byte
* Output		 : None
* Return		 : none
*******************************************************************************/
/*SDRAM_TEXT_SECTION
void hal_spiSendByte(u8 byte)
{
     hx330x_spi0SendByte(byte);
}*/
/*******************************************************************************
* Function Name  : hal_spiSend
* Description	 : spi send data using DMA
* Input 		 : u32 addr : buffer addr
                    u32 len   : data length
* Output		 : None
* Return		 : none
*******************************************************************************/
/*SDRAM_TEXT_SECTION
void hal_spiSend(u32 addr,u32 len)
{
    hx330x_spi0Send0((void *)addr,len);
}*/
/*******************************************************************************
* Function Name  : hal_spiRecvByte
* Description	 : spi recv one byte data
* Input 		 : none
* Output		 : None
* Return		 : u8 : 
*******************************************************************************/
/*SDRAM_TEXT_SECTION
u8 hal_spiRecvByte(void)
{
    return hx330x_spi0RecvByte();
}*/
/*******************************************************************************
* Function Name  : hal_spiRecv
* Description	 : spi recv data using DMA
* Input 		 : u32 addr : buffer addr
                    u32 len   : data length
* Output		 : None
* Return		 : none
*******************************************************************************/
/*SDRAM_TEXT_SECTION
void hal_spiRecv(u32 addr,u32 len)
{

    if(len&0x0f)
		len = (len&(~0x0f))+0x10;
    hx330x_spi0Recv0((void *)addr,len);
}*/
/*******************************************************************************
* Function Name  : hal_spiCSConfig
* Description	 : spi cs config
* Input 		 : u8 level : cs level
* Output		 : None
* Return		 : none
*******************************************************************************/
/*SDRAM_TEXT_SECTION
void hal_spiCSConfig(u8 level)
{
    hx330x_spi0CS0Config(level);
}*/
/*******************************************************************************
* Function Name  : hal_spiFlashReadID
* Description	 : spi flash read id
* Input 		 : none
* Output		 : None
* Return		 : u32 : id
*******************************************************************************/
SDRAM_TEXT_SECTION
u32 hal_spiFlashReadID(void)
{
	u32 u32Result=0;
	hal_spiModeSwitch(0, 1);	

	
	hal_spiCSConfig(0);					//CS LOW
	
	hal_spiSendByte(SF_READ_ID);
	u32Result = hal_spiRecvByte();
	u32Result = (u32Result<<8)+hal_spiRecvByte();
	u32Result = (u32Result<<8)+hal_spiRecvByte();
	hal_spiCSConfig(1);						//CS HIGH
	hal_spiModeSwitch(1, 1);
	return u32Result;	
}
/*******************************************************************************
* Function Name  : hal_spiFlashWriteEnable
* Description	 : spi flash write enable
* Input 		 : none
* Output		 : None
* Return		 : none
*******************************************************************************/
SDRAM_TEXT_SECTION
void hal_spiFlashWriteEnable(void)
{	
	hal_spiCSConfig(0);					//CS LOW
    hal_spiSendByte(SF_WRITE_ENABLE);
    hal_spiCSConfig(1);					//CS HIGH	

}
/*******************************************************************************
* Function Name  : hal_spiFlashWait
* Description	 : wait spi flash idle
* Input 		 : none
* Output		 : None
* Return		 : bool : true success 
                                  false   timeout
*******************************************************************************/
SDRAM_TEXT_SECTION
bool hal_spiFlashWait(void)
{
	volatile u32 iTOCnt = 0;
	
    hal_spiCSConfig(0);					//CS LOW
    hal_spiSendByte(SF_READ_STATUS);
	while (hal_spiRecvByte()&1)
	{
		hal_spiUpdata_led_show(2);
		hal_wdtClear();
		if ((iTOCnt++) > SF_STATUS_TIMEOUT)		//2s
		{
			hal_spiCSConfig(1);			//CS HIGH
			return false;
		}
	}
    hal_spiCSConfig(1);					//CS HIGH
	return true;		
}
/*******************************************************************************
* Function Name  : hal_spiFlashReadPage
* Description	 : read spi flash one page
* Input 		 : u32 addr : spi flash addr in byte
				   u32 buffer : buffer addr
* Output		 : None
* Return		 : None 
*******************************************************************************/
SDRAM_TEXT_SECTION
void hal_spiFlashReadPage(u32 addr,u32 buffer)
{
	u8 *buff = (u8 *)buffer;
    u32 i;
#if 0 //manual mode	
	hal_spiCSConfig(0);					//CS LOW
	hal_spiSendByte(SF_READ_DATA);
	hal_spiSendByte((addr>>16)&0xff);
	hal_spiSendByte((addr>>8 )&0xff);
	hal_spiSendByte((addr    )&0xff);

	if(HAL_CFG_SPI_DMA_USE==0)
	{
		for(i=0;i<SF_PAGE_SIZE;i++)
		{
			*buff++ = hal_spiRecvByte();
		}
	}
	else
	{
		hal_spiRecv((void*)buffer,SF_PAGE_SIZE);
	}

	hal_spiCSConfig(1);					//CS HIGH
#else //auto mode
	u8 * src = (u8 *)(addr | 0x6000000);
	i = SF_PAGE_SIZE;
	while(i--)
        *buff++ = *src++;
	hx330x_sysDcacheWback(buffer,SF_PAGE_SIZE);	
#endif
	return;	
}
/*******************************************************************************
* Function Name  : hal_spiFlashRead
* Description	 : read spi flash
* Input 		 : u32 addr : spi flash addr in byte
				   u32 buffer : buffer addr
				   u32 len   :  data length
* Output		 : None
* Return		 : None
*******************************************************************************/
SDRAM_TEXT_SECTION
void hal_spiFlashRead(u32 addr,u32 buffer,u32 len)
{
	u8 *buff = (u8 *)buffer;
    u32 len0 = len;
#if 0 //manual mode
	hal_spiCSConfig(0);					//CS LOW
	hal_spiSendByte(SF_READ_DATA);
	hal_spiSendByte((addr>>16)&0xff);
	hal_spiSendByte((addr>>8 )&0xff);
	hal_spiSendByte((addr    )&0xff);

	if((len<16) || (HAL_CFG_SPI_DMA_USE==0))
	{
		while(len--)
		{
			*buff++ = hal_spiRecvByte();
		}
		hx330x_sysDcacheWback(buffer,len0);
	}
	else
	{
        hal_spiRecv((void*)buffer,len);
	}

	hal_spiCSConfig(1);					//CS HIGH
#else //auto mode
	u8 * src = (u8 *)(addr | 0x6000000);
	while(len--)
	{
		hal_wdtClear();
		*buff++ = *src++;
	}
        
	hx330x_sysDcacheWback(buffer,len0);	
		
#endif

	return;
}
/*******************************************************************************
* Function Name  : hal_spiFlashWritePage
* Description	 : write spi flash one page
* Input 		 : u32 addr : spi flash addr in byte
				   u32 buffer : buffer addr
* Output		 : None
* Return		 : bool : true success 
*******************************************************************************/
SDRAM_TEXT_SECTION
bool hal_spiFlashWritePage(u32 addr,u32 buffer)
{
	u8 *buff = (u8 *)buffer;
    u32 i;
	addr &= ~(SF_PAGE_SIZE-1);  // page align
	hal_spiFlashWriteEnable();

	hal_spiCSConfig(0);					//CS LOW
	hal_spiSendByte(SF_WRITE_DATA);
	hal_spiSendByte((addr>>16)&0xff);
	hal_spiSendByte((addr>>8 )&0xff);
	hal_spiSendByte((addr    )&0xff);
	
	if(1)//HAL_CFG_SPI_DMA_USE==0)
	{
		for(i=0;i<SF_PAGE_SIZE;i++)
			hal_spiSendByte(buff[i]);
	}
	else
	{
		hal_spiSend((void*)buffer,SF_PAGE_SIZE);
	}
	
	hal_spiCSConfig(1);					//CS HIGH	

    return hal_spiFlashWait();
}
/*******************************************************************************
* Function Name  : hal_spiFlashWrite
* Description	 : write spi flash
* Input 		 : u32 addr : spi flash addr in byte
                    u32 buffer : buffer addr
                    u32 len   : data length
* Output		 : None
* Return		 : bool: true success 
*******************************************************************************/
SDRAM_TEXT_SECTION
bool hal_spiFlashWrite(u32 addr,u32 buffer,u32 len, u32 mode)
{
	int i,cnt;
	if(mode)
		hal_spiModeSwitch(0, 1);
	if(len&(SF_PAGE_SIZE-1))
	{
		len = (len&(~(SF_PAGE_SIZE-1)))+SF_PAGE_SIZE;// page align
	}
    addr &= ~(SF_PAGE_SIZE-1);  // page align
	
	cnt = len/SF_PAGE_SIZE;
	for(i=0;i<cnt;i++)
	{
		if(hal_spiFlashWritePage(addr,buffer) == false)
		{
			if(mode)
				hal_spiModeSwitch(1, 1);
			return false;
		}
		addr+=SF_PAGE_SIZE;
		buffer+=SF_PAGE_SIZE;
	}
	if(mode)
		hal_spiModeSwitch(1, 1);
	return true;
}
/*******************************************************************************
* Function Name  : hal_spiFlashWrite
* Description	 : write spi flash
* Input 		 : u32 addr : spi flash addr in byte
                    u32 buffer : buffer addr
                    u32 len   : data length
* Output		 : None
* Return		 : bool: true success 
*******************************************************************************/
SDRAM_TEXT_SECTION
bool hal_spiFlashWriteInManual(u32 addr,u32 buffer,u32 len)
{
	int i,cnt;
	//hal_spiModeSwitch(0, 1);
	if(len&(SF_PAGE_SIZE-1))
	{
		len = (len&(~(SF_PAGE_SIZE-1)))+SF_PAGE_SIZE;// page align
	}
    addr &= ~(SF_PAGE_SIZE-1);  // page align
	
	cnt = len/SF_PAGE_SIZE;
	for(i=0;i<cnt;i++)
	{
		if(hal_spiFlashWritePage(addr,buffer) == false)
		{
			//hal_spiModeSwitch(1, 1);
			return false;
		}
		addr+=SF_PAGE_SIZE;
		buffer+=SF_PAGE_SIZE;
	}
	//hal_spiModeSwitch(1, 1);
	return true;
}
/*******************************************************************************
* Function Name  : hal_spiFlashEraseSector
* Description	 : erase spi flash one sector
* Input 		 : u32 blockAddr : sector addr aglin in byte
* Output		 : None
* Return		 : bool : true success 
*******************************************************************************/
SDRAM_TEXT_SECTION
bool hal_spiFlashEraseSector(u32 sectorAddr, u32 mode)
{
	bool res;
	sectorAddr&=~(SF_SECTOR_SIZE-1);
	if(mode)
		hal_spiModeSwitch(0, 1);
	hal_spiFlashWriteEnable();
	hal_spiCSConfig(0);					//CS LOW
	hal_spiSendByte(SF_ERASE_SECTOR);
	hal_spiSendByte((sectorAddr>>16)&0xff);
	hal_spiSendByte((sectorAddr>>8 )&0xff);
	hal_spiSendByte((sectorAddr    )&0xff);
	hal_spiCSConfig(1);					//CS HIGH	
	res = hal_spiFlashWait();
	if(mode)
		hal_spiModeSwitch(1, 1);
    return res;
}
/*******************************************************************************
* Function Name  : hal_spiFlashEraseBlock
* Description	 : erase spi flash one block
* Input 		 : u32 blockAddr : block addr aglin in byte
* Output		 : None
* Return		 : bool : true success 
*******************************************************************************/
SDRAM_TEXT_SECTION
bool hal_spiFlashEraseBlock(u32 blockAddr)
{
	bool res;
	blockAddr&=~(SF_BLOCK_SIZE-1);
	hal_spiModeSwitch(0, 1);	
	hal_spiFlashWriteEnable();
	hal_spiCSConfig(0);					//CS LOW
	
	hal_spiSendByte(SF_ERASE_BLOCK);
	hal_spiSendByte((blockAddr>>16)&0xff);
	hal_spiSendByte((blockAddr>>8 )&0xff);
	hal_spiSendByte((blockAddr    )&0xff);
	
	hal_spiCSConfig(1);					//CS HIGH	
	res = hal_spiFlashWait();
	hal_spiModeSwitch(1, 1);
    return res;
}
/*******************************************************************************
* Function Name  : hal_spiFlashEraseChip
* Description	 : erase spi flash all chip
* Input 		 : 
* Output		 : None
* Return		 : bool : true success 
*******************************************************************************/
SDRAM_TEXT_SECTION
bool hal_spiFlashEraseChip(void)
{
	bool res;
	
	hal_spiFlashWriteEnable();
	hal_spiCSConfig(0);					//CS LOW

	hal_spiSendByte(SF_ERASE_CHIP);
	
	hal_spiCSConfig(1);					//CS HIGH	
	res = hal_spiFlashWait();
	//hal_spiModeSwitch(1, 0);
    return res;
}
/*******************************************************************************
* Function Name  : hal_spiFlashReadUniqueID
* Description	 : read flash unique id
* Input 		 : u8 *buffer
* Output		 : None
* Return		 : None
*******************************************************************************/
SDRAM_TEXT_SECTION
void hal_spiFlashReadUniqueID(u8 buffer[8])
{
	hal_spiModeSwitch(0, 1);
	hal_spiCSConfig(0);

	hal_spiSendByte(SF_RUID);
	// dummy
	hal_spiRecvByte();
	hal_spiRecvByte();
	hal_spiRecvByte();
	hal_spiRecvByte();
	
	u32 i;
	for (i = 0; i < 8; i++)
	{
		buffer[i] = hal_spiRecvByte();
	}

	hal_spiCSConfig(1);
	hal_spiModeSwitch(1, 1);
}
/*******************************************************************************
* Function Name  : hal_spiFlashReadOTP
* Description	 : read spi flash otp
* Input 		 : u32 otp_addr
				   u8 * buffer
				   u32 size
* Output		 : None
* Return		 : None
*******************************************************************************/
SDRAM_TEXT_SECTION
void hal_spiFlashReadOTP(u32 otp_addr, u8 * buffer, u32 size)
{
	hal_spiModeSwitch(0, 1);
	hal_spiCSConfig(0);

	hal_spiSendByte(SF_SECRR);
	hal_spiSendByte((otp_addr >> 16) & 0xff);
	hal_spiSendByte((otp_addr >> 8) & 0xff);
	hal_spiSendByte((otp_addr >> 0) & 0xff);
	// dummy
	hal_spiRecvByte();
	
	u32 i;
	for (i = 0; i < size; i++)
	{
		buffer[i] = hal_spiRecvByte();
	}

	hal_spiCSConfig(1);
	hal_spiModeSwitch(1, 1);
}

/*******************************************************************************
* Function Name  : hal_spiFlashReadManual
* Description	 : read spi flash
* Input 		 : u32 addr : spi flash addr in byte
				   u32 buffer : buffer addr
				   u32 len   :  data length
* Output		 : None
* Return		 : None
*******************************************************************************/
SDRAM_TEXT_SECTION
void hal_spiFlashReadManual(u32 addr,u32 buffer,u32 len)
{
	u8 *buff = (u8 *)buffer;
    u32 len0 = len;
#if 1 //manual mode
	hal_spiCSConfig(0);					//CS LOW
	hal_spiSendByte(SF_READ_DATA);
	hal_spiSendByte((addr>>16)&0xff);
	hal_spiSendByte((addr>>8 )&0xff);
	hal_spiSendByte((addr    )&0xff);

	if(1/*(len<16) || (HAL_CFG_SPI_DMA_USE==0)*/)
	{
		while(len--)
		{
			*buff++ = hal_spiRecvByte();
		}
		hx330x_sysDcacheWback(buffer,len0);
	}
	else
	{
        hal_spiRecv((void*)buffer,len);
	}

	hal_spiCSConfig(1);					//CS HIGH
#else //auto mode
	u8 * src = (u8 *)(addr | 0x6000000);
	while(len--)
	{
		hal_wdtClear();
		*buff++ = *src++;
	}
        
	hx330x_sysDcacheWback(buffer,len0);	
		
#endif

	return;
}
/*******************************************************************************
* Function Name  : hal_spiFlashWriteSR1
* Description	 : spi flash write save register
* Input 		 : 
* Output		 : None
* Return		 : None
*******************************************************************************/
SDRAM_TEXT_SECTION
bool hal_spiFlashWriteSR(u8 value)
{
	bool res;
	hal_spiModeSwitch(0, 1);
	hal_spiCSConfig(0);					//CS LOW

    hal_spiSendByte(0x50);

    hal_spiSendByte(SF_WRITE_STATUS);
    hal_spiSendByte(value);
    
    hal_spiCSConfig(1);
      
    res = hal_spiFlashWait();

	
	hal_spiModeSwitch(1, 1);

	return res;
}
/*******************************************************************************
* Function Name  : hal_spiFlashWriteSR1
* Description	 : spi flash read save register
* Input 		 : 
* Output		 : None
* Return		 : None
*******************************************************************************/
SDRAM_TEXT_SECTION
u8 hal_spiFlashReadSR1(void)
{

	hal_spiModeSwitch(0, 1);
    hal_spiCSConfig(0);
    hal_spiSendByte(SF_READ_STATUS);
    u8 val = hal_spiRecvByte();
    
    hal_spiCSConfig(1);
    hal_spiModeSwitch(1, 1);    
    return val;
}