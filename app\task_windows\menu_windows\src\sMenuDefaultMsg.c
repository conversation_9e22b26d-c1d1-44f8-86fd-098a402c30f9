/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuDefaultWin.c"

/*******************************************************************************
* Function Name  : getdefaultResInfor
* Description    : getdefaultResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : none
*******************************************************************************/
static uint32 getdefaultResInfor(u32 item,u32* image,u32* str)
{
	if(item==0)
	{
		if(image)
			*image 	= INVALID_RES_ID;
		if(str)
			*str	= R_ID_STR_COM_OK;
	}
	else if(item==1)
	{
		if(image)
			*image	= INVALID_RES_ID;
		if(str)
			*str	= R_ID_STR_COM_CANCEL;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : defaultKeyMsgOk
* Description    : defaultKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int defaultKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	DATE_TIME_T *rtcTime = hal_rtcTimeGet();
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,DEFAULT_SELECT_ID));
		if(item == 0)
		{
			userConfig_Reset();
			user_config_cfgSysAll();
		//	static char *datetime_str;
		//	datetime_str = task_com_datetime_from_version();
			rtcTime->year 	= hx330x_str2num(SysCtrl.version_str,4);//2022;
			rtcTime->month 	= hx330x_str2num(SysCtrl.version_str+4,2);//4;
			rtcTime->day 	= hx330x_str2num(SysCtrl.version_str+6,2);//1;
			rtcTime->hour 	= 0;
			rtcTime->min 	= 0;
			rtcTime->sec 	= 0;


			hal_rtcTimeSet(rtcTime);			
		}
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : defaultKeyMsgUp
* Description    : defaultKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int defaultKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,DEFAULT_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : defaultKeyMsgDown
* Description    : defaultKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int defaultKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManageNextItem(winItem(handle,DEFAULT_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : defaultKeyMsgPower
* Description    : defaultKeyMsgPower
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int defaultKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : defaultOpenWin
* Description    : defaultOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int defaultOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]defaultOpenWin\n");
	uiItemManageSetItemHeight(winItem(handle,DEFAULT_SELECT_ID),Rh(35));

	uiItemManageCreateItem(		winItem(handle,DEFAULT_SELECT_ID),uiItemCreateMenuOption,getdefaultResInfor,2);
	//uiItemManageSetRowSum(winItem(handle,DEFAULT_SELECT_ID),1,Rh(40));
	//uiItemManageSetColumnSumWithGap(winItem(handle,DEFAULT_SELECT_ID),0,2,Rw(100), Rw(0));

	//uiItemManageCreateItem(		winItem(handle,DEFAULT_SELECT_ID),uiItemCreateMenuOption,getdefaultResInfor,2);
#if 0
	uiItemManageSetCharInfor(	winItem(handle,DEFAULT_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_White);
	uiItemManageSetSelectColor(	winItem(handle,DEFAULT_SELECT_ID),R_ID_PALETTE_DoderBlue);
	uiItemManageSetUnselectColor(winItem(handle,DEFAULT_SELECT_ID),R_ID_PALETTE_Gray);
#else
	uiItemManageSetSelectColorEx(winItem(handle,DEFAULT_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER, SMENU_SELECT_FN_COLOR,   SMENU_SELECT_BG_COLOR);
	uiItemManageSetUnselectColorEx(winItem(handle,DEFAULT_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER, SMENU_UNSELECT_FN_COLOR, SMENU_UNSELECT_BG_COLOR);
#endif
	


	uiItemManageSetCurItem(winItem(handle,DEFAULT_SELECT_ID),1);
	
	return 0;
}
/*******************************************************************************
* Function Name  : defaultCloseWin
* Description    : defaultCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int defaultCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]defaultCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : defaultWinChildClose
* Description    : defaultWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int defaultWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]defaultWinChildClose\n");
	return 0;
}


ALIGNED(4) msgDealInfor defaultMsgDeal[] =
{
	{SYS_OPEN_WINDOW,	defaultOpenWin},
	{SYS_CLOSE_WINDOW,	defaultCloseWin},
	{SYS_CHILE_COLSE,	defaultWinChildClose},
	{KEY_EVENT_OK,		defaultKeyMsgOk},
	{KEY_EVENT_UP,		defaultKeyMsgUp},
	{KEY_EVENT_DOWN,	defaultKeyMsgDown},
	{KEY_EVENT_LEFT,	defaultKeyMsgPower},
	//{KEY_EVENT_RIGHT,	defaultKeyMsgDown},
	{KEY_EVENT_POWER,	defaultKeyMsgPower},
	{EVENT_MAX,NULL},
};

WINDOW(defaultWindow,defaultMsgDeal,defaultWin)


