/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuVersionWin.c"

/*******************************************************************************
* Function Name  : getVersionResInfor
* Description    : getVersionResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : u32: 
*******************************************************************************/
UNUSED static u32 getVersionResInfor(u32 item,u32* image,u32* str)
{
	return 0;
}
/*******************************************************************************
* Function Name  : versionKeyMsgOk
* Description    : versionKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int versionKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : versionKeyMsgPower
* Description    : versionKeyMsgPower
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int versionKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : versionKeyMsgMode
* Description    : versionKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int versionOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]versionOpenWin\n");
	//uiItemManageSetRowSum(			winItem(handle,VERSION_TIPS_ID),1,   Rh(40));
	//uiItemManageSetColumnSumWithGap(	winItem(handle,VERSION_TIPS_ID),0,2,Rw(50),Rw(12));

	return 0;
}
/*******************************************************************************
* Function Name  : versionCloseWin
* Description    : versionCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int versionCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]versionCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : versionWinChildClose
* Description    : versionWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int versionWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]versionWinChildClose\n");
	return 0;
}


ALIGNED(4) msgDealInfor verisonMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	versionOpenWin},
	{SYS_CLOSE_WINDOW,	versionCloseWin},
	{SYS_CHILE_COLSE,	versionWinChildClose},
	{KEY_EVENT_OK,		versionKeyMsgOk},
	//{KEY_EVENT_UP,	NULL},
	//{KEY_EVENT_DOWN,	NULL},
	{KEY_EVENT_LEFT,	versionKeyMsgPower},
	//{KEY_EVENT_RIGHT,	NULL},
	{KEY_EVENT_POWER,	versionKeyMsgPower},
	{EVENT_MAX,			NULL},
};

WINDOW(versionWindow,verisonMsgDeal,versionWin)


