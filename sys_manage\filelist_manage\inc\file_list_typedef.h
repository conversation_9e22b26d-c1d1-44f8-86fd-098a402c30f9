/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef FILELIST_TYPEDEF_H
#define FILELIST_TYPEDEF_H

#define FILE_PATH_LEN			64
#define FILE_NAME_LEN			64		
#define FILE_FULLNAME_LEN		(FILE_PATH_LEN+FILE_NAME_LEN)
#define FILE_LIST_MAX			3		//list: AVI, WAV, MP3
#define FILE_NODE_NUM			3		//list node: 
#define	FILE_NUM_MAX			9999    //AVI OR JPG 
#define	FILE_NUM_MAX_2			512    //OTHER TYPE
#define FILE_INDEX_MIN			1

#define  SUFFIX_AVI  			".avi"
#define  SUFFIX_JPG  			".jpg"
#define  SUFFIX_WAV  			".wav"
#define  SUFFIX_MP3  			".mp3"
#define  SUFFIX_LRC  			".lrc"
#define  SUFFIX_NES  			".nes"


#define  PREFIX_LEN				4  //定义前缀字符个数

#define  PREFIX_AVI  			"MOVI"//"AVI"
#define  PREFIX_JPG  			"PICT"//"IMG"
#define  PREFIX_WAV  			"WAVE"//"WAV"
#define  PREFIX_LOK_AVI  		"SOVI"//"SAV"
#define  PREFIX_LOK_JPG  		"SOJP"//"SAV"
#define  PREFIX_SPI  			"SPI0"//"SPI"
#define  PREFIX_LOK_SPI  		"SOI0"//"SPI"



//index_h
#define  FILELIST_TYPE_AVI    	0x00010000
#define  FILELIST_TYPE_JPG    	0x00020000
#define  FILELIST_TYPE_WAV    	0x00040000
#define  FILELIST_TYPE_MP3    	0x00080000
#define  FILELIST_TYPE_DIR    	0x00100000
#define  FILELIST_TYPE_NES    	0x00200000
#define  FILELIST_TYPE_SPI    	0x00400000

#define  FILELIST_FLAG_AB		0x01000000  //VIDEOB or JPGB FILE
#define  FILELIST_FLAG_LOK    	0x02000000
#define  FILELIST_FLAG_TIME		0x04000000
#define  FILELIST_FLAG_IVL    	0x80000000

#define  FILELIST_TYPE_MASK   	0x00ff0000
#define  FILENODE_TYPE_MASK		(FILELIST_TYPE_MASK|FILELIST_FLAG_AB)
#define  FILELIST_FLAG_MASK  	0xff000000


#define  FILELIST_INDEX_MASK	0x0000ffff	//index: 0~9999
#define  FILELIST_INDEX_MAX		9999		


typedef struct FILELIST_NAME_S
{
	 INT32U	index;
	 INT32U datatime;
}FILELIST_NAME_T;
typedef struct FILE_NAME_LIST_S
{
	char name[FILE_NUM_MAX_2][FILE_NAME_LEN];
}FILE_NAME_LIST_T;

typedef struct FILELIST_NODE_S
{
	s32 	valid;
	void*   plist;
	s32 	count;
	u32 	node_type;
	u32     min_space; //kb
	char 	path[FILE_PATH_LEN];
	char    file[FILE_NAME_LEN];  
}FILELIST_NODE_T;

typedef struct FILELIST_CTRL_S
{
	//int  valid;
	s32  dirty;
	s32  count;
	u32  list_index_max;
	char file_fullname[FILE_PATH_LEN + FILE_NAME_LEN];
	FILELIST_NODE_T 	node[FILE_NODE_NUM];
	FILELIST_NAME_T 	*name;
	FILE_NAME_LIST_T 	*plist_name;
	
}FILELIST_CTRL_T;

#define  REMAIN_MIN_VIDEO     	(128*1024L)  // 128M预留给拍照
#define  REMAIN_MIN_PHOTO     	(5*1024L)  // 5M 预留多5M空间给拍大分辨率照片
#define  REMAIN_MIN_MUSIC    	(1*1024L)  // 1M ->music






#endif
