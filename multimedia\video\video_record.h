/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  VIDEO_RECORD_H
    #define  VIDEO_RECORD_H



typedef struct VIDEO_ARG_S
{	
	AVI_ENC_ARG avi_arg;
	
	INT32U recDelayMode;           //delay record
	INT32U recDelayStep;           //delay step
	INT32U recDelayCurCnt;         //delay step
	INT32U audio_en_save;
	INT32U rectime;                //record time
	INT32U mdtime;				   //mdtime
	INT8U  ftype;
	INT8U  timestramp; 	           // timestramp flag
	INT8U  looprecord;             // loop record flag
	INT8U  quality;                // jpeg quality

	
	int (*callback)(INT32U cmd,INT32U para);   // >=0 success,<0 fail
	

}VIDEO_ARG_T;
typedef struct VideoRec_Ctl_S
{
	INT32U  stat;       // media sta:MEDIA_STAT_STOP/MEDIA_STAT_START
	INT32U  filstat;    // video file status: 0 not write, BIT(0):write video frame, BIT(1): write audio frame
	INT32U  photoStat;  // take photo when recording
	//for write sync ctrl
	INT32U  sync_stat;
	INT32U  tsync_cur; //save current sync frame
	INT32U  tsync_next; //save next sync frame
	INT32S  sync_cnt;
	INT32S  iframe_cnt;
	INT32U  abchangetime;

	
	//file handle
	s32     space;        // video file pre malloc size(kBytes) (ODML)or sd freesize(AVI STANDARD)

	int 	photoFd;   // photo file handler

	VIDEO_ARG_T arg;   // task cfg value
	void *  (*videoFrameGet)(u32 *len,u32 *sync, u32 *sync_next);
	void *  (*audioFrameGet)(u32 *len,u32 *sync, u32 *sync_next);
	void    (*videoFrameFree)(void);
	void    (*audioFrameFree)(void);
}VideoRec_Ctl_T;



#if DBG_VIDEO_REC_EN
    #define  VIDEOREC_DBG     deg_Printf
#else
    #define  VIDEOREC_DBG(...)
#endif
//-----------------cmd table-------------------------
enum
{
	VIDEO_CMD_NULL,
	VIDEO_CMD_START=1,
	VIDEO_CMD_STOP,

	VIDEO_CMD_SET_LOOPTIME,


	VIDEO_CMD_MAX
};
	
/*******************************************************************************
* Function Name  : videoRecordImageWatermark
* Description    : videoRecordImageWatermark UPDATE, VIDEO_CH_A, 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void videoRecordImageWatermark(INT16U width,INT16U height,INT8U en);
/*******************************************************************************
* Function Name  : videoRecordImageWatermark
* Description    : videoRecordImageWatermark UPDATE, VIDEO_CH_A, 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void videoRecordBImageWatermarkAdj(INT16U height);

/*******************************************************************************
* Function Name  : videoRecordInit
* Description    : initial video record 
* Input          : VIDEO_ARG_T *arg : video initial argument
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordInit(VIDEO_ARG_T *arg);

/*******************************************************************************
* Function Name  : videoRecordUninit
* Description    : uninitial video record 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordUninit(void);

/*******************************************************************************
* Function Name  : videoRecordFileStart
* Description    : start video record start for file handler
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordFileStart(VideoRec_Ctl_T *ctrl);
/*******************************************************************************
* Function Name  : videoRecordFileStop
* Description    : stop video record 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordFileStop(VideoRec_Ctl_T *ctrl);

/*******************************************************************************
* Function Name  : videoRecordStart
* Description    : start video record
* Input          : AUDIO_RECORD_ARG_T *arg : video record argument
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordStart(void);

/*******************************************************************************
* Function Name  : videoRecordStop
* Description    : stop video record 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordStop(void);

/*******************************************************************************
* Function Name  : videoRecordGetTimeSec
* Description    : videoRecordGetTimeSec 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
uint32 videoRecordGetTimeSec(void);

/*******************************************************************************
* Function Name  : videoRecordRestart
* Description    : pause video record restart for loop recording
* Input          : u32 mjp_stop: 0: mjp not stop, 1: mjp stop
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordRestart(u32 mjp_stop);

/*******************************************************************************
* Function Name  : videoRecordGetStatus
* Description    : get video record 
* Input          : none
* Output         : none
* Return         : int 
                      
*******************************************************************************/
int videoRecordGetStatus(void);

/*******************************************************************************
* Function Name  : videoRecordFileError
* Description    : start video record error for file handler
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordJunkSync(VideoRec_Ctl_T *ctrl);

/*******************************************************************************
* Function Name  : videoRecordService
* Description    : video record service
* Input          : none
* Output         : none
* Return         : int 
                      
*******************************************************************************/
int videoRecordFrameProcess(VideoRec_Ctl_T *ctrl);

/*******************************************************************************
* Function Name  : videoRecordService
* Description    : video record service
* Input          : none
* Output         : none
* Return         : int 
                      
*******************************************************************************/
int videoRecordService(void);
/*******************************************************************************
* Function Name  : videoRecordCmdSet
* Description    : video record set paramater
* Input          : INT32U cmd : command
                      INT32U para : paramater 
* Output         : none
* Return         : int 
                      
*******************************************************************************/
int videoRecordCmdSet(INT32U cmd,INT32U para);

/*******************************************************************************
* Function Name  : videoRecordSizePreSec
* Description    : video record size pre second
* Input          : INT32U time_s : time,second
                      int channel : video channel ,A,B
* Output         : none
* Return         : int   kb
                      
*******************************************************************************/
int videoRecordSizePreSec(INT32U time_s);

/*******************************************************************************
* Function Name  : videoRecordTakePhoto
* Description    : video record take photo when recording
* Input          : int fd : file handle
* Output         : none
* Return         : int : status
                      
*******************************************************************************/
void videoRecordTakePhotoCfg(u32 stat,int fd);

/*******************************************************************************
* Function Name  : videoRecordTakePhotoStatus
* Description    : video record tabke photo status
* Input          : none
* Output         : none
* Return         : int : status
                      
*******************************************************************************/
int videoRecordTakePhotoStatus(void);

void videoRecordSetPhotoStatus(INT32S status);


int videoRecordTakePhotoFd(void);




#endif
