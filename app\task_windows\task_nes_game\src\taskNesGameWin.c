/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	NESGAME_MODE_ID = 0,
	NESGAME_SD_ID,
	NESGAME_SDVOL_ID,
	NESGAME_BATERRY_ID,
	NESGAME_SELECT_ID,
};

UNUSED ALIGNED(4) const widgetCreateInfor nesGameWin[] =
{
	createFrameWin( 							Rx(0),   Ry(0),   Rw(320), Rh(240),R_ID_PALETTE_Black,WIN_ABS_POS),
	
	//createImageIcon(NESGAME_MODE_ID,    		Rx(5),   <PERSON>y(0),   Rw(25),  Rh(25),  R_ID_ICON_MENUTV, 		ALIGNMENT_LEFT),
	createImageIcon(NESGAME_SD_ID,        		Rx(40),  Ry(0),   Rw(40),  Rh(32), 	R_ID_ICON_MTSDCNORMAL,	ALIGNMENT_CENTER),
	createStringIcon(NESGAME_SDVOL_ID,			Rx(40),  Ry(5),   Rw(36),  Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_GREEN2,	DEFAULT_FONT),
	createImageIcon(NESGAME_BATERRY_ID,   		Rx(280), Ry(0),   Rw(40),  Rh(40), 	R_ID_ICON_MTBATTERY3,	ALIGNMENT_RIGHT),	
	
	createItemManage(NESGAME_SELECT_ID,			Rx(0),   Ry(40),  Rw(320), Rh(200),INVALID_COLOR),
	widgetEnd(),
};


/*******************************************************************************
* Function Name  : playMp3MainSDShow
* Description    : playMp3MainSDShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void nesGameSDShow(winHandle handle)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
	{
		uiWinSetResid(winItem(handle,NESGAME_SD_ID),R_ID_ICON_MTSDCNORMAL);
		uiWinSetVisible(winItem(handle,NESGAME_SDVOL_ID),1);
		uiWinSetResid(winItem(handle,NESGAME_SDVOL_ID),RAM_ID_MAKE(task_com_sdcCap_str()));
		
	}	
	else{
		uiWinSetResid(winItem(handle,NESGAME_SD_ID),R_ID_ICON_MTSDCNULL);
		uiWinSetVisible(winItem(handle,NESGAME_SDVOL_ID),0);
	}	
}
/*******************************************************************************
* Function Name  : playMp3MainBaterryShow
* Description    : playMp3MainBaterryShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/	
static void nesGameBaterryShow(winHandle handle)
{
	resID batid;
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
		batid = R_ID_ICON_MTBATTERYCHARGE;
	else{
		switch(SysCtrl.dev_stat_battery)
		{
			case BATTERY_STAT_0: batid = R_ID_ICON_MTBATTERY0; break;
			case BATTERY_STAT_1: batid = R_ID_ICON_MTBATTERY1; break;
			case BATTERY_STAT_2: batid = R_ID_ICON_MTBATTERY2; break;
			case BATTERY_STAT_3: batid = R_ID_ICON_MTBATTERY3; break;
			//case BATTERY_STAT_4: 
			//case BATTERY_STAT_5: 
			default:
								 batid = R_ID_ICON_MTBATTERY4; break;
		}
	}
	uiWinSetVisible(winItem(handle,NESGAME_BATERRY_ID),1);
	uiWinSetResid(winItem(handle,NESGAME_BATERRY_ID),batid);	
	
}