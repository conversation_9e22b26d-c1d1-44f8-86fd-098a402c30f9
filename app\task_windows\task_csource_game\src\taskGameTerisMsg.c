﻿/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        : 俄罗斯方块
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskGameTerisWin.c"

extern u8 *game_frame_buff;

#define BPIXEL 8	//
#define X_BOXS 16	//
#define Y_BOXS 20	//

INT16U Tetris[19]={0x0F00,0x4444,0x0660,0x4460,0x02E0,0x6220,0x0740,0x2260,0x0E20,0x6440,0x0470,0x0C60,0x2640,0x0360,0x4620,0x04E0,0x2620,0x0E40,0x4640};
INT16U BoxSR[Y_BOXS+4]={0x0000,0x0000,0x0000,0x8001,0x8001,0x8001,0x8001,0x8001,0x8001,0x8001,0x8001,0x8001,0x8001,0x8001,0x8001,0x8001,0x8001,0x8001,0x8001,0x8001,0x8001,0x8001,0x8001,0xFFFF};
static char score_buf[7]={0},level_buf[4]={0};

typedef struct
{
	INT16U x;
	INT16U y;
	INT16U color;
	INT8U  CurNum; //
	INT8U  NextNum;//
	INT8U  TurnNum;//
	INT8U  Move;
}TERRIS_SHAPE;

typedef struct
{
	INT16U speed;
	INT8U  Life;
	INT32U score;
}TERIS_GAME;

TERRIS_SHAPE TerisShape;

TERIS_GAME TerisGame={10000,1,0};

extern u16 game_frame_w;
extern u16 game_frame_h;

/*LCD??С???麯??**/
/*(sx,xy):???????*/
/***color:???????*/
void g_DrawBlock(INT8U*buff,INT16U sx,INT16U sy,INT8U color)
{
	g_DrawRectangle_Color(buff,sx,sy,sx+BPIXEL-1,sy+BPIXEL-1,color);
	g_Draw_Fill(buff,sx+2,sy+2,sx+BPIXEL-3,sy+BPIXEL-3,color);
}

/*********??????κ???************/
/*(sx,sy):??????4*4??????????*/
/*******n:??????α??************/
/***color:????******************/
void g_DrawShape(INT8U*buff,INT16U sx,INT16U sy,INT8U n,INT8U color)
{
	INT8U a,b;
	INT16U temp=Tetris[n];
	INT8U i;

	for(i=0;i<X_BOXS;i++){
		a=i/4;
		b=i%4;
		if(temp&0x8000){
			g_DrawRectangle_Color(buff,sx+b*BPIXEL,sy+a*BPIXEL,sx+(b+1)*BPIXEL-1,sy+(a+1)*BPIXEL-1,color);
			g_Draw_Fill(buff,sx+b*BPIXEL+2,sy+a*BPIXEL+2,sx+(b+1)*BPIXEL-3,sy+(a+1)*BPIXEL-3,color);
            //g_Draw_Fill(buff,sx+b*BPIXEL+2,sy+a*BPIXEL+2,sx+(b+1)*BPIXEL-3,sy+(a+1)*BPIXEL-3,color);
			BoxSR[sy/BPIXEL+a]|=1<<((sx/BPIXEL)+b);	//???÷????????????
		}
		temp<<=1;
	}
}

/*********??????κ???***************/
/*(sx,sy):??????4*4??????????****/
/*******n:??????α??***************/
/***color:????*********************/
/*?????????????????????λ????*/
void g_ClearShape(INT8U*buff,INT16U sx,INT16U sy,INT8U n,INT8U color)
{
	INT8U a,b;
	INT16U temp=Tetris[n];
	INT8U i;
	for(i=0;i<X_BOXS;i++){
		a=i/4;
		b=i%4;
		if(temp&0x8000){
			g_Draw_Fill(buff,sx+b*BPIXEL,sy+a*BPIXEL,sx+(b+1)*BPIXEL-1,sy+(a+1)*BPIXEL-1,color);
			BoxSR[sy/BPIXEL+a]&=~(1<<((sx/BPIXEL)+b));	//???÷???δ????????
		}
		temp<<=1;
	}
}


/***********************?ж????***********************/
/*****************?ж??????ε??????*****************/
/*mode:1->????;2->????;3->???;4->????????;5->????????*/
INT8U Judge(INT16U sx,INT16U sy,INT8U n,INT8U mode)
{
	int cx,cy,temp1=Tetris[n],temp2=Tetris[n];
	INT8U a,b,i,Flag=0;
	switch(mode){
		case 1:	cx=sx-BPIXEL;cy=sy;break;
		case 2:	cx=sx+BPIXEL;cy=sy;break;
		case 3:	cx=sx;
				cy=sy;
				switch(TerisShape.CurNum){
					case 0:temp2=1;break;
					case 1:temp2=0;break;
					case 2:temp2=2;break;
					case 3:temp2=4;break;
					case 4:temp2=5;break;
					case 5:temp2=6;break;
					case 6:temp2=3;break;
					case 7:temp2=8;break;
					case 8:temp2=9;break;
					case 9:temp2=10;break;
					case 10:temp2=7;break;
					case 11:temp2=12;break;
					case 12:temp2=11;break;
					case 13:temp2=14;break;
					case 14:temp2=13;break;
					case 15:temp2=16;break;
					case 16:temp2=17;break;
					case 17:temp2=18;break;
					case 18:temp2=15;break;
				}
				TerisShape.TurnNum=temp2;
				temp2=Tetris[temp2];
				break;
		case 5:	cx=sx;cy=sy+BPIXEL;break;
		default:cx=sx;cy=sy;break;
	}
	for(i=0;i<X_BOXS;i++){
		a=i/4;
		b=i%4;
		if(temp1&0x8000)
            BoxSR[sy/BPIXEL+a]&=~(1<<((sx/BPIXEL)+b));
		temp1<<=1;
	}
	for(i=0;i<X_BOXS;i++){
		a=i/4;
		b=i%4;
		if(temp2&0x8000){
			if(BoxSR[cy/BPIXEL+a]&(1<<((cx/BPIXEL)+b))){
				Flag=1;
			}
		}
		temp2<<=1;
	}
	if(Flag==0)
        return 1;
	else
        return 0;
}

/*????????κ???*/
void Create_Shape(winHandle handle,INT8U *display_buf)
{
	INT8U a,b;
	INT16U temp;
	INT8U i;
	TerisShape.CurNum=TerisShape.NextNum;
	TerisShape.x=60;
	TerisShape.y=30;
	if(BoxSR[4]&0x03C0)
        ResetGame(handle,display_buf);
	else
        g_DrawShape(display_buf,TerisShape.x,TerisShape.y,TerisShape.CurNum,R_ID_PALETTE_Red);
	temp=Tetris[TerisShape.NextNum];
	for(i=0;i<X_BOXS;i++)
	{
		a=i/4;
		b=i%4;
		if(temp&0x8000)
		{
		//	flow_dbg();
			g_DrawBlock(display_buf,170+b*BPIXEL,40+a*BPIXEL,R_ID_PALETTE_Gray);
		}
		temp<<=1;
	}
	TerisShape.NextNum=rand()%19;
	temp=Tetris[TerisShape.NextNum];
	for(i=0;i<X_BOXS;i++)
	{
		a=i/4;
		b=i%4;
		if(temp&0x8000)
		{
			g_DrawBlock(display_buf,170+b*BPIXEL,40+a*BPIXEL,R_ID_PALETTE_Red);
		}
		temp<<=1;
	}
}

/*??????????*/
void MoveLeft(winHandle handle,u8* display_buf)
{
	INT8U Draw_Ready;
	//	deg_Printf("TerisShape.Move:%d\r\n",TerisShape.Move);
	//	deg_Printf("TerisShape.Move:%d\r\n",TerisShape.x);
    if(TerisShape.Move)
        return;

    TerisShape.Move=1;
    Draw_Ready=Judge(TerisShape.x,TerisShape.y,TerisShape.CurNum,1);
    if(Draw_Ready==1)
    {
        g_ClearShape(display_buf,TerisShape.x,TerisShape.y,TerisShape.CurNum,/*BOXS_COLOR*/R_ID_PALETTE_Gray);
        TerisShape.x-=BPIXEL;
        g_DrawShape(display_buf,TerisShape.x,TerisShape.y,TerisShape.CurNum,R_ID_PALETTE_Red);
    }
    else g_DrawShape(display_buf,TerisShape.x,TerisShape.y,TerisShape.CurNum,R_ID_PALETTE_Red);
    TerisShape.Move=0;
    uiWinDrawUpdate();
}

/*??????????*/
void MoveRight(winHandle handle,u8* display_buf)
{
	INT8U Draw_Ready;
    if(TerisShape.Move)
        return;

    TerisShape.Move=1;
    Draw_Ready=Judge(TerisShape.x,TerisShape.y,TerisShape.CurNum,2);
    if(Draw_Ready==1){
        g_ClearShape(display_buf,TerisShape.x,TerisShape.y,TerisShape.CurNum,R_ID_PALETTE_Gray);
        TerisShape.x+=BPIXEL;
        g_DrawShape(display_buf,TerisShape.x,TerisShape.y,TerisShape.CurNum,R_ID_PALETTE_Red);
    }
    else
        g_DrawShape(display_buf,TerisShape.x,TerisShape.y,TerisShape.CurNum,R_ID_PALETTE_Red);
    TerisShape.Move=0;
    uiWinDrawUpdate();
}

/*???????亯??*/
void DownFast(winHandle handle,u8* display_buf)
{
	INT8U Draw_Ready;
    if(TerisShape.Move)
        return;

    TerisShape.Move=1;
    Draw_Ready=Judge(TerisShape.x,TerisShape.y,TerisShape.CurNum,5);
    if(Draw_Ready==1)
    {
        g_ClearShape(display_buf,TerisShape.x,TerisShape.y,TerisShape.CurNum,R_ID_PALETTE_Gray/*BOXS_COLOR*/);
        TerisShape.y+=BPIXEL;
        g_DrawShape(display_buf,TerisShape.x,TerisShape.y,TerisShape.CurNum,R_ID_PALETTE_Red);
    }
    else
    {
        g_DrawShape(display_buf,TerisShape.x,TerisShape.y,TerisShape.CurNum,R_ID_PALETTE_Red);
    }
    TerisShape.Move=0;
    uiWinDrawUpdate();
}

/*???????亯??*/
void DownFree(winHandle handle,u8* display_buf)
{
	INT8U Draw_Ready;
	INT8U i,j,Clear_Flag;
	INT16U temp;

    if(TerisShape.Move)
        return;

    TerisShape.Move=1;
    Draw_Ready=Judge(TerisShape.x,TerisShape.y,TerisShape.CurNum,5);
    if(Draw_Ready==1){
        g_ClearShape(display_buf,TerisShape.x,TerisShape.y,TerisShape.CurNum,R_ID_PALETTE_Gray/*BOXS_COLOR*/);
        TerisShape.y+=BPIXEL;
        g_DrawShape(display_buf,TerisShape.x,TerisShape.y,TerisShape.CurNum,R_ID_PALETTE_Red);
    }else{
        g_DrawShape(display_buf,TerisShape.x,TerisShape.y,TerisShape.CurNum,R_ID_PALETTE_Red);
        for(i=4;i<Y_BOXS+4-1;i++){
            if(BoxSR[i]==0xFFFF){
                TerisGame.score++;
                TerisGame.Life=1+TerisGame.score/100;
                #if 0
                icon.icon_w  =72;
                icon.icon_h  = 30;
                if(icon.icon_w > game_frame_w){
                    icon.icon_w = game_frame_w;
                }
                icon.pos_x = 228;
                icon.pos_y = 175;

                ap_setting_rect_draw((INT16U *)display_buf,R_ID_PALETTE_Green,R_ID_PALETTE_Green,0,&icon);


                score_buf[0]=Game.score/100000+0x30;
                score_buf[1]=Game.score%100000/10000+0x30;
                score_buf[2]=Game.score%100000%10000/1000+0x30;
                score_buf[3]=Game.score%100000%10000%1000/100+0x30;
                score_buf[4]=Game.score%100000%10000%1000%100/10+0x30;
                score_buf[5]=Game.score%100000%10000%1000%100%10+0x30;
                score_buf[6]='\0';
                ascii_str.str_ptr = (char *)score_buf;
                ascii_str.pos_x=230;
                ascii_str.pos_y=180;
                ap_state_resource_string_ascii_draw((INT16U*)display_buf, &ascii_str);
                level_buf[0]=Game.Life/100+0x30;
                level_buf[1]=Game.Life%100/10+0x30;
                level_buf[2]=Game.Life%100%10+0x30;
                level_buf[3]='\0';
                //ascii_str.str_ptr = (char *)level_buf;
                //ascii_str.pos_x=170;
                //ascii_str.pos_y=160;
                //ap_state_resource_string_ascii_draw((INT16U*)Game_buff, &ascii_str);
                //FRONT_COLOR=TEXT_COLOR;
                //R_ID_PALETTE_Black=GBLUE;
                //LCD_ShowString(170,120,tftlcd_data.width,tftlcd_data.height,16,score_buf);
                //LCD_ShowString(170,160,tftlcd_data.width,tftlcd_data.height,16,level_buf);
		#else
			score_buf[0]=TerisGame.score/100000+0x30;
			score_buf[1]=TerisGame.score%100000/10000+0x30;
			score_buf[2]=TerisGame.score%100000%10000/1000+0x30;
			score_buf[3]=TerisGame.score%100000%10000%1000/100+0x30;
			score_buf[4]=TerisGame.score%100000%10000%1000%100/10+0x30;
			score_buf[5]=TerisGame.score%100000%10000%1000%100%10+0x30;
			score_buf[6]='\0';
			uiWinSetResid(winItem(handle,TERIS_GRADE_ID),score_buf);

			level_buf[0]=TerisGame.Life/100+0x30;
			level_buf[1]=TerisGame.Life%100/10+0x30;
			level_buf[2]=TerisGame.Life%100%10+0x30;
			level_buf[3]='\0';
			//uiWinSetResid(winItem(handle,TERIS_LEVEL_NUM_ID),level_buf);
		#endif
                Clear_Flag=1;
                for(j=i;j>3;j--)BoxSR[j]=BoxSR[j-1];
            }
        }
        if(Clear_Flag)g_Draw_Fill(display_buf,10,30,100,166-BPIXEL,R_ID_PALETTE_Gray/*BOXS_COLOR*/);
        for(i=3;i<Y_BOXS+4-1;i++){
            temp=BoxSR[i];
            temp>>=1;
            for(j=1;j<15;j++){
                if(temp&0x0001)g_DrawBlock(display_buf,j*BPIXEL,i*BPIXEL,R_ID_PALETTE_Red);
                temp>>=1;
            }
        }
    Create_Shape(handle,display_buf);
    }
    TerisShape.Move=0;
    uiWinDrawUpdate();

}

void Transform(winHandle handle,u8* display_buf)
{
	INT8U Draw_Ready;
    if(TerisShape.Move)
        return;

    TerisShape.Move=1;
    Draw_Ready=Judge(TerisShape.x,TerisShape.y,TerisShape.CurNum,3);
    if(Draw_Ready==1)
    {
        g_ClearShape(display_buf,TerisShape.x,TerisShape.y,TerisShape.CurNum,R_ID_PALETTE_Gray/*BOXS_COLOR*/);
        TerisShape.CurNum=TerisShape.TurnNum;
        g_DrawShape(display_buf,TerisShape.x,TerisShape.y,TerisShape.CurNum,R_ID_PALETTE_Red);
    }
    else
	g_DrawShape(display_buf,TerisShape.x,TerisShape.y,TerisShape.CurNum,R_ID_PALETTE_Red);
    TerisShape.Move=0;
}

void Show_TetrisFace(winHandle handle,u8* Game_buff)
{
#if 0
   INT8U 		i,j;


   STRING_ASCII_INFO ascii_str;
   DISPLAY_ICONSHOW icon;

 // INT32U Game_buff=game_Background_Frame->base+game_Background_Frame->offset;
	ascii_str.font_color = R_ID_PALETTE_Red;
	ascii_str.font_type = 0;
	ascii_str.buff_w = game_frame_w;
	ascii_str.buff_h = game_frame_h;


	ascii_str.pos_x = 150;
	ascii_str.pos_y = 0;
	ascii_str.str_ptr = (char *)"Tetris";
	ap_state_resource_string_ascii_draw((INT16U *)Game_buff, &ascii_str);

	ascii_str.pos_x = 240;
	ascii_str.pos_y = 40;
	ascii_str.str_ptr = GAME_NEXT;
	ap_state_resource_string_ascii_draw((INT16U *)Game_buff, &ascii_str);

	icon.icon_w  =74;
	icon.icon_h  = 30;
	if(icon.icon_w > game_frame_w){
	icon.icon_w = game_frame_w;
	}
	icon.pos_x = 228;
	icon.pos_y = 175;

	ap_setting_rect_draw((INT16U *)Game_buff,R_ID_PALETTE_Green,R_ID_PALETTE_Green,0,&icon);

	ascii_str.pos_x = 235;
	ascii_str.pos_y = 140;
	ascii_str.str_ptr = GAME_SCORD;
	ap_state_resource_string_ascii_draw((INT16U *)Game_buff, &ascii_str);

   	score_buf[0]=Game.score/100000+0x30;
	score_buf[1]=Game.score%100000/10000+0x30;
	score_buf[2]=Game.score%100000%10000/1000+0x30;
	score_buf[3]=Game.score%100000%10000%1000/100+0x30;
	score_buf[4]=Game.score%100000%10000%1000%100/10+0x30;
	score_buf[5]=Game.score%100000%10000%1000%100%10+0x30;


	ascii_str.str_ptr = score_buf;
	deg_Printf("%s\r\n",score_buf);
	ascii_str.pos_x=230;
	ascii_str.pos_y=180;
	ap_state_resource_string_ascii_draw((INT16U*)Game_buff, &ascii_str);
/*
	level_buf[0]=Game.level/100+0x30;
	level_buf[1]=Game.level%100/10+0x30;
	level_buf[2]=Game.level%100%10+0x30;
	level_buf[3]='\0';
	ascii_str.str_ptr = (char *)level_buf;
	ascii_str.pos_x=170;
	ascii_str.pos_y=160;
	ap_state_resource_string_ascii_draw((INT16U*)buff, &ascii_str, RGB565_DRAW);
   */
#elif 1
	 INT8U 		i,j;
	 INT16U curpos=40;
	curpos=40;

    for(i=0;i<20;i++){
        //flow_dbg();
        g_DrawBlock(Game_buff,79,curpos,R_ID_PALETTE_Green);
        curpos+=BPIXEL;
    }
    curpos=179;
    for(i=0;i<12;i++){
        //flow_dbg();
        g_DrawBlock(Game_buff,curpos,20,R_ID_PALETTE_Green);
        curpos+=BPIXEL;
    }
    for(i=0;i<4;i++){
        for(j=0;j<4;j++){
            //flow_dbg();
            g_DrawBlock(Game_buff,76+i*BPIXEL,70+j*BPIXEL,R_ID_PALETTE_Green);
        }
    }
    curpos=40;
    for(i=0;i<20;i++){
        //flow_dbg();
        g_DrawBlock(Game_buff,75-10,curpos,R_ID_PALETTE_Green);
        curpos+=BPIXEL;
    }
    curpos=159;
    for(i=0;i<14;i++){
        //flow_dbg();
        g_DrawBlock(Game_buff,curpos,75-10,R_ID_PALETTE_Green);
        curpos+=BPIXEL;

    }
    curpos=30;
    for(i=0;i<21;i++){
        //flow_dbg();
        //g_DrawBlock(Game_buff,1,curpos,R_ID_PALETTE_Green);
        g_Draw_Fill(Game_buff,1,curpos,1+BPIXEL-1,curpos+BPIXEL-1,R_ID_PALETTE_Green);
        //g_DrawRectangle_Color((INT16U*)Game_buff,1,curpos,1+BPIXEL-1,curpos+BPIXEL-1,R_ID_PALETTE_Green);
        curpos+=BPIXEL;
    }
    curpos=20;
    for(i=0;i<22;i++){
        //	flow_dbg();
        //g_DrawBlock(Game_buff,150,curpos,R_ID_PALETTE_Green);
        g_Draw_Fill(Game_buff,10,curpos,110+BPIXEL-1,curpos+BPIXEL-1,R_ID_PALETTE_Green);
        curpos+=BPIXEL;
    }
    curpos=11;
    for(i=0;i<14;i++){
        //flow_dbg();
        //g_DrawBlock(Game_buff,curpos,229,R_ID_PALETTE_Green);
        g_Draw_Fill(Game_buff,curpos,56,curpos+BPIXEL-1,156+BPIXEL-1,R_ID_PALETTE_Green);
        curpos+=BPIXEL;
    }
    curpos=1; //TOP
    for(i=0;i<15;i++){
        //flow_dbg();
        //g_DrawBlock(Game_buff,curpos,20,R_ID_PALETTE_Green);
        g_Draw_Fill(Game_buff,curpos,20,curpos+BPIXEL-1,20+BPIXEL-1,R_ID_PALETTE_Green);
        curpos+=BPIXEL;
    }
	uiWinDrawUpdate();
#endif
}



void Start_Game(winHandle handle,u8 * Game_buff)
{
	Create_Shape(handle,Game_buff);
}

void ResetGame(winHandle handle,u8*display_buf)
{
	INT8U i;
	g_Draw_Fill(display_buf,10,30,150,229,R_ID_PALETTE_Gray/*BOXS_COLOR*/);

	deg_Printf("ResetGame\r\n");

	TerisGame.score=0;
	TerisGame.Life=1;
	BoxSR[0]=0x0000;
	BoxSR[1]=0x0000;
	BoxSR[2]=0x0000;
	for(i=3;i<Y_BOXS+4-1;i++)
	{
		BoxSR[i]=0x8001;
	}
	BoxSR[Y_BOXS+4-1]=0xFFFF;

	Start_Game(handle,display_buf);
}

void Tetris_Game_Init(winHandle handle)
{
	Show_TetrisFace(handle,game_frame_buff);
	Start_Game(handle,game_frame_buff);
	//g_Draw_Fill((INT16U*)game_frame_buff,10,30,150,229,R_ID_PALETTE_Black/*BOXS_COLOR*/);
	//OSQPost(DisplayTaskQ, (void *) (MSG_DISPLAY_TASK_MJPEG_DRAW | (INT32U)game_frame_buff));
	//app_sendDrawUIMsg();
	//uiWinDrawUpdate();
}

void Tetris_Game_Exit(void)
{
    INT8U i;
    TerisGame.score=0;
    TerisGame.Life=1;
    BoxSR[0]=0x0000;
    BoxSR[1]=0x0000;
    BoxSR[2]=0x0000;
    for(i=3;i<Y_BOXS+4-1;i++){
        BoxSR[i]=0x8001;
    }
    BoxSR[Y_BOXS+4-1]=0xFFFF;
    //time_stop();

}

//---------------------------END_______________________

/*******************************************************************************
* Function Name  : TerisWinChildClose
* Description    : TerisWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int TerisKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//deg_Printf("TerisKeyMsgUp\n");
		Transform(handle,game_frame_buff);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : TerisWinChildClose
* Description    : TerisWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int TerisKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//deg_Printf("TerisKeyMsgDown\n");
		DownFast(handle,game_frame_buff);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : TerisWinChildClose
* Description    : TerisWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int TerisKeyMsgLeft(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//deg_Printf("TerisKeyMsgLeft\n");
		MoveLeft(handle,game_frame_buff);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : TerisWinChildClose
* Description    : TerisWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int TerisKeyMsgRight(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//deg_Printf("TerisKeyMsgRight\n");
		MoveRight(handle,game_frame_buff);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : TerisWinChildClose
* Description    : TerisWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int TerisKeyMsgMain(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//deg_Printf("TerisKeyMsgMain\n");
		uiWinDestroy(&handle);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : TerisSysMsg500MS
* Description    : TerisSysMsg500MS
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int TerisSysMsg500MS(winHandle handle,u32 parameNum,u32* parame)
{
    if(TerisShape.Move==0)
        DownFree(handle,game_frame_buff);
	return 0;
}
/*******************************************************************************
* Function Name  : TerisOpenWin
* Description    : TerisOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int TerisOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("TerisOpenWin\n");
	Tetris_Game_Init(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : TerisCloseWin
* Description    : TerisCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int TerisCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("TerisCloseWin\n");
	Tetris_Game_Exit();
	return 0;
}
/*******************************************************************************
* Function Name  : TerisWinChildClose
* Description    : TerisWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int TerisWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	//deg_Printf("TerisWinChildClose\n");
	return 0;
}
/*******************************************************************************
* Function Name  : TerisWinChildClose
* Description    : TerisWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int TerisKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("TerisKeyMsgMode\n");
	uiWinDestroy(&handle);
	return 0;
}
ALIGNED(4) msgDealInfor terisMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		TerisOpenWin},
	{SYS_CLOSE_WINDOW,		TerisCloseWin},
	{SYS_CHILE_COLSE,		TerisWinChildClose},
	{KEY_EVENT_UP,	        	TerisKeyMsgUp},
	{KEY_EVENT_DOWN,	    TerisKeyMsgDown},
	{KEY_EVENT_LEFT,	    TerisKeyMsgLeft},
	{KEY_EVENT_RIGHT,	    TerisKeyMsgRight},
	{KEY_EVENT_POWER,	    TerisKeyMsgMain},
	{SYS_EVENT_500MS,	    TerisSysMsg500MS},
	{EVENT_MAX,NULL},
};

WINDOW(terisWindow,terisMsgDeal,terisWin)


