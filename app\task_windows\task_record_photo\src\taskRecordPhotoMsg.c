/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskRecordPhotoWin.c"

/*******************************************************************************
* Function Name  : recordPhotoKeyMsgOk
* Description    : recordPhotoKeyMsgOk
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoProcess(winHandle handle)
{
	int ret;
	recordPhotoLineShow(handle, 1);
	app_draw_Service(1);
	task_com_sound_wait_end();
	dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 0);
	ret = taskRecordPhotoProcess();
	dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 1);
	recordPhotoLineShow(handle, 2);
	app_draw_Service(1);
	if(ret>=0)
	{
		if(SysCtrl.dev_stat_keysound)
			res_music_start(R_ID_MUSIC_TAKE_PHOTO,0,task_com_curVolume_get());
		taskRecordPhotoWinShowKick();
	}
	hal_sysDelayMS(10);	
	recordPhotoLineShow(handle, 0);
	app_draw_Service(1);
	if(ret>=0)
	{
		taskRecordPhotoWinShowProcess();
		if(SysCtrl.dev_stat_keysound)
		{
			task_com_sound_wait_end();
			res_music_end();
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoKeyMsgOk
* Description    : recordPhotoKeyMsgOk
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	
	if(parameNum == 1)
		keyState = parame[0];
	if(recordPhotoOp.capture_kick)
	{
		return 0;
	}
	if(keyState == KEY_PRESSED)
	{
		if(recordPhotoOp.capture_mode)
		{
			if(recordPhotoOp.capture_cnt == 0)
			{
				recordPhotoOp.capture_cnt = recordPhotoOp.capture_time;
				recordPhotoOp.capture_kick = 1;
				//recordPhotoCaptureTimeShow(handle);
				return 0;
			}
		}else
		{
			task_com_keysound_play();
		}
		recordPhotoProcess(handle);
		recordPhotoOp.capture_kick = 0;
		//recordPhotoLineShow(handle, 0);
		recordPhotoRemainShow(handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoKeyMsgUp
* Description    : recordPhotoKeyMsgUp
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(recordPhotoOp.capture_kick)
	{
		recordPhotoOp.upkeystate = KEY_STATE_INVALID;
		return 0;
	}
	if(keyState == KEY_RELEASE && recordPhotoOp.upkeystate  == KEY_PRESSED)
	{
		task_com_keysound_play();
		task_com_sound_wait_end();	
	#if FUN_KID_FRAME_EN || DEV_SENSOR_FILTER_EN || DEV_SENSOR_MAGIC_EN
		//deg_Printf("ENTER [MODE:%d, %d,%d,%d]\n",recordPhotoOp.add_mode,SysCtrl.sensor_filter_index, SysCtrl.kid_frame_index, SysCtrl.sensor_magic_index);
		if(recordPhotoOp.add_mode == PHOTO_NONE_MODE)
		{
			recordPhotoOp.add_mode = PHOTO_FILTER_MODE;
		}
		if(recordPhotoOp.add_mode == PHOTO_FILTER_MODE)
		{
		#if DEV_SENSOR_FILTER_EN
			app_sensor_filter_ctrl(SENSOR_FILTER_CHANGE_PREV);
		#endif
			if(SysCtrl.sensor_filter_index < 0)
			{
				recordPhotoOp.add_mode = PHOTO_LENS_MODE;
			}
					
		}
		if(recordPhotoOp.add_mode == PHOTO_LENS_MODE)
		{
		#if DEV_SENSOR_LENS_EN
			app_sensor_lens_ctrl(SENSOR_LENS_CHANGE_PREV,(MJPEG_PHOTO_ENC_TYPE == MJPEG_ENC_SRC_LCD) ? 0 : 1 );
		#endif
			if(SysCtrl.sensor_lens_index < 0)
			{
				recordPhotoOp.add_mode = PHOTO_MAGIC_MODE;	 //分屏显示暂不支持放大
				//app_lcdVideoShowScaler_cfg(0);
				//recordPhotoScalerShow(handle);
			}
				
		}
		if(recordPhotoOp.add_mode == PHOTO_MAGIC_MODE)
		{
			
		#if DEV_SENSOR_MAGIC_EN
			app_sensor_magic_ctrl(SENSOR_MAGIC_CHANGE_PREV, (MJPEG_PHOTO_ENC_TYPE == MJPEG_ENC_SRC_LCD) ? 0 : 1);
		#endif
			if(SysCtrl.sensor_magic_index < 0)
				recordPhotoOp.add_mode = PHOTO_KIDFRAME_MODE;	
		}
		if(recordPhotoOp.add_mode == PHOTO_KIDFRAME_MODE)
		{
		#if FUN_KID_FRAME_EN
			app_kid_frame_ctrl(KID_FRAME_SHOWPREV, (MJPEG_PHOTO_ENC_TYPE == MJPEG_ENC_SRC_LCD) ? 0 : 1);
		#endif
			if(SysCtrl.kid_frame_index < 0)
			{
		#if FUN_KID_FRAME_EN
				app_kid_frame_ctrl(KID_FRAME_DISTROY, 0);
		#endif
				recordPhotoOp.add_mode = PHOTO_NONE_MODE;
			}	
		}
	#endif	
			
	}else if(keyState == KEY_RELEASE && recordPhotoOp.upkeystate  == KEY_CONTINUE)
	{
		if(recordPhotoOp.add_mode != PHOTO_MAGIC_MODE)
		{
			task_com_keysound_play();
			task_com_sound_wait_end();
			recordPhotoLineShow(handle, 2); //yellow
			recordPhotoScalerBarShow(handle, 0);
			app_draw_Service(1);
			hal_sysDelayMS(10);	
			
			recordPhotoLineShow(handle, 0); //tranfer
			app_draw_Service(1);
				
		}

	}else if(keyState == KEY_CONTINUE)		
	{
		if(recordPhotoOp.add_mode != PHOTO_MAGIC_MODE)
		{
			
		#if LCDSHOW_CSI_SCALE
			while(hal_lcdWinUpdataCheckDone()) hal_wdtClear;
			app_lcdVideoShowScaler_cfg(1);
			recordPhotoScalerShow(handle);
			recordPhotoScalerBarShow(handle, 1);
			//if(hal_lcdWinUpdataCheckDone() >= 0)
			//{
			//	app_lcdVideoShowScaler_cfg(1);
			//	recordPhotoScalerShow(handle);
//
			//}else
			//{
			//	deg_Printf("1111\n");
			//}
			if(recordPhotoOp.upkeystate  == KEY_PRESSED)
			{
				recordPhotoLineShow(handle, 1); //red	
				app_draw_Service(1);		
			}
		#endif	
		}
			
	}
	recordPhotoOp.upkeystate  = keyState;
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoKeyMsgDown
* Description    : recordPhotoKeyMsgDown
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(recordPhotoOp.capture_kick)
	{
		recordPhotoOp.downkeystate = KEY_STATE_INVALID;
		return 0;
	}
	if(keyState == KEY_RELEASE && recordPhotoOp.downkeystate  == KEY_PRESSED)
	{
		task_com_keysound_play();
	#if FUN_KID_FRAME_EN || DEV_SENSOR_FILTER_EN || DEV_SENSOR_MAGIC_EN
		//deg_Printf("ENTER [MODE:%d, %d,%d,%d]\n",recordPhotoOp.add_mode,SysCtrl.sensor_filter_index, SysCtrl.kid_frame_index, SysCtrl.sensor_magic_index);
		if(recordPhotoOp.add_mode == PHOTO_NONE_MODE)
		{
			recordPhotoOp.add_mode = PHOTO_KIDFRAME_MODE;
		}
		if(recordPhotoOp.add_mode == PHOTO_KIDFRAME_MODE)
		{
		#if FUN_KID_FRAME_EN
			app_kid_frame_ctrl(KID_FRAME_SHOWNEXT, (MJPEG_PHOTO_ENC_TYPE == MJPEG_ENC_SRC_LCD) ? 0 : 1);
		#endif
			if(SysCtrl.kid_frame_index < 0)
			{
			#if FUN_KID_FRAME_EN
				app_kid_frame_ctrl(KID_FRAME_DISTROY, 0);
			#endif
				recordPhotoOp.add_mode = PHOTO_MAGIC_MODE;	 //分屏显示暂不支持放大
				//app_lcdVideoShowScaler_cfg(0);
				//recordPhotoScalerShow(handle);
			}
		}
		if(recordPhotoOp.add_mode == PHOTO_MAGIC_MODE)
		{
		#if DEV_SENSOR_MAGIC_EN
			app_sensor_magic_ctrl(SENSOR_MAGIC_CHANGE_NEXT, (MJPEG_PHOTO_ENC_TYPE == MJPEG_ENC_SRC_LCD) ? 0 : 1);
		#endif
			if(SysCtrl.sensor_magic_index < 0)
				recordPhotoOp.add_mode = PHOTO_LENS_MODE;	
		}
		if(recordPhotoOp.add_mode == PHOTO_LENS_MODE)
		{
		#if DEV_SENSOR_LENS_EN
			app_sensor_lens_ctrl(SENSOR_LENS_CHANGE_NEXT, (MJPEG_PHOTO_ENC_TYPE == MJPEG_ENC_SRC_LCD) ? 0 : 1 );
		#endif
			if(SysCtrl.sensor_lens_index < 0)
				recordPhotoOp.add_mode = PHOTO_FILTER_MODE;	
		}
		if(recordPhotoOp.add_mode == PHOTO_FILTER_MODE)
		{
		#if DEV_SENSOR_FILTER_EN
			app_sensor_filter_ctrl(SENSOR_FILTER_CHANGE_NEXT);
		#endif
			if(SysCtrl.sensor_filter_index < 0)
				recordPhotoOp.add_mode = PHOTO_NONE_MODE;			
		}
		//deg_Printf("EXIT [MODE:%d, %d,%d,%d]\n",recordPhotoOp.add_mode,SysCtrl.sensor_filter_index, SysCtrl.kid_frame_index, SysCtrl.sensor_magic_index);	
	#endif
		task_com_sound_wait_end();
	}else if(keyState == KEY_RELEASE && recordPhotoOp.downkeystate  == KEY_CONTINUE)
	{
		if(recordPhotoOp.add_mode != PHOTO_MAGIC_MODE)
		{
			task_com_keysound_play();
			recordPhotoLineShow(handle, 2); //yellow
			recordPhotoScalerBarShow(handle, 0);
			app_draw_Service(1);
			hal_sysDelayMS(10);	
			
			recordPhotoLineShow(handle, 0); //tranfer
			app_draw_Service(1);
			task_com_sound_wait_end();
		}
	
	}else if(keyState == KEY_CONTINUE)		
	{
		if(recordPhotoOp.add_mode != PHOTO_MAGIC_MODE)
		{
		#if LCDSHOW_CSI_SCALE
			while(hal_lcdWinUpdataCheckDone()) hal_wdtClear;
			app_lcdVideoShowScaler_cfg(-1);	
			recordPhotoScalerShow(handle);
			recordPhotoScalerBarShow(handle, 1);
			//if(hal_lcdWinUpdataCheckDone() >= 0)
			//{
			//	app_lcdVideoShowScaler_cfg(-1);	
			//	recordPhotoScalerShow(handle);
			//}else
			//{
			//	deg_Printf("22222\n");
			//}
			if(recordPhotoOp.downkeystate  == KEY_PRESSED)
			{
				recordPhotoLineShow(handle, 1); //yellow	
				app_draw_Service(1);		
			}	
		#endif	
			

		}
			
	}
	recordPhotoOp.downkeystate  = keyState;
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoKeyMsgLeft
* Description    : recordPhotoKeyMsgLeft
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgLeft(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(recordPhotoOp.capture_kick)
	{
		return 0;
	}
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
		if(recordPhotoOp.capture_time)
		{
			user_config_set(CONFIG_ID_CAPTURE_TIME,R_ID_STR_COM_OFF);
		}else
		{
			user_config_set(CONFIG_ID_CAPTURE_TIME,R_ID_STR_TIM_5SEC);
		}
		recordPhotoOp.capture_time 	= user_configValue2Int(CONFIG_ID_CAPTURE_TIME);
		if(recordPhotoOp.capture_time)
		{
			recordPhotoOp.capture_mode = 1;
		}else
		{
			recordPhotoOp.capture_mode = 0;
		}	
		recordPhotoCaptureShow(handle);
		task_com_sound_wait_end();		
	}
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoKeyMsgRight
* Description    : recordPhotoKeyMsgRight
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgRight(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(recordPhotoOp.capture_kick)
	{
		return 0;
	}
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
		app_Cmos_Sensor_Switch();
		recordPhotoOp.add_mode = PHOTO_NONE_MODE;
		task_com_sound_wait_end();		
	}
	return 0;
}

/*******************************************************************************
* Function Name  : recordPhotoKeyMsgMode
* Description    : recordPhotoKeyMsgMode
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(recordPhotoOp.capture_kick)
	{
		recordPhotoOp.capture_kick = 0;
		recordPhotoOp.capture_cnt = 0;
		recordPhotoCaptureTimeShow(handle);
		task_com_keysound_play();
		task_com_sound_wait_end();
		return 0;
	}
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
		task_com_sound_wait_end();	
		SysCtrl.winChangeEnable = 1;
		app_taskStart(TASK_MAIN,0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoSysMsgSD
* Description    : recordPhotoSysMsgSD
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	recordPhotoSDShow(handle);
	task_com_spijpg_Init(0);
	task_com_sdlist_scan(0, 0);
	taskRecordPhotoRemainCal();
	recordPhotoRemainShow(handle);
	if(SysCtrl.spi_jpg_list < 0)
	{
		if(recordPhotoOp.capture_kick == 0)
		{
			task_com_tips_show(TIPS_TYPE_SD);
			return 0;
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoSysMsgUSB
* Description    : recordPhotoSysMsgUSB
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoSysMsgUSB(winHandle handle,uint32 parameNum,uint32* parame)
{
	recordPhotoBatteryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoSysMsgBattery
* Description    : recordPhotoSysMsgBattery
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoSysMsgBattery(winHandle handle,uint32 parameNum,uint32* parame)
{
	recordPhotoBatteryShow(handle);
	return 0;
}

/*******************************************************************************
* Function Name  : recordPhotoSysMsgSec
* Description    : recordPhotoSysMsgSec
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoSysMsg1s(winHandle handle,uint32 parameNum,uint32* parame)
{
	if(recordPhotoOp.capture_mode && recordPhotoOp.capture_kick)
	{
		if(recordPhotoOp.capture_cnt)
		{
			recordPhotoCaptureTimeShow(handle);
			recordPhotoOp.capture_cnt--;
			
		}else{
			recordPhotoOp.capture_kick = 0;
			recordPhotoCaptureTimeShow(handle);
			
			recordPhotoProcess(handle);
			recordPhotoRemainShow(handle);
		}
	}
	recordPhotoSysTimeShow(handle);

	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoOpenWin
* Description    : recordPhotoOpenWin
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoOpenWin(winHandle handle,uint32 parameNum,uint32* parame)
{
	deg_Printf("[WIN]recordPhotoOpenWin\n");
	
	task_com_spijpg_Init(0);
	task_com_sdlist_scan(0, 0);
	taskRecordPhotoRemainCal();
	recordPhotoSDShow(handle);
	recordPhotoCaptureShow(handle);
	recordPhotoRemainShow(handle);
	recordPhotoResShow(handle);
	recordPhotoSysTimeShow(handle);
	recordPhotoBatteryShow(handle);
	recordPhotoScalerBarShow(handle, 0);
	recordPhotoScalerShow(handle);
	recordPhotoCaptureTimeShow(handle);


	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoCloseWin
* Description    : recordPhotoCloseWin
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoCloseWin(winHandle handle,uint32 parameNum,uint32* parame)
{
	deg_Printf("[WIN]recordPhotoCloseWin\n");
	return 0;
}

ALIGNED(4) msgDealInfor photoEncodeMsgDeal[] =
{
	{SYS_OPEN_WINDOW,	recordPhotoOpenWin},
	{SYS_CLOSE_WINDOW,	recordPhotoCloseWin},
	{SYS_CHILE_COLSE,	recordPhotoOpenWin},
	{KEY_EVENT_OK,		recordPhotoKeyMsgOk},
	{KEY_EVENT_UP,		recordPhotoKeyMsgUp},
	{KEY_EVENT_DOWN,	recordPhotoKeyMsgDown},
	{KEY_EVENT_LEFT,	recordPhotoKeyMsgLeft},
	{KEY_EVENT_RIGHT,	recordPhotoKeyMsgRight},
	{KEY_EVENT_POWER,	recordPhotoKeyMsgPower},
	{SYS_EVENT_SDC,		recordPhotoSysMsgSD},
	{SYS_EVENT_USBDEV,	recordPhotoSysMsgUSB},
	{SYS_EVENT_BAT,		recordPhotoSysMsgBattery},
	{SYS_EVENT_1S,		recordPhotoSysMsg1s},
	{EVENT_MAX,			NULL},
};
WINDOW(recordPhotoWindow,photoEncodeMsgDeal,recordPhotoWin)




