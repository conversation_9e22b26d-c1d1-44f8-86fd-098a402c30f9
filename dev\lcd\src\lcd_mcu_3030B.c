/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_MCU_3030B

#define CMD(x)    LCD_CMD_MCU_CMD8(x)
#define DAT(x)    LCD_CMD_MCU_DAT8(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)



LCD_INIT_TAB_BEGIN()


#if 0//新驱动
	CMD(0xfd),//private_access
	DAT(0x06),
	DAT(0x08),
	//osc 11.2K 45.87M
	//CMD(0x60),//
	//DAT(0x03),//osc_user_adj[3:0] 77.6
	//DAT(0x01),//
	//DAT(0x01),//01
	CMD(0x61),//
	DAT(0x07),//
	DAT(0x04),//
	//bias
	CMD(0x62),//bias setting
	DAT(0x00),//01
	DAT(0x44),//04 44
	DAT(0x40),//44 65 40
	//VSP
	CMD(0x65),//Pump1=4.7MHz //PUMP1 VSP
	DAT(0x08),//D6-5:pump1_clk[1:0] clamp 28 2b
	DAT(0x10),//6.26
	DAT(0x21),

	//VSN
	CMD(0x66), //pump=2 AVCL
	DAT(0x08), //clamp 08 0b 09
	DAT(0x10), //10
	DAT(0x21),
	//add source_neg_time
	CMD(0x67),//pump_sel
	DAT(0x20),//21 20
	DAT(0x40),
	//gamma vap/van
	CMD(0x68),//gamma vap/van
	DAT(0x9F),//78-90-af 9f
	DAT(0x30),//
	DAT(0x2a),//
	DAT(0x21),//
	CMD(0xb1),//frame rate
	DAT(0x0f),//0x0f fr_h[5:0]
	DAT(0x02),//0x02 fr_v[4:0]
	DAT(0x03),//0x04 fr_div[2:0]
	CMD(0xB4),
	DAT(0x01), //2dot
	////porch
	CMD(0xB5),
	DAT(0x02),//0x02 vfp[6:0]
	DAT(0x02),//0x02 vbp[6:0]
	DAT(0x0a),//0x0A hfp[6:0]
	DAT(0x14),//0x14 hbp[6:0]
	CMD(0xB6), //display function
	DAT(0x44), //rev sm
	DAT(0x00), //gs norblack
	DAT(0x9f),
	DAT(0x00),
	DAT(0x02),
	//source
	CMD(0xE6),
	DAT(0x00),
	DAT(0xff),//SC_EN_START[7:0] f0
	CMD(0xE7),
	DAT(0x01),//CS_START[3:0] 01
	DAT(0x04),//scdt_inv_sel cs_vp_en
	DAT(0x03),//CS1_WIDTH[7:0] 12
	DAT(0x03),//CS2_WIDTH[7:0] 12
	DAT(0x00),//PREC_START[7:0] 06
	DAT(0x12),//PREC_WIDTH[7:0] 12
	CMD(0xE8), //source
	DAT(0x00), //VCMP_OUT_EN 81-vcmp/vref_output pad
	DAT(0x50), //chopper_sel[6:4]

	DAT(0x00), //gchopper_sel[6:4]
	////gate
	CMD(0xEc),
	DAT(0x52),//47
	////gamme sel
	CMD(0xdf),//
	DAT(0x11),//gofc_gamma_en_sel=1
	////gamma_test1 A1#_wangly
	CMD(0xe0), //gmama set 2.2
	DAT(0x04), //PKP0[4:0]
	DAT(0x05), //PKP1[4:0]
	DAT(0x0c), //PKP2[4:0]
	DAT(0x0f), //PKP3[4:0]
	DAT(0x0e), //PKP4[4:0]
	DAT(0x10), //PKP5[4:0]
	DAT(0x0c), //PKP6[4:0]
	DAT(0x16), //PKP6[4:0]
	CMD(0xe3),
	DAT(0x16), //PKN0[4:0]
	DAT(0x13), //PKN1[4:0]
	DAT(0x10), //PKN2[4:0]
	DAT(0x0d), //PKN3[4:0]
	DAT(0x0e), //PKN4[4:0]
	DAT(0x09), //PKN5[4:0]
	DAT(0x04), //PKN6[4:0]
	DAT(0x05), //PKN6[4:0]
	CMD(0xe1),
	DAT(0x2f), //PRP0[6:0]
	DAT(0x72), //PRP1[6:0]
	CMD(0xe4),
	DAT(0x79), //PRN0[6:0]
	DAT(0x2f), //PRN1[6:0]
	CMD(0xe2),
	DAT(0x26), //VRP0[5:0]
	DAT(0x1f), //VRP1[5:0]
	DAT(0x23), //VRP2[5:0]
	DAT(0x36), //VRP3[5:0]
	DAT(0x38), //VRP4[5:0]
	DAT(0x3f), //VRP5[5:0]
	CMD(0xe5),
	DAT(0x3f), //VRN0[5:0]
	DAT(0x3a), //VRN1[5:0]
	DAT(0x36), //VRN2[5:0]
	DAT(0x20), //VRN3[5:0]
	DAT(0x27), //VRN4[5:0]
	DAT(0x26), //VRN5[5:0]
	CMD(0xF6),
	DAT(0x01),
	DAT(0x30),
	// CMD(0xF1),
	// DAT(0x01),//te
	// DAT(0x01),
	// DAT(0x02),

	CMD(0xfd),
	DAT(0xfa),
	DAT(0xfc),
	CMD(0x3a),
	DAT(0x55),//SH 0x66

	CMD(0x35),
	DAT(0x00),

	CMD(0x36),//bgr_[3]
	DAT(0x00),//c0
	CMD(0x11), // exit sleep
	DLY(100),
	//Delay(200),
	CMD(0x29), // display on
	DLY(10),
	//Delay(10),
	CMD(0x2c),
#elif 1//20250317
 CMD(0xFD),
 DAT(0x06),
 DAT(0x08),
 CMD(0x61),
 DAT(0x07),
 DAT(0x07),

 CMD(0x73),
 DAT(0x70),
 CMD(0x73),
 DAT(0x00), //07
 CMD(0x62),//
 DAT(0x00),
 DAT(0x44),
 DAT(0x40),

 CMD(0x63),
 DAT(0x41),
 DAT(0x07),
 DAT(0x12),
 DAT(0x12),

 CMD(0x64),
 DAT(0x37),
 CMD(0x65),
 DAT(0x09),
 DAT(0x10),
 DAT(0x21),

 CMD(0x65),
 DAT(0x09),
 DAT(0x10),
 DAT(0x21),
 

 CMD(0x66),
 DAT(0x09),
 DAT(0x10),
 DAT(0x21),

 CMD(0x67),
 DAT(0x21),
 DAT(0x40),

 CMD(0x68),
 DAT(0x50),
 DAT(0x60),
 DAT(0x3C),//VCOM
 DAT(0x1C),

 CMD(0xB1),
 DAT(0x0f),
 DAT(0x02),
 DAT(0x03),
 CMD(0xB4),
 DAT(0x01),

 CMD(0xB5),
 DAT(0x02),
 DAT(0x02),//
 DAT(0x0a),//
 DAT(0x14),//

 CMD(0xb6),
 DAT(0x44),
 DAT(0x01),
 DAT(0x9f),
 DAT(0x00),
 DAT(0x02),

 CMD(0xdf),
 DAT(0x11),
 CMD(0xE0),
 DAT(0x04),//pkp0[4:0] V60
 DAT(0x04),//pkp1[4:0] V56
 DAT(0x0C),//pkp2[4:0] V45

 DAT(0x0E),//pkp3[4:0] V37
 DAT(0x10),//pkp4[4:0] V29 //
 DAT(0x0F),//pkp5[4:0] V21
 DAT(0x13),//pkp6[4:0] V7
 DAT(0x17),//pkp7[4:0] V3

 CMD(0xE3),
 DAT(0x17),//pkn0[4:0] V3
 DAT(0x13),//pkn1[4:0] V7
 DAT(0x0D),//pkn2[4:0] V21
 DAT(0x0B),//pkn3[4:0] V29 //
 DAT(0x0F),//pkn4[4:0] V37
 DAT(0x0C),//pkn5[4:0] V45
 DAT(0x05),//pkn6[4:0] V56
 DAT(0x05),//pkn7[4:0] V60

 CMD(0xE1),
 DAT(0x0A),//prp0[6:0] V51
 DAT(0x68),//prp1[6:0] V15 63 68

 CMD(0xE4),
 DAT(0x68),//prn0[6:0] V15 6e 68
 DAT(0x1E),//prn1[6:0] V51

 CMD(0xE2),
 DAT(0x05),//vrp0[5:0] V63
 DAT(0x06),//vrp1[5:0] V62
 DAT(0x05),//vrp2[5:0] V61
 DAT(0x33),//vrp3[5:0] V2
 DAT(0x34),//vrp4[5:0] V1
 DAT(0x3A),//vrp5[5:0] V0

 CMD(0xE5),
 DAT(0x3A),//vrn0[5:0] V0
 DAT(0x35),//vrn1[5:0] V1
 DAT(0x32),//vrn2[5:0] V2
 DAT(0x05),//vrn3[5:0] V61
 DAT(0x06),//vrn4[5:0] V62
 DAT(0x05),//vrn5[5:0] V63

 CMD(0xe6),
 DAT(0x00),
 DAT(0xff),

 CMD(0xe7),
 DAT(0x01),
 DAT(0x04),
 DAT(0x03),
 DAT(0x03),
 DAT(0x00),
 DAT(0x12),

 CMD(0xE8),
 DAT(0x00),
 DAT(0x70),
 DAT(0x00),
 CMD(0xec),
 DAT(0x52),

 CMD(0xf1),
 DAT(0x01),
 DAT(0xAA),
 DAT(0xAB),

 CMD(0xF6),
 DAT(0x01),
 DAT(0x30),//epf[1:0]
 DAT(0x00),
 DAT(0x00),//SPI2L: 40
 
 CMD(0xfd),
 DAT(0xfa),
 DAT(0xfc),
 CMD(0x3a),
 DAT(0x55),
 CMD(0x35),
 DAT(0x00),
 CMD(0x36),
 DAT(0x00), //00
 CMD(0x11), // exit sleep
	DLY(200),
 CMD(0x29), // display on
	DLY(10),
 CMD(0x2C),


#elif 0//20250402

CMD(0xFD),
DAT(0x06),
DAT(0x08),
CMD(0x61),
DAT(0x07),
DAT(0x07),
CMD(0x73),
DAT(0x70),
CMD(0x73),
DAT(0x00), //07
CMD(0x62),//
DAT(0x00),
DAT(0x44),
DAT(0x40),
CMD(0x63),
DAT(0x41),
DAT(0x07),
DAT(0x12),
DAT(0x12),
CMD(0x64),
DAT(0x37),
CMD(0x65),
DAT(0x09),
DAT(0x10),
DAT(0x21),
CMD(0x66),
DAT(0x09),
DAT(0x10),
DAT(0x21),
CMD(0x67),
DAT(0x21),
DAT(0x40),
CMD(0x68),
DAT(0x5d),
DAT(0x4c),//
DAT(0x2c),//
DAT(0x1c),//
CMD(0xB1),
DAT(0x0f), //0c
DAT(0x02), //00
DAT(0x07), //01
CMD(0xB4),
DAT(0x01),
CMD(0xB5),
DAT(0x02),
DAT(0x02),//
DAT(0x0a),//
DAT(0x14),//
CMD(0xb6),
DAT(0x44),
DAT(0x01),
DAT(0x9f),
DAT(0x00),
DAT(0x02),
CMD(0xdf),
DAT(0x11),
///////////NV3030A2 GAMMA/////////////////
CMD(0xe0), //gmama set 2.2
DAT(0x01), //PKP0[4:0]
DAT(0x06), //PKP1[4:0]
DAT(0x0e), //PKP2[4:0]
DAT(0x10), //PKP3[4:0]
DAT(0x0e), //PKP4[4:0]
DAT(0x0c), //PKP5[4:0]
DAT(0x0a), //PKP6[4:0]
DAT(0x16), //PKP6[4:0]
CMD(0xe3),
DAT(0x16), //PKN0[4:0]
DAT(0x13), //PKN1[4:0]
DAT(0x14), //PKN2[4:0]
DAT(0x10), //PKN3[4:0]
DAT(0x0f), //PKN4[4:0]
DAT(0x0f), //PKN5[4:0]
DAT(0x04), //PKN6[4:0]
DAT(0x01), //PKN6[4:0]
CMD(0xe1),
DAT(0x14), //PRP0[6:0]
DAT(0x68), //PRP1[6:0]
CMD(0xe4),
DAT(0x68), //PRN0[6:0]
DAT(0x14), //PRN1[6:0]
CMD(0xe2),
DAT(0x00), //VRP0[5:0]
DAT(0x0a), //VRP1[5:0]
DAT(0x09), //VRP2[5:0]
DAT(0x30), //VRP3[5:0]
DAT(0x39), //VRP4[5:0]
DAT(0x3f), //VRP5[5:0]
CMD(0xe5),
DAT(0x3f), //VRN0[5:0]
DAT(0x33), //VRN1[5:0]
DAT(0x28), //VRN2[5:0]
DAT(0x09), //VRN3[5:0]
DAT(0x0a), //VRN4[5:0]
DAT(0x00), //VRN5[5:0]
CMD(0xe6),
DAT(0x00),
DAT(0xff),
CMD(0xe7),
DAT(0x01),
DAT(0x04),
DAT(0x03),
DAT(0x03),
DAT(0x00),
DAT(0x12),
CMD(0xE8),
DAT(0x00),
DAT(0x70),
DAT(0x00),
CMD(0xec),
DAT(0x52),
CMD(0xf1),
DAT(0x01),
DAT(0x01),
DAT(0x02),
CMD(0xF6),
DAT(0x01),
DAT(0x30),//epf[1:0]
DAT(0x00),
DAT(0x00),//SPI2L: 40
CMD(0xfd),
DAT(0xfa),
DAT(0xfc),
CMD(0x3a),
DAT(0x55),
CMD(0x35),
DAT(0x00),
CMD(0x11), // exit sleep
DLY(200),
CMD(0x29), // display on
DLY(10),
CMD(0x2c),
#elif 0
CMD(0xFD),
DAT(0x06),
DAT(0x08),
CMD(0x61),
DAT(0x07),
DAT(0x07),
CMD(0x73),
DAT(0x70),
CMD(0x73),
DAT(0x00), //07
CMD(0x62),//
DAT(0x00),
DAT(0x44),
DAT(0x40),
CMD(0x63),
DAT(0x41),
DAT(0x07),
DAT(0x12),
DAT(0x12),
CMD(0x64),
DAT(0x37),
CMD(0x65),
DAT(0x09),
DAT(0x10),
DAT(0x21),
CMD(0x66),
DAT(0x09),
DAT(0x10),
DAT(0x21),
CMD(0x67),
DAT(0x21),
DAT(0x40),
CMD(0x68),
DAT(0x5d),
DAT(0x4c),//
DAT(0x2c),//
DAT(0x1c),//


	CMD(0xb1),//frame rate
	DAT(0x0f),//0x0f fr_h[5:0]
	DAT(0x02),//0x02 fr_v[4:0]
	DAT(0x03),//0x04 fr_div[2:0]

CMD(0xB4),
DAT(0x01),

CMD(0xB5),
DAT(0x02),
DAT(0x02),
DAT(0x0a),
DAT(0x14),

CMD(0xb6),
DAT(0x44),
DAT(0x01),
DAT(0x9f),
DAT(0x00),
DAT(0x02),
CMD(0xdf),
DAT(0x11),
///////////NV3030A2 GAMMA/////////////////
CMD(0xe0), //gmama set 2.2
DAT(0x01), //PKP0[4:0]
DAT(0x06), //PKP1[4:0]
DAT(0x0e), //PKP2[4:0]
DAT(0x10), //PKP3[4:0]
DAT(0x0e), //PKP4[4:0]
DAT(0x0c), //PKP5[4:0]
DAT(0x0a), //PKP6[4:0]
DAT(0x16), //PKP6[4:0]
CMD(0xe3),
DAT(0x16), //PKN0[4:0]
DAT(0x13), //PKN1[4:0]
DAT(0x14), //PKN2[4:0]
DAT(0x10), //PKN3[4:0]
DAT(0x0f), //PKN4[4:0]
DAT(0x0f), //PKN5[4:0]
DAT(0x04), //PKN6[4:0]
DAT(0x01), //PKN6[4:0]
CMD(0xe1),
DAT(0x14), //PRP0[6:0]
DAT(0x68), //PRP1[6:0]
CMD(0xe4),
DAT(0x68), //PRN0[6:0]
DAT(0x14), //PRN1[6:0]
CMD(0xe2),
DAT(0x00), //VRP0[5:0]
DAT(0x0a), //VRP1[5:0]
DAT(0x09), //VRP2[5:0]
DAT(0x30), //VRP3[5:0]
DAT(0x39), //VRP4[5:0]
DAT(0x3f), //VRP5[5:0]
CMD(0xe5),
DAT(0x3f), //VRN0[5:0]
DAT(0x33), //VRN1[5:0]
DAT(0x28), //VRN2[5:0]
DAT(0x09), //VRN3[5:0]
DAT(0x0a), //VRN4[5:0]
DAT(0x00), //VRN5[5:0]
CMD(0xe6),
DAT(0x00),
DAT(0xff),
CMD(0xe7),
DAT(0x01),
DAT(0x04),
DAT(0x03),
DAT(0x03),
DAT(0x00),
DAT(0x12),
CMD(0xE8),
DAT(0x00),
DAT(0x70),
DAT(0x00),
CMD(0xec),
DAT(0x52),

// CMD(0xf1),
// DAT(0x01),
// DAT(0x01),
// DAT(0x02),


// CMD(0xF6),
// DAT(0x01),
// DAT(0x30),//epf[1:0]
// DAT(0x05),
// DAT(0x01),//SPI2L: 40

CMD(0xfd),
DAT(0xfa),
DAT(0xfc),
CMD(0x3a),
DAT(0x55),
CMD(0x35),
DAT(0x00),
CMD(0x11), // exit sleep
DLY(200),
CMD(0x29), // display on
DLY(10),
CMD(0x2c),
#endif


LCD_INIT_TAB_END()

LCD_UNINIT_TAB_BEGIN()
    CMD(0x28),
    DLY(10),
LCD_UNINIT_TA_ENDB()
LCD_DESC_BEGIN()
    .name 			= "MCU_3030B",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_90,//LCD_DISPLAY_ROTATE_270,
    .te_mode 		= LCD_MCU_TE_ENABLE,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div 		= LCD_PCLK_DIV(320*240*2*60),
    .clk_per_pixel 	= 2,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .data_mode = LCD_DATA_MODE0_8BIT_RGB565,

    .screen_w 		= 240,
    .screen_h 		= 320,

    .video_w  		= 320,
    .video_h 	 	= 240,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
// 	//20a1镜头用的是 LCD_SATURATION_DEFAULT)(100)
// 	.contrast   	= LCD_SATURATION_DEFAULT,//LCD_SATURATION_115,

//     .brightness 	= 0xff,

//    .saturation 	= LCD_SATURATION_DEFAULT,//LCD_SATURATION_115,
//     .contra_index 	= 5,//4,

//     .gamma_index 	= {3, 4, 3},

//     .asawtooth_index = {2, 2},

//     .lcd_ccm         = {0x100,0x000,0x000,
// 						0x000,0x100,0x000,
// 						0x000,0x000,0x100,
// 						0x000,0x000,0x000},
//     .lcd_saj         = LCD_SAJ_DEFAULT,
/*//20250317
	.contrast   	= LCD_SATURATION_145,

    .brightness 	= -10,

    .saturation 	= LCD_SATURATION_130,

    .contra_index 	= 5,//4,

    .gamma_index 	= {4, 4, 4},

    .asawtooth_index = {2, 2},




    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,
	*/

////20250402
	.contrast   	= LCD_SATURATION_160,

    .brightness 	= -12,

    .saturation 	= LCD_SATURATION_145,

    .contra_index 	= 4,//4,

    .gamma_index 	= {4, 5, 4},

    .asawtooth_index = {2, 2},




    .lcd_ccm         = {220,0x000,0x000,
						0x000,256,0x000,
						0x000,0x000,256,
						0x000,0x000,0x000},
    .lcd_saj         = LCD_SAJ_DEFAULT,


    INIT_TAB_INIT
    UNINIT_TAB_INIT
LCD_DESC_END()



#endif


