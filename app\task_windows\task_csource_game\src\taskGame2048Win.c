/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

enum
{
	G2048_TIPS_RECT_ID1 = 0,
	G2048_TIPS_RECT_ID2,
	G2048_TIPS_STR_TIP,

	G2048_MAX_ID
};

typedef struct{
	INT16U w;
	INT16U h;
	INT8U *ptr;
}ICON_INFOR;

typedef struct {
	INT16U icon_w;
	INT16U icon_h;
	INT16U transparent;
	INT16U pos_x;
	INT16U pos_y;
} DISPLAY_ICONSHOW;

enum {
	GAME_NUM_NULL = 0,//12
	GAME_NUM_2 ,
	GAME_NUM_4,
	GAME_NUM_8,
	GAME_NUM_16,
	GAME_NUM_32,//5
	GAME_NUM_64,
	GAME_NUM_128,
	GAME_NUM_256,
	GAME_NUM_512,
	GAME_NUM_1024,//10
	GAME_NUM_2048,
	GAME_PASS_TIPS,
	GAME_2048_ICON_MAX
};



//-----------------------------------------------------game-------------------------------------------------endf-----
#if 0//defined(  ICON_SAVE_DEBUG)
    ICON_INFOR  game2048_buff[GAME_ICON_MAX]={
        {GAME_ICON_MAX,GAME_ICON_MAX,NULL},
        {24,24,figure_up},
        {24,24,figure_down},
        {24,24,figure_left},
        {24,24,figure_right},
        {1,32,figure_bg},
        {24,24,figure_bg_wall},
        {24,24,figure_box},
        {24,24,figure_target_box},
        {24,24,figure_target_Point},
    };
#elif 1//
ICON_INFOR  game2048_buff[GAME_2048_ICON_MAX]={
    {GAME_2048_ICON_MAX,GAME_2048_ICON_MAX,NULL},
//    {32,32,NULL},
    {60,60,NULL},
    {60,60,NULL},
    {60,60,NULL},
    {60,60,NULL},
    {60,60,NULL},
    {60,60,NULL},
    {60,60,NULL},
    {60,60,NULL},
    {60,60,NULL},
    {60,60,NULL},
    {60,60,NULL},
    {60,60,NULL},
	{220,80,NULL},
};
#else
ICON_INFOR  game2048_buff[GAME_ICON_MAX]={
    {GAME_ICON_MAX,GAME_ICON_MAX,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
    {24,24,NULL},
};

#endif


UNUSED ALIGNED(4) const widgetCreateInfor g2048Win[] =
{
	createFrameWin( 				Rx(0),   Ry(0),   Rw(320), Rh(240), R_ID_PALETTE_DimGray,     WIN_ABS_POS|WIN_NOT_ZOOM),

	//createRect(G2048_TIPS_RECT_ID1,              Rx((220-104)/2),Ry((176-56)/2), Rw(104),Rh(56),R_ID_PALETTE_Yellow),
	//createRect(G2048_TIPS_RECT_ID2,              Rx((220-96)/2),Ry((176-48)/2), Rw(96),Rh(48),R_ID_PALETTE_Gray),
	//createStringIcon(G2048_TIPS_STR_TIP, Rx(0),Ry(105),Rw(220),Rh(30),R_ID_STR_COM_FAILED,ALIGNMENT_CENTER, R_ID_PALETTE_White,0),

	widgetEnd(),
};


