/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
ALIGNED(4) PLAYMP3_OP_T task_playMp3_op;
/*******************************************************************************
* Function Name  : taskPlayMp3MainStart
* Description    : taskPlayMp3MainStart
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
int taskPlayMp3MainStart(u32 index)
{
	int  ret = 0;
	char * name;
	int  type;	
	int  mp3_fd = -1;
	int  lrc_fd = -1;
	SysCtrl.play_total_time = 0;
	SysCtrl.play_cur_time   = 0;
	SysCtrl.play_last_time  = 0;

	SysCtrl.file_index 		= index;

	if(mp3_dec_sta() == MP3_DEC_UINT)
	{
		ret = -1;
		goto MP3_START_FAIL;
	}else if(mp3_dec_sta() >= MP3_DEC_START)
	{
		mp3_api_stop();
	}
	deg_Printf("[PLAY MP3] start: index = %d ",index);
	name = filelist_GetFileFullNameByIndex(SysCtrl.mp3_list,index,&SysCtrl.file_type);
	if(name == NULL || SysCtrl.file_type != FILELIST_TYPE_MP3)
	{
		ret = -2;
		goto MP3_START_FAIL;
	}
	mp3_fd = (int)fs_open(name,FA_READ);
	if(mp3_fd < 0)
	{
		ret = -3;
		goto MP3_START_FAIL;
	}
	deg_Printf("[PLAY MP3] file:%s\n ",name);
	fs_seek(mp3_fd,0,FA_CREATE_LINKMAP);// enable fast seek for this file  
	name = filelist_GetLrcFileFullNameByIndex(SysCtrl.mp3_list, index);
	if(name)
	{
		lrc_fd = (int)fs_open(name,FA_READ);
		deg_Printf("[LRC OPEN] file:%s\n ",name);
		if(lrc_fd < 0 )
		{
			deg_Printf("->fail");
		}
	}
	if(mp3_api_start(mp3_fd, lrc_fd) < 0)
	{
		mp3_api_stop();
		deg_Printf("[PLAY MP3] FAIL\n");
		ret = -4;
		goto MP3_START_FAIL;
	}
	mp3_dac_volume_cfg(task_com_curVolume_get());
	mp3_api_playtime_get(&SysCtrl.play_total_time, NULL);
	hx330x_str_cpy(task_playMp3_op.playfilename, filelist_GetFileNameByIndex(SysCtrl.mp3_list, (int)index));
	
	if(task_playMp3_op.playfirstIndex == INVALID_PLAY_INDEX || task_playMp3_op.playfirstIndex >= SysCtrl.file_cnt)
	{
		task_playMp3_op.playfirstIndex = index;
	}
	task_playMp3_op.playCurDirIndex = task_playMp3_op.opendirindex;
	
	deg_Printf("[PLAY MP3] total time:%dms\n",SysCtrl.play_total_time);
	
	XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_PLAY,UPDATE_PLAYTIME | UPDATE_LRCSHOW | UPDATE_FILEITEM_SHOW|UPDATA_START));
MP3_START_FAIL:
	if(ret < 0)
	{
		task_playMp3_op.playfirstIndex = INVALID_PLAY_INDEX; //停止循环播放音乐
	}
	return ret;
}

/*******************************************************************************
* Function Name  : taskPlayMp3ModeChange
* Description    : taskPlayMp3ModeChange
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
void taskPlayMp3ModeChange(void)
{
	switch(task_playMp3_op.playmode)
	{
		case PLAY_MP3_MODE_NONE: 		task_playMp3_op.playmode = PLAY_MP3_MODE_ALLCYCLE; break;
		case PLAY_MP3_MODE_ALLCYCLE:	task_playMp3_op.playmode = PLAY_MP3_MODE_ONECYCLE; break;
		case PLAY_MP3_MODE_ONECYCLE:	task_playMp3_op.playmode = PLAY_MP3_MODE_ORDER; break;
		case PLAY_MP3_MODE_ORDER:		task_playMp3_op.playmode = PLAY_MP3_MODE_RAND; break;
		case PLAY_MP3_MODE_RAND:		task_playMp3_op.playmode = PLAY_MP3_MODE_NONE; break;
		default:						task_playMp3_op.playmode = PLAY_MP3_MODE_ALLCYCLE; break;
	}
	if(mp3_dec_sta() >= MP3_DEC_START)
	{
		task_playMp3_op.playfirstIndex = SysCtrl.file_index;
	}else
		task_playMp3_op.playfirstIndex = INVALID_PLAY_INDEX;
}		
/*******************************************************************************
* Function Name  : taskPlayMp3OpenDir
* Description    : taskPlayMp3OpenDir
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
void taskPlayMp3OpenDir(char *dir)
{
	u32 fileindex = task_playMp3_op.dirfileindex[task_playMp3_op.opendirindex];
	hx330x_str_cpy(SysCtrl.file_fullname,dir);
	filelist_api_nodedestory(SysCtrl.mp3_list);
	SysCtrl.mp3_list		= filelist_api_nodecreate(SysCtrl.file_fullname, /*FILELIST_TYPE_DIR|*/FILELIST_TYPE_MP3, -1);
	filelist_api_scan(SysCtrl.mp3_list);
	SysCtrl.file_cnt = filelist_api_CountGet(SysCtrl.mp3_list);
	if(fileindex > SysCtrl.file_cnt)
		fileindex = 0;
	SysCtrl.file_index = fileindex;
	SysCtrl.play_total_time = 0;
}
/*******************************************************************************
* Function Name  : taskPlayMp3ParentDirOpen
* Description    : taskPlayMp3ParentDirOpen
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
int taskPlayMp3ParentDirOpen(void)
{
	char * parentDir;
	if(task_playMp3_op.opendirindex == 0)
		return -1;
	parentDir = filenode_parentdir_get(SysCtrl.mp3_list);
	if(parentDir == NULL)
		return -2;
	task_playMp3_op.opendirindex--;
	taskPlayMp3OpenDir(parentDir);
	return 0;
}
/*******************************************************************************
* Function Name  : taskPlayMp3ChildDirOpen
* Description    : taskPlayMp3ChildDirOpen
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
int taskPlayMp3ChildDirOpen(u32 index, char *childDir)
{
	if(task_playMp3_op.opendirindex > 9)
		return -1;
	task_playMp3_op.dirfileindex[task_playMp3_op.opendirindex] = index;
	task_playMp3_op.opendirindex++;
	task_playMp3_op.dirfileindex[task_playMp3_op.opendirindex] = 0;
	taskPlayMp3OpenDir(childDir);
	
	return 0;
}
/*******************************************************************************
* Function Name  : taskPlayMp3MainStart
* Description    : taskPlayMp3MainStart
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
void taskPlayMp3DirAllClose(void)
{
	task_playMp3_op.opendirindex = 0;
	task_playMp3_op.dirfileindex[0] = 0;
	SysCtrl.file_cnt = 0;
	filelist_api_nodedestory(SysCtrl.mp3_list);
	SysCtrl.mp3_list = -1;
}		
/*******************************************************************************
* Function Name  : taskPlayMp3MainStart
* Description    : taskPlayMp3MainStart
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
void taskPlayMp3RootDirOpen(void)
{
	deg_Printf("taskPlayMp3RootDirOpen:%d\n", SysCtrl.mp3_list);
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
	{
		if(SysCtrl.mp3_list < 0)
		{
			deg_Printf("taskPlayMp3RootDirOpen\n");
			task_playMp3_op.opendirindex = 0;
			task_playMp3_op.dirfileindex[0] = 0;
			taskPlayMp3OpenDir(FILEDIR_MP3);	
		}
	}else
	{
		taskPlayMp3DirAllClose();
	}

}	


/*******************************************************************************
* Function Name  : taskPlayMp3DirFindFile
* Description    : taskPlayMp3DirFindFile
* Input          : nextOrPrev: 1:next, 0: prev
* Output         : none
* Return         : none
*******************************************************************************/
int taskPlayMp3DirFindFile(u32 start_index, int end_index, u32 nextOrPrev)
{
	char * name;
	int  type;	
	u32  index;
	if(start_index >= SysCtrl.file_cnt)
		start_index = 0;
	index = start_index;
	if(end_index > 0)
	{
		if(end_index < SysCtrl.file_cnt)
		{
			if(start_index == end_index)	
				return -1;
			else
				start_index = end_index;		
		}

	}
	while(1)
	{
		name = filelist_GetFileFullNameByIndex(SysCtrl.mp3_list,index,&type);
		if(name != NULL && type == FILELIST_TYPE_MP3)
		{
			return (int)index;
		}
		if(nextOrPrev) //next
		{
			index++;
			if(index >= SysCtrl.file_cnt)
				index = 0;
		}else	//prev
		{
			if(index == 0)
				index = SysCtrl.file_cnt - 1;
			else
				index--;
		}

		if(index == start_index) //列表查询结束
			break;
	}
	return -1;

}
/*******************************************************************************
* Function Name  : taskPlayMp3ModeService
* Description    : taskPlayMp3ModeService
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
static void taskPlayMp3EndService(void)
{
	int index, end_index;
	if(task_playMp3_op.playfirstIndex == INVALID_PLAY_INDEX)
		return;
	switch(task_playMp3_op.playmode)
	{
		case PLAY_MP3_MODE_ALLCYCLE:  	index = SysCtrl.file_index + 1; end_index = -1; break;
		case PLAY_MP3_MODE_ONECYCLE:  	taskPlayMp3MainStart(SysCtrl.file_index); return;
		case PLAY_MP3_MODE_RAND:	  	index =  hx330x_abs(rand())%SysCtrl.file_cnt; 
										if(index == SysCtrl.file_index)
											index++;
										end_index = -1;
										break;
		case PLAY_MP3_MODE_ORDER:		index = SysCtrl.file_index + 1; end_index = task_playMp3_op.playfirstIndex; break;
		default: 						task_playMp3_op.playfirstIndex = INVALID_PLAY_INDEX;XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_PLAY,UPDATE_PLAYTIME)); return;
	}
	index = taskPlayMp3DirFindFile(index, end_index, 1); //find next file
	if(index >= 0)
	{
		taskPlayMp3MainStart((u32)index);
	}else
	{
		task_playMp3_op.playfirstIndex = INVALID_PLAY_INDEX;
		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_PLAY,UPDATE_PLAYTIME));
	}
}
/*******************************************************************************
* Function Name  : playMp3MainFindFileAndStart
* Description    : playMp3MainFindFileAndStart
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
int playMp3MainFindFileAndStart(int start_index)
{
	int res = -1;
	if(SysCtrl.file_cnt <= 0)
	{
		return -1;
	}
	deg_Printf("playMp3MainFindFileAndStart\n");
	while(1)
	{
		hal_wdtClear();
		task_playMp3_op.playfirstIndex = taskPlayMp3DirFindFile(start_index, -1, 1); //try to find a mp3 file
		if(task_playMp3_op.playfirstIndex >= 0)
		{
			res = taskPlayMp3MainStart((u32)task_playMp3_op.playfirstIndex);
			if(res)
			{
				start_index++;
				if(start_index >= SysCtrl.file_cnt)
				{
					return -2;
				} 
			}else
			{
				return 0;
			}
		}else{
			return -3;
		}
	}
}
/*******************************************************************************
* Function Name  : taskRecordWinChangeProcess
* Description    : taskRecordWinChangeProcess function.
* Input          : 
* Output         : none                                            
* Return         : int fd : file handle
*******************************************************************************/
static void taskPlayMp3WinChangeProcess(u8 enter)
{
	if(SysCtrl.winChangeEnable == 0)
		return;
	if(enter)
	{
		taskMainWinInit(0,MEDIA_SRC_NVFS, R_ID_IMAGE_MUSIC_PLAY, 0,MAIN_TO_SUB_VOR_UP);
		if(taskWinChangeProcess() < 0)
		{
			res_image_show(R_ID_IMAGE_MUSIC_PLAY, 0);
		}
	}else
	{
		taskMainWinInit(0,MEDIA_SRC_NVFS, R_ID_IMAGE_MUSIC_PLAY, 0,SUB_TO_MAIN_VOR_DOWN);
	}
}
/*******************************************************************************
* Function Name  : app_taskPlayAudioOpen
* Description    : APP LAYER: app_taskPlayAudioOpen
* Input          : u32 arg
* Output         : none
* Return         : none
*******************************************************************************/
static void app_taskPlayMp3Open(u32 arg)
{
	//hal_sysMemPrint();
	task_com_usb_dev_out(1);
	dusb_api_Uninit();
	app_lcdCsiVideoShowStop();
	hal_csiEnable(0);
#if  FUN_KID_FRAME_EN
	app_kid_frame_ctrl(KID_FRAME_DISTROY, 0);
#endif
#if  DEV_SENSOR_MAGIC_EN
	app_sensor_magic_ctrl(SENSOR_MAGIC_CHANGE_NONE, 0);
#endif
#if DEV_SENSOR_FILTER_EN
	app_sensor_filter_ctrl(SENSOR_FILTER_CHANGE_NONE);
#endif

#if DEV_SENSOR_LENS_EN
	app_sensor_lens_ctrl(SENSOR_LENS_CHANGE_NONE, 0);
#endif
	memset(&task_playMp3_op, 0, sizeof(task_playMp3_op));
	//hal_sysMemPrint();
	taskPlayMp3RootDirOpen();
	task_playMp3_op.playmode 		= user_config_get(CONFIG_ID_PLAY_MP3_MODE);
	task_playMp3_op.playfirstIndex 	= INVALID_PLAY_INDEX;
	mp3_api_init();
	taskPlayMp3WinChangeProcess(1);
	uiOpenWindow(&playMp3MainWindow, 0, 0);
	//res_image_show(R_ID_IMAGE_MUSIC_PLAY, 0);
	task_playMp3_op.freqCurTime = XOSTimeGet();
}
/*******************************************************************************
* Function Name  : app_taskPlayAudioOpen
* Description    : APP LAYER: app_taskPlayAudioOpen
* Input          : u32 arg
* Output         : none
* Return         : none
*******************************************************************************/
static void app_taskPlayMp3Close(u32 arg)
{
	//deg_Printf("mp3_api_uinit\n");
	mp3_api_uinit();
	//deg_Printf("taskPlayMp3DirAllClose\n");
	taskPlayMp3DirAllClose();
	task_com_usb_dev_out(0);
	res_iconBuffInit();
	taskPlayMp3WinChangeProcess(0);
	//hal_sysMemPrint();
}
/*******************************************************************************
* Function Name  : app_taskPlayAudioOpen
* Description    : APP LAYER: app_taskPlayAudioOpen
* Input          : u32 arg
* Output         : none
* Return         : none
*******************************************************************************/
static void app_taskPlayMp3Service(u32 arg)
{
	u32 play_event = 0;
	if(mp3_dec_sta() >= MP3_DEC_START)
	{
		if(mp3_api_service() < 0)
		{
			task_playMp3_op.playfirstIndex = INVALID_PLAY_INDEX;	//停止循环播放
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_PLAY,UPDATE_PLAYTIME));
			return;
		}
		mp3_api_playtime_get(NULL, &SysCtrl.play_cur_time);
		if(mp3_lrc_link_match(SysCtrl.play_cur_time) > 0)
		{
			play_event |= UPDATE_LRCSHOW;
		} 
		if((SysCtrl.play_last_time/1000 != SysCtrl.play_cur_time/1000)&&(SysCtrl.play_cur_time!=0))
		{
			SysCtrl.play_last_time = SysCtrl.play_cur_time;
			play_event |= UPDATE_PLAYTIME;
		}
		if(play_event)
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_PLAY,play_event));
		if(task_playMp3_op.freqCurTime + 200 < XOSTimeGet())
		{
			task_playMp3_op.freqCurTime = XOSTimeGet();
			if(mp3_dac_sta() == MP3_DAC_START || mp3_dac_sta() == MP3_DAC_RESUME)
			{
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_TIME_UPDATE,0));
			}				
		}

	}else if(mp3_dec_sta() == MP3_DEC_STOP)
	{
		taskPlayMp3EndService();
	}

}

ALIGNED(4) sysTask_T taskPlayMp3 =
{
	"Play mp3",
	0,
	app_taskPlayMp3Open,
	app_taskPlayMp3Close,
	app_taskPlayMp3Service,
};



