/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HAL_MJPENCODE_H
     #define HAL_MJPENCODE_H

#define _JPGA_SIZE_MIN_DEF_			(HAL_CFG_MJPEG_720_SIZE_MAX)
#define _JPGB_SIZE_MIN_DEF_			(HAL_CFG_MJPEG_VGA_SIZE_MAX)
#define  MJPEG_TYPE_AVI      		0
#define  MJPEG_TYPE_PHOTO      		1
#define  MJPEG_TYPE_UVC        		2


#define  MJPEG_ENC_SRC_CSI			0
#define  MJPEG_ENC_SRC_LCD			1

#define  M(w,h)		        		{(ROUND_UP(w,31) + 1) & ~1,(ROUND_UP(h,31) + 1) & ~1}
#define	 LDB_WORD(ptr)				(WORD)(((WORD)*((BYTE*)(ptr))<<8)|((WORD)*((BYTE*)(ptr) + 1)<<0))
#define	 LDB_DWORD(ptr)				(DWORD)(((DWORD)*((BYTE*)(ptr))<<24)|((DWORD)*((BYTE*)(ptr) + 1)<<16)|((DWORD)*((BYTE*)(ptr)+2)<<8)|((DWORD)*((BYTE*)(ptr)+3)<<0))


#define  MJPEG_ITEM_NUM         	HAL_CFG_MJPEG_BUFFER_NUM
#define  MJPEG_Q_MAX       			11

#define  MJPEG_ENLE_SUPPORT			1 //change this should updata lib file
#define  MJPEG_LLPKG_DOUBLEBUF		0


#define  MJPEG_PHOTO_ENC_TYPE		MJPEG_ENC_SRC_LCD//1: ENC LCD PHOTO, 0: ENC CSI PHOTO		
#define  MJPEG_VIDEO_ENC_TYPE		MJPEG_ENC_SRC_CSI//1: ENC LCD PHOTO, 0: ENC CSI PHOTO	
extern const u8 mjpegQualityTable[MJPEG_Q_MAX];

typedef enum{
	JPG_PKG_INIT  = 0,
	JPG_PKG_START = (1 << 0),
	JPG_PKG_END	  = (1 << 1),
	JPG_PKG_ERR	  = (1 << 2),
}JPG_PKG_FLAG;
typedef enum{
	ENC_MODE_NORMAL = 0,
	ENC_MODE_PKG,
	ENC_MODE_LLPKG,
}JPG_ENC_MODE;
typedef struct{
    u8* addr;
    u32 len;
}MJP_LINK_MAP;
/*******************************************************************************
* Function Name  : hal_mjpA_Sizecalculate
* Description    : hal layer .mjpeg output jpeg size
* Input          : u8 quailty : quality
				   u16 width : output width
				   u16 height: output height
* Output         : None
* Return         : int : size
*******************************************************************************/
u32 hal_mjpA_Sizecalculate(u8 quailty,u16 width,u16 height);
/*******************************************************************************
* Function Name  : hal_jA_fcnt_mnt
* Description    : hal layer .mjpA encode frame debg
* Input          : none 
* Output         : None
* Return         : none
*******************************************************************************/
u32 hal_jA_fcnt_mnt(void);

/*******************************************************************************
* Function Name  : hal_mjpAEncodePhotoResumePKG
* Description    : hal layer .mjpeg callback function for mjpeg irq encode, only one frame
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
int hal_mjpAEncodePhotoResumePKG(void);
/*******************************************************************************
* Function Name  : hal_mjpAEncodePhotoResumeLLPKG
* Description    : hal layer .mjpeg callback function for mjpeg irq encode, only one frame
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
int hal_mjpAEncodePhotoResumeLLPKG(void);
/*******************************************************************************
* Function Name  : hal_mjpAEncodePhotoResumeRam
* Description    : hal layer .mjpeg callback function for mjpeg irq encode, only one frame
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
int hal_mjpAEncodePhotoResumeRam(void);
/*******************************************************************************
* Function Name  : hal_mjpA_Start
* Description    : hal layer .mjpeg start.for sync to frame,this function will be callback by csi frame end
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_Start(void);
/*******************************************************************************
* Function Name  : hal_mjpA_Restart
* Description    : hal layer .mjpeg restart
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_Restart(void);
/*******************************************************************************
* Function Name  : hal_mjpA_EncodeInit
* Description    : hal layer .mjpeg initial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_EncodeInit(void);
/*******************************************************************************
* Function Name  : hal_mjpA_EncodeUninit
* Description    : hal layer .mjpeg uninitial 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_EncodeUninit(void);
/*******************************************************************************
* Function Name  : hal_mjpA_MemInit
* Description    : hal layer .mjpeg memory initial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpA_LineBuf_MenInit(u16 width,u16 height);
/*******************************************************************************
* Function Name  : hal_mjpA_MemInit
* Description    : hal layer .mjpeg memory initial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpA_buf_MenInit(void);
/*******************************************************************************
* Function Name  : hal_mjpA_MemUninit
* Description    : hal layer .mjpeg memory uninitial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_MemUninit(void);
/*******************************************************************************
* Function Name  : hal_mjpA_MemUninit
* Description    : hal layer .mjpeg memory uninitial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_linebufUninit(void);
/*******************************************************************************
* Function Name  : hal_mjpA_Enc_Video_Start
* Description    : hal layer .mjpeg initial 
* Input          : u8 type MJPEG_TYPE_AVI/MJPEG_TYPE_UVC
				   u16 win_w : output width
				   u16 win_h : output height
				   u8  quality: encode quality
				   u8  timestamp
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpA_Enc_Video_Start(u8 type, u16 target_w, u16 target_h, u8 quality, u8 timestamp);
/*******************************************************************************
* Function Name  : hal_mjpA_Enc_Photo_Start
* Description    : hal layer .mjpeg initial
* Input          : 
				   u16 win_w : output width
				   u16 win_h : output height
				   u8  quality: encode quality
				   u8  timestamp
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpA_Enc_Photo_Start(u16 target_w, u16 target_h, u8 quality, u8 timestamp);
/*******************************************************************************
* Function Name  : hal_mjpA_EncLcd_Photo_Start
* Description    : hal layer .mjpeg initial
* Input          : 
				   u16 win_w : output width
				   u16 win_h : output height
				   u8  quality: encode quality
				   u8  timestamp
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpA_EncLcd_Photo_Start(u16 target_w, u16 target_h, u8 quality, u8 timestamp);
/*******************************************************************************
* Function Name  : hal_mjpA_photo_encode_mode
* Description    : hal layer.set mjpeg fram raw data
* Input          : void *buffer : frame buffer
* Output         : None
* Return         : None
*******************************************************************************/
u32 hal_mjpA_photo_encode_mode(void);
/*******************************************************************************
* Function Name  : hal_mjpegRawBufferfree
* Description    : hal layer.set mjpeg fram raw data
* Input          : void *buffer : frame buffer
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_RawBufferfree(void);
/*******************************************************************************
* Function Name  : hal_mjpA_RawBufferGet
* Description    : hal layer.check raw buffer addr &length
* Input          : u32 *len : buff len
 				   u32 *sync: current buf frame stamp
				   u32 *sync_next: current buf next frame stamp
* Output         : None
* Return         : void * : buffer addr
*******************************************************************************/
void *hal_mjpA_RawBufferGet(u32 *len,u32 *sync, u32 *sync_next);
/*******************************************************************************
* Function Name  : sync_flag
* Description    : hal layer.check raw buffer addr &length
* Input          : u32 *len : buff len
 				   u32 *sync: current buf frame stamp
				   u32 *sync_next: current buf next frame stamp
* Output         : None
* Return         : void * : buffer addr
*******************************************************************************/
void *hal_mjpA_RkgBufferGet(u32 *len,u32 *sync, u32 *sync_next);
/*******************************************************************************
* Function Name  : hal_mjpA_Buffer_prefull
* Description    : hal layer.mjpeg buffer pre full(2/3) just
* Input          : None
* Output         : None
* Return         : true:mjpeg buffer is pre_full
*******************************************************************************/
bool hal_mjpA_Buffer_prefull(void);

/*******************************************************************************
* Function Name  : hal_mjpA_Buffer_halffull
* Description    : hal layer.mjpeg buffer pre full(2/3) just
* Input          : None
* Output         : None
* Return         : true:mjpeg buffer is half_full
*******************************************************************************/
bool hal_mjpA_Buffer_halffull(void);
/*******************************************************************************
* Function Name  : hal_mjpA_Enc_Stop
* Description    : hal layer .mjpeg stop
* Input          : u8 type MJPEG_TYPE_AVI/MJPEG_TYPE_PHOTO/MJPEG_TYPE_UVC
				   u16 win_w : output width
				   u16 win_h : output height
				   u8  quality: encode quality
				   u8  timestamp
* Output         : None
* Return         : None
*******************************************************************************/
#define  hal_mjpA_Enc_Stop   hal_mjpA_EncodeUninit

/*******************************************************************************
* Function Name  : hal_mjp_enle_unit
* Description    : 
* Input          : None
* Output         : None
* Return         :
*******************************************************************************/
void hal_mjp_enle_init(void);
/*******************************************************************************
* Function Name  : hal_mjp_enle_unit
* Description    : 
* Input          : None
* Output         : None
* Return         :
*******************************************************************************/
void hal_mjp_enle_unit(void);
/*******************************************************************************
* Function Name  : hal_mjp_enle_tab_get
* Description    :
* Input          : None
* Output         : None
* Return         : 
*******************************************************************************/
int hal_mjp_enle_buf_mdf(void);
/*******************************************************************************
* Function Name  : hal_mjp_enle_check
* Description    :
* Input          : None
* Output         : None
* Return         : 
*******************************************************************************/
int hal_mjp_enle_check(u16 src_w, u16 src_h, u16 tar_w, u16 tar_h, u8 quality, u8 timestamp, u8 buf_mode);
/*******************************************************************************
* Function Name  : hal_mjp_enle_tab_cfg
* Description    :
* Input          : None
* Output         : None
* Return         : 
*******************************************************************************/
void hal_mjp_enle_manual_kickstart(u32 y_addr, u32 uv_addr, u32 mjp_addr, u32 mjp_len);
/*******************************************************************************
* Function Name  : hal_mjp_enle_tab_cfg
* Description    :
* Input          : None
* Output         : None
* Return         : int�� < 0 : ERR
						 0:    continue
						 >0:   done, need to hal_mjp_enle_manual_kickstart
*******************************************************************************/
int hal_mjp_enle_manual_done(u32 len);
/*******************************************************************************
* Function Name  : hal_mjp_enle_tab_get
* Description    :
* Input          : None
* Output         : None
* Return         : 
*******************************************************************************/
MJP_LINK_MAP* hal_mjp_enle_tab_get(void);

#endif //HAL_MJPAENCODE_H

