/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef NES_DAC_H
#define NES_DAC_H

#define NES_DAC_FRAME_SIZE   			(735*2)  //44100/60 = 735 
#define NES_DAC_FRAME_NUM    			(60*5)
#define NES_DAC_BUFF_SIZE    			(NES_DAC_FRAME_SIZE*NES_DAC_FRAME_NUM)
#define NES_DAC_FRAME_COMBINE			2

typedef enum{
	NES_DAC_STOP = 0,
	NES_DAC_START,
	NES_DAC_PAUSE,
}NES_DAC_STAT;
typedef struct NES_DAC_OP_S
{
	BYTE 			stat;  //0: stop, 1: start, 2: pause
	BYTE			drop_flag;
	WORD			sync_combine_cnt;
	DWORD 			fcnt;
	DWORD 			bufLen;
	DWORD			frameLen;
	DWORD			syncLen;
	DWORD			sampleRate;
	BYTE			*buf;
	BYTE			*curBuffer;
	BYTE  			*curFrame;
	BYTE			*nextFrame;	
	DWORD 			volume;	
	Stream_Head_T 	stream;
	Stream_Node_T 	node[NES_DAC_FRAME_NUM];
}NES_DAC_OP_T;	
/*******************************************************************************
* Function Name  : nes_dac_volume_cfg
* Description    : nes_dac_volume_cfg:
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//NES_TEXT_SECTION
void nes_dac_volume_cfg(u32 volume);
/*******************************************************************************
* Function Name  : nes_dac_init
* Description    : nes_dac_init: 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//NES_TEXT_SECTION
int nes_dac_init(u32 samples_per_sync,u32 sample_rate);
/*******************************************************************************
* Function Name  : nes_dac_Uinit
* Description    : nes_dac_Uinit: 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//NES_TEXT_SECTION
void nes_dac_Uinit(void);
/*******************************************************************************
* Function Name  : mp3_dac_stream_malloc
* Description    : mp3_dac_stream_malloc: 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//NES_TEXT_SECTION
int nes_dac_stream_malloc(void);
/*******************************************************************************
* Function Name  : nes_dac_stream_in
* Description    : nes_dac_stream_in: 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//NES_TEXT_SECTION
int nes_dac_stream_in(void);
/*******************************************************************************
* Function Name  : nes_dac_start
* Description    : nes_dac_start: 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//NES_TEXT_SECTION
int nes_dac_start(void);
#endif
