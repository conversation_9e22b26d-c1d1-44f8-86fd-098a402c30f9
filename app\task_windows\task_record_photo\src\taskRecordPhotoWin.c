/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	PHOTO_MODE_ID =0,
	PHOTO_SD_ID,
	PHOTO_SDVOL_ID,
	PHOTO_CAPTURE_ID,
	PHOTO_REMAIN_ID,

	PHOTO_RESOLUTION_ID,
	PHOTO_SYSTIME_ID,
	PHOTO_SCALER_BAR_ID,
	PHOTO_SCALER_ID,
	PHOTO_BATERRY_ID,

	PHOTO_CAPTURE_TIME_ID,

	PHOTO_LINE_00,
	PHOTO_LINE_01,
	PHOTO_LINE_10,
	PHOTO_LINE_11,
	PHOTO_LINE_20,
	PHOTO_LINE_21,
	PHOTO_LINE_30,
	PHOTO_LINE_31,


	PHOTO_MAX_ID
};

/*******************************************************************************
* Function Name  : recordPhotoWin
* Description    : recordPhotoWin
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED ALIGNED(4) const widgetCreateInfor recordPhotoWin[] =
{
	createFrameWin( 							Rx(0),   Ry(0),   Rw(320), Rh(240), R_ID_PALETTE_Transparent,WIN_ABS_POS),
	createImageIcon(PHOTO_MODE_ID,      		Rx(4),   Ry(0),   Rw(32),  Rh(32),  R_ID_ICON_MTPHOTO, 		ALIGNMENT_LEFT),
	createImageIcon(PHOTO_SD_ID,        	    Rx(40),  Ry(0),   Rw(40),  Rh(32),  R_ID_ICON_MTSDCNORMAL,	ALIGNMENT_CENTER),
	createStringIcon(PHOTO_SDVOL_ID,     		Rx(40),  Ry(5),   Rw(36),  Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_GREEN2,DEFAULT_FONT),
	createImageIcon(PHOTO_CAPTURE_ID,    		Rx(104), Ry(0),   Rw(32),  Rh(32),  R_ID_ICON_MENUCLOCK,	ALIGNMENT_CENTER),
	createStringIcon(PHOTO_REMAIN_ID,      		Rx(240), Ry(0),   Rw(75),  Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_RIGHT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	createStringIcon(PHOTO_RESOLUTION_ID,		Rx(265), Ry(24),  Rw(50),  Rh(24),  RAM_ID_MAKE(" "),		ALIGNMENT_RIGHT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	createStringIcon(PHOTO_SYSTIME_ID,      	Rx(4),   Ry(208), Rw(220), Rh(32),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_LightYellow,DEFAULT_FONT),
	

	createProgressBarVer(PHOTO_SCALER_BAR_ID,	Rx(293), Ry(51),  Rw(12),  Rh(130), R_ID_PALETTE_Transparent,R_ID_PALETTE_DoderBlue, R_ID_PALETTE_White,ALIGNMENT_RIGHT),
	createStringIcon(PHOTO_SCALER_ID,      		Rx(265), Ry(184), Rw(50),  Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_RIGHT, 	R_ID_PALETTE_LightYellow,DEFAULT_FONT),
	createImageIcon(PHOTO_BATERRY_ID,    		Rx(265), Ry(208), Rw(50),  Rh(32), 	R_ID_ICON_MTBATTERY4,	ALIGNMENT_RIGHT),
	createStringIcon(PHOTO_CAPTURE_TIME_ID,     Rx(120), Ry(80),  Rw(80),  Rh(80),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_LightYellow,RES_FONT_NUM2),

	//x = 100, y = 90
	createLine(PHOTO_LINE_00,					Rx(70),  Ry(50),  Rx(90),  Ry(50),  Rw(5), 	R_ID_PALETTE_Transparent),
	createLine(PHOTO_LINE_01,					Rx(230), Ry(50),  Rx(250), Ry(50),  Rw(5), 	R_ID_PALETTE_Transparent),
	createLine(PHOTO_LINE_10,					Rx(70),  Ry(55),  Rx(70),  Ry(70),  Rw(5), 	R_ID_PALETTE_Transparent),
	createLine(PHOTO_LINE_11,					Rx(245), Ry(55),  Rx(245), Ry(70),  Rw(5), 	R_ID_PALETTE_Transparent),
	createLine(PHOTO_LINE_20,					Rx(70),  Ry(170), Rx(70),  Ry(185), Rw(5), 	R_ID_PALETTE_Transparent),
	createLine(PHOTO_LINE_21,					Rx(245), Ry(170), Rx(245), Ry(185), Rw(5), 	R_ID_PALETTE_Transparent),
	createLine(PHOTO_LINE_30,					Rx(70),  Ry(185), Rx(90),  Ry(185), Rw(5), 	R_ID_PALETTE_Transparent),
	createLine(PHOTO_LINE_31,					Rx(230), Ry(185), Rx(250), Ry(185), Rw(5), 	R_ID_PALETTE_Transparent),
	widgetEnd(),
};
/*******************************************************************************
* Function Name  : recordPhotoSDShow
* Description    : recordPhotoSDShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoSDShow(winHandle handle)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
	{
		uiWinSetResid(winItem(handle,PHOTO_SD_ID),R_ID_ICON_MTSDCNORMAL);
		uiWinSetVisible(winItem(handle,PHOTO_SDVOL_ID),1);
		uiWinSetResid(winItem(handle,PHOTO_SDVOL_ID),RAM_ID_MAKE(task_com_sdcCap_str()));
		
	}	
	else{
		uiWinSetResid(winItem(handle,PHOTO_SD_ID),R_ID_ICON_MTSDCNULL);
		uiWinSetVisible(winItem(handle,PHOTO_SDVOL_ID),0);
	}	
}
/*******************************************************************************
* Function Name  : recordPhotoCaptureShow
* Description    : recordPhotoCaptureShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoCaptureShow(winHandle handle)
{
	if(recordPhotoOp.capture_mode)
	{
		uiWinSetVisible(winItem(handle,PHOTO_CAPTURE_ID),1);
		uiWinSetfgColor(winItem(handle,PHOTO_CAPTURE_ID),R_ID_PALETTE_LightYellow);	
	}else
	{
		uiWinSetVisible(winItem(handle,PHOTO_CAPTURE_ID),0);
	}
}
/*******************************************************************************
* Function Name  : recordPhotoCaptureShow
* Description    : recordPhotoCaptureShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoCaptureTimeShow(winHandle handle)
{
	static char captureTimeStr[4] = {"000"};
	if(recordPhotoOp.capture_mode && recordPhotoOp.capture_cnt)
	{
		res_music_start(R_ID_MUSIC_KEY_SOUND,0,task_com_curVolume_get());
		if(recordPhotoOp.capture_cnt >= 10)
		{
			hx330x_num2str(captureTimeStr, recordPhotoOp.capture_cnt, 2);
		}else
		{
			hx330x_num2str(captureTimeStr, recordPhotoOp.capture_cnt, 1);
		}
		uiWinSetVisible(winItem(handle,PHOTO_CAPTURE_TIME_ID),1);
		uiWinSetResid(winItem(handle,PHOTO_CAPTURE_TIME_ID),RAM_ID_MAKE(captureTimeStr));
		

		task_com_sound_wait_end();
		res_music_end();
	}else
	{
		uiWinSetVisible(winItem(handle,PHOTO_CAPTURE_TIME_ID),0);
	}
}

/*******************************************************************************
* Function Name  : recordPhotoRemainShow
* Description    : recordPhotoRemainShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoRemainShow(winHandle handle)
{
	static char photoRemainStr[] = {"00000"};
	hx330x_num2str(photoRemainStr, recordPhotoOp.file_remain, 5);
	uiWinSetResid(winItem(handle,PHOTO_REMAIN_ID),RAM_ID_MAKE(photoRemainStr));
}
/*******************************************************************************
* Function Name  : recordPhotoResShow
* Description    : recordPhotoResShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static  void recordPhotoResShow(winHandle handle)
{
	u32 resid = user_config_get(CONFIG_ID_PRESLUTION);
	if(resid != 0)
	{
		uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),resid);
	}else
	{
		uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("???"));
	}

}
/*******************************************************************************
* Function Name  : videoSysTimeShow
* Description    : videoSysTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//u32 testid[]={
//	R_ID_STR_TEST1,
//	R_ID_STR_TEST2,
//	R_ID_STR_TEST3,
//	R_ID_STR_TEST4,
//	R_ID_STR_TEST5,
//	R_ID_STR_TEST6,
//	R_ID_STR_TEST7,
//	R_ID_STR_TEST8,
//	R_ID_STR_TEST9,
//	R_ID_STR_TESTa,
//	R_ID_STR_TESTb,
//	R_ID_STR_TESTc,
//
//};
UNUSED static void recordPhotoSysTimeShow(winHandle handle)
{
	//static u32 cnt = 0;
	//if(cnt < ARRAY_NUM(testid))
	//{
	//	uiWinSetResid(winItem(handle,PHOTO_SYSTIME_ID),testid[cnt]);
	//	cnt++;
	//}//else
	INT32U value = user_configValue2Int(CONFIG_ID_TIMESTAMP);
	if(value)
	uiWinSetResid(winItem(handle,PHOTO_SYSTIME_ID),RAM_ID_MAKE(hal_rtcTime2String(hal_rtcTimeGet())));
}

/*******************************************************************************
* Function Name  : recordPhotoScalerShow
* Description    : recordPhotoScalerShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoScalerShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,PHOTO_SCALER_ID),RAM_ID_MAKE(task_com_scaler_str()));
}
/*******************************************************************************
* Function Name  : recordPhotoScalerBarShow
* Description    : recordPhotoScalerBarShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoScalerBarShow(winHandle handle, u8 visable)
{
	u32 rate = task_com_scaler_rate();
	if(rate == 0)
	{
		visable = 0;
	}
	uiWinSetProgressRate(winItem(handle,PHOTO_SCALER_BAR_ID), rate);
	uiWinSetVisible(winItem(handle,PHOTO_SCALER_BAR_ID),visable);
}
/*******************************************************************************
* Function Name  : recordPhotoBatteryShow
* Description    : recordPhotoBatteryShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoBatteryShow(winHandle handle)
{
	uiWinSetVisible(winItem(handle,PHOTO_BATERRY_ID),1);
	uiWinSetResid(winItem(handle,PHOTO_BATERRY_ID),task_com_battery_res_get());

}
/*******************************************************************************
* Function Name  : recordPhotoResShow
* Description    : recordPhotoResShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static  void recordPhotoLineShow(winHandle handle, u32 sta)
{
	uiColor color;
	switch(sta)
	{
		case 0: color = R_ID_PALETTE_Transparent; break;
		case 1: color = R_ID_PALETTE_Red;		  break;
		case 2: color = R_ID_PALETTE_LightYellow; break;
		default: return;
	}
	uiWinSetbgColor(winItem(handle,PHOTO_LINE_00), color);
	uiWinSetbgColor(winItem(handle,PHOTO_LINE_01), color);
	uiWinSetbgColor(winItem(handle,PHOTO_LINE_10), color);
	uiWinSetbgColor(winItem(handle,PHOTO_LINE_11), color);
	uiWinSetbgColor(winItem(handle,PHOTO_LINE_20), color);
	uiWinSetbgColor(winItem(handle,PHOTO_LINE_21), color);
	uiWinSetbgColor(winItem(handle,PHOTO_LINE_30), color);
	uiWinSetbgColor(winItem(handle,PHOTO_LINE_31), color);
}





