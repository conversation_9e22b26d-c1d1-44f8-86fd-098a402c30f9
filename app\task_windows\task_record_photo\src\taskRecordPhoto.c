/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

ALIGNED(4) taskRecordPhotoOp recordPhotoOp;

/*******************************************************************************
* Function Name  : filelist_remain
* Description    : take a photo by user config
* Input          : none
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void taskRecordPhotoRemainCal(void)
{
	u16 dst_width, dst_height;
	u32 file_size; //kb
	u32 remain_size; //kb
	u32 reamin_file_max;
	u32 filelist_remain;
	u32 quli;
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL)
	{
		dst_width 	= user_configValue2Int(CONFIG_ID_PRESLUTION)>>16;
		dst_height 	= user_configValue2Int(CONFIG_ID_PRESLUTION)&0xffff;
		remain_size = SysCtrl.sdc_freesize;
		filelist_remain = filelist_api_MaxCountGet(SysCtrl.jpg_list) - filelist_api_CountGet(SysCtrl.jpg_list);
		quli = 8;
	}else if( SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
	{
		recordPhotoOp.file_remain = 0;
		return;
	}else
	{
		//hal_SensorResolutionGet(&dst_width,&dst_height);
		hal_lcdGetVideoRatioResolution(&dst_width,&dst_height);
		if(SysCtrl.spi_jpg_list < 0)
		{
			recordPhotoOp.file_remain = 0;
			return;
		}else
		{
			remain_size     = nvjpg_free_size()/1024;
			filelist_remain = filelist_api_MaxCountGet(SysCtrl.spi_jpg_list) - filelist_api_CountGet(SysCtrl.spi_jpg_list);
			if(filelist_remain > nvjpg_free_dir())
				filelist_remain = nvjpg_free_dir();
		}
		quli = 18;//24;
	}
	file_size = (u32)dst_width*dst_height*quli/(100*1024);
	reamin_file_max = remain_size/file_size;
	deg_Printf("file_size:%dkb, remain_size:%dkb\n", file_size, remain_size);
	if((SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL)&&( SysCtrl.dev_stat_sdc != SDC_STAT_FULL)){
		recordPhotoOp.file_remain = (reamin_file_max > filelist_remain)?filelist_remain: reamin_file_max;
	}else{

		recordPhotoOp.file_remain = reamin_file_max;

	}
	deg_Printf("taskRecordPhotoRemainCal:reamin_file_max: %d, filelist_remain:%d\n", reamin_file_max, filelist_remain);

}
/*******************************************************************************
* Function Name  : app_taskRecordPhoto_callback
* Description    : APP LAYER: app_taskRecordPhoto
* Input          : INT32U cmd,INT32U para
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
int app_taskRecordPhoto_callback(INT32U cmd,INT32U para)
{
	int *fdt = (int *)para;
	int ret;
	char *name;
	deg_Printf("CMD:%d\n",cmd);
	

	if(SysCtrl.spi_jpg_list < 0)
	{
		if(SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL)
		{
			//sd_api_unlock();
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SysCtrl.dev_stat_sdc));
			return -1;
		}
	}
	if(cmd == CMD_PHOTO_RECORD_START)
	{
		SysCtrl.jpg_fname.index = FILELIST_FLAG_IVL;
		if(recordPhotoOp.file_remain <= 0)
		{
			if(SysCtrl.spi_jpg_list < 0)
			{
				task_com_sdc_stat_set(SDC_STAT_FULL);
				return -5;
			}else
			{
				task_com_tips_show(TIPS_TYPE_SPI);
				return -6;	
			}
		}
		if(SysCtrl.spi_jpg_list < 0)
		{
			ret = filelist_SpaceCheck(SysCtrl.jpg_list,&SysCtrl.sdc_freesize,1024L*5); //5M
			if(ret<0)
			{
				task_com_sdc_stat_set(SDC_STAT_FULL);
				return -2;
			}
			//---------creater file name
			name = filelist_createNewFileFullName(SysCtrl.jpg_list, &SysCtrl.jpg_fname); //try to create new file name
			if(name == NULL)
			{
				deg_Printf("photo : create file name fail.\n");
				task_com_sdc_stat_set(SDC_STAT_FULL);
				return -3;
			}	
			hx330x_str_cpy(SysCtrl.file_fullname,name);
		//---------open file from file system
			//sd_api_lock();
			*fdt = fs_open(name,FA_CREATE_ALWAYS | FA_WRITE | FA_READ);
			if(*fdt < 0)
			{
				deg_Printf("photo : open file fail.%s\n",name);
				return -4;
			}	
		}else{
			name = filelist_createNewFileFullName(SysCtrl.spi_jpg_list, &SysCtrl.jpg_fname); //try to create new file name
			if(name == NULL)
			{
				deg_Printf("photo : create file name fail.\n");
				task_com_tips_show(TIPS_TYPE_SPI);
				return -3;
			}	
			hx330x_str_cpy(SysCtrl.file_fullname,name);
			deg_Printf("photo : open file.%s\n",name);
			if(NV_OK != nv_jpg_open(SysCtrl.jpg_fname.index & FILELIST_INDEX_MASK,NVFA_CREATE_ALWAYS | NVFA_WRITE | NVFA_READ))
			{
				*fdt = -1;
				deg_Printf("photo : open file fail.%s\n",name);
				task_com_tips_show(TIPS_TYPE_SPI);
				return -4;
			}
			else
			{
				*fdt = SysCtrl.jpg_fname.index & FILELIST_INDEX_MASK;
			}		
		}
	}else if(cmd == CMD_PHOTO_RECORD_STOP || cmd == CMD_COM_ERROR)
	{
		if((*fdt >= 0) && !(SysCtrl.jpg_fname.index & FILELIST_FLAG_IVL))
		{
			if(SysCtrl.spi_jpg_list < 0)
			{
				u32 filesize = fs_size(*fdt);
				fs_close(*fdt);
				if(cmd == CMD_PHOTO_RECORD_STOP)
				{
					task_com_sdc_freesize_modify(-1,filesize);
					filenode_addFileByFname(SysCtrl.jpg_list, &SysCtrl.jpg_fname);
					deg_Printf("photo : take photo ok.<%s>\n",SysCtrl.file_fullname);
				}else
				{
					f_unlink(SysCtrl.file_fullname);
					deg_Printf("photo  : take photo fail.<%s>\n",SysCtrl.file_fullname);
					SysCtrl.jpg_fname.index = FILELIST_FLAG_IVL;
				}
				//SysCtrl.jpg_fname.index = FILELIST_FLAG_IVL;
				*fdt = -1;
			}else
			{
				nv_jpg_close();
				if(cmd == CMD_PHOTO_RECORD_STOP)
				{
					filenode_addFileByFname(SysCtrl.spi_jpg_list, &SysCtrl.jpg_fname);
					deg_Printf("photo : take photo ok.<%s>\n",SysCtrl.file_fullname);
				}else
				{
					nv_jpgfile_delete(SysCtrl.jpg_fname.index & FILELIST_INDEX_MASK);
					deg_Printf("photo  : take photo fail.<%s>\n",SysCtrl.file_fullname);
					SysCtrl.jpg_fname.index = FILELIST_FLAG_IVL;				
				}
			}

			//sd_api_unlock();
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : taskRecordPhotoProcess
* Description    : take a photo by user config
* Input          : none
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
int taskRecordPhotoProcess(void)
{
	JPG_ENC_ARG jpg_arg;
	int ret = 0;

	jpg_arg.fd 			= -1;
	jpg_arg.dst_width 	= user_configValue2Int(CONFIG_ID_PRESLUTION)>>16;
	jpg_arg.dst_height 	= user_configValue2Int(CONFIG_ID_PRESLUTION)&0xffff;
	jpg_arg.img_Q		= JPEG_Q_75; //JPEG_Q_27
	jpg_arg.timestamp	= user_configValue2Int(CONFIG_ID_TIMESTAMP);
	jpg_arg.buf			= NULL;
	jpg_arg.size		= 0;
	//task_com_spijpg_Init(0);
	if(SysCtrl.spi_jpg_list < 0)
	{
		sd_api_lock();
	}else
	{
		hal_lcdGetVideoRatioResolution(&jpg_arg.dst_width,&jpg_arg.dst_height);
		jpg_arg.img_Q		= JPEG_Q_62;//JPEG_Q_75;//JPEG_Q_40; //JPEG_Q_27
	}

    {
		ret = app_taskRecordPhoto_callback(CMD_PHOTO_RECORD_START,(INT32U)&jpg_arg.fd);
		if(ret < 0)
			goto TAKE_PHOTO_END;
		if(SysCtrl.spi_jpg_list < 0)
		{
			deg_Printf("take photo : [%d:%d]\n",jpg_arg.dst_width,jpg_arg.dst_height);
			ret = imageEncodeStart(&jpg_arg, FUN_RECORD_STOP_WIN_SHOW);
		}else
		{
			deg_Printf("take photo SPI: [%d:%d]\n",jpg_arg.dst_width,jpg_arg.dst_height);
			ret = imageEncodeToSpi(&jpg_arg, FUN_RECORD_STOP_WIN_SHOW);
		}
		
		deg_Printf("take photoA : %d\n",ret);
		if(ret<0)
		{
			app_taskRecordPhoto_callback(CMD_COM_ERROR,(INT32U)&jpg_arg.fd);
			goto TAKE_PHOTO_END;
		}else
		{
	#if FUN_RECORD_STOP_WIN_SHOW
			recordPhotoOp.jpgsize = jpg_arg.size;
			recordPhotoOp.jpgbuf  = jpg_arg.buf;
	#endif		
		}
		app_taskRecordPhoto_callback(CMD_PHOTO_RECORD_STOP,(INT32U)&jpg_arg.fd);
    }
	if(SysCtrl.spi_jpg_list < 0)
    	sd_api_Stop();
    ret = 0;
TAKE_PHOTO_END:
	if(ret >= 0)
	{
		recordPhotoOp.file_remain--;	
	}
	if(SysCtrl.spi_jpg_list < 0)
		sd_api_unlock();
	return ret;
}
/*******************************************************************************
* Function Name  : taskRecordPhotoWinShowKick
* Description    : take a photo by user config
* Input          : none
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
int taskRecordPhotoWinShowKick(void)
{
	int ret = 0;
#if FUN_RECORD_STOP_WIN_SHOW
	//JPG_ENC_ARG jpg_arg;

	if(recordPhotoOp.step >= 0)
	{
		return 0;
	}
	if(recordPhotoOp.wintype <= LCDSHOW_NOT_SUB_WIN || recordPhotoOp.wintype >= LCDSHOW_WIN_MAX)
	{
		return 0;
	}
	if(recordPhotoOp.jpgsize == 0 || recordPhotoOp.jpgbuf == NULL)
	{
		ret = -1;
		goto WINKICK_END;
	}
	//jpg_arg.fd 			= -1;
	//hal_SensorResolutionGet(&jpg_arg.dst_width,&jpg_arg.dst_height);
	//jpg_arg.img_Q		= JPEG_Q_27;
	//jpg_arg.timestamp	= user_configValue2Int(CONFIG_ID_TIMESTAMP);
	//jpg_arg.buf			= NULL;
	//jpg_arg.size		= 0;
	//if(imageEncodeToRamStart(&jpg_arg) < 0)
	//{
	//	ret = -1;
	//	goto WINKICK_END;
	//}
	//recordPhotoOp.jpgsize = jpg_arg.size;
	//recordPhotoOp.jpgbuf  = jpg_arg.buf;
	if(hal_mjpDecodeParse((u8 *)recordPhotoOp.jpgbuf,320,240) < 0)
	{
		ret = -2;
		goto WINKICK_END;
	}

	if(app_lcdShowWinModeCfg(recordPhotoOp.wintype, recordPhotoOp.step) != 0)
	{
		ret = -3;
		goto WINKICK_END;
	}

	hal_lcdwin_buf_cfg(recordPhotoOp.jpgbuf, recordPhotoOp.jpgsize);
	//recordPhotoOp.time = XOSTimeGet();
WINKICK_END:
	if(ret < 0)
	{
		deg_Printf("PhotoWinShowKick fail:%d\n",ret);
		if(recordPhotoOp.jpgbuf)
		{
			hal_sysMemFree((void*)recordPhotoOp.jpgbuf);
			recordPhotoOp.jpgbuf = NULL;
		}

		recordPhotoOp.winState = 0;
	}else
	{
		recordPhotoOp.winState = 1;
	}
#endif
	return ret;
}
/*******************************************************************************
* Function Name  : taskRecordPhotoWinShowProcess
* Description    : take a photo by user config
* Input          : none
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
int taskRecordPhotoWinShowProcess(void)
{
#if FUN_RECORD_STOP_WIN_SHOW
	int res;
	//u32 time;
	if(recordPhotoOp.winState)
	{
		while(1)
		{
			hal_wdtClear();
			//hal_lcdwin_framecnt_get();
			if(hal_lcdwin_framesta_get() == WINAB_JFRAME_STA_END)
			{
				//time = XOSTimeGet();
				//if( time < (recordPhotoOp.time + recordPhotoOp.time_interval) )
				//{
				//	XOSTimeDly( recordPhotoOp.time_interval  + recordPhotoOp.time - time);
				//}
				//deg_Printf("DELAY[%d,%d]\n",time ,  recordPhotoOp.time);

				res = app_lcdShowWinModeCfg(recordPhotoOp.wintype, recordPhotoOp.step);
				//recordPhotoOp.time = XOSTimeGet();
				if(res != 0)
				{
					break;
				}
			}
			//

		}
		//HAL_CRITICAL_INIT();
		//HAL_CRITICAL_ENTER();
		if(res != 1)
			app_lcdShowWinModeCfg(LCDSHOW_NOT_SUB_WIN, 0);
		hal_lcdwin_buf_cfg(NULL, 0);
		//HAL_CRITICAL_EXIT();
		if(recordPhotoOp.jpgbuf)
		{
			hal_sysMemFree((void*)recordPhotoOp.jpgbuf);
			recordPhotoOp.jpgbuf = NULL;
		}
		recordPhotoOp.winState = 0;
	}
#endif
	return 0;
}
/*******************************************************************************
* Function Name  : taskRecordWinChangeProcess
* Description    : taskRecordWinChangeProcess function.
* Input          : 
* Output         : none                                            
* Return         : int fd : file handle
*******************************************************************************/
static void taskRecordPhotoWinChangeProcess(u8 enter)
{
	if(SysCtrl.winChangeEnable == 0)
		return;
	JPG_ENC_ARG jpg_arg;
	jpg_arg.fd 			= -1;
	
	jpg_arg.img_Q		= JPEG_Q_27;
	jpg_arg.timestamp	= 0;//user_configValue2Int(CONFIG_ID_TIMESTAMP);
	jpg_arg.buf			= NULL;
	jpg_arg.size		= 0;
	hal_lcdGetVideoRatioResolution(&jpg_arg.dst_width,&jpg_arg.dst_height);
	if(imageEncodeLcdToRamStart(&jpg_arg) < 0)
	{
		return;
	}
	deg_Printf("jpg_arg.buf:%x,jpg_arg.size:%x\n",jpg_arg.buf,jpg_arg.size);	
	if(enter)
	{
		taskMainWinInit(0,MEDIA_SRC_RAM, (u32)jpg_arg.buf, jpg_arg.size,MAIN_TO_SUB_CENTER);
		taskWinChangeProcess();
	}else
	{
		taskMainWinInit(0,MEDIA_SRC_RAM, (u32)jpg_arg.buf, jpg_arg.size,SUB_TO_MAIN_CENTER);
	}
}
/*******************************************************************************
* Function Name  : taskRecordPhotoOpen
* Description    : taskRecordPhotoOpen
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
static void taskRecordPhotoOpen(u32 arg)
{
	//deg_Printf("[TASK OPEN]<RecordPhoto>\n");
    imageEncodeInit();
#if  FUN_KID_FRAME_EN
	app_kid_frame_ctrl(KID_FRAME_DISTROY, 0);
#endif
#if  DEV_SENSOR_MAGIC_EN
	app_sensor_magic_ctrl(SENSOR_MAGIC_CHANGE_NONE, 0);
#endif
#if DEV_SENSOR_FILTER_EN
	app_sensor_filter_ctrl(SENSOR_FILTER_CHANGE_NONE);
#endif
#if DEV_SENSOR_LENS_EN
	app_sensor_lens_ctrl(SENSOR_LENS_CHANGE_NONE, 0);
#endif

	taskRecordPhotoWinChangeProcess(1);
	app_lcdPreview_start();
	while(hardware_setup.lcd_first_drop) //�ȴ�sensor OK
	{
		hal_wdtClear();
	}
	recordPhotoOp.winState = 0;
	recordPhotoOp.jpgbuf = NULL;
	recordPhotoOp.wintype = FUN_RECORD_STOP_WIN_TYPE; //可配 LCDSHOW_WIN_LEFTTOP~LCDSHOW_WIN_DOWN

	recordPhotoOp.step  		= -FUN_RECORD_STOP_WIN_STEP;  //win update change size
	recordPhotoOp.add_mode 		= PHOTO_NONE_MODE;
	recordPhotoOp.capture_time 	= user_configValue2Int(CONFIG_ID_CAPTURE_TIME);
	recordPhotoOp.capture_cnt  	= 0;
	recordPhotoOp.capture_kick  = 0;
	recordPhotoOp.upkeystate 	= KEY_STATE_INVALID;
	recordPhotoOp.downkeystate  = KEY_STATE_INVALID;
	if(recordPhotoOp.capture_time)
	{
		recordPhotoOp.capture_mode = 1;
	}else
	{
		recordPhotoOp.capture_mode = 0;
	}
	//recordPhotoOp.time_interval  = 200;
	uiOpenWindow(&recordPhotoWindow,0,0);
}
/*******************************************************************************
* Function Name  : taskRecordPhotoClose
* Description    : taskRecordPhotoClose
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
static void taskRecordPhotoClose(u32 arg)
{
	taskRecordPhotoWinChangeProcess(0);
	app_lcdCsiVideoShowStop();
	
#if  FUN_KID_FRAME_EN
	app_kid_frame_ctrl(KID_FRAME_DISTROY, 0);
#endif
#if  DEV_SENSOR_MAGIC_EN
	app_sensor_magic_ctrl(SENSOR_MAGIC_CHANGE_NONE, 0);
#endif
#if DEV_SENSOR_FILTER_EN
	app_sensor_filter_ctrl(SENSOR_FILTER_CHANGE_NONE);
#endif
#if DEV_SENSOR_LENS_EN
	app_sensor_lens_ctrl(SENSOR_LENS_CHANGE_NONE, 0);
#endif
	imageEncodeUninit();
	//videoRecordUninit();
	if(SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
	{
		SysCtrl.dev_stat_sdc = SDC_STAT_NORMAL;
	}
	task_com_spijpg_Init(1);
	task_com_sdlist_scan(1, 0);
	//deg_Printf("[TASK CLOSE]<RecordPhoto>\n");
}
/*******************************************************************************
* Function Name  : taskRecordPhotoClose
* Description    : taskRecordPhotoClose
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
static void taskRecordPhotoService(u32 arg)
{
	//task_com_spijpg_Init(0);
}

ALIGNED(4) sysTask_T taskRecordPhoto =
{
	"Record Photo",
	0,
	taskRecordPhotoOpen,
	taskRecordPhotoClose,
	taskRecordPhotoService,
};


